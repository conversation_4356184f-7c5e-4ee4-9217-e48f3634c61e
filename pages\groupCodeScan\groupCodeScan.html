<style>
    .group-scan-divide{
        height: 1px;
        width: 100%;
        border-bottom: 1px solid #dddddd;
    }
    .group-scan-param{
        margin-top:1rem
    }

    .pagination-info {
        margin-left: 1rem;
    }

</style>
<!-- left column -->
<div class="col-sm-12 full-height" style="display:flex">
    <!-- general form elements -->
    <div class="box no-border no-margin" style="width:48%">
        <div class="box-body">

            <h3 data-i18n="SensorGroupNumScan">传感器组号扫描</h3>
            <div class="group-scan-divide"></div>

            <div class="group-scan-param" style="display: none !important;">
                <label class="col-sm-3 control-label" data-i18n="LoraFrequency" style="padding-left: 0;" data-i18n="LoraFrequency">主机区域</label>
                <div class="col-sm-9">
                    <label class=" control-label" id="groupLoraFrequency"></label>
                </div>
            </div>

            <div class="group-scan-param">
                <label for="addScanSensorId" data-i18n="SensorScanID">待扫描传感器ID</label>
                <div class="input-group file-caption-main" style="display: flex;">
                    <input id="addScanSensorId" class="col-sm-4 form-control" role="presentation" autocomplete="off"/>
                    <button type="button" id="addScanSensorIdBtn" class="btn btn-block btn-primary" style="width: 20%;margin-left: 1.5rem;" data-i18n="add">增加</button>
                </div>
            </div>
            <!-- /.fu-scan -->
            <div class="group-scan-param">
                <div class="col-sm-6" style="padding-left: 0;">
                    <label for="fu-aduType" data-i18n="sensorType">传感器类型</label>
                    <select id="fu-aduType" class="form-control select2" data-placeholder="" style="width: 100%;"></select>
                </div>
                <div class="col-sm-6">
                    <label for="fu-aduType" data-i18n="GroupNumRange">组号范围</label>
                    <div>
                        <select id="startGroupNo" class="form-control select2" data-placeholder="" style="width: 45%;"></select>
                        <label>~</label>
                        <select id="endGroupNo" class="form-control select2" data-placeholder="" style="width: 45%;"></select>
                    </div>
                </div>
            </div>
            <!-- /.fu-aduType -->
            <div class="group-scan-param">
                <label for="fu-aduItems" style="margin-top: 1rem;" data-i18n="sensorList">传感器列表</label>
                <div id="fu-aduItems" class="multiselect">
                    <div class="col-xs-5 no-padding full-height">
                        <select name="from[]" id="group_scan_shuttle" class="form-control" style="height: 100%;" multiple="multiple">
                        </select>
                    </div>
                    <div class="col-xs-2">
                        <button type="button" id="group_scan_shuttle_rightAll" class="btn btn-default btn-block"><i
                                class="glyphicon glyphicon-forward"></i></button>
                        <br>
                        <button type="button" id="group_scan_shuttle_rightSelected" class="btn btn-default btn-block"><i
                                class="glyphicon glyphicon-chevron-right"></i></button>
                        <br>
                        <button type="button" id="group_scan_shuttle_leftSelected" class="btn btn-default btn-block"><i
                                class="glyphicon glyphicon-chevron-left"></i></button>
                        <br>
                        <button type="button" id="group_scan_shuttle_leftAll" class="btn btn-default btn-block"><i
                                class="glyphicon glyphicon-backward"></i></button>
                    </div>
                    <div class="col-xs-5 no-padding full-height">
                        <select name="to[]" id="group_scan_shuttle_to" class="form-control" style="height: 100%;"
                            multiple="multiple"></select>
                    </div>
                </div>
            </div>
            <!-- /.fu-aduItems -->
            <br>
            <div id="da-progress" class="progress-group hide">
                <span class="progress-text" data-i18n="PreSensorScan">准备扫描</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- /.progress-group -->
        </div>
        <div class="content-footer">
            <div style="display: flex;">
                <button type="button" id="fu-cancel" class="btn btn-default hide" style="width: 100px;margin: 10px 60px 10px auto;display: flex;
                  justify-content: center;
                  align-items: center;">
                  <img src="dist/img/loading.gif" style="margin: 0 5px 0 0;" class="img-loading hide" alt=""/>
                  <div data-i18n="cancel">取消</div>
                </button>
                <button type="button" id="fu-scan-group" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 60px 10px auto" style="width: 100px" data-i18n="SensorScan">扫描</button>
            </div>
        </div>
        <br />
    </div>
    <div class="box no-border" style="width:50%;margin-left: 1rem;">
        <div class="box-body">
            <h3 data-i18n="SensorScanResult">扫描结果</h3>
            <div class="group-scan-divide"></div>
            <div class="tab-content " style="padding:0px;height: 90%">
                <div class=" tab-pane full-height active" id="groupScanTable">
                    <table id="tableGroupCodeScan" class="table table-bordered table-hover full-height">
                    </table>
                </div>
            </div>
        </div>
    </div>
    <!-- /.box -->

    <!-- /.content-footer -->
</div>
<!--/.col (left) -->



<script>
    //@ sourceURL=hardware_update.js
    //前端类型下拉框
    initPageI18n();
    initGroupCodeScan();
    var hasInitGroupCodeScan = false;

    $('#startGroupNo').on('change',(ele) => {
        var end = $('#endGroupNo').val();
        if(ele.currentTarget.value > parseInt(end)){
            layer.msg(getI18nName('GroupNumStartTips'));
            ele.currentTarget.value = end;
        }
    });
    $('#endGroupNo').on('change',(ele) => {
        var start = $('#startGroupNo').val();
        if(ele.currentTarget.value < parseInt(start)){
            layer.msg(getI18nName('GroupNumEndTips'));
            ele.currentTarget.value = start;
        }
    });

    var aduType = $("#fu-aduType");
    aduType.select2({
        minimumResultsForSearch: Infinity
    });


    $('#startGroupNo').select2();
    $('#endGroupNo').select2();
    aduType.change(function () {
        $("#group_scan_shuttle").empty();
        $("#group_scan_shuttle_to").empty();
        var selectText = $(this).children('option:selected').val();
        getADUVersionListForGroupCodeScan(selectText);
    });

    //前端列表穿梭框
    $('#group_scan_shuttle').multiselect();
    $("#fu-aduItems").height($('#fu-aduItems .col-xs-2').height());

    $('#addScanSensorIdBtn').click(() => {
        var tempAduID = $('#addScanSensorId').val();
        checkIllegalNameValueByPatten(tempAduID, 'addScanSensorId', numberPatten, getI18nName(
                'instNumberPattenTip'));
        if(!checkSenserId(tempAduID)){
            var option = $("<option />", {
            "value": "" + tempAduID + ""
            }).append(tempAduID);
            $("#group_scan_shuttle_to").append(option);
        }
    });

    function checkSenserId(aduId){
        var hasAdded = false;
        var group_scan_shuttle = $('#group_scan_shuttle').find('option');
        group_scan_shuttle = Array.from($('#group_scan_shuttle')[0].getElementsByTagName('option'));
        var group_scan_shuttleTo = $('#group_scan_shuttle_to').find('option');
        group_scan_shuttleTo = Array.from($('#group_scan_shuttle_to')[0].getElementsByTagName('option'));
        var tempArr = [].concat(group_scan_shuttle,group_scan_shuttleTo);
        for (var i = 0; i < tempArr.length; i++) {
            var value = tempArr[i].value;
            if(value == aduId){
                hasAdded = true;
                layer.msg(getI18nName('IDExists'));
                break;
            }
        }
        return hasAdded;
    }

    //开始扫描组号
    $('#fu-scan-group').click(function () {
        var group_scan_shuttleTo = $('#group_scan_shuttle_to').find('option');
        if (group_scan_shuttleTo.length > 0) {
            var aduList = new Array();
            for (var i = 0; i < group_scan_shuttleTo.length; i++) {
                aduList.push(group_scan_shuttleTo[i].value);
            }
            /* var formData = new FormData();
            formData.append("aduType", aduType.children('option:selected').val());
            formData.append("aduList", aduList.toString());
            formData.append("startGroupNo",$('#startGroupNo').val());
            formData.append("endGroupNo",$('#endGroupNo').val()); */

            var formData={
                aduType:aduType.children('option:selected').val(),
                aduList:aduList.toString(),
                startGroupNo:$('#startGroupNo').val(),
                endGroupNo:$('#endGroupNo').val(),
            }

            pollPost('FindAduGroupNo',formData,function(){
                startFindAduGroupNoState(true);
            });
        } else {
            $('.notifications').notify({
                message: {
                    text: getI18nName('SensorGroupNumScanSelectTips') //'Please select LDCU!'
                },
                type: "warning",
            }).show();
        }
    })

    $('#fu-cancel').click(function () {
        $.fn.alertMsg(
            'warning',
            getI18nName('SensorGroupNumScanCancelTips'), [{
                id: 'no',
                text: getI18nName('no')
            }, {
                id: 'yes',
                text: getI18nName('yes'),
                callback: function () {
                    //禁用取消按钮
                    $('#fu-cancel').prop('disabled', true);
                    $('#fu-cancel img').removeClass('hide');
                    //关闭websocket请求
                    pollPost('CancelFindAduGroupNo',{aduType:aduType.children('option:selected').val()})
                }
            }]
        );
    });
</script>