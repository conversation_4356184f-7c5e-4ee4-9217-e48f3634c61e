{"loginTime": "Время входа ", "changerUser": " Переключение пользователя ", "change": " Переключить ", "login": " Регистрация ", "systemShutDownConfirmTip": " Система отключится через 1 минуту, отменяется ли операция? ", "systemShutDownTip": " Одна минута, выключите! ", "systemShutDownCancel": " Отменить выключение ", "loadingData": " Работает над загрузкой данных, пожалуйста, подождите... ", "pageSize": " Каждая страница {1} ", "pageSelecter": " Показать: {1} - {2}, всего {3} полосы ", "search": " Поиск ", "noMatch": " Не найдено совпадений ", "index": " Серийный номер ", "type": " Тип ", "aduId": " <PERSON>од ", "connectedState": " Состояние сети ", "commType": " Средства связи ", "loraSignalLevel": " Интенсивность сигнала LoRa ", "loraSNR": " отношение сигнала к шуму ", "powerSupplyMode": " Режим питания ", "byBattery": " Встроенная батарея ", "byCable": " Внешний источник питания ", "battaryPercent": " Электричество ", "battaryLife": " Продолжительность полета (день) ", "deviceNameTable": " Имя устройства ", "pointType": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "isconnected": " Статус ", "onlineState": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "offlineState": " Потеря связи ", "aduNeverConnectedState": " Не подключен ", "updateTime": " Время обновления ", "data": " Данные ", "chart": " <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "operate": " Операция ", "lookUp": " Посмотреть ", "tbSampleDate": " Время сбора ", "collect": " Сбор ", "errorTip": " Ошибка: {1} ", "AEamp": " Амплитуда AE ", "aeChartTitle": " амплитудный спектр AE ", "humidityChartTitle": " Кривая влажности ", "temperatureChartTitle": " Температурная кривая ", "noiseChartTitle": " Кривая шума ", "coreGroundingCurrentChartTitle": " кривая тока заземления сердечника ", "TEVamp": " амплитуда TEV ", "TEVYunit": " Единицы [dB] ", "UnitFomat": " Единицы [{1] ", "unitParse": " Единицы [{1] ", "maxValue": " Максимальное значение ", "minValue": " Минимальное значение ", "average": " Средний показатель ", "AT&T": " США AT & T ", "China Unicom": " Китай соединен ", "China Mobile": " Китай движется ", "Transformer": " Трансформатор ", "Breaker": " Переключатель ", "Disconnector": " Изолятор ", "Isolator": " <PERSON><PERSON><PERSON><PERSON> ", "Arrester": " Молниеотвод ", "PT": " трансформатор напряжения ", "CT": " трансформатор тока ", "Busbar": " <PERSON><PERSON><PERSON> ", "Circuit": " Союз матерей ", "Switchgear": " Шкаф выключателя ", "Power Cable": " Электрический кабель ", "Lightning Rod": " Молниеотвод ", "Wall Bushing": " Втулка сквозная ", "Reactor": " Реактор ", "Electric Conductor": " Электрический провод (направляющий ряд) ", "Power Capacitor": " Электрический конденсатор ", "Discharge Coil": " разрядная катушка ", "Load Switch": " Переключатель нагрузки ", "Grounding Transformer": " Изменение заземления ", "Grounding Resistance": " сопротивление заземления ", "Grounding Grid": " Сеть заземления ", "Combined Filter": " Фильтр связи ", "Insulator": " Изолятор ", "Coupling Capacitor": " конденсатор связи ", "Cabinet": " Шка<PERSON> ", "Other": " Прочее ", "Fuse": " Плавильный предохранитель ", "Using Transformer": " Используемые изменения ", "Arc Suppression Device": " Устройство дугогашения ", "Main Transformer": " Главный трансформатор ", "Wave Trap": " волнорез ", "Combined Electric Appliance": " Комбинированные электроприборы ", "Combined Transformer": " Комбинированный трансформатор ", "monitor": " Мониторинг в реальном масштабе времени ", "dataSelect": " Запрос данных ", "themeSkin": " Кожа ", "themeBlue": " Синий ", "settings": " Настройка системы ", "datetime_set": " Настройка времени ", "language_set": " Параметры языка ", "soft_setting": " Настройки системы ", "file_manage": " Управление архивами ", "instrument_set": " Настройка датчиков ", "file_point_set": " Настройка точки ", "alarm_param_set": " Настройка напоминаний @ info: whatsthis ", "alarm_manager": " Управление напоминанием @ info: whatsthis ", "audit_view": " Проверка ", "modbus_setting": " Настройка Modbus ", "hardware_update": " Обновление прошивки ", "collect_mode": " Режим сбора ", "net_set": " Настройки сети ", "main_station_params": " Настройка главной станции ", "sync_data": " Синхронизация данных ", "syncingData": " Синхронизация данных... ", "syncingDataCancel": " Отмена синхронизации ", "syncingDataFailed": " Ошибка синхронизации ", "syncingDataSuccess": " Синхронизация данных завершена ", "syncingDataProgress": " Прогресс синхронизации данных: {1} ", "syncingDataProgress2": " Синхронизация прогресса данных [{1}] ", "export_data": " Экспорт данных ", "system_info": " Системная информация ", "monitoringTable": " Таблицы данных ", "PD": " Встроенный датчик размещения ", "PD_THREE": " Встроенный датчик размещения (три в одном) ", "PD_FIVE": " Встроенный датчик размещения (пять в один) ", "OLD_IS": " Интеллектуальный датчик УВЧ ", "MEU": " датчик механических характеристик ", "UHF_IS": " Интеллектуальный датчик УВЧ ", "HFCT_IS": " Интеллектуальный высокочастотный датчик ", "PD_IS": " Внешний интеллигентный датчик ", "TRANSFORMER_AE_IS": " ультразвуковой датчик трансформатора ", "GIS_AE_IS": " ГИС ультразвуковой датчик ", "ENV_IS": " Интеллектуальный датчик окружающей среды ", "Arrester_U_IS": " Интеллектуальный датчик напряжения разрядника ", "Arrester_I_IS": " интеллектуальный датчик тока разрядника ", "LeakageCurrent_IS": " Интеллектуальный датчик тока утечки ", "Vibration_IS": " Интеллектуальный датчик вибрации ", "MECH_IS": " Интеллектуальный монитор механических характеристик ", "TEMP_HUM_IS": " Датчик температуры и влажности ", "GrounddingCurrent_IS": " Интеллектуальный датчик тока заземления ", "MECH_PD_TEMP": " встроенный трехмерный датчик температуры ", "GIS_LOWTEN": " датчик низкого давления ", "CHANNEL_OPTICAL_TEMP": " Волоконно - оптический датчик температуры ", "VaisalaDTP145": " Visala DTP145 ", "Wika_GDT20": " Вика GDT20 ", "Wika_GDHT20": " Вика GDHT20 ", "SHQiuqi_SC75D_SF6": " Скачать Shanghai SC75D ", "SPTR_IS": " датчик тока заземления сердечника ", "TEMPFC_OUT": " Датчик температуры (CLMD) ", "TEMPXP_OUT": " Датчик температуры (SPS061) ", "SF6YN_OUT": " Датчик давления газа SF6 (FTP - 18) ", "FLOOD_IS": " Интеллектуальный датчик погружения ", "TEMPKY_OUT": " Датчик температуры (RFC - 01) ", "SMOKERK_OUT": " Сигнализатор дыма (RS - YG - N01) ", "HIKVIDEO_OUT": " Видеосектор (HIK) ", "TEMPSB_OUT": " Датчик температуры (DS18B20) ", "TEMP_HUM_JDRK_OUT": " Датчик температуры и влажности (RS - WS - N01 - 2) ", "SF6_OUT": " Датчик SF6 & O2 (WFS - S1P - SO) ", "FAN_OUT": " Контроллер вентилятора ", "LIGHT_OUT": " Контроллер освещения ", "NOISE_JDRK_OUT": " Шумовые датчики (RS - WS - N01) ", "IR_DETECTION_OUT": " инфракрасный детектор с двойным считыванием ", "TEMPWS_OUT": " Датчик температуры и влажности (WS - DMC100) ", "FLOODWS_OUT": " Датчик погружения (WS - DMC100) ", "NOISEWS_OUT": " Шумовые датчики (WS - DMC100) ", "VibrationSY_OUT": " датчик колебаний восходящего солнца ", "TEVPRPS_IS": " Интеллектуальный датчик переходного геонапряжения ", "SF6_IS": " SF6 Интеллектуальный датчик ", "phase": " Фазы ", "period": " Период ", "AMP": " Амплитуда ", "RMS": " Эффективное значение ", "max": " Максимальное значение цикла ", "fre1Value": " Частотный компонент 1 ", "fre2Value": " Частотный компонент 2 ", "All-pass": " Полный доступ ", "Low-pass": " Низкий поток ", "High-pass": " Ква<PERSON><PERSON><PERSON><PERSON> ", "No-Record": " Не зарегистрировано ", "TEV": " Переходное геонапряжение ", "AE": " Ультразвук ", "TEMP": " Температура ", "HFCT": " ВЧ высокочастотный ток ", "UHF": " УВЧ ", "Humidity": " Влажность ", "Decibels": " ДБ децибел ", "Noisy": " Звуковое давление ", "Noise": " <PERSON>ум ", "ENVGas": " Атмосфера ", "envgas": " Атмосферные тенденции ", "MP": " Механические характеристики ", "FLOOD": " Затопление водой ", "SMOKE": " Чувство дыма ", "SF6": " Фторид серы (VI) ", "FAN": " Вентилятор ", "LIGHT": " Освещение ", "NOISE": " <PERSON>ум ", "IRDETECTION": " Инфракрасное излучение ", "SF6YN": " SF6 Давление газа ", "ArresterU": " напряжение молниеотвода ", "ArresterI": " ток разрядника ", "LeakageCurrent": " Утечка тока ", "Vibration": " Вибрация ", "FrostPointRaw": " Точка инея ", "FrostPointATM": " Точка инея (стандартное атмосферное давление) ", "DewPointRaw": " Точка росы ", "DewPointATM": " Точка росы (стандартное атмосферное давление) ", "Moisture": " Микровода ", "AbsolutePressure": " Абсолютное давление ", "NormalPressure": " Стандартное давление ", "Density": " Плотность ", "Oxygen": " Содержание кислорода ", "SPTR": " ток заземления сердечника ", "GrounddingCurrent": " Заземленный ток ", "VIDEO": " Видеография ", "TEVPRPS": " Переходное напряжение PRPS ", "confirm": " Определение ", "close": " Закрыть ", "yes": " Да. ", "no": " Нет ", "continue": " Прод<PERSON><PERSON><PERSON><PERSON><PERSON>. ", "none": " Нет ", "DISK": " Хранение ", "MEMORY": " Память ", "noTestData": " Нет тестовых данных ", "connected": " Подключение ", "disconnected": " Отключить ", "virtualKeyBord": " Виртуальная клавиатура ", "pleaseInput": " Введите ", "pleaseInputFormat": " Введите {1}. ", "tips": " Подсказка ", "confirmTips": " Подтвердить подсказку ", "getDirFailed": " Ошибка получения каталога: ", "backupSuccessTip": " Данные успешно скопированы, пожалуйста, посмотрите в каталоге {1} ", "backupFailedTip": " Ошибка резервного копирования данных ", "exportFailedTip": " Ошибка экспорта данных: {1} ", "historyDataTypeNotSuport": " Не поддерживается запрос исторических данных этого типа датчика ", "stopExportTips": " Нужно ли прекращать экспорт данных? ", "stationNameTips": " Пожалуйста, заполните название сайта. ", "powerUnitNameTips": " Заполните, пожалуйста, электрическую единицу. ", "selectDeviceTips": " Пожалуйста, выберите устройство. ", "selectPointTips": " Выберите точку измерения. ", "selectDeviceOfPointTips": " Выберите устройство, которое должно добавить точку измерения ", "saveSuccess": " Сохранить успешно ", "saveFailed": " Ошибка сохранения: ", "operatFailed": " Ошибка операции: {1} ", "chosseSensorUpdate": " Выберите датчик, который необходимо обновить ", "chooseFileUpdate": " Выберите файл обновления ", "cancelUpdateSensorConfirm": " Подтверждаете ли вы отмену программы обновления датчика? ", "enterServerUrlTips": " Введите адрес сервера ", "enterServerUrlError": " Ошибка ввода адреса сервера, введите его снова. ", "enterServerPortTips": " Введите порт сервера. ", "enterServerPortError": " Ошибка ввода порта сервера, диапазон ввода порта: [165535]. ", "NTPserverIpError": " Ошибка ввода адреса сервера времени, введите его снова. ", "NTPserverPortError": " Ошибка ввода порта сервера времени, диапазон ввода порта: [165535]. ", "enterNetName": " Введите имя сети. ", "enterIP": " Введите IP. ", "enterIPError": " Ошибка ввода IP - адреса, например: *********** ", "enterNetMask": " Введите маску подсети ", "enterNetError": " Ошибка ввода маски подсети, например: ************* ", "enterGateWay": " Введите шлюз ", "enterGateWayError": " Ошибка настройки шлюза, например: *********** ", "enterAPN": " Выберите APN ", "pdSampleIntervalTip": " PD - Интервал отбора проб от 20 до 594099s (99h99m99s) ", "meuSampleIntervalTip": " МЕУ - Интервалы отбора проб от 10 до 86 400 ", "monitorAwakeTimeTip": " Интервал между 1 и 86 400. ", "monitorSleepSpaceTip": " Диапазон интервалов пробуждения: [{1}, {2}] min ", "uploadIntervalTip": " Интервал загрузки от 60 до 86 400. ", "sampleIntervalTip": " Целое число с интервалом отбора проб от 1 часа до 72 часов ", "monitorSampleTimeTip": " Начальный момент выборки узла - от 0 до 23 часов. ", "monitorSampleIntervalTip": " Целые числа с интервалом выборки от 1 часа до 168 часов ", "updatingSensor": " Обновляются датчики... ", "updatingSensorProgress": " Прогресс обновления датчика: {1} ", "updatingSensorProgress2": " Прогресс обновления датчиков [{1] ", "selectInstTip": " Выберите датчик, который необходимо изменить ", "selectChannelTip": " Выберите канал, который необходимо изменить ", "instCodeTip": " Код датчика не может быть пустым ", "commLinkTypeTip": " Выберите тип канала связи ", "commLinkPortTip": " Выберите порт связи ", "continuSampTimeTip": " Время непрерывного сбора составляет от {1% min до {2} min. ", "continuSampTimeCompareTip": " Продолжительность непрерывного сбора должна быть меньше интервала отбора проб. ", "instSampleSpaceTip": " Интервалы отбора проб варьируются от {1} мин до {2} мин. ", "instSampleStartTimeTip": " Начальный момент отбора проб - от 0 до 23 часов. ", "instNumberPattenTip": " Может содержать только цифры 0 - 9, буквы a - zA - Z и: ", "instIPPattenTip": " Формат IP и порта: 0.0.0.0: 1 ", "deleteInstTip": " Выберите датчик, который необходимо удалить ", "selectSensorTip": " Выберите датчик ", "deleteInstConfirmTip": " Определить удаление < / br > {1} < / br > Код датчика: {2}? ", "activeInstConfigTip": " Определите, примените ли эту конфигурацию к аналогичному датчику < / br > {1}? ", "activeBatchConfigsSuccess": " Серийный датчик успешно настроен ", "activeBatchConfigsBegin": " Начинается серийная настройка датчиков ", "activeBatchConfigsFailed": " Ошибка пакетной настройки датчиков ", "collectStartOnceTip": " Определить начало сбора данных с аналогичных датчиков < / br > {1}? ", "collectStartTip": " Определить начало сбора данных < / br > {1: 0 < / br > Кодирование датчика: {2}? ", "wakeUpInstrumentOnceTip": " Определить начало пробуждения < / br > {1} аналогичных датчиков? ", "wakeUpInstrumentTip": " Определить начало пробуждения < / br > {1} < / br > датчик: {2}? ", "emptySensorDataOnceTip": " Определить, опустошить все данные датчика < / br > {1: 1. ", "emptySensorDataTip": " Определить, чтобы очистить все данные < / br > {1} < / br >, закодированные датчиком: {2}? ", "emptySensorDataSuccess": " Данные датчика успешно очищены. ", "emptySensorDataFailed": " Ошибка очистки данных датчика: ", "ae_chart": " Мониторинг в реальном масштабе времени - AE ", "uhf_chart": " Мониторинг в реальном масштабе времени - UHF - карта ", "hfct_chart": " Мониторинг в реальном масштабе времени - HFCT - карта ", "tev_chart": " Мониторинг в реальном времени - амплитуда TEV ", "tev_prps_chart": " Мониторинг в реальном времени - спектр PRPS переходного геонапряжения ", "temperature_chart": " Мониторинг в реальном времени - температура ", "humidity_chart": " Мониторинг в реальном времени - влажность ", "mechanical_chart": " Мониторинг в реальном времени - механические характеристики ", "arrester_chart": " Мониторинг в реальном времени - громоотвод ", "leakage_current_chart": " Мониторинг в реальном времени - ток утечки ", "grounddingcurrent_chart": " Мониторинг в реальном времени - ток заземления ", "vibration_pickup_chart": " Мониторинг в реальном времени - вибрация ", "density_micro_water_history": " Мониторинг в реальном масштабе времени - плотность микроводы ", "core_grounding_current": " Мониторинг в реальном времени - ток заземления сердечника ", "noise_chart": " Мониторинг в реальном времени - шум ", "water_immersion": " Мониторинг в реальном времени - затопление ", "smoke_sensor": " Мониторинг в реальном времени - ощущение дыма ", "video_sensor": " Мониторинг в реальном времени - видео ", "sf6_sensor": " Мониторинг в реальном масштабе времени - SF6 ", "error": " Ошибка ", "warning": " предупреждение ", "success": " Успех ", "userErrorTip": " Ошибка имени пользователя или пароля ", "errorNoDeviceId": " Идентификатор устройства отсутствует ", "errorNoSensorId": " Идентификатор датчика отсутствует ", "errorExistSensorId": " Идентификатор датчика уже существует ", "errorNoChannelId": " Идентификатор канала не существует ", "errorNoPointId": " Точка измерения не существует. ", "errorDataExportErrDir": " Экспорт данных, неправильный путь к файлу. ", "errorDataExportErrTime": " Ошибка во времени экспорта данных ", "errorDataExportNoData": " Экспорт данных, нет данных. ", "errorDataExportNoSpace": " Экспорт данных, флэшка заполнена ", "errorDataExportNoDisk": " Экспорт данных, без флешки. ", "errorDataExportNoInfo": " Экспорт данных, нет информации о сайте. ", "errorDataExportOther": " Экспорт данных, другие ошибки. ", "errorSetModeBus": " Ошибка настройки Modebus ", "errorSameNamePoint": " Точка измерения уже существует. ", "errorIP": " Ошибка IP ", "errorGPRSSet": " Ошибка настройки GPRS ", "errorSenorUpdateErrFile": " Ошибка обновления файла ", "errorSenorUpdateNotMatch": " Обновление датчика, тип датчика не совпадает с файлом обновления ", "errorSenorUpdating": " Датчики обновляются, обновляются ", "errorSenorUpdateSizeCheckFailed": " Ошибка проверки размера прошивки ", "errorSenorUpdateVersionCheckFailed": " Ошибка проверки номера версии прошивки ", "errorSenorUpdateDeviceTypeCheckFailed": " Ошибка проверки типа прошивки ", "errorSenorUpdateCRCCheckFailed": " Ошибка проверки прошивки CRC ", "errorSenorUpdateFailed": " Ошибка обновления датчика ", "errorSenorUpdateConnectErr": " Обновление прошивки ", "errorDataCleanFailed": " Ошибка очистки данных! ", "errorCodeWorkGroupNotExist": " Рабочей группы не существует! ", "errorCodeInvalidChannel": " Незаконный проход! ", "errorCodeInvalidWirignMode": " Незаконный способ подключения! ", "errorCodeInvalidAlarmMode": " Незаконный способ оповещения! ", "errorNoPermission": " У этого пользователя нет прав. ", "illegalUser": " Незаконные пользователи! ", "legalPattenMsg": " Может содержать только символы (без пробелов): китайские буквы и цифры Римское число I - Ⅻ -  () [] # ", "groundCurrent": " диаграмма тенденций тока заземления ", "groundCurrentA": " А - сопряженный ток ", "groundCurrentB": " В - фазовый ток ", "groundCurrentC": " С - сопряженный ток ", "leakageCurrent": " диаграмма трендов полного тока ", "leakageCurrentA": " А - фазный полный ток ", "leakageCurrentB": " В - фазный полный ток ", "leakageCurrentC": " С - фазный полный ток ", "ResistiveCurrent": " диаграмма тенденций тока сопротивления ", "ResistiveCurrentA": " А - фазный ток сопротивления ", "ResistiveCurrentB": " В - фазовый ток сопротивления ", "ResistiveCurrentC": " С - фазный ток сопротивления ", "resistiveCurrentA": " А - фазный ток сопротивления ", "resistiveCurrentB": " В - фазовый ток сопротивления ", "resistiveCurrentC": " С - фазный ток сопротивления ", "referenceVoltageA": " опорное напряжение фазы А ", "referenceVoltageB": " опорное напряжение фазы В ", "referenceVoltageC": " Сфазное опорное напряжение ", "grounddingCurrent": " диаграмма тенденций тока заземления ", "grounddingCurrentA": " А - сопряженный ток ", "grounddingCurrentB": " В - фазовый ток ", "grounddingCurrentC": " С - сопряженный ток ", "timeDomain": " Диаграмма временной области ", "frequencyDomain": " Диаграмма частот ", "characterParam": " Характерные параметры ", "TimeDomainDataX": " Вибрационный сигнал оси X ", "TimeDomainDataY": " Вибрационный сигнал оси Y ", "TimeDomainDataZ": " Вибрационный сигнал по оси Z ", "FrequencyDomainDataX": " спектрограмма вибрационного сигнала - ось Х ", "FrequencyDomainDataY": " спектрограмма вибрационного сигнала - ось Y ", "FrequencyDomainDataZ": " спектрограмма вибрационного сигнала - ось Z ", "ACCAVGX": " Среднее ускорение по оси X ", "ACCAVGY": " Среднее ускорение по оси Y ", "ACCAVGZ": " Среднее ускорение по оси Z ", "ACCMAXX": " Максимальное ускорение по оси X ", "ACCMAXY": " Максимальное ускорение по оси Y ", "ACCMAXZ": " Максимальное ускорение по оси Z ", "AMPAVGX": " Среднее значение амплитуды по оси X ", "AMPAVGY": " средняя амплитуда по оси Y ", "AMPAVGZ": " Средняя амплитуда по оси Z ", "AMPMAXX": " Максимальное значение амплитуды по оси X ", "AMPMAXY": " Максимальное значение амплитуды по оси Y ", "AMPMAXZ": " Максимальное значение амплитуды по оси Z ", "MAXFreqX0": " Экстремальная точка по оси X частота 1 ", "MAXFreqY0": " Экстремальная точечная частота по оси Y 1 ", "MAXFreqZ0": " Экстремальная точечная частота по оси Z 1 ", "MAXFreqX1": " Экстремальная точечная частота по оси X 2 ", "MAXFreqY1": " Экстремальная точечная частота по оси Y 2 ", "MAXFreqZ1": " Экстремальная точечная частота по оси Z 2 ", "MAXFreqX2": " Экстремальная точечная частота по оси X 3 ", "MAXFreqY2": " Экстремальная точечная частота оси Y 3 ", "MAXFreqZ2": " Экстремальная точечная частота по оси Z 3 ", "sensorType": " Тип датчика ", "sensorList": " Список датчиков ", "gain": " Усиление ", "trigger": " амплитуда срабатывания ", "wave_filter": " Частотный диапазон ", "sample_date": " Дата отбора проб ", "sample_time": " Время отбора проб ", "rms": " Эффективное значение ", "cycle_max": " Максимальное значение цикла ", "frequency1": " Частотный компонент 1 ", "frequency2": " Частотный компонент 2 ", "time_interval": " Временные интервалы ", "realData": " Данные в реальном времени ", "historyData": " Исторические данные ", "trendSync": " Ана<PERSON><PERSON>з тенденций ", "preData": " Предыдущий ", "nextData": " Следующий ", "systemDate": " Дата системы ", "systemTime": " Системное время ", "backupType": " Тип резервного копирования ", "plusBackup": " Дополнительные резервные копии ", "allBackup": " Полное резервное копирование ", "timeRange": " Сроки ", "exportPath": " Путь экспорта ", "getExportDir": " Доступ к каталогу ", "checkDir": " Проверить каталог ", "exportingData": " Экспортируется... ", "cancel": " Отменить ", "export": " Экспорт ", "stationConfig": " Настройка сайта ", "stationDelSuccess": " Удалить сайт успешно ", "stationDelFailed": " Ошибка удаления сайта: ", "deviceConfig": " Настройка первичного оборудования ", "pointConfig": " Настройка точки ", "stationName": " Название сайта ", "stationPMS": " Код сайта ", "stationLevel": " класс напряжения станции ", "powerUnit": " Электрическая единица ", "save": " Сохранить ", "operationSuccess": " Операция прошла успешно ", "deviceAddSuccessTips": " Успешное добавление оборудования ", "deviceEditSuccessTips": " Успешное изменение устройства. ", "deviceDelSuccessTips": " Удалить устройство успешно ", "pointAddSuccessTips": " Увеличить успех ", "pointEditSuccessTips": " Изменить точку успешно ", "pointDelSuccessTips": " Удалить точку успешно ", "deleteFailedTips": " Ошибка удаления ", "save_add": " Сохранить / увеличить ", "delete": " Удалить ", "deviceType": " Тип устройства ", "deviceLevel": " Класс напряжения оборудования ", "add": " Увеличение ", "channelTypeAlarm": " Тип данных ", "alarmThreshold": " Порог напоминания ", "alarmRecoveryThreshold": " Порог восстановления напоминания ", "alarmChannel": " Выбор канала напоминания @ info: whatsthis ", "built_in_channel_1": " Встроенный канал 1 ", "External_IO_module": " Внешний модуль IO ", "not_associated": " Не связанные ", "alarmExternalIOSN": " Кодирование внешних модулей IO ", "alarmExternalIOChannel": " Внешний канал модуля IO ", "wiringMode": " Подключение ", "alarmMode": " Режим напоминания @ info: whatsthis ", "alarmTime": " Время напоминания @ info: whatsthis ", "alarmInterval": " Интервал напоминания @ info: whatsthis ", "alarmDuration": " Длительность напоминания @ info: whatsthis ", "normal_open": " Открыть ", "normal_close": " Постоянная блокада ", "continuous": " Непрерывность ", "timing": " Время ", "interval": " Интервал ", "alarmSaveSuccess": " Настройка напоминания успешно сохранена ", "alarmDelSuccess": " Настройка напоминания Удалить ", "deviceName": " Название устройства ", "pointName": " Название точки измерения ", "testStation": " Испытательная площадка ", "device": " Первичное оборудование ", "pointList": " Список точек ", "sensorID": " Идентификатор датчика ", "sensorChannel": " Канал датчика ", "channelList": " Список каналов ", "showPoint": " Показать точку измерения ", "fileSelect": " Выбор файла ", "selectPlease": " Выберите ", "deviceList": " Список датчиков ", "updating": " Обновляется... ", "updatingFailed": " Ошибка обновления: ", "updatingCanceled": " Отменить обновление ", "updatingComplete": " Завершение обновления ", "update": " Обновление ", "sampleDate": " Дата отбора проб ", "sampleTime": " Время отбора проб ", "pdMax": " Максимальная амплитуда разряда ", "pdAvg": " Средний амплитуда разряда ", "pdNum": " Количество импульсов ", "acquisitionTime": " Время сбора ", "humidity": " Влажность ", "startDate": " Дата начала ", "endDate": " Дата окончания ", "refresh": " Обновить ", "scanData": " Запрос ", "sensorCode": " Код датчика ", "sensorTypeSet": " Тип датчика ", "senorName": " Имя датчика ", "aduAddress": " Адрес датчика ", "senorWorkMode": " Активная доставка ", "On": " Открой. ", "Off": " <PERSON><PERSON><PERSON><PERSON><PERSON> ", "ADUMode": " Режим работы ", "normalMode": " Режим обслуживания ", "lowPowerMode": " Режим низкого энергопотребления ", "monitorMode": " Модель мониторинга ", "artificialStateStartTime": " Момент начала состояния искусственного вмешательства (целая точка) ", "artificialStateStartTimeTip": " Режим искусственного вмешательства начинается с интервала 0 - 23 ", "artificialStateEndTime": " Момент окончания состояния искусственного вмешательства (целая точка) ", "artificialStateEndTimeTip": " Время окончания состояния искусственного вмешательства 0 - 23 ", "artificialStateWakeUpSpace": " Интервал пробуждения в режиме искусственного вмешательства (мин.) ", "unartificialStateWakeUpSpace": " Интервал пробуждения в состоянии, не связанном с искусственным вмешательством (мин.) ", "isAutoChangeMode": " Автоматическое переключение режима ", "monitorModeSampleSapce": " Интервал сбора данных в режиме мониторинга ", "workGroup": " Номер рабочей группы ", "warnTemp": " Тревога температуры ", "taskGroup": " Номер группы задач ", "commLinkType": " Типы каналов связи ", "commLinkPort": " Порт связи ", "frequencyUnit": " Частота сети (Гц) ", "numInGroup": " Номер группы ", "commSpeed": " Скорость связи ", "commLoad": " Канал связи ", "sampleSpace": " Интервал отбора проб (min) ", "sleepTime": " Стратегия энергопотребления ", "samleStartTime": " Начальное время отбора проб (полная точка) ", "applyData": " Операция ", "applyAllSensor": " Применение к одному и тому же виду ", "collectOnce": " Массовый сбор ", "collectOnceEnd": " Конец сбора данных ", "collectOnceProgress": " Сбор данных: {1} ({2}) ", "collectOnceProgress2": " Сбор данных [{1] ({2}) ", "activeOnce": " Массовое применение ", "wakeUp": " Пробуждение ", "wakeUpInstrument": " Датчик пробуждения ", "wakeUpInstrumentOnce": " Датчик массового пробуждения ", "orderSend": " Приказ отправлен. ", "cleanData": " Очистить данные ", "sensorAddSuccess": " Датчик добавлен успешно ", "sensorEditSuccess": " Дат<PERSON>ик исправлен. ", "sensorEditBegin": " Изменение датчика начинается ", "sensorDelSuccess": " Да<PERSON><PERSON><PERSON><PERSON> удалён. ", "cleanSensorData": " Очистить данные датчика ", "getSensor": " Датчик доступа ", "channelType": " <PERSON>и<PERSON> канала ", "channelName": " Название канала ", "channelInfoSaveSuccess": " Информация канала успешно сохранена ", "channelInfoSaveBegin": " Начало сохранения информации о канале датчика ", "bias": " Смещение [dB] ", "waveFilter": " Диа<PERSON>азон частот ", "gainMode": " Модель усиления ", "gain_unit": " Усиление [dB] ", "samCycle": " Количество циклов отбора проб ", "samPointNum": " Количество точек выборки за цикл ", "samRate": " Количество точек выборки за цикл ", "ratio": " Изменение отношения ", "channelPhase": " отличаться ", "mecLoopCurrentThred": " Порог тока катушки [mA] ", "mecMotorCurrentThred": " Порог электрического тока двигателя [mA] ", "mecSwitchState": " Начальное состояние переключателя ", "awaysOpen": " Открыть ", "awaysClose": " Постоянная блокада ", "mecBreakerType": " Настройка механизма выключателя ", "oneMech": " Трехфазная механическая связь (один механизм) ", "threeMech": " Трехфазная электрическая связь (три механизма) ", "mecMotorFunctionType": " Тип работы двигателя ", "threePhaseAsynMotor": " трехфазный асинхронный Н - образный авиационный ", "onePhaseMotor": " Электрический аппарат постоянного тока ", "setCurrent": " Настройка текущего ", "setAll": " Применяется к одному и тому же датчику. ", "showCoil": " диаграмма тока катушки ", "showSwitch": " Карта переключателей ", "showMotor": " электродинамическая диаграмма ", "showOrig": " первичная диаграмма тока ", "actionDate": " Дата действия ", "actionTime": " Время действия ", "phaseA": " Фаза А ", "phaseB": " Фаза B ", "phaseC": " фаза С ", "coil_charge_time": " время включения катушки ", "coil_cutout_time": " Время отключения катушки ", "max_current": " Максимальный ток катушки ", "hit_time": " Время отсоединения ", "subswitch_close_time": " Время переключения вспомогательных переключателей ", "a_close_time": " время включения фазы А ", "a_close_coil_charge_time": " время включения фазового кольца А ", "a_close_coil_cutout_time": " Время отключения фазового кольца А ", "a_close_max_current": " максимальный ток катушки сопряжения А ", "a_close_hit_time": " Время отключения тормоза ", "a_close_subswitch_close_time": " время переключения вспомогательного переключателя фазы А ", "b_close_time": " Время включения фазы B ", "b_close_coil_charge_time": " Время включения фазового кольца B ", "b_close_coil_cutout_time": " Время отключения фазового кольца B ", "b_close_max_current": " максимальный ток катушки Б - фазового включения ", "b_close_hit_time": " Время отключения тормоза B - фазового включения ", "b_close_subswitch_close_time": " Время переключения B - фазового вспомогательного переключателя ", "c_close_time": " время включения фазы С ", "c_close_coil_charge_time": " время включения фазового кольца C ", "c_close_coil_cutout_time": " Время отключения Сфазового кольца ", "c_close_max_current": " максимальный ток катушки с фазированным затвором ", "c_close_hit_time": " Время отключения тормоза ", "c_close_subswitch_close_time": " Время переключения C - фазового вспомогательного переключателя ", "close_sync": " синхронизация включения ", "close_time": " Время включения ", "a_open_time": " время разделения фазы А ", "a_open_coil_charge_time": " время включения фазового кольца А ", "a_open_coil_cutout_time": " Время отключения фазового кольца А ", "a_open_max_current": " максимальный ток катушки фазового выключателя А ", "a_open_hit_time": " время расцепления тормоза фазы А ", "a_open_subswitch_close_time": " время переключения вспомогательного переключателя фазы А ", "b_open_time": " Время разделения фаз B ", "b_open_coil_charge_time": " Время включения фазового кольца B ", "b_open_coil_cutout_time": " Время отключения фазового кольца B ", "b_open_max_current": " максимальный ток катушки Б - фазового выключателя ", "b_open_hit_time": " Время отключения тормоза фазового разделения ", "b_open_subswitch_close_time": " Время переключения B - фазового вспомогательного переключателя ", "c_open_time": " время разделения фазы С ", "c_open_coil_charge_time": " время включения фазового кольца C ", "c_open_coil_cutout_time": " Время отключения Сфазового кольца", "c_open_max_current": "максимальный ток катушки фазового выключателя ", "c_open_hit_time": " Время отключения тормоза фазового разделения ", "c_open_subswitch_close_time": " Время переключения C - фазового вспомогательного переключателя ", "open_sync": " синхронность включения ", "open_time": " Время выключения ", "a_twice_open_time": " время вторичного выключения фазы А ", "a_twice_open_coil_charge_time": " время заряжения катушки вторичного выключателя фазы А ", "a_twice_open_coil_cutout_time": " время отключения катушки вторичного выключателя фазы А ", "a_twice_open_max_current": " максимальный ток катушки вторичного выключения фазы А ", "a_twice_open_hit_time": " Время отсоединения фазы А ", "a_twice_open_subswitch_close_time": " время выключения вспомогательного выключателя вторичного выключателя фазы А ", "b_twice_open_time": " время вторичного выключения фазы В ", "b_twice_open_coil_cutout_time": " время отключения катушки вторичного выключателя фазы В ", "b_twice_open_max_current": " максимальный ток катушки вторичного выключения фазы В ", "b_twice_open_hit_time": " Б - фазный двойной тормоз ", "b_twice_open_subswitch_close_time": " время выключения вспомогательного выключателя вторичного выключателя фазы В ", "c_twice_open_time": " время переключения фазы С ", "c_twice_open_coil_cutout_time": " время отключения катушки с фазовым вторичным выключателем ", "c_twice_open_max_current": " максимальный ток катушки вторичного выключения фазы С ", "c_twice_open_hit_time": " Время отсоединения фазы С ", "c_twice_open_subswitch_close_time": " время выключения вспомогательного выключателя с - фазового вторичного выключателя ", "twice_open_sync": " синхронизация вторичных шлюзов ", "twice_open_time_text": " время вторичного выключения ", "a_switch_shot_time": " А. Короткое время. ", "b_switch_shot_time": " Б. Короткое время. ", "c_switch_shot_time": " С. Короткое время. ", "a_switch_no_current_time": " Время без тока в фазе А ", "b_switch_no_current_time": " Время без тока в фазе B ", "c_switch_no_current_time": " Время без тока в фазе С ", "motor_start_current": " ток включения двигателя ", "motor_max_current": " Максимальный ток двигателя ", "storage_time": " Время хранения энергии ", "Chan_A_motor_start_current": " ток запуска А - фазного двигателя ", "Chan_A_motor_max_current": " максимальный ток А - фазного двигателя ", "Chan_A_storage_time": " Время хранения энергии в фазе А ", "Chan_B_motor_start_current": " ток запуска B - фазного двигателя ", "Chan_B_motor_max_current": " максимальный ток В - фазного двигателя ", "Chan_B_storage_time": " Время хранения энергии в фазе B ", "Chan_C_motor_start_current": " ток запуска C - фазного двигателя ", "Chan_C_motor_max_current": " максимальный ток C - фазного двигателя ", "Chan_C_storage_time": " Время хранения энергии в C - фазе ", "serialPort": " Последовательный порт ", "baudRate": " коэффициент Портера ", "dataBit": " Биты данных ", "stopBit": " Остановить бит ", "checkBit": " Контрольный бит ", "singleCollect": " Массовый сбор ", "sampling": " Сбор ведется... ", "serverIP": " Сервер IP ", "serverPort": " Порт сервера ", "NTPserverIp": " Сервер NTP IP ", "NTPserverPort": " Порт NTP - сервера ", "netType": " Тип сети ", "deviceIp": " Хостинг IP ", "subnetMask": " Маска подсети ", "gateway": " Шлюз по умолчанию ", "networkAPN": " Сетевой доступ APN ", "userName": " Имя пользователя ", "passWord": " Пароль ", "deviceWorkGroup": " Номер рабочей группы хоста ", "frequency": " Частота сети ", "pdSampleInterval": " Интервал PD - выборки ", "spaceSecond": " & nbsp;  Секунда ", "meuSampleInterval": " МЕУ интервал отбора проб ", "monitorAwakeTime": " Интервал покоя ", "monitorSleepSpace": " Интервал пробуждения ", "wakeStartTime": " Время начала пробуждения (полная точка) ", "intervalServer": " Интервал загрузки (сервер) ", "intervalMinus": " Интервал отбора проб ", "continuousAcquisitionTime": " Время непрерывного сбора ", "startSampleTime": " начальный момент выборки узла ", "spacePoint": " & nbsp;  Точка ", "startSampleInterval": " Начальный интервал выборки узла ", "hour": " <PERSON>а<PERSON>ы ", "poerOffSpace": " Интервал выключения ", "powerOnSpace": " Интервал загрузки ", "dateRange": " Диа<PERSON>азон дат ", "synchronizing": " Синхронизация... ", "synchronize": " Синхронизация ", "amplitude": " Амплитуда ", "switch": " Переключить ", "copyRight": " ©  2020 PMDT Ltd. ", "S1010Name": " Хостинг сбора данных ", "modbusCompanySelf": " <PERSON><PERSON><PERSON>н ", "logOut": " Выход ", "logOutTitle": " Подтверждение выхода ", "logOutConfirm": " Выход из текущего входа пользователя? ", "logOutTips": " Выход из системы ", "enterHost": " Введите имя узла ", "selectDevTip": " Выберите устройство ", "stopSyncDataTip": " Нужно ли прекращать синхронизацию данных? ", "modbusAddress": " <PERSON><PERSON><PERSON><PERSON>с ModBus ", "modbusAddressCheckTip": " Ад<PERSON><PERSON>с ModBus имеет диапазон от 1 до 254 целых чисел. ", "deviceWorkGroupCheckTip": " Номер рабочей группы хоста - целое число, диапазон: [{1} ~ {2}] ", "emptyMonitorDatas": " Очистить данные узла ", "emptyMonitorDatasTip": " Очистить все исторические данные в главном компьютере ", "emptyMonitorDatasSuccess": " Очистить данные успешно ", "emptyMonitorDatasApply": " Заявки на очистку данных представлены и ожидают рассмотрения ", "resetSoftSet": " Восстановление заводских настроек ", "resetSoftSetTip": " Восстановление всех конфигураций узла сбора до заводского состояния ", "resetSoftSetSuccess": " Заявка на восстановление заводских установок подана, ожидает рассмотрения ", "continueConfirmTip": " Операция не может быть восстановлена, продолжать? ", "bias_data": " Диапазон смещения 100 - 100 ", "back_ground_data": " Диапазон фоновых значений 0 - 100 ", "sam_point": " Частотный диапазон выборки составляет 20 - 2000 за цикл ", "sam_rate": " Частотный диапазон выборки составляет 20 - 2000 за цикл ", "smartSensorStatus": " Таблица состояния ", "upThreshold": " Верхний предел порога ", "lowerThreshold": " Нижний порог ", "changeThreshold": " Порог изменения ", "changeThresholdLimit": " Пороговые параметры поддерживают только десятичную дробь ", "upThresholdTip": " Предельная граница ", "lowerThresholdTip": " Нижний пороговый диапазон ", "changeThresholdTip": " Пороговый диапазон изменений ", "upLowerThresholderrTip": " Нижний предел порога не должен превышать верхний предел порога. ", "monitorName": " Имя узла ", "monitorType": " Тип узла ", "MonitorTypeHanging": " Стена ", "MonitorType2U": " 2U ", "MonitorTypeCollectionNode": " Объединить узлы ", "MonitorTypeLowPower": " Солнечный хост с низким энергопотреблением ", "devicePMSCode": " Код устройства ", "forceChange": " Переключение режимов ", "forceChangeSuccess": " Удалось переключить режим ", "currentVersion": " Текущая версия ", "abnormalRecover": " Аномальное самовосстановление. ", "fromLabel": " Время начала ", "toLabel": " Время окончания ", "select": " Выбор ", "sunday": " День ", "monday": " <PERSON>ди<PERSON>. ", "tuesday": " Два. ", "wednesday": " Три. ", "thursday": " Четыре. ", "friday": " Пять. ", "saturday": " Шесть. ", "January": " Январь ", "February": " Февраль ", "March": " Ма<PERSON><PERSON> ", "April": " Апрель ", "May": " <PERSON><PERSON><PERSON> ", "June": " Июнь ", "July": " Июль ", "August": " Август ", "September": " Сентябрь ", "October": " Октябрь ", "November": " Ноябрь ", "December": " Декабрь ", "all": " Все. ", "strong": " Цзян ", "weak": " Слабый ", "poor": " Б ", "min": " Б ", "second": " Секунда ", "uploadInterval": " Интервал загрузки (min) ", "loginWelcome": " Добро пожаловать в систему ", "dateStartIsAfterDateEnd": " Дата начала больше, чем дата окончания, выберите заново ", "maxCurrent": " Максимальный ток ", "standMobus": " Производитель датчиков ", "config1": " Настройка 1 ", "config2": " Настройка 2 ", "fWrnThreshold": " Порог внимания ", "fAlmThreshold": " Порог оповещения ", "regularDormancy": " Периодическая спячка ", "noDormancy": " Бессонница. ", "soakingWater": " Погружение ", "dry": " Сухой ", "normal": " Нормально. ", "alarm": " Предупреждение. ", "lightGunCamera": " Видимый пистолет ", "lightBallCamera": " Видимый фотоаппарат ", "infraredGunCamera": " инфракрасный биокуляр ", "infraredBallCamera": " инфракрасный бинокуляр ", "maxTemp": " Максимальная температура ", "maxTempPosition": " Максимальное температурное положение ", "devIPError": " Этот IP уже занят. ", "unknown": " Неизвестно ", "fault": " Отказ ", "lowPower": " Низкая мощность ", "mediumPower": " Средний уровень ", "highPower": " Высокая мощность ", "slaveid": " Идентификатор машины ", "LoraFrequency": " Область узла ", "AREA_CHINA": " <PERSON><PERSON><PERSON><PERSON><PERSON> < 1 > ", "AREA_VIETNAM": " Вье<PERSON><PERSON>м (AS1) ", "AREA_MALAYSIA": " Малайзия (AS1) ", "AREA_EUROPE": " Европа ", "AREA_US": " Америка ", "AREA_INDONESIA": " Индонезия (AS2) ", "AREA_INDIA": " Индия ", "AREA_KOREA": " Корея ", "AREA_CHINA_RSV": " <PERSON>и<PERSON><PERSON><PERSON> (резерв) < 2 > ", "getLogFileError": " Ошибка получения файла журнала датчика ", "exportLogError": " Файл журнала не существует, ошибка экспорта ", "alertSyncProgress": " Прогресс синхронизации обновлений данных оповещения: {1} ", "alertSyncProgress2": " Прогресс синхронизации обновлений данных оповещения [{1] ", "FIRMWARE_EXCEPTION": " Необычность прошивки ", "AD_INITIALIZATION_EXCEPTION": " Аномальная инициализация AD ", "REFERENCE_VOLTAGE_EXCEPTION": " Аномальное напряжение ", "ONCHIP_FLASH_EXCEPTION": " Необычный Flash внутри ", "OFFCHIP_FLASH_EXCEPTION": " Необычный Flash ", "SYSTEM_PARAMETERS_EXCEPTION": " Необычные системные параметры ", "SAMPLE_PARAMETERS_EXCEPTION": " Параметры сбора аномалии ", "CALIBRATION_PARAMETERS_EXCEPTION": " Параметры калибровки ненормальные ", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": " Необычное восстановление системных параметров ", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": " Необычное восстановление параметров сбора ", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": " Необычное восстановление параметров калибровки ", "LORA_MODULE_EXCEPTION": " Необычные модули LoRa ", "PHASE_NUM": " Количество фаз ", "DISCONNECT_TIME": " Время отключения ", "DATA_TOTAL_COUNT": " Общий объем данных ", "ONLINE_RATE": " Онлайновый показатель ", "aduInfoSetProgress": " Конфигурация датчика: {1} ({2}) ", "aduInfoSetProgress2": " Настройка датчика [{1] ({2}) ", "aduInfoSetting": " Настройка датчиков... ", "aduInfoSetEnd": " Настройка датчика завершена ", "aduDataSample": " Сбор данных ", "aduInfoSet": " Настройка датчиков ", "errorDataSampleRunning": " В настоящее время проводится сбор датчиков, пожалуйста, повторите попытку после завершения. ", "errorAduInfoSetRunning": " В настоящее время ведется работа по настройке параметров датчика, пожалуйста, повторите попытку после завершения ", "SAMPLE_PERIOD": " Количество частот выборки ", "SF6_Density": " SF6 Плотность газа (P20) ", "SF6_Temp": " SF6 Температура газа ", "Device_env_Temp": " Температура окружающей среды оборудования ", "Alarm_Status": " Идентификатор состояния тревоги ", "SF6_Alarm_Low_Pressure": " Сигнализация низкого давления ", "SF6_Alarm_Low_Voltage_Block": " Замок низкого давления ", "SF6_Alarm_Over_Voltage": " Предупреждение о перенапряжении ", "AlarmType": " Тип оповещения ", "AlarmData": " Содержание предупреждений ", "AlarmTime": " Время оповещения ", "AlarmLevel": " Уровень напоминания @ info: whatsthis ", "AlarmStateWarning": " Раннее предупреждение ", "WarningValue": " Значение раннего предупреждения ", "AlarmValue": " Значение напоминания @ info: whatsthis ", "SetSuccess": " Настройка выполнена ", "AlarmDate": " Время напоминания @ info: whatsthis ", "AlarmConfirm": " Подтверждение напоминания @ info: whatsthis ", "Confirm": " Подтверждение ", "Confirmed": " Подтверждено ", "AllData": " Все данные ", "CheckManagerSponsor": " Инициатор ", "CheckManagerCheckType": " Тип ревизии ", "CheckManagerDate": " Дата начала ", "CheckManagerTarget": " Инициатор ", "CheckManagerExtraInfo": " Дополнительная информация ", "CheckManagerResult": " Согласны ", "Refuse": " Отказ ", "Agree": " Согласен. ", "DataExport": " Экспорт данных ", "DataExportStep1": " Выберите данные для экспорта ", "DataExportStep1Title": " Экспорт выбранного файла ", "DataExportStep2": " Расчет размера данных... ", "DataExportStep2Title": " Расчет размера исходных данных ", "DataExportStep3": " Упаковывает сжатые данные... ", "DataExportStep3Title": " Упаковать сжатые данные ", "DataExportStep4": " Сжатие данных завершено ", "DataExportStep4Title": " Сжатие данных завершено ", "DataExportCheckData": " База данных [/ media / data / database] ", "DataExportCheckDataFile": " Файл данных [/ media / data / datafile] ", "DataExportCheckLog": " Файл журнала [/ media / data / log] ", "DataExportCheckConfig": " Файл конфигурации [/ home / root / config.xml] ", "DataExportBegin": " Начать экспорт ", "SelectAtLeastOneTips": " Выберите хотя бы один. ", "DataExportFileSizeError": " Размер файла более 2000 МБ, экспортируйте данные с помощью таких инструментов, как Winscp ", "DataExportFileSizeZeroError": " Выберите размер файла 0 ", "DataExportCancelTips": " Экспорт данных отменен ", "DataExportDataCompressTips": " Исходный размер данных - {1} МБ, предполагаемое время сжатия [{2} min {3} s] ", "LogExportStep1": " Выбор датчика ", "LogExportStep1Title": " Выбор датчика ", "LogExportStep2": " Получение файла журнала... ", "LogExportStep2Title": " Получение файла журнала ", "LogExportStep3": " Получение файла журнала завершено ", "LogExportStep3Title": " Получение файла журнала завершено ", "LogExportStep4": " Упаковать журнал сжатия... ", "LogExportStep4Title": " Упаковать файл журнала сжатия ", "LogExportStep5": " Сжатие завершено ", "LogExportStep5Title": " Сжатие завершено ", "LogExportCancelTips": " Журнал экспорта отменен ", "batteryVoltage": " Батарейное напряжение ", "superCapvoltage": " Суперконденсаторное напряжение ", "OverPressureThreshold": " Порог избыточного давления ", "LowPressureThreshold": " Порог низкого давления ", "ShutThreshold": " Порог блокировки ", "PhysicalChannelType": " Тип физического канала ", "GasAbsPressure": " Газовая изоляция ", "GasGaugePressure": " Газовое манометрическое давление ", "CameraType": " <PERSON>и<PERSON> камеры ", "DEFAULT_CHECK_Tips": " Номер формата m: n, например 1: 100, где m диапазон [1 - 255], n диапазон [0 - 65535] ", "VibrationSY_OUT_CHECK_Tips": " Номер формата address: modbus: id, например 1: 0: 1917, где диапазон address, как [1 - 255], modbus / id все [0 - 65535] ", "NOISEWS_OUT_CHECK_Tips": " Формат нумерации ip: x: y, например *********: 1321: 4512, где формат ip [xxx.xx.xx.xx.xxx.xxx], x / y - все строки длиной 4 ", "IR_DETECTION_OUT_IO_CHECK_Tips": " Номер формата ip: port: ss: se: cs: ce, например 1: 100, где <PERSON> формат [xxx.xx.xx.xx.xxx.xxx] диапазон портов [0 - 65535] диапазон ss [0 - 255], диапазон cs 0 - 255], диапазон cs 0 - 255], диапазон ce 0 - 255] ", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": " Формат нумерации ip: port: x: y, например 1: 100, где формат ip [xxx.xx.xx.xxx.xxx] Порт Порт Диапазон [0 - 65535] x Ди<PERSON><PERSON>азон [1 - 255], y Ди<PERSON><PERSON><PERSON>зон [0 - 65535] ", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": " Номер формата m: n, например 1: 100, где m диапазон [1 - 255], n диапазон [0 - 65535] ", "fWrnThreshold_CHECK_Tips": " Порог внимания составляет [10 - 1000] мА ", "fAlmThreshold_CHECK_1_Tips": " Порог оповещения составляет [50 - 5000] мА ", "fAlmThreshold_CHECK_2_Tips": " Порог тревоги должен превышать порог внимания. ", "AuditContent": " Содержание ревизии ", "OPTime": " Время работы ", "Executed": " Осуществление ", "UserManager": " Управление пользователями ", "SystemManagement": " Управление системой ", "BusinessManagement": " Управление операциями ", "LanguageChange": " Переключение языка ", "AlarmManagement": " Управление оповещением ", "DataOperation": " Операции с данными ", "BackupRecovery": " Восстановление резервного копирования ", "AddUser": " Добавить пользователя ", "AddUserCheck": " Добавить пользовательский аудит ", "UserRightSet": " Параметры прав пользователя ", "UserRightSetCheck": " Настройка прав пользователя ", "DeleteUser": " Удалить пользователя ", "DeleteUserConfirm": " Удалить подтверждение пользователя ", "FreezeUser": " Замораживание пользователей ", "FreezeUserConfirm": " Замораживание подтверждения пользователя ", "UnlockUser": " Разблокировать пользователя ", "UnlockUserConfirm": " Разблокировать подтверждение пользователя ", "FileStationSet": " Настройки файлов (сайтов) ", "FileDeviceSet": " Настройки файла (устройства) ", "SenserSet": " Настройка датчика ", "AlarmSet": " Настройка оповещения ", "SavePointSet": " Сохранить параметры точки измерения ", "DelPointSet": " Удалить настройки точки измерения ", "SingleSample": " Одноразовый сбор ", "DataBackup": " Резервное копирование данных ", "DataRecovery": " Восстановление данных ", "ClearDataCheck": " Проверка данных ", "RestoreFactorySettingsReview": " Возобновление проверки заводских установок ", "SaveMainStation": " Сохранить главную станцию ", "DataView": " Просмотр данных ", "DataSync": " Синхронизация данных ", "RightSet": " Настройка прав ", "GetUserList": " Получение списка пользователей ", "AuditData": " Данные ревизии ", "AuditPermissions": " Полномочия аудита ", "MainStationSet": " Настройка главной станции ", "ChangePasswordSelfOnly": " Изменить пароль (только для данного пользователя) ", "ChangePasswordForNormal": " Изменить обычный пароль пользователя ", "ChangeUserRight": " Изменить / установить права пользователя ", "ChangePassword": " Изменить пароль ", "ChangeLoginInfo": " Изменить регистрационные данные ", "UserStatus": " Статус пользователя ", "UserLanguage": " Язык пользователя ", "HasLogin": " Высаживались ли ", "RoleType": " Тип роли ", "IsPassTimeout": " Время ожидания пароля ", "AuditsManagement": " Управление аудитом ", "SensorOperation": " Работа датчика ", "SensorLogExport": " Экспорт журнала датчиков ", "SensorAlarmDataSync": " Синхронизация данных сигнализации датчиков ", "ViewSensorAlarmData": " Просмотр данных датчиков. ", "Block": " Замораживание ", "BlockPendingReview": " Замораживание - ожидает рассмотрения ", "UnlockPendingReview": " Оттепель - подлежит проверке ", "DeletePendingReview": " Удалено - ожидает рассмотрения ", "AddUserPendingReview": " Добавлено - подлежит рассмотрению ", "ChangePassPendingReview": " Изменение пароля - подлежит проверке ", "ChangeRightPendingReview": " Права на изменение - подлежит рассмотрению ", "abnormal": " Необычные ", "DataQueryNotSupported": " Не поддерживается запрос исторических данных этого типа интерфейса ", "DeviceCodeExists": " Имя устройства или код устройства уже существуют ", "OutputSwitchConfig": " Настройка переключателя ", "OutputSwitch": " Выключатель ", "MainStationAuxRtuID": " Дополнительный RtuID ", "MainStationIedRtuID": " Сбор RtuID ", "MainstationParams1": " Основные параметры станции 1 ", "MainstationParams2": " Основные параметры станции 2 ", "MainstationInterface1": " Интерфейс главной станции 1 ", "MainstationInterface2": " Интерфейс главной станции 2 ", "Port": " Порт ", "IPAddress": " IP - адрес ", "EnterUriTips": " Введите URI пути ", "ConnectUserName": " Имя пользователя связи ", "EnterConnectUserNameTips": " Введите имя пользователя связи ", "ConnectPass": " Пароль связи ", "EnterConnectPassTips": " Введите пароль связи. ", "DataSubmissionInterval": " Интервал передачи данных ", "EnderDataSBIntervalTips": " Введите интервал между отправкой данных ", "HeartbeatInterval": " Интервал сердцебиения ", "EnterHertbeatIntervalTips": " Введите интервал сердцебиения. ", "ConnectStatus": " Состояние соединения ", "Reset": " Сбросить ", "Submit": " Представлено ", "RtuidRepeatTips": " Поддержка RtuID не может быть такой же, как сборка RtuID. ", "DataSubmissionIntervalTips": " Интервал передачи данных не должен быть меньше 60. ", "HeartbeatIntervalTips": " Интервал сердцебиения не должен быть меньше 60. ", "MainstationParamsSaveSuccess": " Параметры главной станции успешно сохранены ", "MainstationParamsSaveFailed": " Ошибка сохранения параметров главной станции ", "OutSwitchSaveSuccess": " Настройка переключателя доступа успешно сохранена ", "OutSwitchSaveFailed": " Ошибка сохранения конфигурации переключателя", "ChartConfig": "Графическая конфигурация", "Measurement": "Измерение", "SENSOR_TIMEOUT_COUNT": "Количество определений оффлайн состояния датчика", "OUTWARD_CONFIG_ENABLE_STATE": "Статус конфигурации", "OUTWARD_CONFIG_ENABLED": "Включено", "OUTWARD_CONFIG_DISABLED": "Отключено", "OUTWARD_CONFIG_ENABLING": "Включение", "SENSOR_TIMEOUT_DISABLING": "Отключение", "ERROR_NO_CONFIG": "Конфигурация не существует", "ERROR_INPUT_PARAMS": "Ошибка входного параметра", "ERROR_INTEGER": "Пожалуйста, введите целое число", "ERROR_TIP_RANGE": "Диапазон значений [{1}~{2}]", "ERROR_IP_FORMAT": "Неверный формат IP", "ERROR_FILE_NAME": "Имя файла должно быть {1}", "ERROR_FILE_SUFFIX": "Расширение файла должно быть {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "Файл должен быть меньше {1} МБ", "ERROR_T2_T1": "T2 должно быть меньше T1", "LENGTH_COMM_LIMIT": "Обратите внимание, это поле не может превышать {1} символов.", "MANU_FAST_SYNC_DATA": "Быстрая синхронизация", "SYNC_DATA_STATE_LIST": "Список статусов синхронизации датчиков", "LAST_SYNC_DATE_RANGE": "Диапазон дат последней синхронизации", "NoError": "Нет ошибки", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "Тайм-аут синхронизации", "SYNC_DATA_ERROR_DISCONNECT": "Отключение", "SYNC_DATA_STATUS_WAITING": "Ожидание подключения", "SYNC_DATA_STATUS_CONNECTED": "Подключено", "SYNC_DATA_STATUS_SYNCING": "Синхронизация", "SYNC_DATA_STATUS_SUCCESS": "Синхронизация успешна", "SYNC_DATA_STATUS_FAILED": "Синхронизация не удалась", "SYNC_DATA_STATUS_CANCELED": "Синхронизация отменена", "Today": "Сегодня", "Yesterday": "Вчера", "LAST_7_DAYS": "Последние 7 дней", "LAST_30_DAYS": "Последние 30 дней", "THIS_MONTH": "Этот месяц", "LAST_MONTH": "Прошлый месяц", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Не удалось запустить службу связи", "FastSyncDataCancelTips": "Вы уверены, что хотите отменить синхронизацию данных?", "FastSyncDataSelectTips": "Пожалуйста, выберите датчики для синхронизации данных", "Band-pass": "Полосовой фильтр", "aeWaveChartTitle": "Спектр формы волны AE", "aePhaseChartTitle": "Спектр фазы AE", "aeFlyChartTitle": "Спектр полета AE", "LOCAL_PARAMS_TIME": "Время", "LOCAL_CHART_COLORDENSITY": "Плотность цвета", "openTime": "Время открытия", "closeTime": "Время закрытия", "triggerUnit": "Амплитуда срабатывания[μV]", "openTimeUnit": "Время открытия[μs]", "closeTimeUnit": "Время закрытия[μs]", "triggerUnitTips": "Диапазон триггерной амплитуды: [AM 1, AM 2] мкВ", "TOP_DISCHARGE_AMPLITUDE": "TOP3 Амплитуда разряда", "WS_901_ERROR_TIPS": "Служба отправки команд регистрируется, пожалуйста, повторите попытку через 2 секунды"}