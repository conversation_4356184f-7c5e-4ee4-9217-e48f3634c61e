var chartIndex;
var chartDataNum;

function getArresterTendDatas() {
    var beginDate = $('#arresterBeginDate').val();
    var endDate = $('#arresterEndDate').val();
    if (moment(beginDate).isAfter(moment(endDate))) {
        layer.msg('<div style="text-align:center">' + getI18nName('dateStartIsAfterDateEnd') +
            '</div>');
        return
    }
    chartIndex = 0;
    $('#charts').empty();
    var pointType = "Arrester";
    var dataGet = "pointId=" + showChartPointId + "&dataId=" + showChartDataId + "&pointType=" + showChartPointType +
        "&startDate=" + beginDate + "&endDate=" + endDate;
    pollGet(GET_TEND_DATA, dataGet, function (response) {
        var respData = response.result;
        if (!respData) {
            return
        }
        // debugger;
        tempArresterXval = respData[0].xValue[respData[0].xValue.length - 1];
        refeshArresterData();
        for (var i = 0; i < respData.length; i++) {
            chartIndex++;
            initArresterTendChart(respData[i], chartIndex);
        }
    });
}

function initArresterTendChart(chartData, chartID) {
    $('#charts').append(
        "<div id='" +
        chartID +
        "' style='width:100%;height:50%;'  onmousedown = 'refeshArresterData()' >" +
        "</div>"
    );
    //        debugger;
    var myChart = echarts.init(document.getElementById(chartID));
    var dataXValue = [" "];
    var colors = ['orange', '#00CC00', 'red'];
    var legendData = chartData.legendData;
    for (var i = 0; i < legendData.length; i++) {
        legendData[i] = getI18nName(legendData[i]);
    }
    var option = {
        color: colors,
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
                var html = '';
                if (params.length > 0) {
                    tempArresterXval = params[0].name;
                    html += tempArresterXval + '<br>';
                    for (var int = 0; int < params.length; int++) {
                        html += formatUtil.getTooltipMarkerTemp(params[int].color) + params[int].seriesName + ': ' + params[int].data + '<br>';
                    }
                }
                return html;
            },
            confine: true
        },
        legend: {
            top: 'bottom',
            data: legendData
        },
        title: {
            left: 'center',
            text: getI18nName(chartData.chartName),
            // x: 'left',
            // y: 'top',
            textAlign: 'left'
        },
        calculable: true,
        xAxis: [{
            type: 'category',
            data: dataXValue,
            boundaryGap: false,
            triggerEvent: true
        }],
        yAxis: [{
            type: 'value'
        }],
        series: []
    };
    if (option) {
        var xvalue = chartData.xValue;
        if (chartData.xUnit) {
            option.xAxis[0].name = stringFormat.format(getI18nName('UnitFomat'), chartData.xUnit);
        }
        if (chartData.yUnit) {
            option.yAxis[0].name = stringFormat.format(getI18nName('UnitFomat'), chartData.yUnit);
        }
        for (var j = 0; j < chartData.dataList.length; j++) {
            chartData.dataList[j].name = getI18nName(chartData.dataList[j].name);
            var data = chartData.dataList[j];
            data.showSymbol = false;
        }
        // debugger;
        option.xAxis[0].data = xvalue;
        option.series = chartData.dataList;
        myChart.setOption(option);
    }
}

/**
 * 右侧参数控制
 */
var tempArresterXval;

function refeshArresterData() {
    var dataGet = "pointId=" + showChartPointId + "&pointType" + showChartPointType + "&dateTime=" + tempArresterXval;
    pollGet(GET_ARRESTER_CHART_PARAMDATA, dataGet, function (response) {
        var result = response.result;
        // debugger;
        initArresterParamItems();
        for (var item in result) {
            var itemData = result[item];
            if (item == "sensorType") {
                itemData = getI18nName(itemData);
            }
            $('#' + item).html(itemData + " " + (arresterParamItems[item] ? arresterParamItems[item] : ''));
        }
    }, function () {

    });
}
var arresterParamItems = {
    "poiontName": "",
    "sensorCode": "",
    "sensorType": "",
    "sample_date": "",
    "sample_time": "",
    "leakageCurrentA": "mA",
    "leakageCurrentB": "mA",
    "leakageCurrentC": "mA",
    "referenceVoltageA": "V",
    "referenceVoltageB": "V",
    "referenceVoltageC": "V",
    "resistiveCurrentA": "mA",
    "resistiveCurrentB": "mA",
    "resistiveCurrentC": "mA",
};

function initArresterParamItems() {
    var formContent = $('#arresterParams');
    formContent.empty();
    for (var id in arresterParamItems) {
        var name = getI18nName(id);
        if (id == "sensorCode") {
            id = "aduId";
        }
        var htmlItem = ['   <div class="form-group">',
            '                    <label class="col-sm-6 control-label" style="padding-right:5px">' + name + '</label>',
            '                    <div class="col-sm-6">',
            '                        <label class=" control-label" id="' + id + '"></label>',
            '                    </div>',
            '                </div>'
        ].join("");
        formContent.append(htmlItem);
    }
}

var formatUtil = {};

formatUtil.getTooltipMarkerTemp = function (color, extraCssText) {
    return color ?
        '<span style="display:inline-block;margin-right:5px;' +
        'border-radius:10px;width:9px;height:9px;background-color:' +
        formatUtil.encodeHTMLTemp(color) + ';' + (extraCssText || '') + '"></span>' :
        '';
};
formatUtil.encodeHTMLTemp = function (source) {
    return String(source)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
};

//http://**************:8003/GetArresterChartParamData?pointId=536f3b49-1fb3-4592-8eaa-b34bc93d9ced&pointType=ArresterI&dateTime=2018/01/15%2016:37:23