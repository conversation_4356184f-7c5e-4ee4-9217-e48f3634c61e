<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin">
        <!--<div class="box-header with-border">-->
        <!--<h3 class="box-title">Sync Data</h3>-->
        <!--</div>-->
        <!-- /.box-header -->
        <div class="box-body">
            <div>
                <div class="ck-content">
                    <label for="exportAll" data-i18n="AllData">所有数据</label>
                    <input id="exportAll" name="exportGroup" type="checkbox" />
                </div>

            </div>
            <!-- /.exp-al-dateRange -->
            <br>
            <div style="display: flex;margin-bottom: 12px;">
                <div id="exportDataRange" style="margin-right:15px;width: 45%;">
                    <label for="exp-al-dateRange" data-i18n="dateRange">日期范围</label>
                    <div class="input-group date">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="exp-al-dateRange" type="text" class="form-control pull-right" readonly="true">
                    </div>
                </div>
                <div style="width: 100%;">
                    <label for="exp-al-aduType" data-i18n="sensorType">传感器类型</label>
                    <select id="exp-al-aduType" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;"></select>
                </div>
            </div>
            <!-- /.exp-al-aduType -->
            <label for="exp-al-aduItems" data-i18n="sensorList">传感器列表</label>
            <div id="exp-al-aduItems">
                <div class="col-xs-5 no-padding full-height">
                    <select name="from[]" id="da-adus" class="form-control" style="height: 200px;" multiple="multiple">
                    </select>
                </div>
                <div class="col-xs-2">
                    <button type="button" id="da-adus_rightAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-forward"></i></button>
                    <br>
                    <button type="button" id="da-adus_rightSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-right"></i></button>
                    <br>
                    <button type="button" id="da-adus_leftSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-left"></i></button>
                    <br>
                    <button type="button" id="da-adus_leftAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-backward"></i></button>
                </div>
                <div class="col-xs-5 no-padding full-height">
                    <select name="to[]" id="da-adus_to" class="form-control" style="height: 200px;"
                        multiple="multiple"></select>
                </div>
            </div>
            <!-- /.exp-al-aduItems -->
            <br>
            <div id="exp-al-progress" class="progress-group hide">
                <span class="progress-text" data-i18n="synchronizing">正在同步...</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- /.progress-group -->
        </div>
        <!-- /.box-body -->
        <div class="content-footer" style="height: 80px;padding-right: 40px;">
            <button type="button" id="exp-al-cancel" class="btn btn-default hide" style="width: 200px;margin: 10px 10px 10px auto;">
                <img src="dist/img/loading.gif" class="img-loading hide" alt="">
                <span data-i18n="cancel">取消</span>
            </button>
            <button type="button" id="exp-al-ok" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 10px 10px auto" data-i18n="synchronize">同步
            </button>
        </div>
        <br />
    </div>
    <!-- /.box -->

    <!-- /.content-footer -->
</div>
<!--/.col (left) -->


<script>
    initPageI18n();

    function chectState() {
        var flag = $.session.get('i-progress-sync');
        if (flag === "true") {
            $('#exp-al-ok').addClass('hide');
            $('#exp-al-cancel').removeClass('hide');
            $('#i_progressText').text(getI18nName('syncingData'));
        } else {
            $('#exp-al-cancel').addClass('hide');
            $('#exp-al-ok').removeClass('hide');
        }
    }
    chectState();

    var exportAllEnable = false;
    $('#exportAll').on('click', function (obj) {
        exportAllEnable = obj.target.checked;
        if (exportAllEnable) {
            $('#exportDataRange').hide();
        } else {
            $('#exportDataRange').show();
        }
    });

    //@ sourceURL=sync_data.js
    //前端类型下拉框
    var aduType = $('#exp-al-aduType');
    aduType.select2(getSyncDataADUType(aduType));
    aduType.select2({
        minimumResultsForSearch: Infinity
    });
    aduType.change(function () {
        $("#da-adus").empty();
        $("#da-adus_to").empty();
        var selectText = $(this).children('option:selected').val();
        getADUList(selectText);
    });

    $('#exp-al-dateRange').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "startDate": moment().subtract(29, 'days'),
        "endDate": moment(),
    });
    //前端版本列表穿梭框
    $('#da-adus').multiselect();
    $("#exp-al-aduItems").height($('#exp-al-aduItems .col-xs-2').height());

    var syncAlertFormData
    //开始同步告警数据
    $('#exp-al-ok').click(function () {
        var shuttleTo = $('#da-adus_to').find('option');
        if (shuttleTo.length > 0) {
            var dateArray = $('#exp-al-dateRange').val().split('-');
            var aduList = new Array();
            for (var i = 0; i < shuttleTo.length; i++) {
                aduList.push(shuttleTo[i].value);
            }
            syncAlertFormData = {
                beginDate: dateArray[0].trim(),
                endDate: dateArray[1].trim(),
                enableGetAllData: exportAllEnable,
                aduType: aduType.children('option:selected').val(),
                aduList: aduList.toString()
            };
            syncAlertProgressFlag = true;
            checkSyncAlertProgress();
            requestSyncAlertData(syncAlertFormData, true);
        } else {
            $('.notifications').notify({
                message: {
                    text: getI18nName('selectDevTip')
                },
                type: "warning",
            }).show();
        }
    })

    $('#exp-al-cancel').click(function () {
        $.fn.alertMsg(
            'warning',
            getI18nName('stopSyncDataTip'), [{
                id: 'no',
                text: getI18nName('no')
            }, {
                id: 'yes',
                text: getI18nName('yes'),
                callback: function () {
                    //禁用取消按钮
                    $('#exp-al-cancel').prop('disabled', true);
                    $('#exp-al-cancel img').removeClass('hide');
                    syncAlertProgressFlag = false;
                    checkSyncAlertProgress();
                    requestSyncAlertData(syncAlertFormData, false);
                    $('.notifications').notify({
                        message: {
                            text: getI18nName('syncingDataCancel')
                        },
                        type: "warning",
                    }).show();
                }
            }]
        );
    })
    checkSyncAlertProgress();

    function requestSyncAlertData(formdata, enablePush) {
        var message = {
            enablePush: enablePush,
            methodName: "PushGetADUAlarmProgress",
        };
        if (!enablePush) {
            syncAlertProgressFlag = false;
            message.parameters = formdata;
        } else {
            syncAlertProgressFlag = true;
            message.parameters = formdata;
        }
        // console.log(message)
        webSocketSend(message);
    }
</script>