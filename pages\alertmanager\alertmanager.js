var alertSearchParams = {
    pointName: undefined,
    alarmLevel: -1,
    alarmState: -1,
    channelType: -1,
};

function setAlert() {
    var setAlertContent = '    <div class="col-lg-4">' +
        '                        <div class="form-horizontal">' +
        '                            <div class="form-group">' +
        '                                <label class="col-lg-5 control-label" style="text-align: left;margin-bottom: 5px">通道类型</label>' +
        '                                <div class="col-lg-12">' +
        '                                    <select class="form-control select2" id="setAlertDataTypeSelect"' +
        '                                            name="setAlertDataTypeSelect"' +
        '                                            style="width: 100%;">' +
        '                                        <option value="0">TEV</option>' +
        '                                        <option value="4">AE</option>' +
        '                                        <option value="13">'+getI18nName('MP')+'</option>' +
        '                                        <option value="26">'+getI18nName('TEMP')+'</option>' +
        '                                    </select>' +
        '                                </div>' +
        '                            </div>' +
        '                        </div>' +
        '                    </div>' +
        '    <div class="col-lg-4">' +
        '                        <div class="form-horizontal">' +
        '                            <div class="form-group">' +
        '                                <label class="col-lg-5 control-label" style="text-align: left;margin-bottom: 5px">'+getI18nName('WarningValue')+'</label>' +
        '                                <div class="col-lg-12">' +
        '                                    <input class="form-control" type="number" id="warnValueParam"' +
        '                                            name="warnValueParam"' +
        '                                            style="width: 100%;"/>' +
        '                                </div>' +
        '                            </div>' +
        '                        </div>' +
        '                    </div>' +
        '    <div class="col-lg-4">' +
        '                        <div class="form-horizontal">' +
        '                            <div class="form-group">' +
        '                                <label class="col-lg-5 control-label" style="text-align: left;margin-bottom: 5px">'+getI18nName('AlarmValue')+'</label>' +
        '                                <div class="col-lg-12">' +
        '                                    <input class="form-control" type="number" id="alarmValueParam"' +
        '                                            name="alarmValueParam"' +
        '                                            style="width: 100%;"/>' +
        '                                </div>' +
        '                            </div>' +
        '                        </div>' +
        '                    </div>';
    layer.open({
        title: getI18nName('alarm_param_set'),
        resize: false,
        anim: 0,
        zIndex: 0,
        area: ['840px', '220px'],
        shadeClose: false,
        success: function () {
            var $selector = $('#setAlertDataTypeSelect');
            $selector.select2({
                minimumResultsForSearch: Infinity,
            });
            $selector.on('change',function (element) {
                pollGet('getSafeAlarmPara',{channelType:element.currentTarget.value},function (res) {
                    $('#warnValueParam').val(res.result.earlyAlarmValue);
                    $('#alarmValueParam').val(res.result.alarmValue);
                })
            });
            $selector.trigger('change');
        },
        yes: function (index) {
            pollGet('setSafeAlarmPara',{
                channelType: $('#setAlertDataTypeSelect').val(),
                warnValueParam:$('#warnValueParam').val(),
                alarmValueParam:$('#alarmValueParam').val()
            },function (res) {
                layer.msg(getI18nName('SetSuccess'));
            });
            layer.close(index);
        },
        content: setAlertContent
    });
}
function searchAlert() {
    alertSearchParams.pointName = $('#alertPointName').val();
    alertSearchParams.alarmLevel = $('#alertLevelSelect').val();
    alertSearchParams.channelType = $('#dataTypeSelect').val();
    refreshAlertTable();
}

function confirmAlert(id) {
    pollGet(SAFE_ALARM_CONFIRM, {alarmId: id}, function () {
        refreshAlertTable();
    })
}

/**
 * 初始化表格内容
 */
initCustomTable({
    id: 'tableAlert',
    method: GET_SAFE_ALARM,
    toolbar: '#alertToolbar',
    responseHandler: function (res) {
        return res.result;
    },
    requestParam: function (params) {
        return {
            page: params.offset / params.limit + 1,
            size: params.limit,
            channelType: alertSearchParams.channelType,
            alarmLevel: alertSearchParams.alarmLevel,
            alarmState: alertSearchParams.alarmState,
        };
    },
    columns: [
        {
            field: 'pointName',
            title: getI18nName('pointName')
        },
        {
            field: 'warnLevel',
            title: getI18nName('AlarmLevel'),
            formatter: function (value, row, index) {
                var a = row.warnLevel === 1 ? '<span style="color: #e08e0b;">'+getI18nName('AlarmStateWarning')+'</span>' : '<span style="color: #dd4b39;">'+getI18nName('alarm')+'</span>';
                return a;
            }
        },
        {
            field: 'channelType',
            title: getI18nName('channelType'),
            formatter: function (value, row, index) {
                return getAlarmChannelTypeNameByCode(row.channelType);
            }
        },
        {
            field: 'warnValue',
            title: getI18nName('AlarmValue')
        },
        {
            field: 'alarmTime',
            title: getI18nName('AlarmDate')
        },
        {
            field: 'option',
            title: getI18nName('AlarmConfirm'),
            formatter: function (value, row, index) {
                var checkExpend = ' onclick = "confirmAlert('
                    + '\'' + row.alarmId + '\''
                    + ')"';
                var btnMsg = getI18nName('Confirm');
                if (row.warnState == 1) {
                    checkExpend = 'disabled="disabled" style="background:#efefef;color:#333;border-color:#333"';
                    btnMsg = getI18nName('Confirmed');
                }
                var a = '<button class="btn btn-primary" ' + checkExpend + '>' + btnMsg + '</button>';
                return a;
            }
        }]
});

function refreshAlertTable() {
    $('#tableAlert').bootstrapTable('refresh', {silent: true});
}