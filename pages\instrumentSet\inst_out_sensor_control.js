/*
 *
 *  var comm_link_type = $('#comm_link_type').val();
 var comm_link_port = $('#comm_link_port').val();

 formData.aduType = $('#inst_type').val();

 formData.aduId = $('#inst_code').val();
 formData.aduName = $('#inst_name').val();
 formData.workMode = $('#inst_work_mode').val();
 formData.workGroup = $('#inst_work_group').val();
 formData.warnTemp = $('#warnTemp').val();
 formData.taskGroup = $('#inst_task_group').val();
 formData.commType = $('#comm_link_type').val();
 formData.commPort = $('#comm_link_port').val();
 formData.sampleSpace = $("#sampleSpace").val();
 formData.sampleStartTime = $("#sampleStartTime").val();
 formData.frequency = $("#frequency").val();
 formData.commSpeed = $("#commSpeed").val();
 formData.commLoad = $("#commLoad").val();
 * */
var getDefConfig = function () {
    return {
        params: {
            sensorCode: {
                id: 'inst_code', //前端编码
                checkFunc: function (value, viewId, view) {
                    var codePatten = /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
                    checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('DEFAULT_CHECK_Tips'));
                }
            },
            sensorType: {
                id: 'inst_type', //前端类型
            },
            sensorName: {
                id: 'inst_name', //前端名称,
                checkFunc: function (value, viewId, view) {
                    checkLegalNameValue(value, 'inst_name');
                }
            },
            sampleSpace: {
                id: 'sampleSpace', //采样间隔
                defValue: 6,
                checkFunc: function (value, viewId, view) {
                    checkNumValue(value, "sampleSpace", getI18nName('instSampleSpaceTip'), 3, 1440);
                }
            },
            commLinkType: {
                id: 'comm_link_type', //通讯链路类型
                defValue: 'Modbus485'
            }
        },
    };
};
var extConfigs = {
    'IR_DETECTION_OUT': {
        params: {
            sensorCode: {
                id: 'inst_code', //前端编码
                checkFunc: function (value, viewId, view) {
                    var commonLinkType = $('#comm_link_type').val();
                    var codePatten;
                    switch (commonLinkType) {
                        case 'IO':
                            codePatten = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]:([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])))$/
                            checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('IR_DETECTION_OUT_IO_CHECK_Tips'));
                            break;
                        case 'Modbus_Over_Tcp':
                            codePatten = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]:([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])):(([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
                            checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips'));
                            break;
                        case 'LoRa':
                        case 'Modbus485':
                            codePatten = /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
                            checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('IR_DETECTION_OUT_LORA_485_CHECK_Tips'));
                            break;
                    }
                }
            },
            commLinkType: {
                id: 'comm_link_type', //通讯链路类型
                defValue: 'IO'
            }
        },
    },
    'FAN_OUT': {
        extendInfoParams: {
            controlType: {
                id: 'controlType',
                defValue: 1,
            },
        },
        extendsFrom: 'IR_DETECTION_OUT',
    },
    'LIGHT_OUT': {
        extendInfoParams: {
            controlType: {
                id: 'controlType',
                defValue: 1,
            },
        },
        extendsFrom: 'IR_DETECTION_OUT',
    },
    'HIKVIDEO_OUT': {
        extendInfoParams: {
            cameraType: {
                id: 'cameraType',
                defValue: 1,
            },
            enterKey: {
                id: 'enterKey',
                defValue: 0,
            }
        },
        params: {
            sensorCode: {
                id: 'inst_code', //前端编码
                checkFunc: function (value, viewId, view) {
                    // codePatten = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5]:([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])))$/
                    // checkLegalNameValueByPatten(value, 'inst_code', codePatten, '编号格式为 ip:port ,如 *********:100,其中ip格式[xxx.xxx.xxx.xxx] port端口范围[0-65535]');
                }
            },
        },
        hideParams: ['comm_link_type']
    },
    NOISEWS_OUT: {
        params: {
            sensorCode: {
                id: 'inst_code', //前端编码
                checkFunc: function (value, viewId, view) {
                    codePatten = /^((\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):[\S\n\s]{0,4}:[\S\n\s]{0,4})$/
                    checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('NOISEWS_OUT_CHECK_Tips'));
                }
            },
        }
    },
    TEMPWS_OUT: {
        extendsFrom: 'NOISEWS_OUT',
    },
    FLOODWS_OUT: {
        extendsFrom: 'NOISEWS_OUT',
    },
    VibrationSY_OUT: {
        extendsFrom: 'NOISEWS_OUT',
        params: {
            sensorCode: {
                id: 'inst_code', //前端编码
                checkFunc: function (value, viewId, view) {
                    codePatten = /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5]))\:([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])\:([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
                    checkLegalNameValueByPatten(value, 'inst_code', codePatten, getI18nName('VibrationSY_OUT_CHECK_Tips'));
                }
            },
        }
    },
};
var sensorConfigControlUtil = {
    currentCacheSensorType: '',
    viewParamsIdEnum: {
        'inst_code': 'aduId',
        'inst_type': 'aduType',
        'inst_name': 'aduName',
        'sampleSpace': 'sampleSpace',
        'comm_link_type': 'comm_link_type',
        'controlType': 'controlType',
        'cameraType': 'cameraType',
        'enterKey': 'enterKey',
    },
    sensorConfig: {
        "NOISE_JDRK_OUT": getDefConfig(),
        "TEMPWS_OUT": getDefConfig(),
        "FLOODWS_OUT": getDefConfig(),
        "NOISEWS_OUT": getDefConfig(),
        "SF6_OUT": getDefConfig(),
        "LIGHT_OUT": getDefConfig(),
        "FAN_OUT": getDefConfig(),
        "IR_DETECTION_OUT": getDefConfig(),
        "HIKVIDEO_OUT": getDefConfig(),
        "VibrationSY_OUT": getDefConfig(),
    },
    init: function () {
        for (var key in extConfigs) {
            var config = extConfigs[key];
            if (config.extendsFrom) {
                config.params = Object.assign({}, extConfigs[config.extendsFrom].params, config.params);
            }
            if (config.params) {
                this.sensorConfig[key].params = Object.assign({}, this.sensorConfig[key].params, config.params);
            }
            if (config.extendInfoParams) {
                this.sensorConfig[key].extendInfoParams = Object.assign({}, this.sensorConfig[key].extendInfoParams, config.extendInfoParams);
            }
            if (config.hideParams) {
                this.sensorConfig[key].hideParams = Object.assign([], this.sensorConfig[key].hideParams, config.hideParams);
            }
        }
    },
    checkIsOut: function (type) {
        this.currentCacheSensorType = type;
        return Object.keys(this.sensorConfig).filter(function (sensorType) {
            return sensorType == type;
        }).length > 0;
    },
    initSensorParamView: function (type) {
        for (var paramId in this.viewParamsIdEnum) {
            $('#' + paramId).parent().parent().removeClass('hide').addClass('hide');
        }
        var tempSensorConfig = Object.assign({}, this.sensorConfig[type].params, this.sensorConfig[type].extendInfoParams);
        for (var key in tempSensorConfig) {
            var viewConfig = tempSensorConfig[key];
            $('#' + viewConfig.id).parent().parent().removeClass('hide');
        }
        if (this.sensorConfig[type].hideParams) {
            this.sensorConfig[type].hideParams.map(function (paramId) {
                $('#' + paramId).parent().parent().removeClass('hide').addClass('hide');
            })
        }
    },
    setDefaultValue: function (type) {
        var tempSensorConfig = Object.assign({}, this.sensorConfig[type].params, this.sensorConfig[type].extendInfoParams);
        for (var key in tempSensorConfig) {
            var viewConfig = tempSensorConfig[key];
            var tempView = $('#' + viewConfig.id);
            tempView.show();
            if (viewConfig.defValue != undefined) {
                var tagName = tempView[0].tagName;
                if (tagName == 'SELECT') {
                    tempView.val(viewConfig.defValue).select2();
                    tempView.trigger('change');
                } else {
                    tempView.val(viewConfig.defValue);
                }
            }
        }
    },
    checkOnSave: function (type) {
        var tempSensorConfig = Object.assign({}, this.sensorConfig[type].params, this.sensorConfig[type].extendInfoParams);
        var flag = true;
        for (var key in tempSensorConfig) {
            var viewConfig = tempSensorConfig[key];
            if (viewConfig.checkFunc != undefined) {
                var tempView = $('#' + viewConfig.id);
                //为后续进行外部判断改造做准备，当前使用内部抛异常中断函数方法，參考旧方案
                if (!viewConfig.checkFunc(tempView.val(), viewConfig.id, tempView)) {
                    flag = false;
                }
            }
        }
        return flag;
    },
    saveSensorInfo: function (formData, saveType) {
        var tempConfig = this.sensorConfig[this.currentCacheSensorType];
        for (var key in tempConfig.params) {
            var config = tempConfig.params[key];
            formData[this.viewParamsIdEnum[config.id]] = $('#' + config.id).val();
        }
        if (tempConfig.extendInfoParams) {
            var extendInfo = {}
            for (var key in tempConfig.extendInfoParams) {
                var config = tempConfig.extendInfoParams[key];
                extendInfo[this.viewParamsIdEnum[config.id]] = $('#' + config.id).val();
            }
            formData.extendInfo = JSON.stringify(extendInfo);
        }
        saveInstInfo(formData, saveType);
    }
};
sensorConfigControlUtil.init();