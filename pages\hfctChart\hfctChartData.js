function getHFCTPRPSChartBody(respData, chartCache, callbacks) {
  let chartData = respData.chartBody;
  let axisInfo = chartData.axisInfo;
  let dataList = chartData.dataList;
  let chartBody = {
    title: 'HFCT PRPS',
    axisInfo: {
      groupNameId: 'prp-chartData',
      xDesc: getI18nName(axisInfo.xDesc),
      xRangeMax: axisInfo.xRangeMax,
      xRangeMin: axisInfo.xRangeMin,
      xUnit: axisInfo.xUnit,
      yDesc: getI18nName(axisInfo.yDesc),
      yRangeMax: axisInfo.yRangeMax,
      yRangeMin: axisInfo.yRangeMin,
      yUnit: axisInfo.yUnit,
      // ampNum: (axisInfo.yRangeMax - axisInfo.yRangeMin) / axisInfo.yInterval,
      // ampInterval: axisInfo.yInterval,
      zDesc: getI18nName(axisInfo.zDesc),
      zMaxValue: '',
      zRangeMax: axisInfo.zRangeMax,
      zRangeMin: axisInfo.zRangeMin,
      zUnit: axisInfo.zUnit,
      phaseShift: 0,
      thresholdFilter: 0,
      zRange: [axisInfo.zRangeMin, axisInfo.zRangeMax],
      isUnitChanged: chartCache.isUnitChanged,
      displayUnit: chartCache.displayUnit,
      phaseNum:
        axisInfo.phaseNum != undefined ? axisInfo.phaseNum : axisInfo.periodNum,
      freqCycleNum: axisInfo.freqCycleNum,
      TOPNValue: chartCache.TOPNValue,
      onUnitChanged: function (flag, displayUnit) {
        chartCache.isUnitChanged = flag;
        chartCache.displayUnit = displayUnit;
        if (chartCache.prps && chartCache.prps.params) {
          preCheckHFCTParams(chartCache.prps, chartCache.isUnitChanged, chartCache);
          callbacks.updateParamsDisplay(chartCache.prps.params);
        }
      },
    },
    series: [
      {
        color: 'jet',
        dataList: [],
      },
    ],
  };

  let zMax = -999;
  dataList.map((params) => {
    if (zMax < params.z) zMax = params.z;
    let seriesData = [params.x, params.y, params.z];
    chartBody.series[0].dataList.push(seriesData);
  });
  chartCache.prps.zMax = zMax;
  chartBody.axisInfo.zMaxValue =
    zMax >= axisInfo.zRangeMax
      ? `Max>=${axisInfo.zRangeMax}`
      : zMax <= axisInfo.zRangeMin
      ? `Max<=${axisInfo.zRangeMin}`
      : `Max=${zMax}`;
  return chartBody;
}

function getHFCTPRPDChartBody(respData, chartCache, callbacks) {
  let chartData = respData.chartBody;
  let axisInfo = chartData.axisInfo;
  let dataList = chartData.dataList;

  let zDesc = getI18nName(axisInfo.zDesc);
  zDesc = validateStrLen(zDesc) == 0 ? getI18nName('pdNum') : zDesc;

  let chartBody = {
    title: 'HFCT PRPD',
    axisInfo: {
      groupNameId: 'prp-chartData',
      xDesc: getI18nName(axisInfo.xDesc),
      xRangeMax: axisInfo.xRangeMax,
      xRangeMin: axisInfo.xRangeMin,
      xUnit: axisInfo.xUnit,
      yDesc: getI18nName(axisInfo.yDesc),
      yRangeMax: axisInfo.yRangeMax,
      yRangeMin: axisInfo.yRangeMin,
      yUnit: axisInfo.yUnit,
      zDesc: zDesc,
      zSubtitle: zDesc,
      zMaxValue: '',
      zRangeMax: 100,
      zRangeMin: 0,
      zUnit: '',
      thresholdFilter: 0,
      zRange: [0, 100],
      cycleNum: 0,
      isUnitChanged: chartCache.isUnitChanged,
      prpsDataList: chartCache.prpsDataList,
      displayUnit: chartCache.displayUnit,
      phaseNum:
        axisInfo.phaseNum != undefined ? axisInfo.phaseNum : axisInfo.periodNum,
      freqCycleNum: axisInfo.freqCycleNum,
      TOPNValue: chartCache.TOPNValue,
      onUnitChanged: function (flag, displayUnit) {
        chartCache.isUnitChanged = flag;
        chartCache.displayUnit = displayUnit;
        if (chartCache.prps && chartCache.prps.params) {
          preCheckHFCTParams(chartCache.prps, chartCache.isUnitChanged, chartCache);
          callbacks.updateParamsDisplay(chartCache.prps.params);
        }
      },
    },
    series: [
      {
        dataList: [],
      },
    ],
  };

  let yMax = -999;
  dataList.map((params) => {
    if (yMax < params.y) yMax = params.y;
    let zValue =
      params.y < axisInfo.yRangeMin ? 0 : params.z != undefined ? params.z : 0;
    let seriesData = [params.x, params.y, zValue];
    chartBody.series[0].dataList.push(seriesData);
  });
  let unit = respData.chartBody ? respData.chartBody.axisInfo.yUnit : 'dB';
  let checkYMaxValue = chartCache.prps.zMax;
  if (checkYMaxValue != yMax) yMax = checkYMaxValue;
  chartBody.axisInfo.zMaxValue =
    yMax >= axisInfo.yRangeMax
      ? `Max>=${axisInfo.yRangeMax}`
      : yMax <= axisInfo.yRangeMin
      ? `Max<=${axisInfo.yRangeMin}`
      : `Max=${yMax}`;
  return chartBody;
}

function preCheckHFCTParams(respData, isUnitChanged = false, chartCache) {
  // 需要检查的参数名列表
  let checkParamsNameList = ['pd_max', 'pd_min', 'pd_avg', 'gain'];
  let unit = respData.chartBody ? respData.chartBody.axisInfo.zUnit : 'dB';
  checkParamsNameList.map((paramsName) => {
    let params = respData.params[paramsName];
    if (params) {
      let value = params.replace(unit, '');
      if (respData.params[paramsName + '_org_value'] === undefined) {
        respData.params[paramsName + '_org_value'] = value;
      }
      if (paramsName != 'gain') {
        //gain不进行值校验
        let valueStr = checkHFCTChartValue(
          respData.params[paramsName + '_org_value'],
          unit,
          isUnitChanged,
          chartCache,
        );
        respData.params[paramsName] = valueStr;
      }
    }
  });
}

function checkHFCTChartValue(value, unit, isUnitChanged = false, chartCache) {
  let chartConfigUtil = pdcharts.ChartConfigUtil;
  let CONFIG_TYPE = chartConfigUtil.CONFIG_TYPE;
  let { getValueByConfig, getVisualTransedValueByConfig } = chartConfigUtil;
  let checkFunc = isUnitChanged
    ? getVisualTransedValueByConfig
    : getValueByConfig;
  var resultObj = checkFunc({
    dataType: CONFIG_TYPE.HFCT,
    chartType: pdcharts.chartType.hfctprpsd,
    value: value,
    unit: unit,
    displayUnit: chartCache ? chartCache.displayUnit : undefined,
  });
  // console.log(resultObj)
  return resultObj.valueStr;
}
