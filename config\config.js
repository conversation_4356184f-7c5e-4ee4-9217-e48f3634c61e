﻿/**
 * 页面打包版本号
 * @type {string}
 */
var webVersionCode = '2.0.0_yuenan_new_sensor_202506051623';

var debugUrlPool = [
  '172.16.3.110',
  '192.168.14.198',
  '192.168.14.240',
  '172.16.3.158',
  '172.16.3.211',
  '172.16.3.243',
  '172.16.3.116',
  '172.16.3.108',
  '172.16.3.116',
  '192.168.4.10',
  '172.16.3.53',
  '172.16.3.207',
  '172.16.3.143',
  '172.16.3.158',
  '172.16.3.107',
  '172.16.3.31',
  '172.16.3.31',
  '172.16.3.254',
  '172.16.3.108',
  '172.16.3.87',
  '192.168.14.98',
  '172.16.3.87',
  '172.16.3.106',
  '172.16.3.143',
  '172.16.3.123',
  '172.16.3.38',
  '172.16.3.180',
  '172.16.3.118',
  '172.16.3.26',
  '172.16.3.23',
];

var currentDebugUrl = debugUrlPool[0];

var debugServer = 'http://' + currentDebugUrl + ':8003';
// var debugServer = 'http://' + currentDebugUrl + ':8089';

var debugWebSocket = 'ws://' + currentDebugUrl + ':8004/SocketServer';
// var debugWebSocket = 'ws://' + debugUrlPool[1] + ':8004/SocketServer';

// var mokerServer = 'http://921066bd-daa9-43c7-8d45-90eaa1c5b6a3.mock.pstmn.io/';
// var mokerServer = 'http://192.168.14.240:10086/';
var mokerServer = 'http://192.168.14.240:8089/';

/*
 壁挂机、2U、辅控型号都用：PDS-S500-LDCU，
 名称：
 壁挂、2U用：数据采集主机
 辅控用：智能运维监测终端

 汇集型号用：PDS-S500-MEU
 名称用：汇集节点
 */
var _BuildModeEnum = {
  PDStars: {
    //页面打包版本(国内)
    code: 'PdsafeOrg',
    defLan: 'zh-CN',
    logo: './dist/img/pdstars.png',
    icon: './dist/img/logo.png',
    monitorInfo: {
      MonitorTypeHanging: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorType2U: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeCollectionNode: {
        monitorType: 'PDS-S500-MEU',
        monitorName: '状态监测设备汇集节点',
      },
      MonitorTypeLowPower: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeAux: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '智能运维监测终端',
      },
    },
    noDisplayIDList: [],
    commonLangJson: {
      'zh-CN': {
        S1010Name: '数据采集主机',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'en-US': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
    },
  },
  PdSafe: {
    //页面打包版本(国内-PdSafe版本)
    code: 'PdSafe',
    defLan: 'zh-CN',
    logo: './dist/img/pdstars.png',
    icon: './dist/img/logo.png',
    monitorInfo: {
      MonitorTypeHanging: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
        monitorNameCN: '数据采集主机',
      },
      MonitorType2U: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeCollectionNode: {
        monitorType: 'PDS-S500-MEU',
        monitorName: '状态监测设备汇集节点',
      },
      MonitorTypeLowPower: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeAux: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '智能运维监测终端',
      },
    },
    noDisplayIDList: [],
    commonLangJson: {
      'zh-CN': {
        S1010Name: '数据采集主机',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'en-US': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'ar-SA': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'es-ES': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'fr-FR': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
      'zh-TW': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025 华乘电气科技股份有限公司',
      },
    },
  },
  PdSafeCommon: {
    //页面打包版本(国内-PdSafe通用无logo公司版本)
    code: 'PdSafeCommon',
    defLan: 'zh-CN',
    logo: './dist/img/default.png',
    icon: './dist/img/default.png',
    monitorInfo: {
      MonitorTypeHanging: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorType2U: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeCollectionNode: {
        monitorType: 'PDS-S500-MEU',
        monitorName: '状态监测设备汇集节点',
      },
      MonitorTypeLowPower: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeAux: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '智能运维监测终端',
      },
    },
    noDisplayIDList: [],
    commonLangJson: {
      'zh-CN': {
        S1010Name: '数据采集主机',
        copyRight: '©2025',
      },
      'en-US': {
        S1010Name: 'Local Data Collecting Unit',
        copyRight: '©2025',
      },
    },
  },
  PdBeijingSifang: {
    //页面打包版本(国内-PdSafe版本)
    code: 'BeijingSifang',
    defLan: 'zh-CN',
    logo: './dist/img/bjsf-logo.png',
    icon: './dist/img/bjsf-icon.png',
    monitorInfo: {
      MonitorTypeHanging: {
        monitorType: 'CSC-171L',
        monitorName: '物联网智能接入节点（局部放电）',
      },
      MonitorType2U: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeCollectionNode: {
        monitorType: 'PDS-S500-MEU',
        monitorName: '状态监测设备汇集节点',
      },
      MonitorTypeLowPower: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeAux: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '智能运维监测终端',
      },
    },
    noDisplayIDList: [],
    commonLangJson: {
      'zh-CN': {
        S1010Name: '物联网智能接入节点（局部放电）',
        copyRight: '©2025 北京四方继保自动化股份有限公司',
        modbusCompanySelf: '北京四方',
      },
      'en-US': {
        S1010Name: '物联网智能接入节点（局部放电）',
        copyRight: '©2025 BeiJingSiFangAutoMation CO.,LTD',
        modbusCompanySelf: '北京四方',
      },
    },
  },
  PMDT: {
    //页面打包版本（PMDT）
    code: 'PMDT',
    defLan: 'en-US',
    logo: './dist/img/logo_pmdt.png',
    icon: './dist/img/ic_logo_pmdt.png',
    monitorInfo: {
      MonitorTypeHanging: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorType2U: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeCollectionNode: {
        monitorType: 'PDS-S500-MEU',
        monitorName: '状态监测设备汇集节点',
      },
      MonitorTypeLowPower: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '数据采集主机',
      },
      MonitorTypeAux: {
        monitorType: 'PDS-S500-LDCU',
        monitorName: '智能运维监测终端',
      },
    },
    noDisplayIDList: [],
    commonLangJson: {
      'zh-CN': {
        S1010Name: '状态监测设备汇集节点',
        copyRight: '©2025 PMDT Ltd.',
      },
      'en-US': {
        S1010Name: 'Status Monitoring Equipment Aggregation Node',
        copyRight: '©2025 PMDT Ltd.',
      },
    },
  },
};

var buildMode = _BuildModeEnum.PdSafe;

function initBuildModeType(code) {
  var tempMode;
  for (var item in _BuildModeEnum) {
    if (_BuildModeEnum[item].code == code) {
      tempMode = _BuildModeEnum[item];
      break;
    }
  }
  if (!tempMode) {
    tempMode = buildMode;
  }
  buildMode = tempMode;
}
