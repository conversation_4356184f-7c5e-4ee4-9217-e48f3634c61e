var groupCodeScanLorFrequency = 0;
var groupNoMax = 6;
var groupNoMin = 0;
function initGroupCodeScan() {
  var data = null;
  poll(
    'GET',
    GET_Sys_Info,
    data,
    function (text) {
      var respData = text.result;
      groupCodeScanLorFrequency = respData.lora_frequency;

      var tempName = getLoraFrequencyName(groupCodeScanLorFrequency);
      $('#groupLoraFrequency').html(getI18nName(tempName));
      initScanGroupRange();
      initScanTable();
    },
    function () {}
  );
}

function initScanGroupRange() {
  pollGet(
    'GetGroupNoRange',
    { lora_frequency: groupCodeScanLorFrequency },
    function (res) {
      // console.log(res)
      getSyncDataADUTypeForGroupCodeScan(aduType);
      let { startGroupNo, endGroupNo } = res.result;
      if (res.result) {
        groupNoMax = endGroupNo;
        groupNoMin = startGroupNo;
        var startEle = $('#startGroupNo');
        var endEle = $('#endGroupNo');
        for (var i = 0; i <= groupNoMax; i++) {
          startEle.append(`<option value=${i}>${i}</option>`);
          endEle.append(`<option value=${i}>${i}</option>`);
        }
        startEle.select2();
        endEle
          .select2()
          .val(groupNoMax > 6 ? 6 : groupNoMax)
          .trigger('change');
        /* startEle.select2().val(3).trigger('change');
            endEle.select2().val(3).trigger('change'); */
      }
    }
  );
}

function initScanTable() {
  /**
   * 初始化表格内容
   */
  initLocalCustomTable({
    id: 'tableGroupCodeScan',
    height: $("#global").height() - 100,
    columns: [
      {
        field: 'id',
        title: getI18nName('sensorCode'),
      },
      {
        field: 'sensorGroupCode',
        title: getI18nName('SensorGroupNum'),
        formatter: function (value, row, index) {
          return value;
        },
      },
      {
        field: 'scanState',
        title: getI18nName('SensorScanState'),
        formatter: function (value, row, index) {
          return value;
        },
      },
    ],
  });
}

function updateGroupNoTableHandler() {
  var data = [];
  var allResult = findAduGroupNoWSCache.allArr.split(',');

  var currentId = findAduGroupNoWSCache.id;
  var progress = findAduGroupNoWSCache.progress;

  var successArr = findAduGroupNoWSCache.successArr;
  var failedArr = findAduGroupNoWSCache.failedArr;

  allResult.map((id) => {
    var item = {
      id: id,
      sensorGroupCode: '-',
      scanState: '',
    };
    findAduGroupNoWSCache.extraMsg.findResult.map((fRes) => {
      if (fRes.adulist.indexOf(id) >= 0) {
        item.sensorGroupCode = fRes.groupNo;
      }
    });

    var state = '';
    if (currentId == id) {
      var SampleProgressEnum = getEnumByCode(progress, ADU_PROGRESS_ENUM);
      state = SampleProgressEnum.name;
    }
    if (successArr.indexOf(id) >= 0) {
      state = getI18nName('Complete');
    } else if (failedArr.indexOf(id) >= 0) {
      state = getI18nName('Failed');
    }
    item.scanState = state;
    data.push(item);
  });
  updateScanTable(data);
}

var findAduGroupNoWSCache = {};

function onFindCancel() {
  layer.msg(getI18nName('ScanCancelMsg'));
  startFindAduGroupNoState(false);
}

function startFindAduGroupNoState(flag) {
  if (flag) {
    $('#addScanSensorId').attr('disabled', 'disabled');
    $('#addScanSensorIdBtn').attr('disabled', 'disabled');
    $('#fu-aduType').attr('disabled', 'disabled');
    $('#startGroupNo').attr('disabled', 'disabled');
    $('#endGroupNo').attr('disabled', 'disabled');

    $('#group_scan_shuttle_rightAll').attr('disabled', 'disabled');
    $('#group_scan_shuttle_rightSelected').attr('disabled', 'disabled');
    $('#group_scan_shuttle_leftSelected').attr('disabled', 'disabled');
    $('#group_scan_shuttle_leftAll').attr('disabled', 'disabled');

    $('#group_scan_shuttle').attr('disabled', 'disabled');
    $('#group_scan_shuttle_to').attr('disabled', 'disabled');
    $('#fu-scan-group').addClass('hide');
    $('#fu-cancel').removeClass('hide');
  } else {
    $('#addScanSensorId').removeAttr('disabled');
    $('#addScanSensorIdBtn').removeAttr('disabled');
    $('#fu-aduType').removeAttr('disabled');
    $('#startGroupNo').removeAttr('disabled');
    $('#endGroupNo').removeAttr('disabled');
    $('#fu-cancel').removeAttr('disabled');

    $('#group_scan_shuttle_rightAll').removeAttr('disabled');
    $('#group_scan_shuttle_rightSelected').removeAttr('disabled');
    $('#group_scan_shuttle_leftSelected').removeAttr('disabled');
    $('#group_scan_shuttle_leftAll').removeAttr('disabled');

    $('#group_scan_shuttle').removeAttr('disabled');
    $('#group_scan_shuttle_to').removeAttr('disabled');
    $('#fu-scan-group').removeClass('hide');
    $('#fu-cancel').addClass('hide');
    $('#fu-cancel img').addClass('hide');
  }
}

function onFindAduGroupNo(value, code, key) {
  console.log(value);
  findAduGroupNoWSCache = value;
  startFindAduGroupNoState(true);
  updateGroupNoTableHandler();
  /* var tempRes={
        id:  ''//当前正在执行的 传感器
        index: 1 //当前index
        total:  3//当前组号扫描下的传感器总数
        progress:	//当前传感器进度
        successArr:  //成功的传感器列表，","分隔
        failedArr:   //失败的传感器列表， ","分隔，给定组号范围内的组号扫描完成都没有连接上的传感器
        extraMsg:{
                  currentWorkGroupNo：5,    //当前正在扫描的组号
                  findResult:[				//组号扫描结果信息，存放传感器组号对应关系 JSON Array
                                {
                                 GroupNo : 1,   //组号
                                 adulist : 1,1  //该组号下对应的传感器列表 ","分隔
                                },
                            ]
                }  
     } */
  var currentId = value.id;
  var index = value.index;
  var total = value.total;
  var progress = value.progress;

  var successArr = value.successArr.split(',');
  var failedArr = value.failedArr.split(',');
  var allArr = value.allArr ? value.allArr.split(',') : [];

  var SampleProgressEnum = getEnumByCode(progress, ADU_PROGRESS_ENUM);
  if (SampleProgressEnum.code == ADU_PROGRESS_ENUM.FAILED.code) {
    $('.notifications')
      .notify({
        fadeOut: {
          enabled: false,
        },
        message: {
          text: stringFormat.format(
            getI18nName('errorTip'),
            `${currentId} ${convertProgressErrorCode(errorCode)}`
          ),
        },
        type: 'danger',
      })
      .show();
  }
  var percent =
    SampleProgressEnum.percent * (1 / total) + ((index - 1) / total) * 100;
  var progressInfo = `${currentId} ${getI18nName('State')}:${
    SampleProgressEnum.name
  }`;
  var progressTextMsg = getI18nName('SensorGroupNumScan');
  var iProgressTextMsg = '';

  if (percent >= 0 && percent < 100) {
    progressTextMsg = stringFormat.format(
      getI18nName('GroupNumScaning') + ' [{1}]:{2} ({3})',
      value.extraMsg.currentWorkGroupNo,
      `${index}/${total}`,
      progressInfo
    );
    iProgressTextMsg = stringFormat.format(
      getI18nName('GroupNumScaning') + ' [{1}]:[{2}]({3})',
      value.extraMsg.currentWorkGroupNo,
      `${index}/${total}`,
      progressInfo
    );
  }

  if (progress == ADU_PROGRESS_ENUM.END.code) {
    percent = 100;
    startFindAduGroupNoState(false);
  }

  progressUtil.updateProgress({
    colCtrlId: '',
    currentId: currentId,
    currentIndex: index,
    currentTitle: getI18nName('SensorGroupNumScan'),
    percent: percent,
    currentProgress: SampleProgressEnum.percent,
    successArr: successArr,
    failedArr: failedArr,
    allArr: allArr,
    progressTextMsg: progressTextMsg,
    iProgressTextMsg: iProgressTextMsg,
    successMsg: getI18nName('SensorGroupNumScanEnd'),
  });
}

function updateScanTable(data) {
  $('#tableGroupCodeScan').bootstrapTable('refreshOptions', { data: data });
  $('.keep-open.btn-group').removeAttr('title');
}

//更新当前页面初始化状态
function updateGroupCodePageStatus(statusRes) {
  var isCancelFind = statusRes.isCancelFind;
  var currentAduType = statusRes.currentFindAduType;
  var startGroupNo = statusRes.currentFindGroupNoRange.startGroupNo;
  var endGroupNo = statusRes.currentFindGroupNoRange.endGroupNo;
  var allAduList = statusRes.currentFindAduList.split(',');

  if (isCancelFind) {
    $('#fu-cancel').prop('disabled', true);
    $('#fu-cancel img').removeClass('hide');
  }

  var startEle = $('#startGroupNo');
  var endEle = $('#endGroupNo');
  startEle.select2().val(startGroupNo).trigger('change');
  endEle.select2().val(endGroupNo).trigger('change');

  //切换类型并不触发change事件
  aduType.select2().val(currentAduType);
  aduType.select2().val();

  var data = {
    aduType: currentAduType,
  };
  poll('GET', 'GetADUVersionList', data, function (text) {
    var select = $('#group_scan_shuttle');
    var selectTo = $('#group_scan_shuttle_to');
    select.empty();
    selectTo.empty();
    var respData = text.result;
    respData.forEach(function (item, index) {
      if (allAduList.indexOf(item.aduId) < 0) {
        var option = $('<option />', {
          value: '' + item.aduId + '',
        }).append(item.aduId);
        select.append(option);
      }
    });
    allAduList.forEach(function (item, index) {
      var option = $('<option />', {
        value: '' + item + '',
      }).append(item);
      selectTo.append(option);
    });
  });

  pollPost('PushFindAduGroupNoProgress');
}

/*
 * 获取前端类型-仅包含前端中有数据的类型
 * -----------------------
 */
function getSyncDataADUTypeForGroupCodeScan(element) {
  var data = null;
  poll('GET', 'getFindGroupNoAduType', data, function (text) {
    var respData = text.result;
    respData.forEach(function (item, index) {
      if (item.value.length > 0) {
        var option = $('<option />', {
          value: '' + item.value + '',
        }).append(getI18nName(item.value));
        element.append(option);
      }
      if (index === 0 && item.value.length > 0) {
        // element.select2().val('HFCT_IS').trigger('change')
        getADUVersionListForGroupCodeScan(item.value);
      }
    });
  });
}

/*
 * 获取前端版本信息列表
 * -----------------------
 * aduType：前端类型
 */
function getADUVersionListForGroupCodeScan(aduType) {
  var data = {
    aduType: aduType,
  };
  poll('GET', 'GetADUVersionList', data, function (text) {
    var select = $('#group_scan_shuttle');
    select.empty();
    $('#group_scan_shuttle_to').empty();
    var respData = text.result;
    respData.forEach(function (item, index) {
      /* var option = $("<option />", {
                "value": "" + item.aduId + ""
            }).append(item.aduId + "-[" + item.aduName + "]"); */
      var option = $('<option />', {
        value: '' + item.aduId + '',
      }).append(item.aduId);
      select.append(option);
    });
    checkGroupCodeScanPageStatus();
  });
}

function checkGroupCodeScanPageStatus() {
  if (!hasInitGroupCodeScan) {
    hasInitGroupCodeScan = true;
    pollGet('GetFindAduGroupNoStatus', {}, function (res) {
      var statusRes = res.result;
      if (statusRes.isFindAduGroupNo) {
        updateGroupCodePageStatus(statusRes);
      }
    });
  }
}