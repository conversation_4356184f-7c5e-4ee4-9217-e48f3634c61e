<style>
  .tempParam {
    height: 32px;
  }

  .tempParam label {
    text-align: right;
  }
</style>
<div class="col-sm-12 full-height" id="maingraph" style="padding: 0px">
  <div
    class="form-group"
    style="
      height: 55px;
      border-left: 1px solid #b9b7b7;
      border-right: 1px solid #b9b7b7;
      background-color: white;
      margin-bottom: 0px;
    "
  >
    <div class="col-sm-5" style="margin-top: 10px">
      <label
        for="dateStart"
        class="col-sm-4 control-label"
        style="margin-top: 7px"
        data-i18n="startDate"
        >起始日期</label
      >
      <div class="col-sm-8">
        <div class="bootstrap-datepicker">
          <div class="input-group">
            <div class="input-group-addon">
              <i class="fa fa-calendar"></i>
            </div>
            <input
              id="dateStart"
              type="text"
              class="form-control pull-right"
              readonly="true"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-5" style="margin-top: 10px">
      <label
        for="dateEnd"
        class="col-sm-4 control-label"
        style="margin-top: 7px"
        data-i18n="endDate"
        >结束日期</label
      >
      <div class="col-sm-8">
        <div class="bootstrap-datepicker">
          <div class="input-group">
            <div class="input-group-addon">
              <i class="fa fa-calendar"></i>
            </div>
            <input
              id="dateEnd"
              type="text"
              class="form-control pull-right"
              readonly="true"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-2">
      <button
        type="button"
        id="refreshBtn"
        class="btn btn-block btn-primary"
        style="width: 80px; margin: 10px 0px 10px auto"
        data-i18n="refresh"
      >
        刷新
      </button>
    </div>
  </div>
  <div class="row">
    <div
      class="form-group col-lg-9 col-md-9 col-sm-9"
      id="graph"
      style="
        height: 90%;
        border: 1px solid #b9b7b7;
        background-color: white;
        margin-bottom: 0px;
      "
    >
      <div id="tmpchart" style="width: 100%; height: 100%"></div>
    </div>
    <div
      class="form-group col-lg-3 col-md-3 col-sm-3"
      id="params"
      style="
        height: 100%;
        border: 1px solid #b9b7b7;
        background-color: white;
        margin-bottom: 0px;
        padding-left: 0px;
        min-width: 350px;
      "
    >
      <div style="height: 10%"></div>
      <div class="form-group tempParam">
        <label class="col-sm-5 control-label" data-i18n="pointName"
          >测点名称:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="pointName"></label>
        </div>
      </div>
      <div class="form-group tempParam">
        <label class="col-sm-5 control-label" data-i18n="sensorCode"
          >传感器编码:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="sensorCode"></label>
        </div>
      </div>
      <div class="form-group tempParam">
        <label class="col-sm-5 control-label" data-i18n="sensorType"
          >传感器类型:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="sensorType"></label>
        </div>
      </div>
      <div class="form-group tempParam">
        <label class="col-sm-5 control-label" data-i18n="acquisitionTime"
          >采集时间:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="simpleTime"></label>
        </div>
      </div>
      <div class="form-group tempParam">
        <label class="col-sm-5 control-label" data-i18n="TEMP"
          >温度:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="amp"></label>
        </div>
      </div>
    </div>
  </div>
</div>
<!--</div>-->

<script type="text/javascript">
  initPageI18n();
  var dataUnit = '';
  $(function () {
    // 基于准备好的dom，初始化echarts图表
    //@ sourceURL=temp_chart.js

    //Daterangepicker
    $('#dateStart').daterangepicker({
      opens: 'right',
      autoApply: true,
      timePicker: true,
      singleDatePicker: true,
      timePicker24Hour: true,
      locale: {
        format: 'YYYY/MM/DD HH:mm:ss',
      },
      startDate: new Date(new Date().toDateString()).pattern(
        'yyyy/MM/dd HH:mm:ss'
      ),
    });

    //Daterangepicker
    $('#dateEnd').daterangepicker({
      opens: 'right',
      autoApply: true,
      timePicker: true,
      singleDatePicker: true,
      timePicker24Hour: true,
      locale: {
        format: 'YYYY/MM/DD HH:mm:ss',
      },
      startDate: new Date().pattern('yyyy/MM/dd HH:mm:ss'),
    });

    $('#dateStart').click(function () {
      $('.minuteselect').attr('disabled', 'disabled');
      $('.minuteselect').css('background', 'lightgray');
    });

    $('#dateEnd').click(function () {
      $('.minuteselect').attr('disabled', 'disabled');
      $('.minuteselect').css('background', 'lightgray');
    });

    window.addEventListener('resize', function () {
      // if (myChart)
      //   myChart.resize();
    });

    $(window).resize(function () {
      if (!document.getElementById('tmpchart')) {
        //js判断元素是否存在
        return;
      }
      var height = $('#maingraph').height() - 120;
      var height = $('#maingraph').height() - 120;
      $('#graph').height(height);
      $('#params').height(height);
      var paramsWidth = $('#params')[0].offsetWidth;
      if(paramsWidth == 350){
        $('#graph').width($('#params').parent().width() - 350 - 32)
      }
    });
    $(window).resize();

    // var myChart = echarts.init(document.getElementById('tmpchart'));

    var dataXValue = [' '],
      dataYValue = [0];

    var option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        confine: true,
        formatter: function (params) {
          let htmlStr = `<div style="padding:2px;display:block;text-align:start;">`;
          htmlStr += `<span style="text-align: start;line-height:13px;font-size:12px">${params[0].axisValue}</span><br/>`;
          params.map(function (item) {
            let color = item.color; //图例颜色
            //为了保证和原来的效果一样，这里自己实现了一个点的效果
            htmlStr += `<div style="display: flex;align-items: center;"><span style="display:inline-block;width:10px;height:10px;border-radius:5px;margin-right:5px;background-color:${color};"></span>`;
            let dataValue = item.data.value !=undefined ? item.data.value : item.data;
            let tempUnit = item.data.unit ? item.data.unit : dataUnit;
            // 根据配置类型和值获取对应的显示值
            var resultObj = pdcharts.ChartConfigUtil.getValueByConfig({
              dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
              chartType: pdcharts.chartType.temperature,
              value: dataValue,
              unit: tempUnit,
            });
            htmlStr += `<span style="text-align: start;line-height:13px;font-size:12px">${resultObj.value}${tempUnit}</span><br/>`;
            htmlStr += '</div>';
          });
          return htmlStr;
        },
      },
      title: {
        text: getI18nName('temperatureChartTitle'),
        x: 'center',
        y: 'top',
        textAlign: 'left',
      },
      calculable: true,
      xAxis: [
        {
          type: 'category',
          data: dataXValue,
          boundaryGap: false,
          axisLabel: {
            showMinLabel: true, // 是否显示第一个标签，默认为 true
            showMaxLabel: true, // 是否显示最后一个标签，将其设置为 true 即可强制显示
            // formatter: function (value) {
            //   if (value != null) {
            //     return `${value.replace(' ', '\n')}`
            //   }
            // }
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: [
        {
          name: getI18nName('TEMP'),
          type: 'line',
          data: dataYValue,
          itemStyle: {
            normal: {
              color: '#3c8dbc',
            },
          },
          markPoint: {
            data: [
              {
                type: 'max',
                name: getI18nName('maxValue'),
              },
              {
                type: 'min',
                name: getI18nName('minValue'),
              },
            ],
          },
          markLine: {
            precision: 1,
            data: [
              {
                type: 'average',
                name: getI18nName('average'),
              },
            ],
          },
        },
      ],
    };
    // myChart.setOption(option);
    pdcharts.draw(document.getElementById('tmpchart'), {
      width: $('#tmpchart').width(),
      height: $('#tmpchart').height(),
      type: pdcharts.chartType.trend,
      cover: true,
      data: {
        axisInfo: {
          unit: '℃',
          chartType: pdcharts.chartType.temperature,
          dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
        },
      },
      orgOption: option,
    });

    $('#refreshBtn').click(function () {
      var dateStartValue = $('#dateStart').val();
      var dateEndValue = $('#dateEnd').val();
      if (moment(dateStartValue).isAfter(moment(dateEndValue))) {
        layer.msg(
          '<div style="text-align:center">' +
            getI18nName('dateStartIsAfterDateEnd') +
            '</div>'
        );
      } else {
        loadTemperature(
          showChartPointId,
          showChartDataId,
          $('#dateStart').val(),
          $('#dateEnd').val()
        );
      }
    });

    loadTemperature(showChartPointId, showChartDataId, '', '');

    function loadTemperature(pointId, dataId, startDate, endDate) {
      var pointType = 'TEMP';
      var dataGet =
        'pointId=' +
        pointId +
        '&dataId=' +
        dataId +
        '&pointType=' +
        pointType +
        '&startDate=' +
        startDate +
        '&endDate=' +
        endDate;
      poll('GET', 'GetTendData', dataGet, function (text) {
        var respData = text.result;

        if (!respData) return;

        drawChart(respData);

        var sensorType = getI18nName(respData.sensorType);
        $('#pointName').html(respData.pointName);
        $('#sensorCode').html(respData.aduId);
        $('#sensorType').html(sensorType);
        $('#simpleTime').html(respData.sample_time);
        // 根据配置类型和值获取对应的显示值
        var resultObj = pdcharts.ChartConfigUtil.getValueByConfig({
          dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
          chartType: pdcharts.chartType.temperature,
          value: respData.AMP,
          unit: respData.unit,
        });
        $('#amp').html(resultObj.value + resultObj.unit);

        if (startDate !== '' && endDate !== '') {
          return;
        }

        var tempStartD = new Date(respData.startDate.split(' ')[0]);
        var tempEndD = new Date(respData.endDate.split(' ')[0]);
        tempEndD.setDate(tempEndD.getDate() + 1);
        $('#dateStart').daterangepicker({
          opens: 'right',
          autoApply: true,
          timePicker: true,
          singleDatePicker: true,
          timePicker24Hour: true,
          locale: {
            format: 'YYYY/MM/DD HH:mm:ss',
          },
          startDate: tempStartD.pattern('yyyy/MM/dd HH:mm:ss'),
        });

        //Daterangepicker
        $('#dateEnd').daterangepicker({
          opens: 'right',
          autoApply: true,
          timePicker: true,
          singleDatePicker: true,
          timePicker24Hour: true,
          locale: {
            format: 'YYYY/MM/DD HH:mm:ss',
          },
          startDate: tempEndD.pattern('yyyy/MM/dd HH:mm:ss'),
        });
        $('.minuteselect').attr('disabled', 'disabled');
        $('.minuteselect').css('background', 'lightgray');
      });
    }

    function drawChart(respData) {
      dataUnit = respData.unit;

      if (option) {
        option.xAxis[0].data = respData.dataChart.xValue;
        //                respData.dataChart.chartInit = undefined;
        if (respData.dataChart.chartInit) {
          option.series[0] = getDataItem({
            name: '原始值',
            data: respData.dataChart.chartInit,
            unit: dataUnit,
            color: '#3c8dbc',
          });
        } else {
          option.legend = {
            data: [getI18nName('maxValue'), getI18nName('average')],
            top: 15,
            left: 180,
          };
          option.series = [];
          option.series.push(
            getDataItem({
              name: getI18nName('maxValue'),
              // data: respData.dataChart.chartMax,
              data: respData.dataChart.chartInit,
              unit: dataUnit,
              color: '#3c8dbc',
            })
          );
          option.series.push(
            getDataItem({
              name: getI18nName('average'),
              // data: respData.dataChart.chartAvg,
              data: respData.dataChart.chartInit,
              unit: dataUnit,
              lineStyleType: 'dotted',
              color: '#f39c12',
            })
          );
        }

        // myChart.setOption(option);
        pdcharts.draw(document.getElementById('tmpchart'), {
          width: $('#tmpchart').width(),
          height: $('#tmpchart').height(),
          type: pdcharts.chartType.trend,
          cover: true,
          data: {
            axisInfo: {
              unit: dataUnit,
              chartType: pdcharts.chartType.temperature,
              dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
            },
          },
          orgOption: option,
        });
      }
    }

    function getDataItem(config) {
      // 根据配置类型和值获取对应的显示值
      var checkObj = pdcharts.ChartConfigUtil.getValueByConfig({
        dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
        chartType: pdcharts.chartType.temperature,
        value: 1.111111,
        unit: config.unit,
      });
      var precision = checkObj.precision;
      if (checkObj.unit != config.unit) {
        precision = pdcharts.ChartConfigUtil.getValueByConfig({
          dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
          chartType: pdcharts.chartType.temperature,
          value: 1.111111,
          unit: checkObj.unit,
        }).precision;
      }
      return {
        name: config.name,
        type: 'line',
        data: config.data,
        itemStyle: {
          normal: {
            color: config.color,
            lineStyle: {
              type: config.lineStyleType ? config.lineStyleType : 'solid',
            },
          },
        },
        markPoint: {
          label: {
            formatter(params) {
              // 自定义精度，保留两位小数
              if (params.data.type === 'max' || params.data.type === 'min') {
                let tempData = pdcharts.ChartConfigUtil.getValueByConfig({
                  dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
                  chartType: pdcharts.chartType.temperature,
                  value: params.data.value,
                  unit: checkObj.unit,
                });
                return tempData.value;
              }
              return params.data.value;
            },
          },
          symbolSize:65,
          // symbolSize: function(value, params) {
          //   // 动态设置大小，例如根据数值的大小设置点的大小
          //   if (params.data.type === 'max' || params.data.type === 'min') {
          //       return 65;  // 最大值和最小值固定大小
          //   }
          //   return 50;  // 其他点根据数值动态调整大小
          // },
          data: [
            {
              type: 'max',
              name: getI18nName('maxValue'),
            },
            {
              type: 'min',
              name: getI18nName('minValue'),
            },
          ],
        },
        markLine: {
          // precision: precision,
          label: {
            formatter(params) {
              // 自定义精度，保留两位小数
              if (params.data.type === 'average') {
                let tempData = pdcharts.ChartConfigUtil.getValueByConfig({
                  dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
                  chartType: pdcharts.chartType.temperature,
                  value: params.data.value,
                  unit: checkObj.unit,
                });
                return tempData.value;
              }
              return params.data.value;
            },
          },
          data: [
            {
              type: 'average',
              name: getI18nName('average'),
            },
          ],
        },
      };
    }
  });
</script>
