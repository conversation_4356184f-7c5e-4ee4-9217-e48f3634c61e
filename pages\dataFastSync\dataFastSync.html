<style>
  .quick-data-sync-divide {
    height: 1px;
    width: 100%;
    border-bottom: 1px solid #dddddd;
  }
  .quick-data-sync-param {
    margin-top: 1rem;
  }

  .pagination-info {
    margin-left: 1rem;
  }

  #quick-data-sync-dateRange {
    padding: 5px;
  }

  #fastSyncTable .dropdown-menu label {
    display: inline-block;
    white-space: nowrap;
  }
</style>
<!-- left column -->
<div class="col-sm-12 full-height" style="display: flex">
  <!-- general form elements -->
  <div class="box no-border no-margin" style="width: 28%">
    <div class="box-body" style="height: 100%">
      <h3 data-i18n="MANU_FAST_SYNC_DATA">快速同步数据</h3>
      <div class="quick-data-sync-divide"></div>

      <div class="quick-data-sync-param">
        <label for="quick-data-sync-dateRange" data-i18n="dateRange"
          >日期范围</label
        >
        <div class="input-group date">
          <div class="input-group-addon">
            <i class="fa fa-calendar"></i>
          </div>
          <input
            id="quick-data-sync-dateRange"
            type="text"
            class="form-control pull-right"
            readonly="true"
          />
        </div>
        <!-- /.da-dateRange -->
      </div>
      <!-- /.fu-aduTree -->
      <div class="quick-data-sync-param" style="height: 75%">
        <label for="fu-aduItems" style="margin-top: 1rem" data-i18n="sensorList"
          >传感器列表</label
        >
        <div
          id="fu-aduItems"
          class="ztree"
          style="height: 95%; overflow: auto"
        ></div>
      </div>
      <!-- /.fu-aduTree -->
      <br />
      <div id="da-progress" class="progress-group hide">
        <span class="progress-text" data-i18n="synchronize">同步</span>
        <span class="progress-number"></span>
        <div class="progress progress-lg active">
          <div
            class="progress-bar progress-bar-success progress-bar-striped"
            role="progressbar"
            aria-valuenow="20"
            aria-valuemin="0"
            aria-valuemax="100"
          ></div>
        </div>
      </div>
      <!-- /.progress-group -->
    </div>
    <div class="content-footer">
      <div style="display: flex">
        <button
          type="button"
          id="fu-cancel"
          class="btn btn-default hide"
          style="
            width: 120px;
            margin: 10px -2px 10px auto;
            display: flex;
            justify-content: center;
            align-items: center;
          "
        >
          <img
            src="dist/img/loading.gif"
            style="margin: 0 5px 0 0"
            class="img-loading hide"
            alt=""
          />
          <div data-i18n="cancel">取消</div>
        </button>
        <button
          type="button"
          id="fu-fast-data-sync"
          class="btn btn-block btn-primary"
          style="width: 120px; margin: 10px -2px 10px auto"
          style="width: 120px"
          data-i18n="synchronize"
        >
          同步
        </button>
      </div>
    </div>
    <br />
  </div>
  <div class="box no-border" style="width: 70%; margin-left: 1rem">
    <div class="box-body">
      <h3 data-i18n="SYNC_DATA_STATE_LIST">同步状态列表</h3>
      <div class="quick-data-sync-divide"></div>
      <div class="tab-content" style="padding: 0px; height: 90%">
        <div class="tab-pane full-height active" id="fastSyncTable">
          <table
            id="tableFastSyncData"
            class="table table-bordered table-hover full-height"
          ></table>
        </div>
      </div>
    </div>
  </div>
  <!-- /.box -->

  <!-- /.content-footer -->
</div>
<script src="./pages/dataFastSync/dataFastSyncSensorTable.js"></script>
<script src="./pages/dataFastSync/dataFastSyncSensorTree.js"></script>
<script src="./pages/dataFastSync/dataFastSync.js"></script>
<!--/.col (left) -->

<script>
  //@ sourceURL=hardware_update.js
  //前端类型下拉框
  initPageI18n();
  // initScanTable();
  var hasInitFastSyncData = false;

  $('#quick-data-sync-dateRange').daterangepicker({
    opens: 'right',
    autoApply: true,
    timePicker: true,
    timePicker24Hour: true,
    timePickerSeconds: true,
    startDate: moment().subtract(29, 'days').startOf('days'),
    endDate: moment().endOf('days'),
    ranges: {
      Today: [moment().startOf('days'), moment().endOf('days')],
      Yesterday: [
        moment().subtract(1, 'days').startOf('days'),
        moment().subtract(1, 'days').endOf('days'),
      ],
      LAST_7_DAYS: [
        moment().subtract(6, 'days').startOf('days'),
        moment().endOf('days'),
      ],
      LAST_30_DAYS: [
        moment().subtract(29, 'days').startOf('days'),
        moment().endOf('days'),
      ],
      THIS_MONTH: [moment().startOf('month'), moment().endOf('month')],
      LAST_MONTH: [
        moment().subtract(1, 'month').startOf('month'),
        moment().subtract(1, 'month').endOf('month'),
      ],
    },
    locale: {
      format: 'YYYY/MM/DD HH:mm:ss',
    },
  });
  // 创建实例
  var dataFastSync = undefined;
  // 使用示例：
  $(document).ready(function () {
    dataFastSync = new DataFastSyncManager();
    // 初始化页面
    dataFastSync.init();
    dataFastSync.getSyncDataADUTree();
    // // 示例：更新表格数据
    // const data = [
    //   {
    //     index:1,
    //     id: 'sensor1',
    //     deviceName: 'Device A',
    //     pointName: 'Point 1',
    //     scanState: 'Idle',
    //   },
    //   {
    //     index:2,
    //     id: 'sensor2',
    //     deviceName: 'Device B',
    //     pointName: 'Point 2',
    //     scanState: 'Scanning',
    //   },
    // ];
    // dataFastSync.refreshTable(data);

    // 示例：获取新数据后刷新表格
    // $.ajax({
    //     url: 'your-api-endpoint',
    //     success: function(newData) {
    //         dataFastSync.refreshTable(newData);
    //     }
    // });
  });
  // for (var i = 0; i < quick_data_sync_shuttleTo.length; i++) {
  //     aduList.push(quick_data_sync_shuttleTo[i].value);
  // }
  /* var formData = new FormData();
          formData.append("aduType", aduType.children('option:selected').val());
          formData.append("aduList", aduList.toString());
          formData.append("startGroupNo",$('#startGroupNo').val());
          formData.append("endGroupNo",$('#endGroupNo').val()); */
  //开始快速同步
  $('#fu-fast-data-sync').click(function () {
    var quickSynctableData = dataFastSync.sensorTable.tableData;
    if (quickSynctableData.length > 0) {
      var dateArray = $('#quick-data-sync-dateRange').val().split('-');
      var points = quickSynctableData.map((item) => {
        return {
          pointGUID: item.pointGUID,
          sensors: [
            {
              sensorID: item.id,
            },
          ],
        };
      });

      var formData = {
        startTime: dateArray[0].trim(),
        endTime: dateArray[1].trim(),
        points: JSON.stringify(points),
      };

      // dataFastSync.startFastSyncDataState(true);
      console.log('dataFastSync-html-formData', formData);
      pollPost(FASTSYNCDATA_START, formData, function () {
        dataFastSync.getSensorState();
      });
    } else {
      $('.notifications')
        .notify({
          message: {
            text: getI18nName('FastSyncDataSelectTips'),
          },
          type: 'warning',
        })
        .show();
    }
  });

  $('#fu-cancel').click(function () {
    $.fn.alertMsg('warning', getI18nName('FastSyncDataCancelTips'), [
      {
        id: 'no',
        text: getI18nName('no'),
      },
      {
        id: 'yes',
        text: getI18nName('yes'),
        callback: function () {
          pollPost(FASTSYNCDATA_CANCEL, {}, function () {
            dataFastSync.getSensorState();
          });
        },
      },
    ]);
  });
</script>
