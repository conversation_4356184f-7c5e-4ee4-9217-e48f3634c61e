/* ******************适配相关start****************** */
window.onload = function () {
    window.onresize = adjust;
};

//设定页面各模块的高度
function adjust() {
    resizeModelView();
}

/**
 * 注册子模块页面刷新resize
 */
var onWindowResizeCallBackCache = {};
function registeOnWindowResize(name, fanc) {
    onWindowResizeCallBackCache[name] = fanc;
}
function resizeModelView() {
    EXPLORER_SCREEN_RECT_PARAMS = getExplorerScreenRectParams();//重新初始化参数值
    for (var key in onWindowResizeCallBackCache) {
        var action = onWindowResizeCallBackCache[key];
        if (action != undefined && action != "") {
            action();
        }
    }
}

var EXPLORER_SCREEN_RECT_PARAMS = {
    clientWidth: document.body.clientWidth,//网页可见区域宽
    clientHeight: document.body.clientHeight,//网页可见区域高
    offsetWidth: document.body.offsetWidth,//网页可见区域宽(包括边线和滚动条的宽)
    offsetHeight: document.body.offsetHeight,//网页可见区域高(包括边线的宽)
    scrollWidth: document.body.scrollWidth,//网页正文全文宽
    scrollHeight: document.body.scrollHeight,//网页正文全文高
    scrollTopFF: document.body.scrollTop,//网页被卷去的高(ff)
    scrollTopIE: document.documentElement.scrollTop,//网页被卷去的高(ie)
    scrollLeft: document.documentElement.scrollLeft,//网页被卷去的左
    screenTop: window.screenTop,//网页正文部分上
    screenLeft: window.screenLeft,//网页正文部分左
    screenHeight: window.screen.height,//屏幕分辨率的高
    screenWidth: window.screen.width,//屏幕分辨率的宽
    availHeight: window.screen.availHeight,//屏幕可用工作区高度
    availWidth: window.screen.availWidth,//屏幕可用工作区宽度
    colorDepth: window.screen.colorDepth,//屏幕彩色设置位数
    deviceXDPI: window.devicePixelRatio,//屏幕像素尺寸设置
};

function getExplorerScreenRectParams() {
    var params = {
        clientWidth: document.body.clientWidth,//网页可见区域宽
        clientHeight: document.body.clientHeight,//网页可见区域高
        offsetWidth: document.body.offsetWidth,//网页可见区域宽(包括边线和滚动条的宽)
        offsetHeight: document.body.offsetHeight,//网页可见区域高(包括边线的宽)
        scrollWidth: document.body.scrollWidth,//网页正文全文宽
        scrollHeight: document.body.scrollHeight,//网页正文全文高
        scrollTopFF: document.body.scrollTop,//网页被卷去的高(ff)
        scrollTopIE: document.documentElement.scrollTop,//网页被卷去的高(ie)
        scrollLeft: document.documentElement.scrollLeft,//网页被卷去的左
        screenTop: window.screenTop,//网页正文部分上
        screenLeft: window.screenLeft,//网页正文部分左
        screenHeight: window.screen.height,//屏幕分辨率的高
        screenWidth: window.screen.width,//屏幕分辨率的宽
        availHeight: window.screen.availHeight,//屏幕可用工作区高度
        availWidth: window.screen.availWidth,//屏幕可用工作区宽度
        colorDepth: window.screen.colorDepth,//屏幕彩色设置位数
        deviceXDPI: window.devicePixelRatio,//屏幕像素尺寸设置
    };
    return params;
}
;
/* ******************适配相关end****************** */