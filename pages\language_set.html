<!--
 * @Author: liyaoxu <EMAIL>
 * @Date: 2023-05-25 14:04:25
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-11-17 18:10:27
 * @FilePath: \pds_z058_web\pages\language_set.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <br>
        <form class="form-horizontal">
            <div class="form-group">
                <label for="languageSelect" class="col-sm-3 control-label" data-i18n="language_set">语言设置</label>
                <div class="col-sm-8">
                    <select id="languageSelect" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;">
                        <option value="en-US">English(US)</option>
                        <option value="zh-CN">中文简体</option>
                        <!-- <option value="zh-TW">繁體中文</option>
                        <option value="vi-VN">Vietnamese(Tieng Viet)</option>
                        <option value="ko-KR">한국어</option>
                        <option value="pt-PT">Português(Portugal)</option>
                        <option value="de-DE">Deutsch</option>
                        <option value="es-ES">Español</option>
                        <option value="fr-FR">français</option>
                        <option value="ru-RU">русский</option>
                        <option value="ar-SA">Arabic</option> -->
                    </select>
                </div>
            </div>
        </form>
        <div class="content-footer">
            <button type="button" id="save_language_set" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 60px 10px auto" data-i18n="save">保存
            </button>
        </div>
        <br>
    </div>

</div>


<!-- /.row -->
<script>
    //select2
    initPageI18n();
    $('.select2').select2({
        minimumResultsForSearch: Infinity
    });

    $('#languageSelect').val(language).select2();

    checkNeedKeyboard();

    //保存全局信息
    $("#save_language_set").click(function () {
        var data = {
            lan: $('#languageSelect').val()
        }
        pollGet(GET_Change_Language, data, function () {
            changeLanguage($('#languageSelect').val());
            setTimeout(function () {
                var user = getSession('user');
                setUser(user ? user : 'Guest');
            }, 500)
            // location.reload(); 
        });
    });
</script>