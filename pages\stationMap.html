<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <ul class="nav nav-tabs" style="height:9%">
            <li class="active" onclick="getStationTree()"><a href="#stationSet_tab" data-toggle="tab">站点配置</a></li>
            <li><a href="#sensorSet_tab" data-toggle="tab">传感器配置</a></li>
            <li><a href="#relation_tab" data-toggle="tab">测点配置</a></li>
        </ul>
        <div class="tab-content" style="padding:0px;height:91%">
            <div class="tab-pane full-height active" id="stationSet_tab">
                <div class="col-sm-4 full-height zTreeDemoBackground left" style="overflow:auto;border: 1px solid #f4f4f4;">
                    <div id="station_tree" class="ztree" ></div>
                </div>
                <div class="col-sm-8">
                    <form class="form-horizontal">
                        <!--<div class="form-group">-->
                        <div for="stationName" class="col-sm-12 control-label"
                             style="text-align:left;">站点
                        </div>
                        <div class="col-sm-6">
                            <input id="stationName" vk="true" type="text" class="form-control pull-right">
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info" id="add_station">添加</button>
                            <button type="button" class="btn btn-info" id="modify_station">修改</button>
                            <button type="button" class="btn btn-info" id="del_station" data-i18n="delete">删除</button>
                        </div>
                        <!--</div>-->
                        <!--<div class="form-group">-->
                        <div for="stationName" class="col-sm-8 control-label"
                             style="text-align:left;">设备名称
                        </div>
                        <div class="col-sm-6">
                            <input id="devName" vk="true" type="text" class="form-control pull-right">
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info">添加</button>
                            <button type="button" class="btn btn-info">修改</button>
                            <button type="button" class="btn btn-info">删除</button>
                        </div>
                        <!--</div>-->
                        <!--<div class="form-group">-->
                        <div for="stationName" class="col-sm-8 control-label"
                             style="text-align:left;" data-i18n="pointName">测点名称
                        </div>
                        <div class="col-sm-6">
                            <input id="testPointStation" vk="true" type="text"
                                   class="form-control pull-right">
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-info">添加</button>
                            <button type="button" class="btn btn-info">修改</button>
                            <button type="button" class="btn btn-info">删除</button>
                        </div>
                        <!--</div>-->
                    </form>
                </div>
            </div>
            <div class="tab-pane full-height" id="sensorSet_tab">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label for="lbSensorDev" class="col-sm-3 control-label">前端ID</label>
                        <div class="col-sm-6">
                            <select class="form-control" id="sensorID">
                                <option>12-34-56-70-90</option>
                                <option>12-34-56-70-91</option>
                                <option>12-34-56-70-92</option>
                                <option>12-34-56-70-93</option>
                                <option>12-34-56-70-94</option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <button type="button" class="btn btn-block btn-primary">获取前端</button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lbsensorType" class="col-sm-3 control-label">传感器类型</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sensorType">
                                <option>UHF</option>
                                <option>HFCT</option>
                                <option>AE</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lbSensorConfig" class="col-sm-3 control-label">传感器配置</label>
                        <div class="col-sm-9">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lbSamp" class="col-sm-3 control-label">采样周期</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sampPer">
                                <option>50</option>
                                <option>60</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="sam-point-set">
                        <label for="lbSamp" class="col-sm-3 control-label">采样点数</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sampPer">
                                <option>60</option>
                                <option>72</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="zy-set">
                        <label for="lbSamp" class="col-sm-3 control-label">增益</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sampPer">
                                <option>0</option>
                                <option>20</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="zymodel-set">
                        <label for="lbSamp" class="col-sm-3 control-label">增益模式</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sampPer">
                                <option>自动</option>
                                <option>手动</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group" id="lvbo-get">
                        <label for="lbSamp" class="col-sm-3 control-label">滤波设置</label>
                        <div class="col-sm-9">
                            <select class="form-control" id="sampPer">
                                <option>高通</option>
                                <option>低通</option>
                                <option>全通</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group hide" id="pt-get">
                        <label for="lbSamp" class="col-sm-3 control-label">PT变化</label>
                        <div class="col-sm-9">
                                <span class="form-control" id="ptText">
                                </span>
                        </div>
                    </div>
                </form>
                <div class="content-footer col-sm-12">
                    <button type="button" id="save_sensor_config" class="btn btn-block btn-primary"
                            style="width: 200px; margin: 0px 30px 0px auto" data-i18n="save">保存
                    </button>
                </div>
                <!--<div class="col-sm-12" style="margin-top:10px">-->
                <!--<div class="col-sm-3">-->
                <!--<label style="line-height: 34px;">前端ID</label>-->
                <!--</div>-->
                <!--<div class="col-sm-6">-->
                <!--<select class="form-control" id="sensorID">-->
                <!--<option>12-34-56-70-90</option>-->
                <!--<option>12-34-56-70-91</option>-->
                <!--<option>12-34-56-70-92</option>-->
                <!--<option>12-34-56-70-93</option>-->
                <!--<option>12-34-56-70-94</option>-->
                <!--</select>-->
                <!--</div>-->
                <!--<div class="col-sm-3">-->
                <!--<button type="button" class="btn btn-block btn-primary">获取传感器</button>-->
                <!--</div>-->
                <!--</div>-->
                <!--<div class="col-sm-12">-->
                <!--<div class="col-sm-3">-->
                <!--<label style="line-height: 34px;">传感器类型</label>-->
                <!--</div>-->
                <!--<div class="col-sm-6">-->
                <!--<select class="form-control" id="sensorChn">-->
                <!--<option>AE</option>-->
                <!--<option>UHF</option>-->
                <!--<option>HFCT</option>-->
                <!--</select>-->
                <!--</div>-->
                <!--</div>-->
                <!--<div class="col-sm-12">-->
                <!--<div class="col-sm-3">-->
                <!--<label style="line-height: 34px;">传感器配置</label>-->
                <!--</div>-->
                <!--</div>-->
            </div>
            <div class="tab-pane full-height" id="relation_tab">
                <div class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label" data-i18n="pointName">测点名称</label>
                        <div class="col-sm-9">
                            <input class="form-control" id="testPosintShow">
                            </input>
                        </div>
                    </div>
                </div>
                <div id="da-aduems full-height">
                    <div class="col-xs-6 no-padding full-height">
                    
                    </div>
                    
                    
                    <div class="col-xs-5 no-padding full-height">
                    </div>
                    
                    
                    <div class="col-xs-6 no-padding full-height">
                        <select name="from[]" id="da-asdus" class="form-control" style="height: 100%;"
                                multiple="multiple"> </select>
                    </div>
                    <div class="col-xs-1">
                        <br>
                        <br>
                        <br>
                        <label></label>
                        <br>
                        <br>
                        <br>
                    </div>
                    <div class="col-xs-5 no-padding full-height">
                        <select name="to[]" id="da-asdus_to" class="form-control" style="height: 100%;"
                                multiple="multiple"></select>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    //@ sourceURL=stationMap.js
    checkNeedKeyboard();
    getStation();

    var setting = {
        data: {
            simpleData: {
                enable: true
            }
        }
    };

    var zNodes =[
        { id:1, pId:0, name:"展开、折叠 自定义图标不同", open:true, iconOpen:"./plugins/zTree/css/zTreeStyle/img/diy/1_open.png", iconClose:"../../../css/zTreeStyle/img/diy/1_close.png"},
        { id:11, pId:1, name:"叶子节点1", icon:"./plugins/zTree/css/zTreeStyle/img/diy/2.png"},
        { id:12, pId:1, name:"叶子节点2", icon:"./plugins/zTree/css/zTreeStyle/img/diy/3.png"},
        { id:13, pId:1, name:"叶子节点3", icon:"./plugins/zTree/css/zTreeStyle/img/diy/5.png"},
        { id:2, pId:0, name:"展开、折叠 自定义图标相同", open:true, icon:"./plugins/zTree/css/zTreeStyle/img/diy/4.png"},
        { id:21, pId:2, name:"叶子节点1展开、折叠 自定义图标相同展开、折叠 自定义图标相同展开、折叠 自定义图标相同", icon:"./plugins/zTree/css/zTreeStyle/img/diy/6.png"},
        { id:22, pId:2, name:"叶子节点2", icon:"./plugins/zTree/css/zTreeStyle/img/diy/7.png"},
        { id:23, pId:2, name:"叶子节点3", icon:"./plugins/zTree/css/zTreeStyle/img/diy/8.png"},
        { id:3, pId:0, name:"不使用自定义图标", open:true },
        { id:31, pId:3, name:"叶子节点1"},
        { id:32, pId:3, name:"叶子节点2"},
        { id:33, pId:3, name:"叶子节点3"}

    ];

    $.fn.zTree.init($("#station_tree"), setting, zNodes);
    
//    $(function () {
//
//        onLoad();
//
//        BindEvent();
//        //页面加载
//        function onLoad() {
//            //渲染树
//            $('#station_tree').treeview({
//                data: getTree(),
//                levels: 1,
//                showBorder: false,
//                onNodeSelected: function (event, node) {
//                    if (node.level === 1) {
//                        $('#stationName').val(node.text);
//                        getDeviceNameList();
//                    }
//                    if (node.level === 2) {
//                        $('#devName').val(node.text);
//                        getTestPointList(node.id);
//                    }
//                    else if (node.level === 3)
//                        $('#testPointStation').val(node.text);
//                },
//                showCheckbox: false//是否显示多选
//            });
//        }
//
//        //事件注册
//        function BindEvent() {
//            //保存-新增
//            $("#Save").click(function () {
//                $('#addOperation-dialog').modal('hide')
//                //静态添加节点
//                var parentNode = $('#station_tree').treeview('getSelected');
//                var node = {
//                    text: $('#addName').val()
//                };
//                $('#station_tree').treeview('addNode', [node, parentNode]);
//
//            });
//        }
//
//        //保存-编辑
//        $('#Edit').click(function () {
//            var node = $('#left-tree').treeview('getSelected');
//            var newNode = {
//                text: $('#editName').val()
//            };
//            $('#left-tree').treeview('updateNode', [node, newNode]);
//        });
//
//        //显示-添加站点
//        $("#add_station").click(function () {
//            var node = $('#station_tree').treeview('getNodes');
//            if (node.length != 0) {
//                $.showMsgText('只能添加一个站点！');
//                return;
//            }
//
//            var formData = {};
//            formData.stationName = $('#stationName').val();
//            formData.stationSvg = "";
//            if (validateStrLen(formData.stationName) == 0) {
//                var text = $("#substation label[for=stationName]").text().toLowerCase();
//                $('.notifications').notify({
//                    message: {
//                        text: '请输入 ' + text + '.'
//                    },
//                    type: "warning",
//                }).show();
//            }
//
//            console.log(formData);
//            saveStationInfo(formData, "add");
//        });
//
//        //显示-修改站点
//        $("#modify_station").click(function () {
//            var node = $('#station_tree').treeview('getSelected');
//            if (node.length == 0 || node[0].level != 1) {
//                $.showMsgText('请选择站点！');
//                return;
//            }
//
//            var formData = {};
//            formData.stationName = $('#stationName').val();
//            formData.stationSvg = "";
//            if (validateStrLen(formData.stationName) == 0) {
//                var text = $("#substation label[for=stationName]").text().toLowerCase();
//                $('.notifications').notify({
//                    message: {
//                        text: '请输入 ' + text + '.'
//                    },
//                    type: "warning",
//                }).show();
//            }
//            console.log(formData);
//            saveStationInfo(formData, "modified");
//        });
//
//        //删除
//        $("#btnDel").click(function () {
//            var node = $('#station_tree').treeview('getSelected');
//            if (node.level === 1) {
//                $.showMsgText('请选择站点!');
//                return;
//            }
//            BootstrapDialog.confirm({
//                title: '提示',
//                message: '确定删除该站点及其所有数据?',
//                size: BootstrapDialog.SIZE_SMALL,
//                type: BootstrapDialog.TYPE_DEFAULT,
//                closable: true,
//                btnCancelLabel: '取消',
//                btnOKLabel: '确定',
//                callback: function (result) {
//                    if (result) {
//                        del();
//                    }
//                }
//            });
//            function del() {
//                $('#station_tree').treeview('removeNode', [node, {silent: true}]);
//            }
//
//        });
//
//        //显示-添加
//        $("#btnAdd").click(function () {
//            var node = $('#left-tree').treeview('getSelected');
//            if (node.length == 0) {
//                $.showMsgText('请选择节点');
//                return;
//            }
//            $('#stationName').val('');
//            $('#addOperation-dialog').modal('show');
//
//        });
//        //显示-编辑
//        $("#btnEdit").click(function () {
//            var node = $('#left-tree').treeview('getSelected');
//            $('#editShow').show();
//
//        });
//        //删除
//        $("#btnDel").click(function () {
//            var node = $('#left-tree').treeview('getSelected');
//            if (node.length == 0) {
//                $.showMsgText('请选择节点');
//                return;
//            }
//            BootstrapDialog.confirm({
//                title: '提示',
//                message: '确定删除此节点?',
//                size: BootstrapDialog.SIZE_SMALL,
//                type: BootstrapDialog.TYPE_DEFAULT,
//                closable: true,
//                btnCancelLabel: '取消',
//                btnOKLabel: '确定',
//                callback: function (result) {
//                    if (result) {
//                        del();
//                    }
//                }
//            });
//            function del() {
//
//                $('#left-tree').treeview('removeNode', [node, {silent: true}]);
//            }
//
//        });
//        $("#btnMove").click(function () {
//            $.showMsgText('更新中...');
//        });
//
//        //获取树数据
//        function getTree() {
//            var tree = [];
//            return tree;
//        }
//
//        /*-----页面pannel内容区高度自适应 start-----*/
//        $(window).resize(function () {
//            setCenterHeight();
//        });
//        setCenterHeight();
//        function setCenterHeight() {
//            var height = $(window).height();
//            var centerHight = height - 240;
//            $(".right_centent").height(centerHight).css("overflow", "auto");
//        }
//
//        /*-----页面pannel内容区高度自适应 end-----*/
//    });
</script>