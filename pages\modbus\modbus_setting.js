function onStandModbusChange(ele) {
    var tempValue = ele.value;
    var changeValue = ele.value === '1' ? 0 : 1;
    $("select[name='stand_modbus']").val(changeValue).select2()
    $(ele).val(tempValue).select2()
    console.log('test')
}

/*
 * 获取ModBus参数信息
 * -----------------------
 */
function getModbusConfig() {
    var data = null;
    if (MONITOR_TYPE_CODE === 'MonitorTypeCollectionNode') {
        $('#modbusConfig1').hide();
        $('#modbusConfig2').find('.modbus-title')[0].innerHTML = getI18nName('config1');
    }
    poll('GET', 'GetModbusConfig', data, function (text) {
        var respData = text.result;
        for (var item in respData) {
            if (item == 'serialPort')
                continue;
            var $item1 = $('#modbusConfig1').find("[name='" + item + "']");
            var $item2 = $('#modbusConfig2').find("[name='" + item + "']");
            $item1.empty();
            $item2.empty();
            var data = respData[item];
            if (data.length > 0) {
                data.forEach(function (value, index) {
                    var option = $("<option />", {
                        "value": "" + value + ""
                    }).append(value);
                    $item1.append(option);
                    var option2 = $("<option />", {
                        "value": "" + value + ""
                    }).append(value);
                    $item2.append(option2);
                });
            }
        }
        getModbusInfo();
    })
}

/*
 * 获取ModBus信息
 * -----------------------
 */
function getModbusInfo() {
    var data = null;
    poll('GET', 'GetModbusInfo', data, function (text) {
        var respData = text.result;

        var setInfo = function (contentId, itemData) {
            for (var item in itemData) {
                if (item == 'serialPort')
                    continue;
                if (itemData[item] === " ") {
                    itemData[item] = "";
                }
                var $item = $('#' + contentId).find("[name='" + item + "']");
                $item.val(itemData[item]).trigger("change");
            }
        };
        var info1 = respData.result ? respData.result[0] : respData[0];
        var info2 = respData.result ? respData.result[1] : respData[1];
        var contentId1 = info1.serialPort == "/dev/ttyO2" ? 'modbusConfig1' : 'modbusConfig2';
        var contentId2 = info2.serialPort == "/dev/ttyO2" ? 'modbusConfig1' : 'modbusConfig2';
        setInfo(contentId1, info1);
        setInfo(contentId2, info2);
    });
}


function saveModbusInfo(contentId) {
    var check = true;
    var formData = {};
    $("#" + contentId).find("select").each(function () {
        /* if (validateStrLen($(this).val()) == 0) {
         var label = "label[for=" + $(this).attr("id") + "]";
         var text = $("#modbus").find(label).text().toLowerCase();
         $('.notifications').notify({
         message: {
         text: getI18nName('selectPlease') + ' : ' + text + '.'
         },
         type: "warning",
         }).show();
         check = false;
         }*/
        formData[$(this).attr("name")] = $(this).val();
    });
    formData.serialPort = $('#' + contentId).find("[name='serialPort']").val();
    formData.modbusAddress = $('#' + contentId).find("[name='modbusAddress']").val();
    // checkNumValue(formData.modbusAddress, "modbusAddress", getI18nName('modbusAddressCheckTip'), 0, 254);
    if (check) {
        saveModbus(formData);
    }
}
/*
 * 保存ModBus信息
 * -----------------------
 */
function saveModbus(formData) {
    poll('POST', 'SaveModbus', formData, function (text) {
        $('.notifications').notify({
            message: {
                text: getI18nName('saveSuccess')
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('saveFailed') + convertErrorCode(errorCode)
            },
            type: "danger",
        }).show();
    });
}