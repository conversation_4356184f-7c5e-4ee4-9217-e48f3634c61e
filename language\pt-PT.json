{"loginTime": "Tempo de início de sessão", "changerUser": "Mudança de utilizador", "change": "interruptor", "login": "<PERSON><PERSON>", "systemShutDownConfirmTip": "O sistema será desligado dentro de 1 minuto. Deseja cancelar a operação?", "systemShutDownTip": "1 minuto passou, desligamento automático!", "systemShutDownCancel": "desligamento - a", "loadingData": "A tentar carregar dados, aguarde por favor", "pageSize": "{1} itens por página", "pageSelecter": "Mostrar: {1} - {2}, um total de {3} itens", "search": "pesquisa", "noMatch": "Não foram encontrados registos correspondentes", "index": "Número de série", "type": "tipo", "aduId": "codificação", "connectedState": "Estado da rede", "commType": "Método de comunicação", "loraSignalLevel": "Força do sinal LoRa", "loraSNR": "Relação sinal/ruído", "powerSupplyMode": "Método de alimentação eléctrica", "byBattery": "Bateria incorporada", "byCable": "Fonte de alimentação externa", "battaryPercent": "Nível de electricidade", "battaryLife": "Tempo de resistência (dias)", "deviceNameTable": "Nome do Dispositivo", "pointType": "sensor", "isconnected": "estado", "onlineState": "on line", "offlineState": "Perda de ligação", "aduNeverConnectedState": "Não ligado", "updateTime": "Hora da actualização", "data": "dados", "chart": "atlas", "operate": "operação", "lookUp": "check", "tbSampleDate": "Tempo de recolha", "collect": "recolha", "errorTip": "Erro: {1}", "AEamp": "Amplitude do AE", "aeChartTitle": "Espectro de amplitude AE", "humidityChartTitle": "Curva de humidade", "temperatureChartTitle": "curva de temperatura", "noiseChartTitle": "Curva de ruído", "coreGroundingCurrentChartTitle": "Curva de corrente de aterramento do núcleo", "TEVamp": "Amplitude TEV", "TEVYunit": "Unidade [dB]", "UnitFomat": "Unidade [{1}]", "unitParse": "Unidade [{1}]", "maxValue": "<PERSON>or máxi<PERSON>", "minValue": "valor mínimo", "average": "valor médio", "AT&T": "US AT&T", "China Unicom": "China Unicom", "China Mobile": "China Mobile", "Transformer": "transformador", "Breaker": "<PERSON><PERSON><PERSON><PERSON>", "Disconnector": "Interruptor de isolamento", "Isolator": "Interruptor de faca", "Arrester": "Detetor de raios", "PT": "Transformador de tensão", "CT": "Transformador de corrente", "Busbar": "generatrix", "Circuit": "União <PERSON>", "Switchgear": "Armário de comutação", "Power Cable": "cabo de alimentação", "Lightning Rod": "Vareta de relâmpago", "Wall Bushing": "Manga de parede", "Reactor": "reactor", "Electric Conductor": "Condutor de potência (barra guia)", "Power Capacitor": "Capacitor de potência", "Discharge Coil": "<PERSON><PERSON>", "Load Switch": "Interruptor de carga", "Grounding Transformer": "Transformador de aterramento", "Grounding Resistance": "Resistência ao aterramento", "Grounding Grid": "<PERSON><PERSON><PERSON>mento", "Combined Filter": "Filtro combinado", "Insulator": "isolador", "Coupling Capacitor": "Capacitor de acoplamento", "Cabinet": "Armário de <PERSON>", "Other": "outros", "Fuse": "Fusíveis", "Using Transformer": "Variável usada", "Arc Suppression Device": "Dispositivo de extinção por arco", "Main Transformer": "transformador principal", "Wave Trap": "<PERSON><PERSON><PERSON><PERSON>", "Combined Electric Appliance": "Aparelhos eléctricos combinados", "Combined Transformer": "Transformador combinado", "monitor": "Monitorização em tempo real", "dataSelect": "Consulta de Dados", "themeSkin": "pele", "themeBlue": "azul", "settings": "configuração do sistema", "datetime_set": "definição do tempo", "language_set": "Configuração do idioma", "soft_setting": "Configuração do sistema", "file_manage": "gestão de ficheiros", "instrument_set": "Configuração do Sensor", "file_point_set": "Configuração do ponto de medição", "alarm_param_set": "Configuração do Alarme", "alarm_manager": "Gestão de alarmes", "audit_view": "Área de Auditoria", "modbus_setting": "Configuração do Modbus", "hardware_update": "Actualização do Firmware", "collect_mode": "<PERSON>do de re<PERSON>", "net_set": "Configuração da Rede", "main_station_params": "Configuração da Estação Principal", "sync_data": "Sincronizar os dados", "syncingData": "A sincronizar os dados", "syncingDataCancel": "Cancelar a sincronização", "syncingDataFailed": "A sincronização falhou", "syncingDataSuccess": "Sincronização dos dados concluída", "syncingDataProgress": "Progresso da sincronização dos dados: {1}", "syncingDataProgress2": "Sincronizar o progresso dos dados [{1}]", "export_data": "Exportar Dados", "system_info": "informações do sistema", "monitoringTable": "ficha técnica", "PD": "Sensor de descarga parcial incorporado", "PD_THREE": "Sensor de descarga parcial incorporado (três em um)", "PD_FIVE": "Sensor de descarga parcial incorporado (cinco em um)", "OLD_IS": "Sensor inteligente de ultra alta frequência", "MEU": "Sensor mecânico característico", "UHF_IS": "Sensor inteligente de ultra alta frequência", "HFCT_IS": "Sensor inteligente de alta frequência", "PD_IS": "Sensor inteligente externo três em um", "TRANSFORMER_AE_IS": "Sensor ultra-sônico do transformador", "GIS_AE_IS": "Sensor ultrassónico SIG", "ENV_IS": "Sensor de inteligência ambiental", "Arrester_U_IS": "Sensor inteligente de tensão de pára-raios", "Arrester_I_IS": "Sensor inteligente de corrente de pára-raios", "LeakageCurrent_IS": "Sensor inteligente de corrente de fuga", "Vibration_IS": "Sensor inteligente de vibração", "MECH_IS": "Instrumento de monitoramento inteligente para características mecânicas", "TEMP_HUM_IS": "Sensor de temperatura e humidade", "GrounddingCurrent_IS": "Sensor inteligente para corrente de aterramento", "MECH_PD_TEMP": "Built-in descarga parcial mecânica característica temperatura três-em-um sensor", "GIS_LOWTEN": "Sensor de baixa pressão", "CHANNEL_OPTICAL_TEMP": "Sensor de temperatura de fibra óptica", "VaisalaDTP145": "Vaisala DTP145", "Wika_GDT20": "WIKA GDT20", "Wika_GDHT20": "WIKA GDHT20", "SHQiuqi_SC75D_SF6": "Shanghai Qiuqi SC75D", "SPTR_IS": "Sensor de corrente de aterramento de núcleo de ferro", "TEMPFC_OUT": "Sensor de temperatura (CLMD)", "TEMPXP_OUT": "Sensor de temperatura (SPS061)", "SF6YN_OUT": "Sensor de pressão do gás SF6 (FTP-18)", "FLOOD_IS": "Sensor inteligente de imersão em água", "TEMPKY_OUT": "Sensor de temperatura (RFC-01)", "SMOKERK_OUT": "Detector de fumo (RS-YG-N01)", "HIKVIDEO_OUT": "Sensor de Vídeo (HIK)", "TEMPSB_OUT": "Sensor de temperatura (DS18B20)", "TEMP_HUM_JDRK_OUT": "Sensor de temperatura e humidade (RS-WS-N01-2)", "SF6_OUT": "Sensor SF6&O2 (WFS-S1P-SO)", "FAN_OUT": "Controlador de ventoinhas", "LIGHT_OUT": "Controlador de iluminação", "NOISE_JDRK_OUT": "<PERSON><PERSON>u<PERSON> (RS-WS-N01)", "IR_DETECTION_OUT": "Detector duplo infravermelho", "TEMPWS_OUT": "Sensor de temperatura e humidade (WS-DMC100)", "FLOODWS_OUT": "Sensor de imersão em água (WS-DMC100)", "NOISEWS_OUT": "Sensor de ru<PERSON>do (WS-DMC100)", "VibrationSY_OUT": "Sensor de vibração do sol nascente", "TEVPRPS_IS": "Sensor inteligente de tensão de aterramento transitório", "SF6_IS": "Sensor inteligente SF6", "phase": "fase", "period": "ciclo", "AMP": "amplitude", "RMS": "Valor efectivo", "max": "Valor máximo do ciclo", "fre1Value": "Componente de frequência 1", "fre2Value": "Componente de frequência 2", "All-pass": "Omnidirecional", "Low-pass": "Passa-baixo", "High-pass": "passe alto", "No-Record": "Não registado", "TEV": "Tensão transitória do solo", "AE": "ultra- sônico", "TEMP": "temperatura", "HFCT": "Corrente de alta frequência", "UHF": "Frequência ultra alta", "Humidity": "humidade", "Decibels": "Decibel", "Noisy": "pressão sonora", "Noise": "r<PERSON><PERSON><PERSON>", "ENVGas": "atmosfera", "envgas": "Tendências atmosféricas", "MP": "propriedades mecânicas", "FLOOD": "Imersão em água", "SMOKE": "Sensação de fumo", "SF6": "Hexafluoreto de enxofre", "FAN": "Ventilador", "LIGHT": "iluminação", "NOISE": "r<PERSON><PERSON><PERSON>", "IRDETECTION": "infravermelho", "SF6YN": "Pressão do gás SF6", "ArresterU": "Tensão do pára-raios", "ArresterI": "Corrente de pára-raios", "LeakageCurrent": "Corrente de fuga", "Vibration": "vibração", "FrostPointRaw": "<PERSON><PERSON> gelada", "FrostPointATM": "Ponto de congelação (pressão atmosférica normal)", "DewPointRaw": "Ponto <PERSON>ho", "DewPointATM": "Ponto de orvalho (pressão atmosférica normal)", "Moisture": "Micro água", "AbsolutePressure": "Pressão absoluta", "NormalPressure": "Pressão padrão", "Density": "densidade", "Oxygen": "teor de oxigénio", "SPTR": "Corrente de aterramento do núcleo", "GrounddingCurrent": "Corrente de aterramento", "VIDEO": "Imagens de vídeo", "TEVPRPS": "Tensão transitória no solo PRPS", "confirm": "confirmar", "close": "fechar", "yes": "sim", "no": "não", "continue": "continuar", "none": "nada", "DISK": "armazenamento", "MEMORY": "Memória", "noTestData": "Sem dados de ensaio", "connected": "ligar", "disconnected": "<PERSON><PERSON><PERSON>", "virtualKeyBord": "Teclado virtual", "pleaseInput": "Por favor, introduza", "pleaseInputFormat": "Introduza por favor {1}", "tips": "prompt", "confirmTips": "Prompt de confirmação", "getDirFailed": "Não foi possível obter a pasta:", "backupSuccessTip": "Os dados foram copiados com sucesso. Por favor, veja- os na pasta {1}", "backupFailedTip": "A cópia de segurança dos dados falhou", "exportFailedTip": "A exportação de dados falhou: {1}", "historyDataTypeNotSuport": "A consulta de dados históricos deste tipo de sensor não é suportada no momento", "stopExportTips": "Você precisa parar de exportar dados?", "stationNameTips": "Por favor, preencha o nome do site", "powerUnitNameTips": "Por favor, preencha a unidade eléctrica.", "selectDeviceTips": "Seleccione por favor um dispositivo uma vez", "selectPointTips": "Seleccione por favor um ponto de medição", "selectDeviceOfPointTips": "Selecione o dispositivo principal que precisa adicionar pontos de medição", "saveSuccess": "Gravado com sucesso", "saveFailed": "A gravação falhou:", "operatFailed": "A operação falhou: {1}", "chosseSensorUpdate": "Selecione o sensor que precisa ser atualizado", "chooseFileUpdate": "Seleccione por favor um ficheiro de actualização", "cancelUpdateSensorConfirm": "Tem a certeza de cancelar a actualização do programa de sensores?", "enterServerUrlTips": "Indique por favor o endereço do servidor", "enterServerUrlError": "Erro de introdução do endereço do servidor, por favor volte a introduzir", "enterServerPortTips": "Indique por favor a porta do servidor", "enterServerPortError": "Erro de entrada de porta do servidor, intervalo de entrada de porta: [165535]", "NTPserverIpError": "O endereço do servidor de sincronização foi introduzido incorrectamente. Indique- o novamente", "NTPserverPortError": "Erro de entrada de porta do servidor de temporização, intervalo de entrada de porta: [165535]", "enterNetName": "Indique por favor o nome da rede", "enterIP": "Indique por favor o IP", "enterIPError": "Erro de entrada de endereço IP, como ***********", "enterNetMask": "Indique por favor a máscara de sub- rede", "enterNetError": "Erro de entrada da máscara de sub-rede, como *************", "enterGateWay": "Introduza por favor um portal", "enterGateWayError": "Erro do gateway de configuração, como ***********", "enterAPN": "Seleccione por favor o APN", "pdSampleIntervalTip": "Intervalo de amostragem PD entre 20s e 594099s (99h99m99s)", "meuSampleIntervalTip": "MEU - intervalo de amostragem entre 10s e 86400", "monitorAwakeTimeTip": "Intervalo de sono entre 1 segundo e 86400", "monitorSleepSpaceTip": "Intervalo de despertar: [{1}, {2}] min", "uploadIntervalTip": "Intervalo de upload entre 60s e 86400", "sampleIntervalTip": "Um número inteiro com um intervalo de amostragem entre 1 hora e 72 horas", "monitorSampleTimeTip": "O tempo de amostragem inicial do hospedeiro é entre 0:00 e 23:00", "monitorSampleIntervalTip": "Um número inteiro com um intervalo de amostragem do hospedeiro entre 1 hora e 168 horas", "updatingSensor": "Actualizar o sensor", "updatingSensorProgress": "Progresso da actualização do sensor: {1}", "updatingSensorProgress2": "Progresso da actualização do sensor [{1}]", "selectInstTip": "Selecione o sensor que precisa ser modificado", "selectChannelTip": "Selecione o canal que precisa ser modificado", "instCodeTip": "O código do sensor não pode estar vazio", "commLinkTypeTip": "Seleccione por favor o tipo de ligação de comunicação", "commLinkPortTip": "Seleccione por favor uma porta de comunicação", "continuSampTimeTip": "O tempo de coleta contínua está entre {1} min e {2} min.", "continuSampTimeCompareTip": "O tempo de recolha contínua deve ser inferior ao intervalo de amostragem.", "instSampleSpaceTip": "O intervalo de amostragem está entre {1} min e {2} min.", "instSampleStartTimeTip": "O tempo de amostragem inicial é entre 0:00 e 23:00.", "instNumberPattenTip": "Pode conter apenas 0-9 d<PERSON><PERSON><PERSON>, letras a-zA-Z e:", "instIPPattenTip": "Os formatos IP e porta são os seguintes: 0.0.0.0:1", "deleteInstTip": "Selecione o sensor que precisa ser excluído", "selectSensorTip": "Seleccione por favor um sensor", "deleteInstConfirmTip": "Você tem certeza de excluir o código do sensor </br>{1}</br>: {2}?", "activeInstConfigTip": "Você tem certeza de aplicar essa configuração a sensores similares de</br>{1}?", "activeBatchConfigsSuccess": "Configuração em lote dos sensores foi bem sucedida", "activeBatchConfigsBegin": "Início da configuração do lote dos sensores", "activeBatchConfigsFailed": "Falha na configuração do lote dos sensores", "collectStartOnceTip": "Você tem certeza de começar a coletar dados de sensores semelhantes?", "collectStartTip": "Você tem certeza de começar a coletar dados para o código do sensor </br>{1}</br>: {2}?", "wakeUpInstrumentOnceTip": "Você tem certeza de começar a despertar sensores do mesmo tipo</br>{1}?", "wakeUpInstrumentTip": "Você tem certeza de começar a despertar o sensor </br>{1}</br>: {2}?", "emptySensorDataOnceTip": "Você tem certeza de limpar todos os dados de sensores semelhantes em</br>{1}?", "emptySensorDataTip": "Você tem certeza de limpar todos os dados para </br>{1}</br>código do sensor: {2}?", "emptySensorDataSuccess": "Os dados do sensor foram limpos com êxito", "emptySensorDataFailed": "A limpeza dos dados do sensor falhou:", "ae_chart": "Monitorização em tempo real - Gráfico AE", "uhf_chart": "Monitorização em tempo real - espectro UHF", "hfct_chart": "Monitorização em tempo real - Gráfico HFCT", "tev_chart": "Monitorização em tempo real - amplitude TEV", "tev_prps_chart": "Monitorização em tempo real - gráfico transitório de tensão de terra PRPS", "temperature_chart": "Monitorização em tempo real - temperatura", "humidity_chart": "Monitorização em tempo real - humidade", "mechanical_chart": "Monitorização em tempo real - características mecânicas", "arrester_chart": "Monitorização em tempo real - pára-raios", "leakage_current_chart": "Monitorização em tempo real - corrente de fuga", "grounddingcurrent_chart": "Monitorização em tempo real - corrente de aterramento", "vibration_pickup_chart": "Monitorização em tempo real - vibração", "density_micro_water_history": "Monitorização em tempo real - densidade de micro água", "core_grounding_current": "Monitorização em tempo real - corrente de aterramento do núcleo", "noise_chart": "Monitorização em tempo real - ruído", "water_immersion": "Monitorização em tempo real - imersão em água", "smoke_sensor": "Monitorização em tempo real - detecção de fumaça", "video_sensor": "Monitorização em tempo real - vídeo", "sf6_sensor": "Monitorização em tempo real - SF6", "error": "erro", "warning": "aviso", "success": "sucesso", "userErrorTip": "Nome de utilizador ou palavra-passe incorrectos", "errorNoDeviceId": "O ID do dispositivo não existe", "errorNoSensorId": "O ID do sensor não existe", "errorExistSensorId": "O ID do sensor já existe", "errorNoChannelId": "O ID do Canal não existe", "errorNoPointId": "O ponto de medição não existe", "errorDataExportErrDir": "Exportação de dados, erro na localização do ficheiro.", "errorDataExportErrTime": "Erro na hora da exportação dos dados", "errorDataExportNoData": "Exportação de dados, não há dados disponíveis.", "errorDataExportNoSpace": "Exportação de dados, a unidade USB está cheia", "errorDataExportNoDisk": "Exportação de dados sem pen drive USB.", "errorDataExportNoInfo": "Exportação de dados sem informação do local.", "errorDataExportOther": "Exportação de dados, outros erros.", "errorSetModeBus": "Erro de configuração do Modbus", "errorSameNamePoint": "Já existe um ponto de medição com o mesmo nome", "errorIP": "Erro IP", "errorGPRSSet": "Erro de configuração do GPRS", "errorSenorUpdateErrFile": "Erro do ficheiro de actualização do sensor", "errorSenorUpdateNotMatch": "Atualização do sensor, tipo de sensor não corresponde ao arquivo de atualização", "errorSenorUpdating": "Actualização do sensor, actualização em curso", "errorSenorUpdateSizeCheckFailed": "A verificação do tamanho do firmware falhou", "errorSenorUpdateVersionCheckFailed": "Falha na verificação do número da versão do firmware", "errorSenorUpdateDeviceTypeCheckFailed": "Falha na verificação do tipo de dispositivo de firmware", "errorSenorUpdateCRCCheckFailed": "Falha na verificação do CRC do firmware", "errorSenorUpdateFailed": "A actualização do sensor falhou", "errorSenorUpdateConnectErr": "Comunicação anormal de actualização de firmware", "errorDataCleanFailed": "A limpeza dos dados falhou!", "errorCodeWorkGroupNotExist": "O grupo de trabalho não existe!", "errorCodeInvalidChannel": "Canal ilegal!", "errorCodeInvalidWirignMode": "Método ilegal de fiação!", "errorCodeInvalidAlarmMode": "Método de alarme ilegal!", "errorNoPermission": "Este utilizador não tem esta permissão", "illegalUser": "Usuário ilegal!", "legalPattenMsg": "Só pode conter caracteres (excluindo espaços): caracteres chineses, alfabetos, dígitos, números romanos I - XII `- () [] #_", "groundCurrent": "Executar o gráfico da corrente de aterramento", "groundCurrentA": "Corrente de aterramento de fase A", "groundCurrentB": "Corrente de aterramento da fase B", "groundCurrentC": "Corrente de aterramento em fase C", "leakageCurrent": "Gráfico de execução actual completo", "leakageCurrentA": "Corrente total em fase A", "leakageCurrentB": "Corrente total da fase B", "leakageCurrentC": "Corrente total da fase C", "ResistiveCurrent": "Corrente resistiva Gráfico de execução", "ResistiveCurrentA": "Corrente resistiva de fase A", "ResistiveCurrentB": "Corrente resistiva de fase B", "ResistiveCurrentC": "Corrente resistiva em fase C", "resistiveCurrentA": "Corrente resistiva de fase A", "resistiveCurrentB": "Corrente resistiva de fase B", "resistiveCurrentC": "Corrente resistiva em fase C", "referenceVoltageA": "Tensão de referência de fase A", "referenceVoltageB": "Tensão de referência da fase B", "referenceVoltageC": "Tensão de referência da fase C", "grounddingCurrent": "Executar o gráfico da corrente de aterramento", "grounddingCurrentA": "Corrente de aterramento de fase A", "grounddingCurrentB": "Corrente de aterramento da fase B", "grounddingCurrentC": "Corrente de aterramento em fase C", "timeDomain": "Diagrama do domínio temporal", "frequencyDomain": "Diagrama do domínio da frequência", "characterParam": "parâmetro característico", "TimeDomainDataX": "Sinal de vibração do eixo X", "TimeDomainDataY": "Sinal de vibração do eixo Y", "TimeDomainDataZ": "Sinal de vibração do eixo Z", "FrequencyDomainDataX": "Diagrama do espectro do sinal de vibração - eixo X", "FrequencyDomainDataY": "Diagrama do espectro do sinal de vibração - eixo Y", "FrequencyDomainDataZ": "Diagrama do espectro do sinal de vibração - eixo Z", "ACCAVGX": "Aceleração média do eixo X", "ACCAVGY": "Aceleração média do eixo Y", "ACCAVGZ": "Aceleração média do eixo Z", "ACCMAXX": "Aceleração máxima do eixo X", "ACCMAXY": "Aceleração máxima do eixo Y", "ACCMAXZ": "Aceleração máxima do eixo Z", "AMPAVGX": "Amplitude média do eixo X", "AMPAVGY": "Média da amplitude do eixo Y", "AMPAVGZ": "Média da amplitude do eixo Z", "AMPMAXX": "Amplitude máxima do eixo X", "AMPMAXY": "Amplitude máxima do eixo Y", "AMPMAXZ": "Amplitude máxima do eixo Z", "MAXFreqX0": "Frequência de pontos extremos do eixo X 1", "MAXFreqY0": "Frequência de pontos extremos do eixo Y 1", "MAXFreqZ0": "Frequência de pontos extremos do eixo Z 1", "MAXFreqX1": "Frequência de pontos extremos do eixo X 2", "MAXFreqY1": "Frequência de pontos extremos do eixo Y 2", "MAXFreqZ1": "Frequência de pontos extremos do eixo Z 2", "MAXFreqX2": "Frequência de pontos extremos do eixo X 3", "MAXFreqY2": "Frequência de pontos extremos do eixo Y 3", "MAXFreqZ2": "Frequência de pontos extremos do eixo Z 3", "sensorType": "Tipo de sensor", "sensorList": "Lista de Sensores", "gain": "ganho", "trigger": "Amplitude do gatilho", "wave_filter": "banda de frequência", "sample_date": "Data da amostragem", "sample_time": "Tempo de amostragem", "rms": "Valor efectivo", "cycle_max": "Valor máximo do ciclo", "frequency1": "Componente de frequência 1", "frequency2": "Componente de frequência 2", "time_interval": "intervalo de tempo", "realData": "dados em tempo real", "historyData": "dados históricos", "trendSync": "análise de tendências", "preData": "Anterior", "nextData": "Próximo", "systemDate": "data do sistema", "systemTime": "tempo do sistema", "backupType": "Tipo de cópia de segurança", "plusBackup": "Backup incremental", "allBackup": "Backup completo", "timeRange": "período de tempo", "exportPath": "caminho de exportação", "getExportDir": "Obter a pasta", "checkDir": "verificar as pastas", "exportingData": "Exportar", "cancel": "cancelar", "export": "exportação", "stationConfig": "Configuração do Site", "stationDelSuccess": "Apagou com sucesso o site", "stationDelFailed": "A remoção do site falhou:", "deviceConfig": "Configuração primária do dispositivo", "pointConfig": "Configuração do ponto de medição", "stationName": "Nome do Site", "stationPMS": "Código do local", "stationLevel": "Nível de tensão da estação", "powerUnit": "Unidade eléctrica", "save": "preservar", "operationSuccess": "Operação bem sucedida", "deviceAddSuccessTips": "Adicionou com sucesso um dispositivo de uma só vez", "deviceEditSuccessTips": "Modificou com sucesso o dispositivo uma vez", "deviceDelSuccessTips": "Apagou com sucesso o dispositivo uma vez", "pointAddSuccessTips": "Pontos de medição adicionados com sucesso", "pointEditSuccessTips": "Modificou com sucesso o ponto de medição", "pointDelSuccessTips": "Eliminado com êxito o ponto de medição", "deleteFailedTips": "A eliminação falhou", "save_add": "Gravar/ Adicionar", "delete": "apagar", "deviceType": "Tipo de equipamento", "deviceLevel": "Nível de tensão do equipamento", "add": "aumento", "channelTypeAlarm": "tipo de dados", "alarmThreshold": "<PERSON><PERSON>", "alarmRecoveryThreshold": "Limiar de recuperação de alarmes", "alarmChannel": "Selecção do canal de alarme", "built_in_channel_1": "Construído no canal 1", "External_IO_module": "Módulo IO externo", "not_associated": "Não relacionado", "alarmExternalIOSN": "Codificação do módulo IO externo", "alarmExternalIOChannel": "Canal externo do módulo IO", "wiringMode": "Método de fiação", "alarmMode": "<PERSON><PERSON><PERSON><PERSON>", "alarmTime": "Temporização do alarme", "alarmInterval": "Intervalo de alarme", "alarmDuration": "Duração do alarme", "normal_open": "Normalmente aberto", "normal_close": "Normalmente fechado", "continuous": "continuidade", "timing": "cronometragem", "interval": "intervalo", "alarmSaveSuccess": "A configuração do alarme foi gravada com sucesso", "alarmDelSuccess": "A configuração do alarme foi apagada com sucesso", "deviceName": "Nome do dispositivo principal", "pointName": "Nome do ponto de medição", "testStation": "Local de ensaio", "device": "Equipamento primário", "pointList": "Lista dos pontos de medição", "sensorID": "ID do sensor", "sensorChannel": "Canal do sensor", "channelList": "Lista de Canais", "showPoint": "Mostrar pontos de medição", "fileSelect": "Selecção de ficheiros", "selectPlease": "Seleccione por favor", "deviceList": "Lista de Sensores", "updating": "Actualização", "updatingFailed": "A actualização falhou:", "updatingCanceled": "Cancelar a actualização", "updatingComplete": "Actualização completa", "update": "actualização", "sampleDate": "Data da amostragem", "sampleTime": "Tempo de amostragem", "pdMax": "Amplitude máxima da descarga", "pdAvg": "Amplitude média da descarga", "pdNum": "Número de pulsos", "acquisitionTime": "Tempo de recolha", "humidity": "humidade", "startDate": "Data de início", "endDate": "Data final", "refresh": "Actualizar", "scanData": "consulta", "sensorCode": "Codificação dos sensores", "sensorTypeSet": "Tipo de sensor", "senorName": "Nome do Sensor", "aduAddress": "Endereço do sensor", "senorWorkMode": "Apresentação activa", "On": "abrir", "Off": "fechado", "ADUMode": "<PERSON><PERSON>", "normalMode": "modo de manutenção", "lowPowerMode": "Modo de baixo consumo de energia", "monitorMode": "Modo de monitorização", "artificialStateStartTime": "Tempo de início do estado de intervenção manual (hora)", "artificialStateStartTimeTip": "Intervalo de tempo de início do estado de intervenção manual 0-23", "artificialStateEndTime": "Tempo final do estado de intervenção manual (hora)", "artificialStateEndTimeTip": "Intervalo de tempo final do estado de intervenção manual 0-23", "artificialStateWakeUpSpace": "Intervalo de despertar do estado de intervenção artificial (minutos)", "unartificialStateWakeUpSpace": "Intervalo de despertar em estado de intervenção não manual (minutos)", "isAutoChangeMode": "Mudar automaticamente os modos", "monitorModeSampleSapce": "Intervalo de recolha do modo de monitorização", "workGroup": "Número do grupo de trabalho", "warnTemp": "Temperatura do alarme", "taskGroup": "Número do grupo de tarefas", "commLinkType": "Tipo de ligação de comunicação", "commLinkPort": "Porto de Comunicação", "frequencyUnit": "Frequência da grelha (Hz)", "numInGroup": "ID do Grupo", "commSpeed": "Taxa de comunicação", "commLoad": "Canal de comunicação", "sampleSpace": "Intervalo de amostragem (min)", "sleepTime": "Estratégia de consumo de energia", "samleStartTime": "Tempo de início da amostragem (hora)", "applyData": "operação", "applyAllSensor": "Aplicar ao mesmo tipo", "collectOnce": "<PERSON><PERSON><PERSON><PERSON>", "collectOnceEnd": "<PERSON>m da recolha de dados", "collectOnceProgress": "Recolha de dados: {1} ({2})", "collectOnceProgress2": "Recolha de dados [{1}] ({2})", "activeOnce": "Aplicação do lote", "wakeUp": "despertar", "wakeUpInstrument": "Sensor de despertar", "wakeUpInstrumentOnce": "Sensores de despertar por lotes", "orderSend": "Comand<PERSON> emitido", "cleanData": "limpar os dados", "sensorAddSuccess": "Sensor adicionado com sucesso", "sensorEditSuccess": "A modificação do sensor foi bem sucedida", "sensorEditBegin": "Início da modificação do sensor", "sensorDelSuccess": "Sensor apagado com sucesso", "cleanSensorData": "Limpar os dados do sensor", "getSensor": "Obtenção de sensores", "channelType": "Tipo de canal", "channelName": "Nome do Canal", "channelInfoSaveSuccess": "As informações do canal gravadas com sucesso", "channelInfoSaveBegin": "Início da gravação da informação do canal do sensor", "bias": "Vi<PERSON> [dB]", "waveFilter": "Configuração da faixa de frequência", "gainMode": "<PERSON><PERSON>", "gain_unit": "<PERSON><PERSON><PERSON> [dB]", "samCycle": "Número de ciclos de amostragem", "samPointNum": "Número de pontos de amostragem por ciclo", "samRate": "Número de pontos de amostragem por ciclo", "ratio": "Rácio de transformação", "channelPhase": "Separação de fases", "mecLoopCurrentThred": "<PERSON><PERSON> <PERSON> corrente da bobina [mA]", "mecMotorCurrentThred": "<PERSON><PERSON> da corrente do motor [mA]", "mecSwitchState": "Estado inicial do valor de comutação", "awaysOpen": "Normalmente aberto", "awaysClose": "Normalmente fechado", "mecBreakerType": "Configuração do mecanismo do disjuntor", "oneMech": "Ligação mecânica trifásica (um mecanismo)", "threeMech": "Ligação eléctrica trifásica (três mecanismos)", "mecMotorFunctionType": "Tipo de funcionamento do motor", "threePhaseAsynMotor": "Motor assíncrono trifásico", "onePhaseMotor": "Motor CA/CC único", "setCurrent": "Definir a Actualidade", "setAll": "Aplicado ao mesmo tipo de sensor", "showCoil": "Gráfico actual da bobina", "showSwitch": "Gráfico de valores de comutação", "showMotor": "Gráfico da corrente do motor", "showOrig": "Gráfico actual original", "actionDate": "Data da Acção", "actionTime": "Tempo de acção", "phaseA": "Fase A", "phaseB": "Fase B", "phaseC": "Fase C", "coil_charge_time": "Tempo de activação da bobina", "coil_cutout_time": "Tempo de interrupção da bobina", "max_current": "<PERSON><PERSON><PERSON> m<PERSON><PERSON> da bob<PERSON>", "hit_time": "Tempo de libertação do extrator", "subswitch_close_time": "Tempo de comutação do interruptor auxiliar", "a_close_time": "Tempo de encerramento da fase A", "a_close_coil_charge_time": "Tempo de activação da bobina de fase A", "a_close_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase A", "a_close_max_current": "Corrente máxima da bobina de fecho de fase A", "a_close_hit_time": "Tempo de libertação do travão de fecho da fase A", "a_close_subswitch_close_time": "Tempo de comutação do interruptor auxiliar de fase A", "b_close_time": "Tempo de encerramento da fase B", "b_close_coil_charge_time": "Tempo de activação da bobina de fase B", "b_close_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase B", "b_close_max_current": "Corrente máxima da bobina de fecho da fase B", "b_close_hit_time": "Tempo de disparo do interruptor de fechamento da fase B", "b_close_subswitch_close_time": "Tempo de comutação do interruptor auxiliar da fase B", "c_close_time": "Tempo de fecho da fase C", "c_close_coil_charge_time": "Tempo de activação da bobina de fase C", "c_close_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase C", "c_close_max_current": "<PERSON><PERSON>nte máxima da bobina de fecho da fase C", "c_close_hit_time": "Tempo de libertação do travão de fecho da fase C", "c_close_subswitch_close_time": "Tempo de comutação do interruptor auxiliar da fase C", "close_sync": "Sincronicidade de fecho", "close_time": "Hora de encerramento", "a_open_time": "Hora de abertura da fase A", "a_open_coil_charge_time": "Tempo de activação da bobina de fase A", "a_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase A", "a_open_max_current": "Corrente máxima da bobina de abertura de fase A", "a_open_hit_time": "Tempo de disparo do interruptor de abertura de fase A", "a_open_subswitch_close_time": "Tempo de comutação do interruptor auxiliar de fase A", "b_open_time": "Hora de abertura da fase B", "b_open_coil_charge_time": "Tempo de activação da bobina de fase B", "b_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase B", "b_open_max_current": "Corrente máxima da bobina de abertura da fase B", "b_open_hit_time": "Tempo de disparo do interruptor de abertura da fase B", "b_open_subswitch_close_time": "Tempo de comutação do interruptor auxiliar da fase B", "c_open_time": "Tempo de abertura da fase C", "c_open_coil_charge_time": "Tempo de activação da bobina de fase C", "c_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de fase C", "c_open_max_current": "Corrente máxima da bobina de abertura da fase C", "c_open_hit_time": "Tempo de disparo do interruptor de abertura da fase C", "c_open_subswitch_close_time": "Tempo de comutação do interruptor auxiliar da fase C", "open_sync": "Abrir a sincronização", "open_time": "<PERSON><PERSON><PERSON><PERSON>", "a_twice_open_time": "Fase A tempo de abertura secundário", "a_twice_open_coil_charge_time": "Tempo de vida da bobina de abertura secundária da fase A", "a_twice_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de abertura secundária da fase A", "a_twice_open_max_current": "Corrente máxima da bobina de abertura secundária de fase A", "a_twice_open_hit_time": "Tempo de disparo do interruptor dividido em fase A", "a_twice_open_subswitch_close_time": "Tempo de abertura do interruptor auxiliar de abertura secundário da fase A", "b_twice_open_time": "Tempo de abertura secundário da fase B", "b_twice_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de abertura secundária da fase B", "b_twice_open_max_current": "Corrente máxima da bobina de abertura secundária da fase B", "b_twice_open_hit_time": "Tempo de disparo do interruptor de divisão da fase B", "b_twice_open_subswitch_close_time": "Tempo de abertura do interruptor auxiliar de abertura secundário da fase B", "c_twice_open_time": "Tempo de abertura secundário da fase C", "c_twice_open_coil_cutout_time": "Tempo de interrupção de energia da bobina de abertura secundária da fase C", "c_twice_open_max_current": "Corrente máxima da bobina de abertura secundária da fase C", "c_twice_open_hit_time": "Tempo de disparo do interruptor dividido em fase C", "c_twice_open_subswitch_close_time": "Tempo de abertura do interruptor auxiliar de abertura secundário da fase C", "twice_open_sync": "Sincronidade da abertura secundária", "twice_open_time_text": "Tempo de abertura secundário", "a_switch_shot_time": "Tempo curto do ouro da fase A", "b_switch_shot_time": "Tempo curto do ouro da fase B", "c_switch_shot_time": "Tempo curto do ouro da fase C", "a_switch_no_current_time": "Fase A sem tempo actual", "b_switch_no_current_time": "Fase B sem tempo actual", "c_switch_no_current_time": "Fase C sem tempo actual", "motor_start_current": "Corrente de arranque do motor", "motor_max_current": "Corrente máxima do motor", "storage_time": "Tempo de armazenamento de energia do motor", "Chan_A_motor_start_current": "Corrente de arranque do motor de fase A", "Chan_A_motor_max_current": "Corrente máxima do motor de fase A", "Chan_A_storage_time": "Tempo de armazenamento de energia do motor de fase A", "Chan_B_motor_start_current": "Corrente inicial do motor de fase B", "Chan_B_motor_max_current": "Corrente máxima do motor de fase B", "Chan_B_storage_time": "Tempo de armazenamento de energia do motor da fase B", "Chan_C_motor_start_current": "Corrente de arranque do motor de fase C", "Chan_C_motor_max_current": "Corrente máxima do motor de fase C", "Chan_C_storage_time": "Tempo de armazenamento de energia do motor em fase C", "serialPort": "Porta serial", "baudRate": "<PERSON><PERSON>", "dataBit": "Bits de dados", "stopBit": "<PERSON>re de morder.", "checkBit": "dígito de verificação", "singleCollect": "<PERSON><PERSON><PERSON><PERSON>", "sampling": "Recolha", "serverIP": "IP do servidor", "serverPort": "Porta do Servidor", "NTPserverIp": "IP do Servidor NTP", "NTPserverPort": "Porta do servidor NTP", "netType": "Tipo de rede", "deviceIp": "IP da máquina", "subnetMask": "Máscara de sub- rede", "gateway": "Gateway predefinido", "networkAPN": "APN de acesso à rede", "userName": "nome de utilizador", "passWord": "<PERSON><PERSON>a", "deviceWorkGroup": "Número do grupo de trabalho do anfitrião", "frequency": "Frequência da grelha", "pdSampleInterval": "Intervalo de amostragem PD", "spaceSecond": "&Nbsp; segundo", "meuSampleInterval": "Intervalo de amostragem MEU", "monitorAwakeTime": "Intervalo de sono", "monitorSleepSpace": "Intervalo de despertar", "wakeStartTime": "Tempo de início do despertar (hora)", "intervalServer": "Intervalo de envio (servidor)", "intervalMinus": "Intervalo de amostragem", "continuousAcquisitionTime": "Tempo de recolha contínuo", "startSampleTime": "Tempo de início da amostragem do hospedeiro", "spacePoint": "&Nbsp; spot", "startSampleInterval": "Intervalo de amostragem inicial do anfitrião", "hour": "hora", "poerOffSpace": "Intervalo de paragem", "powerOnSpace": "Intervalo de arranque", "dateRange": "Intervalo de Data", "synchronizing": "Sincronizar", "synchronize": "sincronização", "amplitude": "amplitude", "switch": "interruptor", "copyRight": "© 2020 PMDT Ltd", "S1010Name": "Máquina de recolha de dados", "modbusCompanySelf": "Huacheng", "logOut": "sair", "logOutTitle": "Confirmação de saída", "logOutConfirm": "Sair do utilizador actual?", "logOutTips": "<PERSON><PERSON><PERSON>", "enterHost": "Indique por favor o Nome da Máquina", "selectDevTip": "Seleccione por favor um dispositivo", "stopSyncDataTip": "Você precisa parar de sincronizar dados?", "modbusAddress": "Endereço ModBus", "modbusAddressCheckTip": "Os endereços ModBus variam de 1 a 254 inteiros", "deviceWorkGroupCheckTip": "O número do grupo de trabalho host é um número inteiro, intervalo: [{1}~{2}]", "emptyMonitorDatas": "Limpar os Dados da Máquina", "emptyMonitorDatasTip": "<PERSON><PERSON> todos os dados históricos no host de coleta", "emptyMonitorDatasSuccess": "Dados limpos com sucesso", "emptyMonitorDatasApply": "O pedido de compensação de dados foi apresentado e está pendente de revisão", "resetSoftSet": "Reposição de fábrica", "resetSoftSetTip": "<PERSON>au<PERSON> to<PERSON> as configurações no host da coleção para o estado de fábrica", "resetSoftSetSuccess": "O pedido para Factory reset foi enviado para revisão", "continueConfirmTip": "Esta operação não é recuperável. Deseja continuar?", "bias_data": "O intervalo de valores de viés é -100-100", "back_ground_data": "Os valores de fundo variam de 0 a 100", "sam_point": "A faixa de frequência de amostragem para cada ciclo é 20-2000", "sam_rate": "A faixa de frequência de amostragem para cada ciclo é 20-2000", "smartSensorStatus": "Tabela de Status", "upThreshold": "Limiar superior", "lowerThreshold": "Limiar inferior", "changeThreshold": "Limite de alteração", "changeThresholdLimit": "A definição do limiar suporta apenas uma casa decimal", "upThresholdTip": "Intervalo de limiar superior", "lowerThresholdTip": "Intervalo de limiar inferior", "changeThresholdTip": "Alterar o intervalo de limiares", "upLowerThresholderrTip": "O limiar inferior não pode ser maior do que o limiar superior", "monitorName": "Nome da máquina", "monitorType": "Tipo de <PERSON>quina", "MonitorTypeHanging": "Pendurados de parede", "MonitorType2U": "2U", "MonitorTypeCollectionNode": "Nó de agregação", "MonitorTypeLowPower": "Máquina solar de baixa potência", "devicePMSCode": "Codificação do equipamento", "forceChange": "Mudança de modo", "forceChangeSuccess": "Mudança de modo bem sucedida", "currentVersion": "versão actual", "abnormalRecover": "Autorecuperação anormal", "fromLabel": "Hora de início", "toLabel": "Hora do fim", "select": "escolha", "sunday": "dia", "monday": "um", "tuesday": "dois", "wednesday": "três", "thursday": "quatro", "friday": "cinco", "saturday": "seis", "January": "Janeiro", "February": "<PERSON><PERSON>", "March": "Março", "April": "Abril", "May": "<PERSON><PERSON>", "June": "<PERSON><PERSON>", "July": "<PERSON><PERSON>", "August": "Agosto", "September": "Setembro", "October": "Out<PERSON>ro", "November": "Novembro", "December": "Dezembro", "all": "inteiro", "strong": "forte", "weak": "fraco", "poor": "diferença", "min": "ramo", "second": "segundo", "uploadInterval": "Intervalo de envio (min)", "loginWelcome": "<PERSON><PERSON>- vindo ao login", "dateStartIsAfterDateEnd": "A data de início é maior que a data de término, por favor selecione novamente", "maxCurrent": "<PERSON><PERSON><PERSON>", "standMobus": "Fabricante do sensor", "config1": "Configuração 1", "config2": "Configuração 2", "fWrnThreshold": "<PERSON>iar <PERSON> atenção", "fAlmThreshold": "<PERSON><PERSON>", "regularDormancy": "Sono cronometrado", "noDormancy": "<PERSON><PERSON> dormente", "soakingWater": "Imers<PERSON>", "dry": "dessecação", "normal": "normal", "alarm": "Alarme", "lightGunCamera": "Máquina de pistola de luz visível", "lightBallCamera": "Máquina de esfera de luz visível", "infraredGunCamera": "Máquina de pistola binocular infravermelha", "infraredBallCamera": "Máquina de bolas binoculares infravermelhos", "maxTemp": "temperatura máxima", "maxTempPosition": "Posição máxima da temperatura", "devIPError": "O PI já está ocupado", "unknown": "desconhecido", "fault": "falha", "lowPower": "Bateria fraca", "mediumPower": "Potência média", "highPower": "Bateria elevada", "slaveid": "ID do Escravo", "LoraFrequency": "<PERSON>rea de acolhimento", "AREA_CHINA": "China", "AREA_VIETNAM": "<PERSON>e (AS1)", "AREA_MALAYSIA": "Malásia (AS1)", "AREA_EUROPE": "Europa", "AREA_US": "América", "AREA_INDONESIA": "Indonésia (AS2)", "AREA_INDIA": "Índia", "AREA_KOREA": "Coreia", "AREA_CHINA_RSV": "China (backup)<2>", "getLogFileError": "Não foi possível obter o ficheiro de registo do sensor", "exportLogError": "O ficheiro de registo não existe, a exportação falhou", "alertSyncProgress": "Progresso da actualização da sincronização dos dados do alarme: {1}", "alertSyncProgress2": "Progresso da actualização da sincronização dos dados do alarme [{1}]", "FIRMWARE_EXCEPTION": "Excepção de firmware", "AD_INITIALIZATION_EXCEPTION": "Excepção de inicialização do AD", "REFERENCE_VOLTAGE_EXCEPTION": "Tensão de referência anormal", "ONCHIP_FLASH_EXCEPTION": "Excepção Flash no chip", "OFFCHIP_FLASH_EXCEPTION": "Excepção flash fora do chip", "SYSTEM_PARAMETERS_EXCEPTION": "Parâmetros anormais do sistema", "SAMPLE_PARAMETERS_EXCEPTION": "Parâmetros anormais da recolha", "CALIBRATION_PARAMETERS_EXCEPTION": "Parâmetros de calibração anormais", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "Recuperação anormal dos parâmetros do sistema", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "Recuperação anormal dos parâmetros de recolha", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "Recuperação anormal dos parâmetros de calibração", "LORA_MODULE_EXCEPTION": "Anomalia do módulo LoRa", "PHASE_NUM": "<PERSON>úmer<PERSON> da fase", "DISCONNECT_TIME": "Tempo de desligar", "DATA_TOTAL_COUNT": "Volume total de dados", "ONLINE_RATE": "Taxa online", "aduInfoSetProgress": "Configuração do sensor: {1} ({2})", "aduInfoSetProgress2": "Configuração do sensor [{1}] ({2})", "aduInfoSetting": "Configurar o sensor", "aduInfoSetEnd": "Configuração do sensor concluída", "aduDataSample": "aquisição de dados", "aduInfoSet": "Configuração do Sensor", "errorDataSampleRunning": "O serviço de coleta de sensores está em andamento. Tente novamente após completá-lo", "errorAduInfoSetRunning": "O serviço de configuração de parâmetros do sensor está atualmente em andamento. Tente novamente após completá-lo", "SAMPLE_PERIOD": "Número do ciclo de amostragem", "SF6_Density": "Densidade dos gases SF6 (P20)", "SF6_Temp": "Temperatura do gás SF6", "Device_env_Temp": "Temperatura ambiente do equipamento", "Alarm_Status": "Identificação do estado do alarme", "SF6_Alarm_Low_Pressure": "Alarme de baixa pressão", "SF6_Alarm_Low_Voltage_Block": "Bloqueio de baixa tensão", "SF6_Alarm_Over_Voltage": "Alarme de sobretensão", "AlarmType": "Tipo de Alarme", "AlarmData": "Conteúdo do <PERSON>e", "AlarmTime": "<PERSON>ra do alarme", "AlarmLevel": "Nível do alarme", "AlarmStateWarning": "alerta precoce", "WarningValue": "Valor de aviso", "AlarmValue": "Valor do alarme", "SetSuccess": "Definido com sucesso", "AlarmDate": "<PERSON>ra do alarme", "AlarmConfirm": "Confirmação do alarme", "Confirm": "confirmar", "Confirmed": "<PERSON><PERSON><PERSON><PERSON>", "AllData": "Todos os dados", "CheckManagerSponsor": "Iniciador", "CheckManagerCheckType": "Tipo de auditoria", "CheckManagerDate": "Data de origem", "CheckManagerTarget": "Iniciado por", "CheckManagerExtraInfo": "Informações adicionais", "CheckManagerResult": "<PERSON>o ou não", "Refuse": "Recusar", "Agree": "concordo", "DataExport": "Exportação de dados", "DataExportStep1": "Seleccione os dados a exportar", "DataExportStep1Title": "Exportar a Selecção de Ficheiros", "DataExportStep2": "Cálculo da dimensão dos dados", "DataExportStep2Title": "Calcular o tamanho dos dados brutos", "DataExportStep3": "Embalagem de dados comprimidos", "DataExportStep3Title": "Embalagem de dados comprimidos", "DataExportStep4": "Compressão de dados concluída", "DataExportStep4Title": "Dados comprimidos concluídos", "DataExportCheckData": "Base de dados [/media/data/base de dados]", "DataExportCheckDataFile": "Ficheiro de dados [/media/data/datafile]", "DataExportCheckLog": "Ficheiro de registo [/media/data/log]", "DataExportCheckConfig": "Ficheiro de configuração [/home/<USER>/config. xml]", "DataExportBegin": "Iniciar a Exportação", "SelectAtLeastOneTips": "Seleccione pelo menos um item", "DataExportFileSizeError": "O tamanho do arquivo excede 2000MB. Por favor, use ferramentas como Winscp para exportar os dados", "DataExportFileSizeZeroError": "O tamanho do ficheiro seleccionado é 0", "DataExportCancelTips": "Exportar dados cancelados", "DataExportDataCompressTips": "O tamanho original dos dados é {1} MB, e o tempo estimado de compressão é [{2} min {3} s]", "LogExportStep1": "Seleccionar o sensor", "LogExportStep1Title": "Seleccionar o sensor", "LogExportStep2": "Obter o Ficheiro de Registo", "LogExportStep2Title": "Obter <PERSON><PERSON><PERSON><PERSON> de Registo", "LogExportStep3": "Obter o ficheiro de registo concluído", "LogExportStep3Title": "Obter o ficheiro de registo concluído", "LogExportStep4": "Embalar e comprimir ficheiros de registo", "LogExportStep4Title": "Embalagem dos ficheiros de registo comprimidos", "LogExportStep5": "Compressão concluída", "LogExportStep5Title": "Compressão concluída", "LogExportCancelTips": "Registo de exportação cancelado", "batteryVoltage": "Tensão da bateria", "superCapvoltage": "Tensão do supercapacitor", "OverPressureThreshold": "<PERSON><PERSON>", "LowPressureThreshold": "<PERSON><PERSON>", "ShutThreshold": "<PERSON><PERSON>", "PhysicalChannelType": "Tipo de canal físico", "GasAbsPressure": "Pressão absoluta do gás", "GasGaugePressure": "Pressão do medidor de gás", "CameraType": "Tipo de <PERSON>âmara", "DEFAULT_CHECK_Tips": "O formato de numeração é m: n, como 1:100, onde m varia de [1 a 255] e n varia de [0 a 65535].", "VibrationSY_OUT_CHECK_Tips": "O formato de numeração é endereço: modbus: id, como 1:0:1917, onde o intervalo de endereços é [1-255] e modbus/id é [0-65535]", "NOISEWS_OUT_CHECK_Tips": "O formato de numeração é ip: x: y, como *********:1321:4512, onde o formato ip [xxx. xxx. xxx. xxx] e x/y são strings de comprimento 4", "IR_DETECTION_OUT_IO_CHECK_Tips": "O formato de numeração é ip: port: ss: se: cs: ce, como 1:100, onde formato ip [xxx. xxx. xxx. xxx] intervalo de porta [0-65535] intervalo ss [0-255], intervalo se 0-255], intervalo cs 0-255], intervalo ce 0-255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "O formato de numeração é ip: porta: x: y, como 1:100, onde formato ip [xxx. xxx. xxx. xxx] intervalo de porta [0-65535] x intervalo [1-255], intervalo y [0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "O formato de numeração é m: n, como 1:100, onde m varia de [1 a 255] e n varia de [0 a 65535].", "fWrnThreshold_CHECK_Tips": "Observe que a faixa de limiar é de [10-1000] mA", "fAlmThreshold_CHECK_1_Tips": "A faixa limite de alarme é de [50-5000] mA", "fAlmThreshold_CHECK_2_Tips": "O limiar de alarme deve ser maior do que o limiar de atenção", "AuditContent": "<PERSON><PERSON><PERSON><PERSON> da auditoria", "OPTime": "Tempo de operação", "Executed": "implementar", "UserManager": "gestão de utilizadores", "SystemManagement": "gestão do sistema", "BusinessManagement": "Gestão Empresarial", "LanguageChange": "Mudança de idioma", "AlarmManagement": "gestão de alarmes", "DataOperation": "Operação de dados", "BackupRecovery": "Recuperação de Cópia de Segurança", "AddUser": "<PERSON><PERSON><PERSON><PERSON>", "AddUserCheck": "Adicionar Auditoria de Utilizador", "UserRightSet": "Configuração das permissões do utilizador", "UserRightSetCheck": "Revisão da configuração de permissões do utilizador", "DeleteUser": "apagar o utilizador", "DeleteUserConfirm": "Apagar a Confirmação do Utilizador", "FreezeUser": "<PERSON><PERSON><PERSON> os Utilizadores", "FreezeUserConfirm": "Confirmação de Congelar o Utilizador", "UnlockUser": "Desbloquear o Utilizador", "UnlockUserConfirm": "Desbloquear a Confirmação do Utilizador", "FileStationSet": "Configuração do Perfil (Site)", "FileDeviceSet": "Configuração do Arquivo (Equipamento)", "SenserSet": "Configuração do sensor", "AlarmSet": "Configuração do alarme", "SavePointSet": "<PERSON><PERSON><PERSON> as configurações do ponto de medição", "DelPointSet": "A<PERSON>gar as definições do ponto de medição", "SingleSample": "Aquisição única", "DataBackup": "Cópia de segurança dos dados", "DataRecovery": "recuperação de dados", "ClearDataCheck": "Limpar a Auditoria de Dados", "RestoreFactorySettingsReview": "Auditoria de reinicialização de fábrica", "SaveMainStation": "Gravar a Estação Principal", "DataView": "Visualização de dados", "DataSync": "Sincronização de dados", "RightSet": "Configuração da permissão", "GetUserList": "Obter a lista de utilizadores", "AuditData": "Dados de auditoria", "AuditPermissions": "Permissões de auditoria", "MainStationSet": "Configuração da Estação Principal", "ChangePasswordSelfOnly": "Alterar a senha (apenas para este utilizador)", "ChangePasswordForNormal": "Modificar uma senha de utilizador normal", "ChangeUserRight": "Modificar/ Definir as Permissões do Utilizador", "ChangePassword": "<PERSON><PERSON> a senha", "ChangeLoginInfo": "Modificar as informações de início de sessão", "UserStatus": "Estado do Utilizador", "UserLanguage": "Idioma do Utilizador", "HasLogin": "Você já fez login antes", "RoleType": "Tipo de Função", "IsPassTimeout": "É o tempo limite da senha", "AuditsManagement": "Gestão da Auditoria", "SensorOperation": "Operação do sensor", "SensorLogExport": "Exportação do registo do sensor", "SensorAlarmDataSync": "Sincronização dos dados do alarme do sensor", "ViewSensorAlarmData": "Ver os dados do alarme do sensor", "Block": "congelar", "BlockPendingReview": "Congelar - <PERSON><PERSON><PERSON> pendente", "UnlockPendingReview": "Descongelar - <PERSON><PERSON><PERSON> pendente", "DeletePendingReview": "Apagar - <PERSON><PERSON><PERSON> pendente", "AddUserPendingReview": "Novo - Revisão pendente", "ChangePassPendingReview": "Alterar a Senha - Rev<PERSON>ão Pendente", "ChangeRightPendingReview": "Modificar Permissões - Revisão pendente", "abnormal": "anormal", "DataQueryNotSupported": "Atualmente, a consulta de dados históricos deste tipo front-end não é suportada", "DeviceCodeExists": "O nome do dispositivo ou o código do dispositivo já existe", "OutputSwitchConfig": "Configuração do interruptor de saída", "OutputSwitch": "Interruptor de saída", "MainStationAuxRtuID": "Controlo auxiliar RtuID", "MainStationIedRtuID": "Recolher o RtuID", "MainstationParams1": "Parâmetro 1 da estação principal", "MainstationParams2": "Parâmetro 2 da estação principal", "MainstationInterface1": "Interface da Estação Principal 1", "MainstationInterface2": "Interface da Estação Principal 2", "Port": "porto", "IPAddress": "Endereço IP", "EnterUriTips": "Indique por favor o URI da localização", "ConnectUserName": "Nome de utilizador da comunicação", "EnterConnectUserNameTips": "Indique por favor o nome de utilizador da comunicação", "ConnectPass": "Senha de comunicação", "EnterConnectPassTips": "Indique por favor a senha da comunicação", "DataSubmissionInterval": "Intervalo de envio de dados", "EnderDataSBIntervalTips": "Indique por favor o intervalo de apresentação dos dados", "HeartbeatInterval": "intervalo de batimentos cardíacos", "EnterHertbeatIntervalTips": "Indique por favor o intervalo de batimentos cardíacos", "ConnectStatus": "Estado da ligação", "Reset": "Repor", "Submit": "Enviar", "RtuidRepeatTips": "Controle auxiliar RtuID e coleta RtuID não podem ser os mesmos", "DataSubmissionIntervalTips": "O intervalo de apresentação de dados não pode ser inferior a 60", "HeartbeatIntervalTips": "O intervalo de batimento cardíaco não pode ser inferior a 60", "MainstationParamsSaveSuccess": "Parâmetros da estação principal gravados com sucesso", "MainstationParamsSaveFailed": "Não foi possível gravar os parâmetros da estação principal", "OutSwitchSaveSuccess": "Salvou com sucesso a configuração do interruptor de saída", "OutSwitchSaveFailed": "Não foi possível gravar a configuração do interruptor de saída", "ChartConfig": "Configuração do gráfico", "Measurement": "Medição", "SENSOR_TIMEOUT_COUNT": "Número de Determinações de Offline do Sensor", "OUTWARD_CONFIG_ENABLE_STATE": "Estado da Configuração", "OUTWARD_CONFIG_ENABLED": "<PERSON><PERSON>do", "OUTWARD_CONFIG_DISABLED": "Desativado", "OUTWARD_CONFIG_ENABLING": "Ativando", "SENSOR_TIMEOUT_DISABLING": "Desativando", "ERROR_NO_CONFIG": "A configuração não existe", "ERROR_INPUT_PARAMS": "Erro nos parâmetros de entrada", "ERROR_INTEGER": "Por favor, insira um número inteiro", "ERROR_TIP_RANGE": "Intervalo de valores [{1}~{2}]", "ERROR_IP_FORMAT": "O formato do IP está incorreto", "ERROR_FILE_NAME": "O nome do arquivo deve ser {1}", "ERROR_FILE_SUFFIX": "A extensão do arquivo deve ser {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "O arquivo deve ser menor que {1} MB", "ERROR_T2_T1": "T2 deve ser menor que T1", "LENGTH_COMM_LIMIT": "Por favor, note que este campo não pode exceder {1} caracteres.", "MANU_FAST_SYNC_DATA": "Sincronização Rápida", "SYNC_DATA_STATE_LIST": "Lista de Status de Sincronização de Sensores", "LAST_SYNC_DATE_RANGE": "Intervalo de Data da Última Sincronização", "NoError": "<PERSON><PERSON>", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "Tempo Limite de Sincronização", "SYNC_DATA_ERROR_DISCONNECT": "Desconectar", "SYNC_DATA_STATUS_WAITING": "<PERSON><PERSON><PERSON><PERSON>", "SYNC_DATA_STATUS_CONNECTED": "Conectado", "SYNC_DATA_STATUS_SYNCING": "Sincronizando", "SYNC_DATA_STATUS_SUCCESS": "Sincronização Bem-sucedida", "SYNC_DATA_STATUS_FAILED": "Sincronização Falhou", "SYNC_DATA_STATUS_CANCELED": "Sincronização Cancelada", "Today": "Hoje", "Yesterday": "Ontem", "LAST_7_DAYS": "Últimos 7 Dias", "LAST_30_DAYS": "Últimos 30 Dias", "THIS_MONTH": "<PERSON><PERSON>", "LAST_MONTH": "<PERSON><PERSON><PERSON>", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Falha ao Iniciar o Serviço de Comunicação", "FastSyncDataCancelTips": "Tem certeza de que deseja cancelar a sincronização de dados?", "FastSyncDataSelectTips": "Por favor, selecione os sensores para sincronização de dados", "Band-pass": "Passa-Banda", "aeWaveChartTitle": "Espectro de Forma de Onda AE", "aePhaseChartTitle": "Espectro de Fase AE", "aeFlyChartTitle": "Espectro de Voo AE", "LOCAL_PARAMS_TIME": "Tempo", "LOCAL_CHART_COLORDENSITY": "Densidade de Cor", "openTime": "Tempo de Abertura", "closeTime": "Tempo de Fechamento", "triggerUnit": "Amplitude de Disparo[μV]", "openTimeUnit": "Tempo de Abertura[μs]", "closeTimeUnit": "Tempo de Fechamento[μs]", "triggerUnitTips": "Faixa de amplitude do gatilho: [{1},{2}]μV", "TOP_DISCHARGE_AMPLITUDE": "TOP3 Amplitude de Descarga", "WS_901_ERROR_TIPS": "O serviço de envio de comandos está a registar-se, por favor tente novamente após 2 segundos"}