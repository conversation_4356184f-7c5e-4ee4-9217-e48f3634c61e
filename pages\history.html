<!-- left column -->
<div class="col-sm-12 full-height">
  <div class="nav-tabs-custom full-height">
    <br>
    <form class="form-horizontal">
      <div class="form-group">
        <label for="systemDate" class="col-sm-3 control-label">系统日期</label>
        <div class="col-sm-8">
          <div class="bootstrap-datepicker">
            <div class="input-group">
              <div class="input-group-addon">
                <i class="fa fa-calendar"></i>
              </div>
              <input id="systemDate" type="text" class="form-control pull-right" readonly="true">
            </div>
          </div>
        </div>
      </div>
      <div class="form-group">
        <label for="systemTime" class="col-sm-3 control-label">系统时间</label>
        <div class="col-sm-8">
          <div class="bootstrap-timepicker">
            <div class="input-group">
              <div class="input-group-addon">
                <i class="fa fa-clock-o"></i>
              </div>
              <input id="systemTime" type="text" class="form-control timepicker" readonly="true">
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="content-footer">
      <button type="button" id="save_soft_set" class="btn btn-block btn-primary"
        style="width: 200px; margin: 10px 60px 10px auto" data-i18n="save">保存
      </button>
    </div>
    <br>
  </div>

</div>


<!-- /.row -->
<script>
  //@ sourceURL=history.js
  //select2
  initPageI18n();
  $('.select2').select2({
    minimumResultsForSearch: Infinity
  });

  //Daterangepicker
  $('#systemDate').daterangepicker({
    "opens": "right",
    "autoApply": true,
    "timePicker": false,
    "singleDatePicker": true,
    format: 'YYYY-MM-DD'
  });
  //Timepicker
  $("#systemTime").timepicker({
    showMeridian: false,
    showInputs: false,
    showSeconds: true,
    minuteStep: 1,
    secondStep: 1
  });

  //获取网络参数信息
  getSysInfo();

  checkNeedKeyboard();

  //保存全局信息
  $("#save_soft_set").click(function () {
    var check = true;
    var formData = {};
    var systemDate = $("#systemDate").val();
    formData.sysTime = $("#systemDate").val() + " " + $("#systemTime").val();

    if (check) {
      saveSysInfo(formData);
    }
  });
</script>