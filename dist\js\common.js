/**
 * Created by bob<PERSON> on 2017/6/12.
 */
/*************多语言****************/
//根据当前环境或设置加载多语言文件
var bootstrapLan = JSON.parse(window.sessionStorage.getItem('language'));
//自动切换多语言
function replaceLang() {
    $("*[lang]").each(function () {
        var repChar = "bootstrapLan." + this.lang;
        if (eval(repChar) != undefined)
            this.innerText = eval(repChar);
    });
}
$.stringFormat = {
    format: function () {
        {
            var args = [].slice.call(arguments);
            if (this.toString() != '[object Object]') {
                args.unshift(this.toString());
            }

            var pattern = new RegExp('{([1-' + args.length + '])}', 'g');
            return String(args[0]).replace(pattern, function (match, index) {
                return args[index];
            });
        }
    }
}
var stringFormat = {
    format: function () {
        {
            var args = [].slice.call(arguments);
            if (this.toString() != '[object Object]') {
                args.unshift(this.toString());
            }

            var pattern = new RegExp('{([1-' + args.length + '])}', 'g');
            return String(args[0]).replace(pattern, function (match, index) {
                return args[index];
            });
        }
    }
}

/**
 * 绑定提示框
 */
$.extend({
    /**
     *dialog
     *des:提示框
     */
    showMsg: function (options) {
        var defaults = {
            text: getI18nName('success'),
            title: getI18nName('tips')
        }
        var opts = $.extend({}, defaults, options);
        BootstrapDialog.show({
            title: getI18nName('tips'),
            size: BootstrapDialog.SIZE_SMALL,
            type: BootstrapDialog.TYPE_DEFAULT,
            message: opts.text,
            buttons: [{
                label: getI18nName('confirm'),
                action: function (dialog) {
                    dialog.close();
                }
            }]
        });
    },
    /**
     *dialog
     *des:提示框
     */
    showMsgText: function (text) {
        $.showMsg({
            text: text
        });
    }
});

/*
 * 检测是否是数组
 * -----------------------
 */
function isArray(o) {
    return Object.prototype.toString.call(o) == '[object Array]';
}

/*
 * 检测是否是数字
 * -----------------------
 * strval:字符串
 */
function validateNumeric(strval) {
    // var c;
    // for (var i = 0; i < strval.length; i++) {
    //     c = strval.charAt(i);
    //     if (c < "0" || c > "9") {
    //         return false;
    //     }
    // }
    return /^(\+|-)?\d+($|\.\d+$)/.test(strval);
}

'use strict';


/*
 * 获取系统时间
 * -----------------------
 */
function getCurrentTime(type) {
    var reslut;
    var time = $(".time-lg>p").text();
    switch (type) {
        case "date":
            reslut = time.split(' ')[0];
            break;
        case "time":
            reslut = time.split(' ')[1];
            break;
        default:
            reslut = time;
            break;
    }
    return reslut;
}

/*
 * 加载完成显示页面
 * -----------------------
 */
function displayPage() {
    $('body').css({
        display: 'inherit'
    });
    setTimeout(function () {
        $('body').css({
            display: 'inherit'
        });
    }, 5000);
}

/*
 * 生成Modal模型
 * -----------------------
 */
function createModal(id) {
    var modal_header = $('<div/>', {
        class: "modal-header"
    })
        .append('<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button><h4 class="modal-title"></h4>');
    var modal_body = $('<div/>', {
        class: "modal-body"
    });
    var modal_footer = $('<div/>', {
        class: "modal-footer"
    });
    var modal_content = $('<div/>', {
        class: "modal-content"
    })
        .append(modal_header)
        .append(modal_body)
        .append(modal_footer);
    var modal_dialog = $('<div/>', {
        class: "modal-dialog"
    })
        .append(modal_content);
    var modal = $('<div/>', {
        id: id,
        class: "modal"
    })
        .append(modal_dialog);
    return modal;
}

function inputFile(files, types, previewCallback,maxSizeInput) {
    var message = "";
    var minSize = 1024;
    var maxSize = maxSizeInput!=undefined?maxSizeInput: 1024 * 1024 * 5;
    if (files.length == 1) {
        var file = files[0];
        var name = file.name;
        var checkType = false;
        types.forEach(function (type) {
            if (name.indexOf(type) > -1) {
                checkType = true;
            }
        })
        if (checkType) {
            var size = file.size;
            if (size < minSize) {
                // message = "文件 \"" + name + "\" (" + parseInt(size / 1024) + "KB) 不足允许的最小上传大小1 KB.";
                message = stringFormat.format(getI18nName())
            } else if (size > maxSize) {
                message = "文件 \"" + name + "\" (" + parseInt(size / 1024) + "KB) 超过允许的最大上传大小 "+(maxSize/1024/1024)+" MB.";
            } else {
                var captionname = $("<div/>", {
                    "class": 'file-caption-name',
                    "title": name
                })
                    .append('<i class="glyphicon glyphicon-file kv-caption-icon"></i>' + name);
                $('.kv-fileinput-caption').empty().append(captionname)
            }
        } else {
            message = "无效文件 \"" + name + "\". 仅支持 \"" + types.toString() + "\" 格式文件.";
        }
    } else {
        message = "上传所选文件的数目(" + files.length + ") 超过最大允许限额 1."
    }
    if (message != "") {
        $('.notifications').notify({
            message: {
                text: message
            },
            type: "warning",
        }).show();
    } else {
        if (previewCallback != undefined) {
            var reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = function (e) {
                previewCallback(this.result)
            };
        }
    }
};

function convertErrorCode(errorCode, extendInfo) {
    var errorMsg = "";
    switch (errorCode) {
        case "ADUUpdateFileError":
            errorMsg = "LDCU upgrade file error";
            break;
        case "ADUUpdateIDError":
            errorMsg = "LDCU ID Error";
            break;
        case "WiredNetWorkFalse":
            errorMsg = "Local IP address error";
            break;
        case "GPRSFalse":
            errorMsg = "SIM card configuration error";
            break;
        case "SaveModbusFalse":
            errorMsg = "Modbus configuration error";
            break;
        case "SyncDataError":
            errorMsg = "Data SYNC ID error";
            break;
        case 0:
            errorMsg = getI18nName('success');
            break;
        case 1:
            errorMsg = getI18nName('errorNoDeviceId');
            break;
        case 2:
            errorMsg = getI18nName('errorNoSensorId');
            break;
        case 3:
            errorMsg = getI18nName('errorExistSensorId');
            break;
        case 4:
            errorMsg = getI18nName('errorNoChannelId');
            break;
        case 5:
            errorMsg = getI18nName('errorNoPointId');
            break;
        case 11:
            errorMsg = getI18nName('FileSaveFailed');
            break;
        case 101:
            errorMsg = getI18nName('errorDataExportErrDir');
            break;
        case 102:
            errorMsg = getI18nName('errorDataExportErrTime');
            break;
        case 103:
            errorMsg = getI18nName('errorDataExportNoData');
            break;
        case 104:
            errorMsg = getI18nName('errorDataExportNoSpace');
            break;
        case 105:
            errorMsg = getI18nName('errorDataExportNoDisk');
            break;
        case 106:
            errorMsg = getI18nName('errorDataExportNoInfo');
            break;
        case 107:
            errorMsg = getI18nName('errorDataExportOther');
            break;
        case 201:
            errorMsg = getI18nName('errorSetModeBus');
            break;
        case 6:
            errorMsg = getI18nName('errorSameNamePoint');
            break;
        case 202:
            errorMsg = getI18nName('errorIP');
            break;
        case 203:
            errorMsg = getI18nName('errorGPRSSet');
            break;
        case 301:
            errorMsg = getI18nName('errorSenorUpdateErrFile');
            break;
        case 302:
            errorMsg = getI18nName('errorSenorUpdateNotMatch');
            break;
        case 303:
            errorMsg = getI18nName('errorSenorUpdating');
            break;
        case 304:
            errorMsg = getI18nName('errorSenorUpdateSizeCheckFailed');
            break;
        case 305:
            errorMsg = getI18nName('errorSenorUpdateVersionCheckFailed');
            break;
        case 306:
            errorMsg = getI18nName('errorSenorUpdateDeviceTypeCheckFailed');
            break;
        case 307:
            errorMsg = getI18nName('errorSenorUpdateCRCCheckFailed');
            break;
        case 308:
            errorMsg = getI18nName('errorSenorUpdateFailed');
            break;
        case 309:
            errorMsg = getI18nName('errorSenorUpdateConnectErr');
            break;
        case 400:
            errorMsg = getI18nName('errorSenorUpdateConnectErr');
            break;
        case 401:
            errorMsg = getI18nName('GetConnectFailed');
            break;
        case 402:
            errorMsg = getI18nName('WakeUpFailed');
            break;
        case 403:
            errorMsg = getI18nName('ConnectFailed');
            break;
        case 404:
            errorMsg = getI18nName('CollectDataFailed');
            break;
        case 501:
            errorMsg = getI18nName('errorDataCleanFailed');
            break;
        case 550:
            errorMsg = getI18nName('UnpackFailed');
            break;
        case 551:
            errorMsg = getI18nName('ConfigFileError');
            break;
        case 552:
            errorMsg = getI18nName('ProgramError');
            break;
        case 601:
            errorMsg = getI18nName('NoData');
            break;
        case 701:
            errorMsg = getI18nName('errorCodeWorkGroupNotExist');
            break;
        case 702:
            errorMsg = getI18nName('errorCodeInvalidChannel');
            break;
        case 703:
            errorMsg = getI18nName('errorCodeInvalidWirignMode');
            break;
        case 704:
            errorMsg = getI18nName('errorCodeInvalidAlarmMode');
            break;
        case 801:
            errorMsg = getI18nName('errorDataSampleRunning');
            break;
        case 802:
            errorMsg = getI18nName('errorAduInfoSetRunning');
            break;
        case 803:
            errorMsg = getI18nName('SensorOptRepeatTips');
            var repeatedArr = extendInfo.PushRepeatedOperations.repeatedArr.split(',');
            repeatedArr.map(function (value) {
                if (value != '') {
                    errorMsg += `\nID: ${value}`;
                }
            })
            break;
        case 900:
            errorMsg = getI18nName('errorNoPermission');
            break;
        case 901:
            errorMsg = getI18nName('illegalUser');
            break;
        case 901:
            errorMsg = getI18nName('illegalUser');
            break;
        case 1000:
            errorMsg = getI18nName('ERROR_NO_CONFIG');
            break;
        case 1001:
            errorMsg = getI18nName('saveFailed');
            break;
        // case 1000:
        //     errorMsg = getI18nName('ERROR_NO_CONFIG');
            break;
        case 2200202:
            errorMsg = getI18nName('devIPError');
            break;
        default:
            errorMsg = errorCode;
            break;
    }
    return errorMsg;
}

/*
 * 生成客户端GUID
 * -----------------------
 */
function generateGUID() {
    var guid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0;
        var v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
    return guid;
}

/*
 * session管理
 */

function setSession(name, value) {
    $.session.set(name, value);
}

function getSession(name) {
    return $.session.get(name);
}


function setCookie(name, value) {
    $.cookie(name, value);
}

function getCookie(name) {
    return $.cookie(name);
}

// function deleteCookie(){
//     $.cookie(window.location.href,'',{expires:-1});
// }

function deleteCookie() {
    var cookies = document.cookie.split(";");
    for (var i = 0; i < cookies.length; i++) {
        var cookie = cookies[i];
        var eqPos = cookie.indexOf("=");
        var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/";
    }
    if(cookies.length > 0){
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i];
            var eqPos = cookie.indexOf("=");
            var name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
            var domain = location.host.substr(location.host.indexOf('.'));
            document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/; domain=" + domain;
        }
    }
    document.cookie="domain=" + location.host.substr(location.host.indexOf('.'));
}

/*
 * 获取本地存储信息
 * -----------------------
 * name：存储的key
 */
function getStore(name) {
    var reslut;
    if (typeof (Storage) !== "undefined") {
        try {
            reslut = localStorage.getItem(name);
            if (reslut == null) {
                reslut = $.cookie(name);
            }
        } catch (err) {
            reslut = $.cookie(name);
        }
    } else {
        reslut = $.cookie(name);
    }
    return reslut
};

/*
 * 获取本地存储信息
 * -----------------------
 * name：存储的key
 * value：存储的值
 */
function setStore(name, value) {
    if (typeof (Storage) !== "undefined") {
        try {
            localStorage.setItem(name, value);
        } catch (err) {
            $.cookie(name, value);
        }
    }
};

/*
 * 获取字符串长度,判断是否为空
 * -----------------------
 * strval:字符串
 */
function validateStrLen(str) {
    var len = 0;
    if (str != null) {
        for (var i = 0; i < str.length; i++) {
            var c = str.charCodeAt(i);
            //单字节加1
            if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                len++;
            } else {
                len += 2;
            }
        }
    }
    return len;
}

/*
 *  验证IP的合法性
 * -----------------------
 * str：点分十进制的IP(如：***********)
 */
function validateIP(str) {
    if (str.search(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/) == -1)
        return false;
    var fs = 0,
        ls = 0;
    var myArray = str.split(/\./);
    var i;
    for (i = 0; i < 4; i++) {
        if (!isNumeric(myArray[i]))
            return false;

        var t = parseInt(myArray[i]);
        /* 每个域值范围0-255 */
        if ((t < 0) || (t > 255))
            return false;
    }
    fs = parseInt(myArray[0]); //取第一位进行校验
    ls = parseInt(myArray[3]); //取最后一位进行校验

    /* 主机部分不能全是1和0（第一位不能为255和0），网络部分不能全是0 */
    if ((fs == 255) || (fs == 0)) {
        return false;
    }
    return true;
}

/*
 *  验证域名的合法性
 * -----------------------
 * str：网站域名（www.baidu.com）
 */
function validateURL(str) {
    var strRegex = "^((https|http|ftp|rtsp|mms)?://)" +
        "?(([0-9a-z_!~*'().&=+$%-]+: )?[0-9a-z_!~*'().&=+$%-]+@)?" //ftp的user@
        +
        "(([0-9]{1,3}\.){3}[0-9]{1,3}" // IP形式的URL- **************
        +
        "|" // 允许IP和DOMAIN（域名）
        +
        "([0-9a-z_!~*'()-]+\.)*" // 域名- www.
        +
        "([0-9a-z][0-9a-z-]{0,61})?[0-9a-z]\." // 二级域名
        +
        "[a-z]{2,6})" // first level domain- .com or .museum
        +
        "(:[0-9]{1,4})?" // 端口- :80
        +
        "((/?)|" // a slash isn't required if there is no file name
        +
        "(/[0-9a-z_!~*'().;?:@&=+$,%#-]+)+/?)$";
    var re = new RegExp(strRegex);
    //re.test()
    if (re.test(str)) {
        return (true);
    } else {
        return (false);
    }
}


/*
 * 验证子网掩码的合法性
 * -----------------------
 * MaskStr:点分十进制的子网掩码(如：***************)
 * 调用函数： _checkIput_fomartIP(ip)
 */
function validateMask(MaskStr) {
    /* 有效性校验 */
    var IPPattern = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/
    if (!IPPattern.test(MaskStr)) return false;

    /* 检查域值 */
    var IPArray = MaskStr.split(".");
    var ip1 = parseInt(IPArray[0]);
    var ip2 = parseInt(IPArray[1]);
    var ip3 = parseInt(IPArray[2]);
    var ip4 = parseInt(IPArray[3]);
    /* 每个域值范围0-255 */
    if (ip1 < 0 || ip1 > 255 ||
        ip2 < 0 || ip2 > 255 ||
        ip3 < 0 || ip3 > 255 ||
        ip4 < 0 || ip4 > 255) {
        return false;
    }

    /* 检查二进制值是否合法 */
    //拼接二进制字符串
    var ip_binary = _checkIput_fomartIP(ip1) + _checkIput_fomartIP(ip2) + _checkIput_fomartIP(ip3) + _checkIput_fomartIP(ip4);

    if (-1 != ip_binary.indexOf("01")) {
        return false;
    }
    return true;
}

/*
 * 返回传入参数对应的8位二进制值
 * -----------------------
 * ip:点分十进制的值(0~255),int类型的值，
 */
function _checkIput_fomartIP(ip) {
    return (ip + 256).toString(2).substring(1); //格式化输出(补零)
}

/*
 * 验证字符串是否包含非法字符
 * -----------------------
 * strval:字符串
 */
function validateIllegal(str) {
    var IllegalPatrn = /[\\\/:*?"<>|]/; //非法字符
    if (!IllegalPatrn.test(str)) {
        return false;
    } else {
        return true;
    }
}

function compareDate(d1, d2) {
    //2把字符串格式转换为日期类
    var startTime = new Date(Date.parse(d1));
    var endTime = new Date(Date.parse(d2));

    //3进行比较
    return (startTime <= endTime);
}

/*
 * 时间格式化
 * -----------------------
 * fmt：格式
 */
Date.prototype.pattern = function (fmt) {
    var o = {
        "M+": this.getMonth() + 1, //月份
        "d+": this.getDate(), //日
        "h+": this.getHours() % 12 == 0 ? 12 : this.getHours() % 12, //小时
        "H+": this.getHours(), //小时
        "m+": this.getMinutes(), //分
        "s+": this.getSeconds(), //秒
        "q+": Math.floor((this.getMonth() + 3) / 3), //季度
        "S": this.getMilliseconds() //毫秒
    };
    var week = {
        "0": "/u65e5",
        "1": "/u4e00",
        "2": "/u4e8c",
        "3": "/u4e09",
        "4": "/u56db",
        "5": "/u4e94",
        "6": "/u516d"
    };

    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    }
    if (/(E+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, ((RegExp.$1.length > 1) ? (RegExp.$1.length > 2 ? "/u661f/u671f" : "/u5468") : "") + week[this.getDay() + ""]);
    }
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return fmt;
};

var legalPatten = /^(|[\u4e00-\u9fa5]|[A-Za-z0-9]|Ⅰ|Ⅱ|Ⅲ|Ⅳ|Ⅴ|Ⅵ|Ⅶ|Ⅷ|Ⅸ|Ⅹ|Ⅺ|Ⅻ|`|\.|-|\(|\)|（|）|\[|\]|#|、|_|\s)*$/;
var legalPattenMsg = getI18nName('legalPattenMsg');

function checkLegalNameValue(value, name) {
    checkLegalNameValueByPatten(value, name, legalPatten, legalPattenMsg);
}
/**
 * 强校验：不能包含任何合法字符以外的字符校验
 * @param value
 * @param name
 * @param patten
 * @param pattenMsg
 * @returns {boolean}
 */
function checkLegalNameValueByPatten(value, name, patten, pattenMsg) {
    var check = true;
    var id = '#' + name;
    if ($(id).is(":hidden")) {
        return check;
    }
    var label = "label[for=" + name + "]";
    var text = $("#global").find(label).text().toLowerCase();
    var msg = text + pattenMsg;
    if (validateStrLen(value) == 0) {
        $('.notifications').notify({
            message: {
                text: stringFormat.format(getI18nName('pleaseInputFormat'), text)
            },
            type: "warning"
        }).show();
        check = false;
        throw SyntaxError();
    } else {
        if (!patten.test(value)) {
            //因同一个input可能有不同的格式化要求，暂时取消绑定toggle提示功能，待设计方案优化  -by liyaoxu  20200518
            /*  var temp = $("#" + name);
             if (temp.attr('data-toggle') == undefined) {
                 temp.attr("title", msg);
                 temp.attr("data-toggle", "tooltip");
                 temp.attr("data-placement", "right");
                 temp.attr("data-isShow", false);
                 temp.on('hide.bs.tooltip', function () {
                     temp.attr("data-isShow", false);
                 })
             }
             if (temp.attr("data-isShow") == "false") {
                 temp.attr("data-isShow", true);
                 temp.tooltip('show');
             } */
            $.notifyUtil.notifyWarning(msg)
            // $('.notifications').notify({
            //     message: {
            //         text: msg//'Upload Interval (seconds) should be an integer between [60,86400].'
            //     },
            //     type: "warning"
            // }).show();
            check = false;
            throw SyntaxError();
        }
    }
    return check;
}

/**
 * 校验录入名称合法性(弱校验，只校验非法字符包含性)
 * @param value
 * @param name
 * @returns {boolean}
 */
var namePatten = /[\\\/:*?"<>|]/;

function checkNameValue(value, name) {
    checkIllegalNameValueByPatten(value, name, namePatten, " 中不能包含下列任何字符：\\ \/ : * ? \" < > |")
}

var numberPatten = /[^0-9a-zA-Z:：]/;
var IPPatten = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5]):([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
/**
 * 校验录入名称合法性(自定义非法字符匹配)
 * @param value
 * @param name
 * @param patten
 * @param pattenMsg
 * @returns {boolean}
 */
function checkIllegalNameValueByPatten(value, name, patten, pattenMsg) {
    var check = true;
    var id = '#' + name + '-d';
    if ($(id).is(":hidden")) {
        return check;
    }
    var label = "label[for=" + name + "]";
    var text = $("#global").find(label).text().toLowerCase();
    var msg = text + pattenMsg;
    if (validateStrLen(value) == 0) {
        $('.notifications').notify({
            message: {
                text: stringFormat.format(getI18nName('pleaseInputFormat'), text)
            },
            type: "warning"
        }).show();
        check = false;
        throw SyntaxError();
    } else {
        if (patten.test(value)) {
            var temp = $("#" + name);
            if (temp.attr('data-toggle') == undefined) {
                temp.attr("title", msg);
                temp.attr("data-toggle", "tooltip");
                temp.attr("data-placement", "bottom");
                temp.attr("data-isShow", false);
                temp.on('hide.bs.tooltip', function () {
                    temp.attr("data-isShow", false);
                })
            }
            if (temp.attr("data-isShow") == "false") {
                temp.attr("data-isShow", true);
                temp.tooltip('show');
            }
            $('.notifications').notify({
                message: {
                    text: msg //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning"
            }).show();
            check = false;
            throw SyntaxError();
        }
    }
    return check;
}
/**
 * 校验录入数字合法性
 * @param value
 * @param name
 * @param msg
 * @param min
 * @param max
 * @returns {boolean}
 */
function checkNumValue(value, name, msg, min, max) {
    var check = true;
    var id = '#' + name + '-d';
    if ($(id).is(":hidden")) {
        return check;
    }
    if (validateStrLen(value) == 0) {
        var label = "label[for=" + name + "]";
        var text = $("#global").find(label).text().toLowerCase();
        $('.notifications').notify({
            message: {
                text: stringFormat.format(getI18nName('pleaseInputFormat'), text)
            },
            type: "warning"
        }).show();
        check = false;
        throw SyntaxError();
    } else {
        if ((validateNumeric(value) == false || value < min || value > max)) {
            $('.notifications').notify({
                message: {
                    text: stringFormat.format(msg, min, max) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning"
            }).show();
            check = false;
            throw SyntaxError();
        }
    }
    return check;
}

function Base64Util() {

    // private property
    var _keyStr = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";

    // public method for encoding
    this.encode = function (input) {
        var output = "";
        var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
        var i = 0;
        input = _utf8_encode(input);
        while (i < input.length) {
            chr1 = input.charCodeAt(i++);
            chr2 = input.charCodeAt(i++);
            chr3 = input.charCodeAt(i++);
            enc1 = chr1 >> 2;
            enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
            enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
            enc4 = chr3 & 63;
            if (isNaN(chr2)) {
                enc3 = enc4 = 64;
            } else if (isNaN(chr3)) {
                enc4 = 64;
            }
            output = output +
                _keyStr.charAt(enc1) + _keyStr.charAt(enc2) +
                _keyStr.charAt(enc3) + _keyStr.charAt(enc4);
        }
        return output;
    }

    // public method for decoding
    this.decode = function (input) {
        var output = "";
        var chr1, chr2, chr3;
        var enc1, enc2, enc3, enc4;
        var i = 0;
        input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");
        while (i < input.length) {
            enc1 = _keyStr.indexOf(input.charAt(i++));
            enc2 = _keyStr.indexOf(input.charAt(i++));
            enc3 = _keyStr.indexOf(input.charAt(i++));
            enc4 = _keyStr.indexOf(input.charAt(i++));
            chr1 = (enc1 << 2) | (enc2 >> 4);
            chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
            chr3 = ((enc3 & 3) << 6) | enc4;
            output = output + String.fromCharCode(chr1);
            if (enc3 != 64) {
                output = output + String.fromCharCode(chr2);
            }
            if (enc4 != 64) {
                output = output + String.fromCharCode(chr3);
            }
        }
        output = _utf8_decode(output);
        return output;
    }

    // private method for UTF-8 encoding
    _utf8_encode = function (string) {
        string = string.replace(/\r\n/g, "\n");
        var utftext = "";
        for (var n = 0; n < string.length; n++) {
            var c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if ((c > 127) && (c < 2048)) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }

        }
        return utftext;
    }

    // private method for UTF-8 decoding
    _utf8_decode = function (utftext) {
        var string = "";
        var i = 0;
        var c, c1, c2 = 0;
        while (i < utftext.length) {
            c = utftext.charCodeAt(i);
            if (c < 128) {
                string += String.fromCharCode(c);
                i++;
            } else if ((c > 191) && (c < 224)) {
                c1 = utftext.charCodeAt(i + 1);
                string += String.fromCharCode(((c & 31) << 6) | (c1 & 63));
                i += 2;
            } else {
                c1 = utftext.charCodeAt(i + 1);
                c2 = utftext.charCodeAt(i + 2);
                string += String.fromCharCode(((c & 15) << 12) | ((c1 & 63) << 6) | (c2 & 63));
                i += 3;
            }
        }
        return string;
    }
}

$.notifyUtil = {
    notifySuccess: function (msg) {
        this.commonNotify(msg, 'success');
    },
    notifyWarning: function (msg) {
        this.commonNotify(msg, 'warning');
    },
    notifyError: function (msg) {
        this.commonNotify(msg, 'danger');
    },
    commonNotify: function (msg, type) {
        if (!type) {
            type = "success";
        }
        $('.notifications').notify({
            message: {
                text: msg
            },
            type: type,
        }).show();
    },
};