var currentPage = 1;
function initHistoryData() {
    $('#dmwHistory').bootstrapTable({
        formatLoadingMessage: function () {
            return getI18nName('loadingData');
        },
        formatRecordsPerPage: function (pageNumber) {
//            return '每页 ' + pageNumber + ' 条';
            return stringFormat.format(getI18nName('pageSize'), pageNumber);
        },
        formatShowingRows: function (pageFrom, pageTo, totalRows) {
//            return '显示：' + pageFrom + '-' + pageTo + '，共 ' + totalRows + ' 条';
            return stringFormat.format(getI18nName('pageSelecter'), pageFrom, pageTo, totalRows);
        },
        formatSearch: function () {
            return getI18nName('search');
        },
        formatNoMatches: function () {
            return getI18nName('noMatch');
        },
//        onClickRow: function (row, element) {
//            checkChart(row.pointType, row.dataId, row.pointId);
//        },
        onLoadSuccess: function (data) {
//         $('#dmwHistory').bootstrapTable('hideLoading');
//         if (getCookie('monitorIndex') != currentPage) {
//             $('#tableAll').bootstrapTable('selectPage', parseInt(getCookie('monitorIndex')));
// //                $('li.bootstrap-table-page-go').find('select').val(getCookie('monitorIndex')).trigger('change');
//         }
            $('div .pull-left.pagination-detail').css("margin-left", "25px")
            $('div .pull-right.pagination').css("margin-right", "25px")
        },
        onPageChange: function (number, size) {
            currentPage = number;
            // setSession('monitorIndex', number);
        }
        ,
        method: 'get',
        pagination: true, //分页
        singleSelect: false,
        cache: false,
        striped: true,
        sidePagination: 'server',
        showRefresh: false,
        paginationLoop: false,
        searchAlign: 'left',
        pageList: [7, 10, 25],
        url: serverUrl + GET_HISTORY_DATAS,
        search: false,
        paginationPreText: '<<',
        paginationNextText: '>>',
        pageSize: 7,
        rowStyle: function (row, index) {
            var style = {};
            style={css:{'font-size':'18px'}};
            return style;
        },
        responseHandler: function (res) {
            return res.result;
        },
        queryParams: function (params) {
//            debugger;
            return {
                page: params.offset / params.limit + 1,
                size: params.limit,
                startDate: getHistoryBeginDate(),
                endDate: getHistoryEndDate,
                channelType: selectedHistoryArchives.channelType,
                pointId: selectedHistoryArchives.testPoint
            };
        },
        columns: [
            {
                field: 'index',
                title: getI18nName('index'),
                formatter: function (value, row, index) {
                    var option = $('#dmwHistory').bootstrapTable('getOptions');
                    return index + 1 + (option.pageNumber - 1) * option.pageSize;
                }
            }, {
                field: 'pointName',
                title: getI18nName('pointName')
            }, {
                field: 'sensorType',
                title: getI18nName('pointType'),
                formatter: function (value, row, index) {
                    //value：当前field的值，即id
                    //row：当前行的数据
                    var a = getI18nName(row.sensorType);
                    return a;
                }
            }, {
                field: 'sampleDate',
                title: getI18nName('tbSampleDate')
            }, {
                field: 'value',
                title: getI18nName('data'),
                formatter: function (value, row, index) {
                    //value：当前field的值，即id
                    //row：当前行的数据
                    var a = row.value + " " + row.unit;
                    return a;
                }
            }],
        onRefresh: function () {

        }
    });
}

function refeshHistoryTable() {
    // $('#dmwHistory').bootstrapTable('refresh').filter(":contains('"+( $(this).val() )+"')");
    $('#dmwHistory').bootstrapTable('selectPage', 1);
}