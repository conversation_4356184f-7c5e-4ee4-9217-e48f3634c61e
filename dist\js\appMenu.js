/*
 * 动态加载左侧导航
 * -----------------------
 *
 */
(function ($) {
    var menu_list = [{
        name: getI18nName('monitor'),
        icon: "fa-institution",
        id: "monitor",
        display: true,
        href: "monitorTb.html",
        items: []
    },
    {
        name: getI18nName('dataSelect'),
        icon: "fa-database",
        id: "dataSelect",
        display: false,
        href: "dataQuery.html",
        items: []
    }, {
        name: getI18nName('settings'),
        icon: "fa-gears",
        id: "settings",
        display: true,
        href: "#",
        items: [{
            name: getI18nName('datetime_set'),
            icon: "fa-calendar-times-o",
            id: "datetime_set",
            display: false,
            href: "datetime_set.html"

        }
        // , {
        //     name: getI18nName('language_set'),
        //     icon: "fa-language",
        //     id: "language_set",
        //     display: false,
        //     href: "language_set.html"
        // }
        , {
            name: getI18nName('soft_setting'),
            icon: "fa-wrench",
            id: "soft_setting",
            display: false,
            href: "soft_settings.html"

        },
        {
            name: getI18nName('file_manage'),
            icon: "fa-file",
            id: "file_manage",
            display: false,
            href: "fileManage/file_manage.html",
            items: []
        },
        {
            name: getI18nName('instrument_set'),
            icon: "fa-cubes",
            id: "instrument_set",
            display: false,
            href: "instrumentSet/instrument_set.html",
            items: []
        },
        {
            name: getI18nName('file_point_set'),
            icon: "fa-sitemap",
            id: "file_point_set",
            display: false,
            href: "file_point_set.html",
            items: []
        },
        {
            name: getI18nName('alarm_param_set'),
            icon: "fa-bell-o",
            id: "alarm_param_set",
            display: false,
            href: "alarm_param_setting.html"
        },
        {
            name: getI18nName('modbus_setting'),
            icon: "fa-bars",
            id: "modbus_setting",
            display: false,
            href: "modbus/modbus_setting.html"
        },
        {
            name: getI18nName('hardware_update'),
            icon: "fa-level-up",
            id: "hardware_update",
            display: false,
            href: "hardware_update.html"
        },
        {
            name: getI18nName('audit_view'),
            icon: "fa-suitcase",
            id: "audit_view",
            display: false,
            href: "logmanager/logmanager.html"
        },
        {
            name: getI18nName('alarm_manager'),
            icon: "fa-bell",
            id: "alarm_manager",
            display: false,
            href: "alertmanager/alertmanager.html"
        },
        {
            name: getI18nName('net_set'),
            icon: "fa-life-buoy",
            id: "net_set",
            display: false,
            href: "net_set.html"
        },
        {
            // name: getI18nName('main_station_params'),
            name: getI18nName('ConnectionProtocolConfiguration'),
            icon: "fa-television",
            id: "main_station_params",
            display: false,
            // href: "mainStationParams/main_station_params.html"
            href: "mainStationParams/main_station_params_output_switch.html"
        },
        // {
        //     name: getI18nName('ConnectionProtocolConfiguration'),
        //     icon: "fa-television",
        //     id: "main_station_params",
        //     display: false,
        //     href: "mainStationConfig/mainStationConfig.html"
        // },
        {
            name: getI18nName('UserManager'),
            icon: "fa-users",
            id: "user_manager",
            display: false,
            href: "userManager/user_manager.html"
        },
        {
            name: getI18nName('AuditsManagement'),
            icon: "fa-building-o",
            id: "check_manager",
            display: true,
            href: "checkManager/checkmanager.html"
        },
        {
          name: getI18nName('ChartConfig'),
          icon: "fa-keyboard-o",
          id: "chart_config",
          display: true,
          href: "chartConfig/chartConfig.html"
        },
        // {
        //     name: '数据导出',
        //     icon: "fa-upload",
        //     id: "export_data",
        //     display: true,
        //     href: "exportData/exportData.html"
        // },
        // {
        //     name: '数据导入',
        //     icon: "fa-download",
        //     id: "import_data",
        //     display: true,
        //     href: "importData/importData.html"
        // }
        ]
    }, {
        name: getI18nName('SensorOperation'),
        icon: " fa-object-group",
        id: "sensor-option",
        display: true,
        href: "#",
        items: [
            {
                name: getI18nName('SensorLogExport'),
                icon: "fa-hospital-o",
                id: "export_log",
                display: true,
                href: "exportLog/export_log.html"
            },
            {
                name: getI18nName('SensorAlarmDataSync'),
                icon: "fa-map-pin",
                id: "sync_alert_data",
                display: true,
                href: "alertSync/alertSync.html"
            },
            {
                name: getI18nName('ViewSensorAlarmData'),
                icon: "fa-map-o",
                id: "check_alert_data",
                display: true,
                href: "alertData/alertData.html"
            },{
                name: getI18nName('GroupNumberScan'),
                icon: "fa-connectdevelop",
                id: "group_code_sacn",
                display: true,
                href: "groupCodeScan/groupCodeScan.html"
            },]
    },{
      name: getI18nName('sync_data'),
      icon: "fa-th",
      id: "sync_data_content",
      display: false,
      display: true,
      href: "#",
      items: [
          {
              name: getI18nName('sync_data'),
              icon: "fa-rotate-left",
              id: "sync_data",
              display: true,
              href: "sync_data.html"
          },
          // {
          //     name: getI18nName('MANU_FAST_SYNC_DATA'),
          //     icon: "fa-angle-double-right",
          //     id: "dataFastSync",
          //     display: true,
          //     href: "dataFastSync/dataFastSync.html"
          // },
        ]
  }, {
        name: getI18nName('sync_data'),
        icon: "fa-th",
        id: "sync_data",
        display: false,
        href: "sync_data.html",
        items: []
    }, {
        name: getI18nName('system_info'),
        icon: "fa-info-circle",
        id: "system_info",
        display: true,
        href: "system_info.html",
        items: []
    }];
    var menu_ul = $(".sidebar-menu");
    menu_ul.empty();
    menu_list.forEach(function (menu) {
        var menu_li;
        var menu_li_display = menu.display ? 'block' : 'none';
        if (menu.items.length === 0) {
            menu_li = $("<li />", {
                style: "display:" + menu_li_display
            })
                .append('<a href="#" dhref="' + menu.href + '" ><i class="fa ' + menu.icon + '"></i> <span title="' + menu.name + '" id="' + menu.id + '" lang="' + menu.id + '" >' + menu.name + '</span></a>');
        } else {
            var menu_item_ul = $("<ul />", {
                class: 'treeview-menu'
            });
            menu.items.forEach(function (item) {
                var menu_item_li_display = item.display ? 'block' : 'none';
                var menu_item_li = $("<li />", {
                    style: "display:" + menu_item_li_display
                })
                    .append('<a style="display:flex;" href="#" dhref="' + item.href + '"><i style="margin-top:3px;text-align:center;" class="fa ' + item.icon + '"></i> <span  style="display:block;width:90%;overflow: hidden;text-overflow: ellipsis;padding-left: 5px;"  title="' + item.name + '" id="' + item.id + '" lang="' + item.id + '" >' + item.name + '</span></a>');
                menu_item_ul.append(menu_item_li);
            }, this);
            menu_li = $("<li />", {
                class: "treeview",
                style: "display:" + menu_li_display
            })
                .append('<a href="#"><i class="fa ' + menu.icon + '"></i> <span  title="' + menu.name + '" id="' + menu.id + '" lang="' + menu.id + '" >' + menu.name + '</span><span class="pull-right-container"><i class="fa fa-angle-left pull-right"></i></span></a>')
                .append(menu_item_ul);
        }
        menu_ul.append(menu_li);
    }, this);

    var menu_footer = $("<li />", {
        class: "footer"
    });
    menu_footer.append('<div class="time-mini"><p></p></div><div class="time-lg"><p></p></div>');
    menu_ul.append(menu_footer);
    $('.page-title').html(getI18nName('monitor'));
    //虚拟键盘
    $.fn.keysoft = function (element) {
        var softkey_modal = createModal("softkey-modal");
        softkey_modal.find(".modal-content").css({
            width: "681px"
        });
        softkey_modal.find(".modal-title").text(getI18nName('virtualKeyBord'));
        // debugger;
        //输入框+键盘
        var inputText = $("<input />", {
            id: "content",
            type: "text",
            class: "form-control",
            placeholder: getI18nName('pleaseInput'),
            value: element.val()
        });
        softkey_modal.find(".modal-body").empty()
            .append(inputText)
            .append("<br><div id='softkey' style='height:191px'></div>");
        //语言选择+确认按钮
        var divLangType = $("<div />", {
            id: "softkey-langType",
            class: "pull-left",
            style: "text-align: left"
        });
        softkey_modal.find(".modal-footer").empty()
            .append(divLangType)
            .append('<button id="ok" type="button" class="btn btn-default" style="width: 100px">' + (getI18nName('confirm')) + '</button>');
        //文本框默认选中内容
        softkey_modal.find("#content").focus(function () {
            $(this).select();
        });
        //点击确定按钮+Enter键
        softkey_modal.find("#ok").click(function () {
            var targetId = "#" + element.attr('id');
            $(targetId).val($("#content").val());
            softkey_modal.modal('hide');
        });

        //打开modal
        softkey_modal.on('shown.bs.modal', function () {
            VirtualKeyboard.toggle('content', 'softkey');
            //重绘
            var langType = $("#kb_langselector").clone();
            $("#softkey-langType").append(langType);
            langType.select2({
                minimumResultsForSearch: Infinity
            });
            langType.change(function () {
                var selectLang = $(this).find('option:selected').val();
                VirtualKeyboard.switchLayout(selectLang);
            });
            var langVal = getStore('vk_layout');
            if (validateStrLen(langVal) == 0) {
                langVal = "US US";
            }
            langType.val(langVal).trigger('change');

            //隐藏虚拟键盘组件
            if ($('#kb_langselector') != null) {
                $('#kb_langselector').css({
                    display: "none"
                });
                $('#kb_mappingselector').css({
                    display: "none"
                });
                $('#copyrights').css({
                    display: "none"
                });
            }
        });

        //关闭modal
        softkey_modal.on('hidden.bs.modal', function () {
            VirtualKeyboard.toggle('content', 'softkey');
            $(this).remove();
        })
        softkey_modal.modal('show');
        softkey_modal.find("#kb_benter").unbind('click').click(function () {
            var targetId = "#" + element.attr('id');
            $(targetId).val($("#content").val());
            softkey_modal.modal('hide');
        });
    };

    //确认提示框
    $.fn.alertMsg = function (state, message, btns) {
        var alert_modal = createModal("alert-modal");
        alert_modal.find(".modal-title").html('<i class="icon fa fa-' + state + '"></i>' + getI18nName('tips'));
        alert_modal.find(".modal-body").html('<h4>' + message + '</h4>');
        if (btns != null && btns.length > 0) {
            btns.forEach(function (btn) {
                alert_modal.find(".modal-footer").append('<button id=' + btn.id + ' type="button" class="btn btn-default" style="width: 100px">' + btn.text + '</button>');
                alert_modal.find("#" + btn.id).click(function () {
                    if (btn.callback != undefined) {
                        btn.callback();
                    }
                    alert_modal.modal('hide');
                })
            });
        }

        //关闭modal
        alert_modal.on('hidden.bs.modal', function () {
            $(this).remove();
        });

        alert_modal.modal('show');
    }
}(jQuery));