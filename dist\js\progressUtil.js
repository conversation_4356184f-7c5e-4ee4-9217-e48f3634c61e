/* 采集进度：
开始-唤醒-连接-调速-采集数据-结束 
*/
/* 写传感器配置：
开始-唤醒-连接-调速-写系统参数-写工作模式参数-
写传感器参数-读设备信息-读系统参数-读通道参数-结束 
*/
/* 写通道参数：
开始-唤醒-连接-调速-写通道参数-读通道参数-结束 */
var ADU_PROGRESS_ENUM = {
    //通用进度（包含采集进度）
    INVALID_PROGRESS: { code: -1, name: getI18nName('ADUProgressInvalid'), percent: 100, }, //无效进度
    BEGIN: { code: 0, name: getI18nName('ADUProgressBEGIN'), percent: 0, }, //任务开始
    WAKEUP: { code: 1, name: getI18nName('ADUProgressWAKEUP'), percent: 10, }, //唤醒
    CONNECT: { code: 2, name: getI18nName('ADUProgressCONNECT'), percent: 20, },//连接
    REGULATE_SPEED: { code: 3, name: getI18nName('ADUProgressREGULATE_SPEED'), percent: 30, },  //调速  
    SAMPLE_DATA: { code: 4, name: getI18nName('ADUProgressSAMPLE_DATA'), percent: 40, }, //采集数据
    SUCCESS: { code: 5, name: getI18nName('ADUProgressSUCCESS'), percent: 99, },   //任务成功
    FAILED: { code: 6, name: getI18nName('ADUProgressFAILED'), percent: 99, },   //任务失败
    END: { code: 7, name: getI18nName('ADUProgressEND'), percent: 100, },   //任务结束

    //传感器配置额外进度（参数配置、通道配置）
    WRITE_WORKER_MODEL_PARAM: { code: 9, name: getI18nName('ADUProgressWRITE_WORKER_MODEL_PARAM'), percent:  35, },   //写工作模式参数
    WRITE_SYSTEM_PARAM: { code: 8, name: getI18nName('ADUProgressWRITE_SYSTEM_PARAM'), percent: 45, },   //写系统参数
    WRITE_ADU_PARAM: { code: 10, name: getI18nName('ADUProgressWRITE_ADU_PARAM'), percent: 55, },   //写传感器参数
    WRITE_CHANNEL_PARAM: { code: 11, name: getI18nName('ADUProgressWRITE_CHANNEL_PARAM'), percent: 65, },   //写通道参数
    READ_MONITOR_INFO: { code: 12, name: getI18nName('ADUProgressREAD_MONITOR_INFO'), percent: 75, },   //读设备信息
    READ_SYSTEM_PARAM: { code: 13, name: getI18nName('ADUProgressREAD_SYSTEM_PARAM'), percent: 85, },   //读系统参数
    READ_CHANNEL_PARAM: { code: 14, name: getI18nName('ADUProgressREAD_CHANNEL_PARAM'), percent: 95, },   //读通道参数
}



var MonitorOperateErrorCode = {
    UNKNOWN_ERROR: -1, //未知错误
    NO_ERROR: 400,           //无错误
    GET_LINK_FAILED: 401,    //获取通信链路失败
    WAKEUP_FAILED: 402,      //唤醒失败
    CONNECT_FAILED: 403,     //连接失败
    SAMPLE_DATA_FAILED: 404,  //采集数据失败

    WRITE_SYSTEM_PARAM_FAILED: 405,              //写系统参数失败
    WRITE_WORKER_MODEL_PARAM_FAILED: 406,        //写工作模式参数失败
    WRITE_ADU_PARAM_FAILED: 407,                //写传感器参数失败
    WRITE_CHANNEL_PARAM_FAILED: 408,            //写通道参数失败
    READ_MONITOR_INFO_FAILED: 409,              //读设备信息失败
    READ_SYSTEM_PARAM_FAILED: 410,              //读系统参数失败
    READ_CHANNEL_PARAM_FAILED: 411               //读通道参数失败
};


var convertProgressErrorCode = (errorCode) => {
    var errorMsg = "";
    switch (errorCode) {
        case MonitorOperateErrorCode.GET_LINK_FAILED:
            errorMsg = getI18nName('GetConnectFailed');
            break;
        case MonitorOperateErrorCode.WAKEUP_FAILED:
            errorMsg = getI18nName('WakeUpFailed');
            break;
        case MonitorOperateErrorCode.CONNECT_FAILED:
            errorMsg = getI18nName('ConnectFailed');
            break;
        case MonitorOperateErrorCode.SAMPLE_DATA_FAILED:
            errorMsg = getI18nName('CollectDataFailed');
            break;
        case MonitorOperateErrorCode.WRITE_SYSTEM_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressWRITE_SYSTEM_PARAM_FAILED');
            break;
        case MonitorOperateErrorCode.WRITE_WORKER_MODEL_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressWRITE_WORKER_MODEL_PARAM_FAILED');
            break;
        case MonitorOperateErrorCode.WRITE_ADU_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressWRITE_ADU_PARAM_FAILED');
            break;
        case MonitorOperateErrorCode.WRITE_CHANNEL_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressWRITE_CHANNEL_PARAM_FAILED');
            break;
        case MonitorOperateErrorCode.READ_MONITOR_INFO_FAILED:
            errorMsg = getI18nName('ADUPRogressREAD_MONITOR_INFO_FAILED');
            break;
        case MonitorOperateErrorCode.READ_SYSTEM_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressREAD_SYSTEM_PARAM_FAILED');
            break;
        case MonitorOperateErrorCode.READ_CHANNEL_PARAM_FAILED:
            errorMsg = getI18nName('ADUPRogressREAD_CHANNEL_PARAM_FAILED');
            break;
        default:
            errorMsg = errorCode;
            break;
    }
    return errorMsg;
}

/* padding: 0 39px;
position: absolute;
width: 600px;
top: 12px;
right: 20%;
display: flex;
flex-direction: initial; */

var progressUtil = {
    currentId: '',
    currentTitle: getI18nName('None'),
    currentProgress: 0,
    totalProgress: 0,
    successArr: [],
    failedArr: [],
    allArr: [],
    isProgressing: false,
    isShowDetail: false,
    initCache: function () {
        progressUtil.currentId = '';
        progressUtil.currentProgress = 0;
        progressUtil.totalProgress = 0;
        progressUtil.successArr = [];
        progressUtil.failedArr = [];
    },
    copyValue: function (content) {
        var oInput = document.createElement("input");
        oInput.style.dispay = "none";
        document.body.appendChild(oInput);
        oInput.value = content;
        oInput.select();
        document.execCommand("Copy");
        layer.open({
            title: stringFormat.format(getI18nName('ADUPRogressCopyIDTips'),content),
            type: 1,
        });
        setTimeout(function () {
            document.body.removeChild(oInput);
        }, 1000)
    },
    refreshProgressDetial: function () {
        $('#commonProgressDetial-success').html('');
        // $('#commonProgressDetial-title').html('进度信息 [' + progressUtil.currentTitle + ']')
        var titleText = stringFormat.format(getI18nName('ADUPRogressMsgIDTips'),progressUtil.currentTitle);
        if(language!='zh-CN'&&language!='zh-TW'){
          titleText = titleText.replace('[','<br/>[');
        }
        $('#commonProgressDetial-title').html(titleText)
        progressUtil.allArr.map((value, index) => {
            if (value != '') {
                var iClass = 'fa fa-clock-o';
                var color = 'lightgray';
                var msg = getI18nName('Wait');
                if (progressUtil.successArr.indexOf(value) > -1) {
                    iClass = 'fa fa-check-circle-o';
                    color = 'green';
                    msg = getI18nName('Complete');
                } else if (progressUtil.failedArr.indexOf(value) > -1) {
                    iClass = 'fa fa-times-circle-o';
                    color = '#dd4b39';
                    msg = getI18nName('Failed');
                } else if (value == progressUtil.currentId) {
                    var color = 'orange';
                    var msg = getI18nName('Executed');
                }
                $('#commonProgressDetial-success').prepend(`<div style="display:flex;align-items:center;">
                    <i class="${iClass}" style="color:${color}"></i><div style="margin-top:2px">${msg} ${value}&nbsp;</div><a href="javascript:void(0);" onclick="progressUtil.copyValue('${value}')">[${getI18nName('CopyID')}]</a></div>`)
            }
        })
        /* progressUtil.successArr.map(function (value, index) {
            if (value != '') {
                $('#commonProgressDetial-success').prepend(`<div style="display:flex;align-items:center;"><i class="fa fa-check-circle-o"></i><div style="margin-top:2px">完成 ${value}</div></div>`)
            }
        })
        progressUtil.failedArr.map(function (value, index) {
            if (value != '') {
                $('#commonProgressDetial-success').prepend(`<div style="display:flex;align-items:center;"><i class="fa fa-times-circle-o" style="color:#dd4b39"></i><div style="margin-top:2px">失败 ${value}</div></div>`)
            }
        }) */
    },
    showDetail: function (parentContent) {
        if (progressUtil.isShowDetail != false) {
            layer.close(progressUtil.isShowDetail);
            return;
        }
        layer.open({
            // title: '共有10个成功，5个失败',
            type: 4,
            // tips: [4, '#3498db'],
            tips: [1, '#fff'],
            anim: 5,
            time: 0,
            // area: ['650px', '400px'],
            area: ['340px', '360px'],
            shade: false,
            fixed: true,
            closeBtn: true,
            shadeClose: true,
            content: ['<div id="commonProgressDetial" style="color: black;padding-top: 5px;padding-bottom: 5px;position: absolute;height: 100%;width: 100%;top: 0px;left: 0;"></div>', parentContent],
            success: function (el, index) {
                progressUtil.isShowDetail = index;
                var titleText = stringFormat.format(getI18nName('ADUPRogressMsgIDTips'),progressUtil.currentTitle);
                if(language!='zh-CN'&&language!='zh-TW'){
                  titleText = titleText.replace('[','<br/>[');
                }
                var titleContent = '<div id="commonProgressDetial-title" style="min-height: 35px;padding: 5px 15px;font-size: 20px;border-bottom: 1px solid #ddd;">'
                    // + '进度信息 [' + progressUtil.currentTitle + ']' +
                    + titleText +
                    '</div>'
                $('#commonProgressDetial').append(titleContent);
                // $('#commonProgressDetial').append('<div id="commonProgressDetial-btn-g"><button>成功</button><button>失败</button></div>');
                $('#commonProgressDetial').append('<div id="commonProgressDetial-success"></div>');
                $('#commonProgressDetial').append('<div id="commonProgressDetial-error"></div>');
                progressUtil.refreshProgressDetial()
            },
            end: function () {
                progressUtil.isShowDetail = false;
            },
        });
        return;
    },
    /**
     * 
     * @param {*} params 
     */
    updateProgress: function (params) {
        var percent = params.percent;
        var progressTextMsg = params.progressTextMsg;
        var iProgressTextMsg = params.iProgressTextMsg;
        var colCtrlId = '#' + params.colCtrlId ? params.colCtrlId : '#null';
        var endMsg = params.endMsg ? params.endMsg : 'collectOnceEnd';

        progressUtil.currentId = params.currentId;
        progressUtil.currentIndex = params.currentIndex;
        progressUtil.currentProgress = params.currentProgress;
        progressUtil.currentTitle = params.currentTitle;
        progressUtil.successArr = params.successArr ? params.successArr : [];
        progressUtil.failedArr = params.failedArr ? params.failedArr : [];
        progressUtil.allArr = params.allArr;
        progressUtil.isProgressing = true;

        var progress = $('#da-progress');
        var progressBar = $('#da-progress>.progress>.progress-bar');
        var progressText = $('#da-progress>.progress-text');

        var i_progress = $('#i-progress-common');
        var i_progressBar = $('#i-prog-common');
        var i_progressText = $('#i-proglabel-common');
        var i_progressNum = $('#i-prognum-common');
        var i_progressDetailBtn = $('#i-progress-detail');

        var colCtrl = $(colCtrlId);
        if (percent >= 0 && percent < 100) {
            colCtrl.prop("disabled", true);
            progress.removeClass("hide");
            progressText.text(progressTextMsg);

            i_progress.removeClass("hide");
            i_progressText.text(iProgressTextMsg);
            i_progressNum.text(progressUtil.currentProgress.toFixed(2) + "%");
            i_progressDetailBtn.removeClass("hide");

            var num = progressUtil.currentProgress.toFixed(2);
            progressBar.css({
                "width": num + "%"
            });
            i_progressBar.css({
                "width": num + "%"
            });
            progressUtil.refreshProgressDetial();
        } else {
            progressUtil.refreshProgressDetial();
            colCtrl.prop("disabled", false);
            progress.addClass("hide");
            progressBar.css({
                "width": ""
            });
            progressText.text(progressTextMsg);

            i_progress.addClass("hide");
            i_progressBar.css({
                "width": ""
            });
            i_progressText.text("");
            i_progressNum.text("");
            //全局信息暂定一直存在-20221207
            // i_progressDetailBtn.addClass("hide");
            progressUtil.isProgressing = false;
            $('.notifications').notify({
                message: {
                    text: params.successMsg ? params.successMsg : getI18nName('collectOnceEnd'),
                },
                type: "success"
            }).show();
        }
    }
};