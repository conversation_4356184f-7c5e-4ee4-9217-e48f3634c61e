function getCodeName(code, codeCacheObj) {
    for (var item in codeCacheObj) {
        if (codeCacheObj[item].code == code) {
            return codeCacheObj[item].name;
        }
    }
}

function getCodeKey(code, codeCacheObj) {
    for (var item in codeCacheObj) {
        if (codeCacheObj[item].code == code) {
            return item;
        }
    }
}

function getEnumByCode(code, codeCacheObj) {
    for (var item in codeCacheObj) {
        if (codeCacheObj[item].code == code) {
            return codeCacheObj[item];
        }
    }
}

var CHARACTER_TYPE_ENUM = {
    CTYPE_BEGIN: 0, //普通用户
    CTYPE_NORMAL: 1, //系统管理员
    CTYPE_SYS: 2, //审核管理员
    CTYPE_CONFIRM: 3, //安全管理员
    CTYPE_SUP: 4 //超级管理员（测试用户）
}

function getAlarmChannelTypeNameByCode(code) {
    return getCodeName(code, ALARM_CHANNEL_TYPE_ENUM);
}

var ALARM_CHANNEL_TYPE_ENUM = {
    All: {
        code: -1,
        name: getI18nName('all')
    },
    TEVAmp: {
        code: 0,
        name: getI18nName('TEVamp')
    },
    AEAmp: {
        code: 4,
        name: getI18nName('AEamp')
    },
    Mech: {
        code: 13,
        name: getI18nName('MP')
    },
    Temp: {
        code: 26,
        name: getI18nName('TEMP')
    },
};

var AUDIT_DATA_TYPE_ENUM = {
    All: {
        code: -1,
        name: getI18nName('all')
    },
    USER_MANAGER: {
        code: 0,
        name: getI18nName("UserManager")
    },
    SYS_MANAGER: {
        code: 1,
        name: getI18nName('SystemManagement')
    },
    BUS_MANAGER: {
        code: 2,
        name: getI18nName('BusinessManagement')
    },
    LANGUAGE_CHANGE: {
        code: 3,
        name: getI18nName('LanguageChange')
    },
    ALARM_MANAGER: {
        code: 4,
        name: getI18nName('AlarmManagement')
    },
    DATA_MANAGER: {
        code: 5,
        name: getI18nName('DataOperation')
    },
    BACKUP_RECOVER: {
        code: 6,
        name: getI18nName('BackupRecovery')
    },
};

var AUDIT_OPREATE_TYPE_ENUM = {
    AUDIT_LOGIN_IN: {
        code: 0,
        name: getI18nName('login')
    }, //登录
    AUDIT_LOGIN_OUT: {
        code: 1,
        name: getI18nName('logOut')
    }, //退出
    AUDIT_ADD_USER: {
        code: 2,
        name: getI18nName('AddUser')
    }, //添加用户
    AUDIT_ADD_USER_CHECK: {
        code: 3,
        name: getI18nName('AddUserCheck')
    }, //添加用户审核
    AUDIT_OPERATES_USER: {
        code: 4,
        name: getI18nName('UserRightSet')
    }, //用户权限设置
    AUDIT_OPERATES_USER_CHECK: {
        code: 5,
        name: getI18nName('UserRightSetCheck')
    }, //用户权限设置审核
    AUDIT_DELETE_USER: {
        code: 6,
        name: getI18nName('DeleteUser')
    }, //删除用户
    AUDIT_DELETE_USER_CHECK: {
        code: 7,
        name: getI18nName('DeleteUserConfirm')
    }, //删除用户确认
    AUDIT_LOCK_USER: {
        code: 8,
        name: getI18nName('FreezeUser')
    }, //冻结用户
    AUDIT_LOCK_USER_CHECK: {
        code: 9,
        name: getI18nName('FreezeUserConfirm')
    }, //冻结用户确认
    AUDIT_UNLOCK_USER: {
        code: 10,
        name: getI18nName('UnlockUser')
    }, //解锁用户
    AUDIT_UNLOCK_USER_CHECK: {
        code: 11,
        name: getI18nName('UnlockUserConfirm')
    }, //解锁用户确认
    AUDIT_TIME_SET: {
        code: 12,
        name: getI18nName('datetime_set')
    }, //时间设置
    AUDIT_SYS_SET: {
        code: 13,
        name: getI18nName('soft_setting')
    }, //系统设置
    AUDIT_MODBUS_SET: {
        code: 14,
        name: getI18nName('modbus_setting')
    }, //modbus设置
    AUDIT_NET_SET: {
        code: 15,
        name: getI18nName('net_set')
    }, //网络设置
    AUDIT_FILE_STATION_SET: {
        code: 16,
        name: getI18nName('FileStationSet')
    }, //档案(站点)设置
    AUDIT_FILE_DEVICE_SET: {
        code: 17,
        name: getI18nName('FileDeviceSet')
    }, //档案(设备)设置
    AUDIT_ADU_SET: {
        code: 18,
        name: getI18nName('SenserSet')
    }, //前段设置
    AUDIT_SAVE_POINT_SET: {
        code: 19,
        name: getI18nName('SavePointSet')
    }, //保存测点设置
    AUDIT_DEL_POINT_SET: {
        code: 20,
        name: getI18nName('DelPointSet')
    }, //删除测点设置
    AUDIT_FIRM_SET: {
        code: 21,
        name: getI18nName('hardware_update')
    }, //固件更新
    AUDIT_LANGUAGE_SET: {
        code: 22,
        name: getI18nName('language_set')
    }, //语言设置
    ADUTI_ALARAM_SET: {
        code: 23,
        name: getI18nName('AlarmSet')
    }, //告警设置
    AUDIT_ALARAM_MANA: {
        code: 24,
        name: getI18nName('AlarmManagement')
    }, //告警管理
    AUDIT_SINGLE_SAMPLE: {
        code: 25,
        name: getI18nName('SingleSample')
    }, //单次采集
    AUDIT_BATCH_SAMPLE: {
        code: 26,
        name: getI18nName('collectOnce')
    }, //批量采集
    AUDIT_SYNC: {
        code: 27,
        name: getI18nName('synchronize')
    }, //同步
    AUDIT_BACK_DATA: {
        code: 28,
        name: getI18nName('DataBackup')
    }, //数据备份
    AUDIT_RECOVER_DATA: {
        code: 29,
        name: getI18nName('DataRecovery')
    }, //数据恢复
    AUDIT_CLEAR_DATA: {
        code: 30,
        name: getI18nName('cleanData')
    }, //清空数据
    AUDIT_FACTORYSET: {
        code: 31,
        name: getI18nName('resetSoftSet')
    }, //恢复出厂设置
    AUDIT_CLEAR_DATA_CHECK: {
        code: 32,
        name: getI18nName('ClearDataCheck')
    }, // 清空数据审核
    AUDIT_FACTORYSET_CHECK: {
        code: 33,
        name: getI18nName('RestoreFactorySettingsReview')
    }, // 恢复出厂设置审核
    AUDIT_MAINSTATION: {
        code: 34,
        name: getI18nName('SaveMainStation')
    }, // 保存主站
}

var RIGHT_OPREATE_TYPE_ENUM = {
    All: {
        code: -1,
        name: 'all'
    },

    DATA_COLLECT: {
        code: 0,
        name: 'aduDataSample'
    },
    DATA_VIEW: {
        code: 1,
        name: 'DataView'
    },
    DATA_SYNC: {
        code: 2,
        name: 'DataSync'
    },
    TIME_SET: {
        code: 3,
        name: 'datetime_set'
    },
    SYS_SET: {
        code: 4,
        name: 'soft_setting'
    },
    ARCHIVE_MANA: {
        code: 5,
        name: 'file_manage'
    },
    ADU_MANA: {
        code: 6,
        name: 'instrument_set'
    },
    POINT_MANA: {
        code: 7,
        name: 'file_point_set'
    },
    MODBUS_SET: {
        code: 8,
        name: 'modbus_setting'
    },
    FIRM_FRESH: {
        code: 9,
        name: 'hardware_update'
    },
    NET_SET: {
        code: 10,
        name: 'net_set'
    },

    ADD_USER: {
        code: 11,
        name: 'AddUser'
    },
    DEL_USER: {
        code: 12,
        name: 'DeleteUser'
    },
    CHANGE_PASS: {
        code: 13,
        name: 'ChangePasswordSelfOnly'
    },
    SET_AUTHORITY: {
        code: 14,
        name: 'RightSet'
    },
    LOCK: {
        code: 15,
        name: 'FreezeUser'
    },
    UNLOCK: {
        code: 16,
        name: 'UnlockUser'
    },
    USERLIST: {
        code: 17,
        name: 'GetUserList'
    },
    AUDIT_DATA: {
        code: 18,
        name: 'AuditData'
    },
    BACK_DATA: {
        code: 19,
        name: 'DataBackup'
    },
    RECOVER_DATA: {
        code: 20,
        name: 'DataRecovery'
    },
    AUDIT_CONFIRM: {
        code: 21,
        name: 'AuditPermissions'
    },
    ALARM_MANAGER: {
        code: 23,
        name: 'AlarmManagement'
    },
    ALARM_MANAGER_CONFIRM: {
        code: 24,
        name: 'AlarmConfirm'
    },
    CLEAR_DATA: {
        code: 25,
        name: 'emptyMonitorDatas'
    },
    RESTORE: {
        code: 26,
        name: 'resetSoftSet'
    },
    MAINSTATION: {
        code: 27,
        name: 'MainStationSet'
    },
    CHANGE_PASS_NORMAL_USER: {
        code: 28,
        name: 'ChangePasswordForNormal'
    },
    CHANGE_LANGUAGE: {
        code: 29,
        name: 'language_set'
    },
    EXPOR_DATA: {
        code: 30,
        name: 'DataExport'
    },
};

var _CheckStateEnum = { //审核状态枚举
    CLEARDATA: {
        name: 'emptyMonitorDatas',
        code: 0
    },
    FACTORYSET: {
        name: 'resetSoftSet',
        code: 1
    },
    ADDUSER: {
        name: 'Executed',
        code: 2
    },
    CHANGEUSER: {
        name: 'ChangeUserRight',
        code: 3
    },
    CHANGEPAS: {
        name: 'ChangePassword',
        code: 4
    },
    DELETEUSER: {
        name: 'DeleteUser',
        code: 5
    },
    UNLOCKUSER: {
        name: 'UnlockUser',
        code: 6
    },
    BACKUPDATA: {
        name: 'DataBackup',
        code: 7
    },
    RECOVERDATA: {
        name: 'DataRecovery',
        code: 8
    },
    LOCKUSER: {
        name: 'FreezeUser',
        code: 9
    },
    CHANGELOGININFO: {
        name: 'ChangeLoginInfo',
        code: 10
    }
};

function getCheckStateName(code) {
    return getCodeName(code, _CheckStateEnum);
}

var _UserStateEnum = {
    NORMAL: {
        name: 'normal',
        code: 0
    },
    LOCK_CONFIRM: {
        name: 'BlockPendingReview',
        code: 1
    },
    LOCK: {
        name: 'Block',
        code: 2
    },
    UNLOCK_CONFIRM: {
        name: 'UnlockPendingReview',
        code: 3
    },
    DELETE_CONFIRM: {
        name: 'DeletePendingReview',
        code: 5
    },
    ADD_CONFIRM: {
        name: 'AddUserPendingReview',
        code: 6
    },
    CHANGEPAS_CONFIRM: {
        name: 'ChangePassPendingReview',
        code: 7
    },
    CHANGEPER_CONFIRM: {
        name: 'ChangeRightPendingReview',
        code: 8
    }
};

function getUserStateName(code) {
    return getCodeName(code, _UserStateEnum);
}

var _netStateEnum = {
    connecting: {
        name: 'abnormal',
        code: 0
    },
    connected: {
        name: 'normal',
        code: 1
    }
};

function getNetStateName(code) {
    return getCodeName(code, _netStateEnum);
}

var _connectStateEnum = {
    isbreak: {
        name: 'StateLinkError',//链路异常
        code: 0
    },
    connectedWithCallBreak: {
        name: 'StateConnectError',//通信异常
        code: 1
    },
    connected: {
        name: 'normal',//正常
        code: 2
    },
    connecting: {
        name: 'StateConnecting',//连接中
        code: 3
    }
};

function getConnectedStateName(code) {
    return getCodeName(code, _connectStateEnum);
}


/**
 * Lora频点枚举
 */
var _loraFrequencyEnum = {
    AREA_CHINA: {
        name: 'AREA_CHINA',
        code: 0,
        range: [0, 19],
    },
    AREA_VIETNAM: {
        name: 'AREA_VIETNAM',
        code: 1,
        range: [0, 2],
    },
    AREA_MALAYSIA: {
        name: 'AREA_MALAYSIA',
        code: 2,
        range: [0, 2],
    },
    AREA_EUROPE: {
        name: 'AREA_EUROPE',
        code: 3,
        range: [0, 7],
    },
    AREA_US: {
        name: 'AREA_US',
        code: 4,
        range: [0, 19],
    },
    AREA_INDONESIA: {
        name: 'AREA_INDONESIA',
        code: 5,
        range: [0, 1],
    },
    AREA_INDIA: {
        name: 'AREA_INDIA',
        code: 6,
        range: [0, 1],
    },
    AREA_KOREA: {
        name: 'AREA_KOREA',
        code: 7,
        range: [0, 2],
    },
    AREA_CHINA_RSV: {
        name: 'AREA_CHINA_RSV',
        code: 8,
        range: [0, 19],
    },
};

function getLoraFrequencyName(code) {
    return getCodeName(code, _loraFrequencyEnum);
}

var _alarmDataTypeEnum = {
  AlarmData1000: {
      name: 'AlarmData1000',//固件CRC计算结果和固件自带的CRC校验结果不一致
      code: 1000
  },
  AlarmData1001: {
      name: 'AlarmData1001',//AD初始化错误,AD无法正常工作
      code: 1001
  },
  AlarmData1002: {
      name: 'AlarmData1002',//电池电压值为 xxV，xx处填入DataValue
      code: 1002
  },
  AlarmData1003: {
      name: 'AlarmData1003',//片内Flash读写错误
      code: 1003
  },
  AlarmData1004: {
      name: 'AlarmData1004',//片外Flash读写错误
      code: 1004
  },
  AlarmData1005: {
      name: 'AlarmData1005',//存储区未读取到参数
      code: 1005
  },
  AlarmData1006: {
      name: 'AlarmData1006',//读取到的参数CRC校验不一致
      code: 1006
  },
  AlarmData1007: {
      name: 'AlarmData1007',//读取到的参数超合法范围
      code: 1007
  },
  AlarmData1008: {
      name: 'AlarmData1008',//能够正确读取参数，且参数在合法范围内
      code: 1008
  },
  AlarmData1009: {
      name: 'AlarmData1009',//初始化返回值错误
      code: 1009
  },
};

function getAlarmDataTypeName(code) {
  return getCodeName(code, _alarmDataTypeEnum);
}

var COMMON_PROTOCOL = {
  COMMON_1000:{
    code:1000,
    name:'I2',
  },
  COMMONL_2000:{
    code:2000,
    name:'IEC104',
  },
  COMMON_3000:{
    code:3000,
    name:'IEC61850',
  },
  COMMON_4000:{
    code:4000,
    name:'Modbus',
  },
}

var BUSI_PROTOCOL = {
  BUSI_1001:{
    code:1001,
    name:getI18nName('PDStarsProtocol'),//华乘协议 PDStarsProtocol
  },
  BUSI_2001:{
    code:2001,
    name:getI18nName('DistributionAutoProtocol'),//配电自动化应用规约 DistributionAutoProtocol
  },
  BUSI_3001:{
    code:3001,
    name:getI18nName('PDStarsProtocol'),//华乘协议 PDStarsProtocol
  },
  BUSI_3002:{
    code:3002,
    name:getI18nName('SGCCProtocol'),//国网协议 SGCCProtocol
  },
  BUSI_3003:{
    code:3003,
    name:getI18nName('SouthPowerGridProtocol'),//南网协议 SouthPowerGridProtocol
  },
  BUSI_4001:{
    code:4001,
    name:getI18nName('GuangZhouDistributionAutoProtocol'),//广州智能配电房协议 GuangZhouDistributionAutoProtocol
  },
}