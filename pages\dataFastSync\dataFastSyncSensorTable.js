// 传感器表管理构造函数
function SensorTableManager(containerId) {
  this.containerId = containerId;
  this.tableObj = null;
  this.tableData = []; // 增加 tableData 缓存
}

// 在原型上定义方法
SensorTableManager.prototype = {
  // 重新指定构造函数
  constructor: SensorTableManager,

  // 表格配置
  tableSetting: {
    id: 'tableFastSyncData',
    height: $('#global').height() - 100,
    pageSize: 10,
    toolbar:
      '<div style="display: flex;" class="hide">' +
      '<label>' +
      getI18nName('LAST_SYNC_DATE_RANGE') +
      '</label>'+
      '<div id="lastFastSyncDateRange" style="margin-left:10px"></div>' +
      '<div id="lastFastSyncStatus" class="hide" style="margin-left:10px"></div>' +
      '</div>',
    columns: [
      {
        field: 'index',
        title: getI18nName('index'),
        width: 120,
        width: '10%',
      },
      {
        field: 'pointName',
        title: getI18nName('pointName'),
        width: '20%',
      },
      {
        field: 'syncState',
        title: getI18nName('State'),
        width: '15%',
      },
      {
        field: 'id',
        title: getI18nName('sensorCode'),
        width: '35%',
      },
      {
        field: 'deviceName',
        title: getI18nName('deviceNameTable'),
        width: '20%',
        formatter: function (value, row, index) {
          var a = value ? value : '-';
          return '<div style="white-space: pre-wrap;">' + a + '</div>';
        },
      },
    ],
  },

  // 初始化表格
  init: function () {
    try {
      const _that = this;
      this.tableSetting.onPageChange = function (index, size) {
        if (_that.parentManager && _that.parentManager.sensorTable) {
          _that.parentManager.getSensorState(index, size);
        }
      };
      (this.tableSetting.columns[2].formatter = function (value, row, index) {
        let content = '-';
        // if(dataFastSync.isSyncing){
        //   content = value;
        // }
        if (!checkIsEmpty(value)) {
          content = _that.getSyncStateDisplayValue(value, row.error, row);
        }
        return content;
      }),
        (this.tableObj = initLocalCustomTable(this.tableSetting));
      // 初始化时调用 refreshTable 方法刷新表格
      if (this.tableData.length > 0) {
        this.refreshTable(this.tableData);
      }
      return this.tableObj;
    } catch (error) {
      console.error('Error initializing table:', error);
      return null;
    }
  },

  // 获取当前表格实例
  getTableObj: function () {
    return this.tableObj;
  },

  // 获取 syncState 的实际展示值
  getSyncStateDisplayValue: function (syncState, errorCode, row) {
    const syncStateMap = {
      Waiting: getI18nName('SYNC_DATA_STATUS_WAITING'), // 等待连接
      Connected: getI18nName('SYNC_DATA_STATUS_CONNECTED'), // 已连接
      Syncing: getI18nName('SYNC_DATA_STATUS_SYNCING'), // 同步中
      Success: getI18nName('SYNC_DATA_STATUS_SUCCESS'), // 同步成功
      Failed: getI18nName('SYNC_DATA_STATUS_FAILED'), // 同步失败
      Canceled: getI18nName('SYNC_DATA_STATUS_CANCELED'), // 同步取消
    };

    const syncCountDispalyMap = ['Syncing', 'Success', 'Failed', 'Canceled'];

    const errorCodeMap = {
      // NoError: getI18nName('NoError'), // 无错误
      SyncTimeout: getI18nName('SYNC_DATA_ERROR_SYNC_TIME_OUT'), // 同步超时
      Disconnect: getI18nName('SYNC_DATA_ERROR_DISCONNECT'), // 断开连接
    };

    const dataCountContent = `<div>[${row.currentSyncedCount}/${row.totalSyncCount}]</div>`;

    // 当 errorCode 不为空且枚举值有值时，返回 errorCode 对应的值
    if (!checkIsEmpty(errorCode) && errorCodeMap[errorCode]) {
      let errorStateContent = `<div>${syncStateMap[syncState]}-${errorCodeMap[errorCode]}</div>`;
      if (
        !checkIsEmpty(row.currentSyncedCount) &&
        !checkIsEmpty(row.totalSyncCount)
      ) {
        errorStateContent += dataCountContent;
      }
      return errorStateContent;
      // return `<div>${syncStateMap['Failed']}-${errorCodeMap['SyncTimeOut']}</div><div>[${row.currentSyncedCount}/${row.totalSyncCount}]</div>`;
    }

    if (syncCountDispalyMap.indexOf(syncState) > -1) {
      let stateContent = `<div>${syncStateMap[syncState]}</div>`;
      if (
        !checkIsEmpty(row.currentSyncedCount) &&
        !checkIsEmpty(row.totalSyncCount)
      ) {
        stateContent += dataCountContent;
      }
      return stateContent;
    }

    // 否则返回 syncState 对应的值
    return syncStateMap[syncState] || '-';
  },

  // 刷新表格数据
  refreshTable: function (newData) {
    if (!this.tableObj) return;
    try {
      // 更新缓存数据
      this.tableData = newData;
      // 使用缓存数据刷新表格
      this.tableObj.bootstrapTable('load', this.tableData);
      $('.keep-open.btn-group').removeAttr('title');

      // 通过parentManager访问父容器实例
      if (this.parentManager) {
        // 可以在这里调用父容器的其他方法
        // console.log('Table refreshed, parent manager:', this.parentManager);
      }
    } catch (error) {
      console.error('Error refreshing table:', error);
    }
  },
};

// 使用示例：
// $(document).ready(function() {
//     // 创建实例
//     const sensorTable = new SensorTableManager('tableFastSyncData');
//     // 初始化表格
//     sensorTable.init();

//     // 示例：更新表格数据
//     const data = [
//         { id: 'sensor1', deviceName: 'Device A', pointName: 'Point 1', scanState: 'Idle' },
//         { id: 'sensor2', deviceName: 'Device B', pointName: 'Point 2', scanState: 'Scanning' }
//     ];
//     sensorTable.refreshTable(data);

//     // 示例：获取新数据后刷新表格
//     // $.ajax({
//     //     url: 'your-api-endpoint',
//     //     success: function(newData) {
//     //         sensorTable.refreshTable(newData);
//     //     }
//     // });
// });

var fastDataSyncCache = {};
