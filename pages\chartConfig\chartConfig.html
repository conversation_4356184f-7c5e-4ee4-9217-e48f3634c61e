<!--
 * @Author: liyaoxu <EMAIL>
 * @Date: 2024-02-20 10:45:56
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2024-07-02 14:10:30
 * @FilePath: \pds_z058_web\pages\chartConfig\chartConfig.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style>
  .pdchart-config-inner-content #title {
    display: flex;
    align-items: center;
  }

  .pdchart-btn-save {
    width: 100px;
    position: absolute;
    right: 4rem;
    bottom: 6rem;
  }

  .pdchart-config-inner-content #pds_chart_config_left_nav li {
    height: 2.5rem !important;
  }

  #pds_chart_config_tab_container{
    height: 100% !important;
  }
</style>
<div style="height: 100%;padding:1rem">
  <div id="chartConfigContent" style="width: 100%;height:100%"></div>
  <button type="button" id="now_data" class="btn btn-block btn-primary pdchart-btn-save" data-i18n="save"
    onclick="onConfigSaveClick()">
  </button>
</div>
<script>
  function onConfigSaveClick () {
    pdcharts.ChartConfigUtil.saveConfig()
    layer.msg(getI18nName('saveSuccess'));
  }
  (function () {
    initPageI18n();
    let chartConfigContent = $('#chartConfigContent');
    pdcharts.drawChartConfig(chartConfigContent[0], { theme: 'light', language: language });
  })()
</script>