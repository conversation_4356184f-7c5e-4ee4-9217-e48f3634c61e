# 前端国际化修改指南

## 概述

本文档总结了前端国际化业务的修改方式，用于指导后续的国际化改造工作。系统采用基于JSON语言文件的国际化方案，支持多语言切换。

## 国际化架构

### 1. 核心文件结构
```
├── language/                    # 语言文件目录
│   ├── zh-CN.json              # 中文语言包
│   ├── en-US.json              # 英文语言包
│   ├── ko-KR.json              # 韩文语言包
│   └── ...                     # 其他语言包
├── dist/js/lang-i18n.js        # 国际化核心脚本
├── config/config.js            # 配置文件（包含公共语言项）
└── pages/                      # 页面文件
```

### 2. 核心机制
- **语言变量**: `language` 全局变量控制当前语言（如 'zh-CN', 'en-US'）
- **语言包**: JSON格式的键值对翻译文件
- **初始化函数**: `initPageI18n()` 自动处理页面国际化
- **翻译函数**: `getI18nName(key)` 获取翻译文本
- **语言切换**: `changeLanguage(lang)` 切换语言

## 修改步骤

### 步骤1: 添加翻译项到语言文件

#### 1.1 在中文语言文件中添加
文件: `language/zh-CN.json`
```json
{
    "existingKey": "现有翻译",
    "newKey1": "新翻译项1",
    "newKey2": "新翻译项2"
}
```

#### 1.2 在英文语言文件中添加对应翻译
文件: `language/en-US.json`
```json
{
    "existingKey": "Existing Translation",
    "newKey1": "New Translation 1", 
    "newKey2": "New Translation 2"
}
```

#### 1.3 其他语言文件
根据需要在其他语言文件中添加对应翻译。

### 步骤2: 修改HTML页面

#### 2.1 静态文本国际化
使用 `data-i18n` 属性标记需要翻译的元素：

**修改前:**
```html
<label>数据类型:</label>
<button>刷新趋势图</button>
<span>数据详情</span>
```

**修改后:**
```html
<label data-i18n="dataType">数据类型:</label>
<button data-i18n="refreshTrend">刷新趋势图</button>
<span data-i18n="dataDetails">数据详情</span>
```

#### 2.2 下拉选项国际化
```html
<select id="dataTypeSelect">
    <option value="SF6_Pressure" data-i18n="SF6_Pressure">SF6气体压力</option>
    <option value="SF6_Density" data-i18n="SF6_Density">SF6气体密度</option>
</select>
```

#### 2.3 复合元素国际化
对于包含图标的按钮等复合元素：
```html
<button type="button" id="preData" class="btn btn-primary">
    <i class="fa fa-chevron-left"></i> <span data-i18n="preData">上一条</span>
</button>
```

### 步骤3: 修改JavaScript代码

#### 3.1 动态文本国际化
使用 `getI18nName(key)` 函数：

**修改前:**
```javascript
title: {
    text: 'SF6数据趋势'
}
```

**修改后:**
```javascript
title: {
    text: getI18nName('sf6TrendChart')
}
```

#### 3.2 提示信息国际化
```javascript
// 修改前
layer.msg('<div style="text-align:center">暂无趋势数据</div>');

// 修改后  
layer.msg('<div style="text-align:center">' + getI18nName('noTrendData') + '</div>');
```

#### 3.3 动态内容拼接
```javascript
// 修改前
option.title[0].text = dataTypeName + '趋势图';

// 修改后
option.title[0].text = dataTypeName + getI18nName('trendChart');
```

### 步骤4: 页面初始化

确保页面包含国际化初始化调用：
```javascript
// 在页面脚本中调用
initPageI18n();
```

## 最佳实践

### 1. 命名规范
- **功能性键名**: 使用功能描述，如 `dataType`, `startTime`, `endTime`
- **组件特定键名**: 使用组件前缀，如 `sf6TrendChart`, `SF6_Pressure`
- **通用键名**: 使用通用描述，如 `refresh`, `save`, `cancel`

### 2. 翻译原则
- **准确性**: 确保翻译准确表达原意
- **一致性**: 相同概念在不同页面使用相同翻译
- **简洁性**: 避免过长的翻译文本影响界面布局

### 3. 修改原则
- **最小化**: 只修改必要的文本，保持现有功能不变
- **简单化**: 使用现有国际化框架，不引入新的复杂机制
- **一致性**: 与现有代码的国际化方式保持一致

### 4. 测试验证
- 创建测试页面验证新增翻译项
- 测试语言切换功能
- 检查所有国际化标记是否生效
- 验证动态内容的国际化

## 常见问题

### 1. 翻译不生效
- 检查语言文件JSON格式是否正确
- 确认键名拼写是否正确
- 验证是否调用了 `initPageI18n()`

### 2. 动态内容不更新
- 确保使用 `getI18nName()` 函数
- 检查语言切换后是否重新渲染内容

### 3. 特殊字符处理
- JSON中的特殊字符需要转义
- HTML中的特殊字符使用HTML实体

## 示例模板

### 完整页面国际化示例
```html
<!DOCTYPE html>
<html>
<head>
    <script src="./dist/js/lang-i18n.js"></script>
</head>
<body>
    <h1 data-i18n="pageTitle">页面标题</h1>
    <form>
        <label data-i18n="fieldLabel">字段标签:</label>
        <input type="text" data-i18n-placeholder="inputPlaceholder">
        <button data-i18n="submitBtn">提交</button>
    </form>
    
    <script>
        initPageI18n();
        
        // 动态内容示例
        function showMessage() {
            alert(getI18nName('successMessage'));
        }
    </script>
</body>
</html>
```

## 总结

遵循本指南可以确保国际化修改的一致性和可维护性。关键是：
1. 在语言文件中添加翻译项
2. 在HTML中使用 `data-i18n` 属性
3. 在JavaScript中使用 `getI18nName()` 函数
4. 确保页面初始化调用 `initPageI18n()`

通过这种方式，可以实现完整的多语言支持，提升用户体验。
