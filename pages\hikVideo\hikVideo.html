<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<style>
    #regionInfoContent {
        border-bottom: 1px solid #dfdfdf;
    }
    #regionInfoContent th,
    #regionInfoContent td {
        border: 1px solid #dfdfdf;
    }
</style>
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div id="graph-Video" class="col-sm-9 border-left full-height">
            <div class="row border-left" id="videoDiv"
                 style="min-height: 400px;height: 100%;display: flex;justify-content: center;align-items: center;flex-wrap: wrap;padding: 0 40px;">
                <img id="videoImg1" style="max-width: 100%;max-height: 100%;padding: 6px;"/>
                <img id="videoImg2" style="max-width: 100%;max-height: 48%;padding: 6px;"/>
            </div>
        </div>
        <div class="col-sm-3 full-height bootstrap-dialog" style="border: 1px solid #f4f4f4;">
            <br>
            <form class="form-horizontal">
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sensorType">传感器类型:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sensorType" data-i18n="VIDEO">视频图片</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="CameraType">摄像头类型</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="captureType" style="text-align: left;"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_date" for="sample_date">采样日期:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_date"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_time">采样时间:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_time"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="maxTemp">最高温度</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="maxTemp"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="maxTempPosition">最高温度位置</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="maxTempPosition"></label>
                    </div>
                </div>
                <div class="form-group hide" style="padding: 0 1rem;">
                    <table id="regionInfoContent" class="table table-striped table-bordered table-hover">
                        <thead>
                            <tr>
                                <th data-i18n="pointName">测点名称</th>
                                <th data-i18n="maxTemp">最高温度</th>
                                <th data-i18n="maxTempPosition">最高温度位置</th>
                            </tr>
                        </thead>
                        <tbody id="regionInfoTable">

                        </tbody>
                    </table>
                </div>
            </form>
            <div class="content-footer" style="display: flex">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                        style="width: 140px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                        style="width: 140px; margin: 10px 0px 10px 10px" data-i18n="nextData">下一条
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    initPageI18n();
    $(function () {
        //@ sourceURL=ae_chart.js
        $(window).resize(function () {
            var height = $("#graph-Video").height() - $(".content-footer").height() - 20;
        });
        $(window).resize();

        var preId = 0;
        var nextId = 0;
        var nowId;

        function loadVideoTest(dataId, pointId) {

            var data = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=VIDEO&dataType=VIDEO";
            poll('GET', 'GetChartData', data, function (text) {
                var respData = text.result;

                preId = respData.preDataId;
                nextId = respData.nextDataId;
                //判断上一条/下一条按钮是否可用并改变状态
                if (preId === 0) {
                    $("#preData").attr("disabled", true);
                } else {
                    $("#preData").removeAttr("disabled");
                }
                if (nextId === 0) {
                    $("#nextData").attr("disabled", true);
                } else {
                    $("#nextData").removeAttr("disabled");
                }
                var tempPic1 = windowDebug ? serverUrl + respData.params.pic0 : respData.params.pic0;
                var tempPic2 = windowDebug ? serverUrl + respData.params.pic1 : respData.params.pic1;
                var captureType = respData.params.captureType;
                $('#videoImg1').attr('src', tempPic1);
                if (captureType > 2) {
                    $('#videoImg2').attr('src', tempPic2);
                    $('#videoImg1').css('max-height', '48%');
                }
                var captureTypeName;
                switch (captureType) {
                    case 1:
                        captureTypeName = getI18nName('lightGunCamera');
                        $("#maxTemp").parent().parent().hide();
                        $("#maxTempPosition").parent().parent().hide();
                        break;
                    case 2:
                        captureTypeName = getI18nName('lightBallCamera');
                        $("#maxTemp").parent().parent().hide();
                        $("#maxTempPosition").parent().parent().hide();
                        break;
                    case 3:
                        captureTypeName = getI18nName('infraredGunCamera');
                        break;
                    case 4:
                        captureTypeName = getI18nName('infraredBallCamera');
                        break;
                }

                //新增
                var regionInfo = {ThermometryRulesTemperatureInfo:[]};
                var hasRegionData = false;
                if(captureType > 2 && respData.params.regionInfo){
                  regionInfo = JSON.parse(respData.params.regionInfo);
                  hasRegionData = regionInfo.ThermometryRulesTemperatureInfo.length > 0;
                }
                // var maxTempValue = captureType > 2 ? (hasRegionData ? respData.params.maxTemp.toFixed(1) : '-') +'°C' : (respData.params.maxTemp!=undefined? respData.params.maxTemp.toFixed(1):'-') +'°C';
                var maxTempValue = captureType > 2 ? (hasRegionData ? respData.params.maxTemp.toFixed(1) : '-') +'°C' : '-' +'°C';
                //暂时由页面进行Type判断转换中文，未知type直接显示
                var sensorType = getI18nName(respData.params.sensorType);
                $("#sensorType").html(sensorType);
                $("#sample_date").html(respData.params.sample_date);
                $("#sample_time").html(respData.params.sample_time);
                $("#captureType").html(captureTypeName);
                $("#maxTemp").html(maxTempValue);
                $("#maxTempPosition").html(hasRegionData ? "[" + respData.params.posX + "," + respData.params.posY + "]" : "[-,-]");

                //新增
                // var regionInfo = JSON.parse(respData.params.regionInfo);
                var $regionInfoContent = $('#regionInfoContent');
                var $regionInfoTable = $('#regionInfoTable');
                if(hasRegionData){
                    $regionInfoContent.parent().removeClass('hide');
                }else{
                    $regionInfoContent.parent().addClass('hide');
                }
                $regionInfoTable.html("");
                if(captureType > 2){
                  regionInfo.ThermometryRulesTemperatureInfo.map(function (item) {
                    // $regionInfoContent.append(`名称 ${item.regionName} | 温度 ${(item.maxTemperature ? item.maxTemperature.toFixed(1) : '-')+'°C'} | 坐标 [${item.maxTemPositionX},${item.maxTemPositionY}]</br>`)
                    var content=`
                    <tr>
                        <td>
                            ${item.regionName}
                        </td>
                        <td>
                            ${(item.maxTemperature != undefined ? item.maxTemperature.toFixed(1) : '-')+'°C'}
                        </td>
                        <td>
                            [${item.maxTemPositionX},${item.maxTemPositionY}]
                        </td>
                    <tr>
                    `;
                    $regionInfoTable.append(content)
                  })
                }
            });
        }

        loadVideoTest(showChartDataId, showChartPointId);

        $("#preData").click(function () {
            if (preId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            loadVideoTest(preId, showChartPointId);
        });
        $("#nextData").click(function () {
            if (nextId === 0) {
                $.fn.alertMsg(
                        'warning',
                        getI18nName(''), [{
                            id: 'no',
                            text: getI18nName('confirm')
                        }]
                );
                return;
            }
            loadVideoTest(nextId, showChartPointId);
        });
    });
</script>