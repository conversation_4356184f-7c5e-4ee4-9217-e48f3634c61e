/**
 * Created by PDS-Archar on 2017/7/17.
 */
//获取浏览器页面可见高度和宽度
var _PageHeight = document.documentElement.clientHeight,
    _PageWidth = document.documentElement.clientWidth;
//计算loading框距离顶部和左部的距离（loading框的宽度为215px，高度为61px）
var _LoadingTop = _PageHeight > 61 ? (_PageHeight - 61) / 2 : 0,
    _LoadingLeft = _PageWidth > 215 ? (_PageWidth - 215) / 2 : 0;
//在页面未加载完毕之前显示的loading Html自定义内容
var _LoadingHtml = '<div id="loadingDiv" style="position:absolute;left:0;width:100%;height:' + _PageHeight + 'px;top:0;background:#f3f8ff;opacity:1;filter:alpha(opacity=80);z-index:10000;"><div style="position: absolute; cursor1: wait; left: ' + _LoadingLeft + 'px; top:' + _LoadingTop + 'px; width: auto; height: 55px; line-height: 55px; padding-left: 50px; padding-right: 5px; background: #f3f8ff url(./dist/img/loading1.gif) no-repeat  8px 10px; ; color: #696969;font-family: simsun;font-size:16px; font-weight: bold;"></div></div>';
//呈现loading效果
document.write(_LoadingHtml);

//window.onload = function () {
//    var loadingMask = document.getElementById('loadingDiv');
//    loadingMask.parentNode.removeChild(loadingMask);
//};

//监听加载状态改变
// document.onreadystatechange = completeLoading;
//
// //加载状态为complete时移除loading效果
// function completeLoading() {
//     if (document.readyState == "interactive" || document.readyState == "complete") {
//         var loadingMask = document.getElementById('loadingDiv');
//         if(loadingMask!=null)
//             loadingMask.parentNode.removeChild(loadingMask);
//     }
// }

// var loadingMask = document.getElementById('loadingDiv');
// loadingMask.parentNode.removeChild(loadingMask);
//定时器监视complete,加载到complete显示，并且定时器关闭；如果5s之后没有到complete，reload
var timesRun = 0;
intervalLoad = setInterval(function () {
    timesRun += 1;
    if (timesRun === 180) {
        clearInterval(intervalLoad);
        window.location.reload();
        // window.location.href = 'index.html?t=' + Date.parse(new Date());
    }
    else {
        if (document.readyState == "complete") {
            var loadingMask = document.getElementById('loadingDiv');
            loadingMask.parentNode.removeChild(loadingMask);

            clearInterval(intervalLoad);
        }
    }
//do whatever here..
}, 1000);
