<style>
    .pd-copy-right {
        margin-left: 15px;
        font-size: 16px;
    }
</style>

<div class="col-sm-12 full-height">

    <div class="box no-border no-margin  full-height">
        <div class="box-body" style="">
            <div style="padding-top: 40px;
                        padding-bottom: 50px;
                        width: 50%;">
                <h1 id="mType" style="margin-left: 20px"></h1>
                <h3 id="mName" style="margin-left: 19px"></h3>
                <h3 id="version-info" style="margin-left: 20px"> V1.0.24.1</h3>
                <h3 id="version-SN" style="margin-left: 20px"> SN:0</h3>
            </div>

            <div style="width: 50%;">
                <div class="col-sm-12 control-label">
                    <img id="copyRightLogo" src="dist/img/pdstars.png" class="img-rounded" style="height:110px;">
                </div>
                <!-- <div class="col-sm-8 control-label">
                    <h4 class="pd-copy-right">©2020 华乘电气科技股份有限公司</h4>
                </div> -->
            </div>
        </div>
    </div>
</div>
<!-- <button class="btn btn-primary" style="position: absolute;
z-index: 109000;
top: 450px;
left: 50px;">Test</button> -->
<script>
    /*
      壁挂机、2U、辅控型号都用：PDS-S500-LDCU，
      名称：
      壁挂、2U用：数据采集主机
      辅控用：智能运维监测终端
  
      汇集型号用：PDS-S500-MEU
      名称用：汇集节点
     */
    function getBrowser(n) {
        var ua = navigator.userAgent.toLowerCase(),
            s,
            name = '',
            ver = 0;
        //探测浏览器
        (s = ua.match(/msie ([\d.]+)/)) ? _set("ie", _toFixedVersion(s[1])):
            (s = ua.match(/firefox\/([\d.]+)/)) ? _set("firefox", _toFixedVersion(s[1])) :
            (s = ua.match(/chrome\/([\d.]+)/)) ? _set("chrome", _toFixedVersion(s[1])) :
            (s = ua.match(/opera.([\d.]+)/)) ? _set("opera", _toFixedVersion(s[1])) :
            (s = ua.match(/version\/([\d.]+).*safari/)) ? _set("safari", _toFixedVersion(s[1])) : 0;

        function _toFixedVersion(ver, floatLength) {
            ver = ('' + ver).replace(/_/g, '.');
            floatLength = floatLength || 1;
            ver = String(ver).split('.');
            ver = ver[0] + '.' + (ver[1] || '0');
            ver = Number(ver).toFixed(floatLength);
            return ver;
        }

        function _set(bname, bver) {
            name = bname;
            ver = bver;
        }

        return (n == 'n' ? name : (n == 'v' ? ver : name + ver));
    };

    var neihe = getBrowser("n"); // 所获得的就是浏览器所用内核。
    var banben = getBrowser("v"); // 所获得的就是浏览器的版本号。
    var browser = getBrowser(); // 所获得的就是浏览器内核加版本号。

    var clickTime = 9;
    initPageI18n();
    $('#mType').html(MONITOR_TYPE);
    $('#mName').html(MONITOR_NAME);
    if(buildMode.code == 'PdSafeCommon'){
      $('#copyRightLogo').attr('src', '');
    }else {
      $('#copyRightLogo').attr('src', buildMode.logo);
    }
    $('#version-info').html(version);
    $('#version-SN').html('SN:' + versionSN);
    $('#version-info').on('click', function () {
        if (clickTime == 0) {
            layer.open({
                title: '页面版本',
                content: '<div>Web Version</div><div style="color:coral">[' + webVersionCode +
                    ']</div><div>Explorer Version</div><div style="color:cadetblue">[' +
                    browser + ']</div>',
                end:function(){
                    debugger
                }
            });
        } else {
            clickTime--;
        }

    });
</script>