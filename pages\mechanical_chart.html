<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div id="graph" class="col-sm-7 border-left full-height" style="padding-left:7px;padding-right:0px">
            <div id="graph-head" class="row border-right" style="height: 48px;padding-right:5px">
                <div class="col-sm-4">
                    <button type="button" id="showCoil" class="btn btn-block btn-primary"
                        style="margin: 0px auto;margin-top: 5px" data-i18n="showCoil">线圈电流图谱
                    </button>
                </div>
                <div class="col-sm-4">
                    <button type="button" id="showMotor" class="btn btn-block btn-primary"
                        style="margin: 0px auto;margin-top: 5px" data-i18n="showMotor">电机电流图谱
                    </button>
                </div>
                <div class="col-sm-4">
                    <button type="button" id="showOrig" class="btn btn-block btn-primary"
                        style="margin: 0px auto;margin-top: 5px" data-i18n="showOrig">原始电流图谱
                    </button>
                </div>
            </div>
            <div class="row border-left" style="padding-left:18px" id="mechDiv">
                <div class="row" id="coilDiv"></div>
                <div class="row" id="switchDiv"></div>
                <div class="row" id="motorDiv"></div>
            </div>
            <div id="graph-end" class="row" style="border: 1px solid #f4f4f4;">
            </div>
        </div>
        <div id="infoDiv" class="col-sm-5 bootstrap-dialog"
            style="border: 1px solid #f4f4f4;overflow:auto;padding-left:7px;padding-right:7px">
            <form class="form-horizontal" id="info" style="overflow: auto;">
                <br>
                <div class="form-group">
                    <label class="col-sm-8 control-label" style="padding-right:5px" data-i18n="pointName">测点名称:</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="pointName"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-8 control-label" style="padding-right:5px"
                        data-i18n="sensorCode">传感器编码:</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="sensorCode"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-8 control-label" style="padding-right:5px" data-i18n="sensorType">传感器类型</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="sensorType" style="text-align:left"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-8 control-label" style="padding-right:5px" data-i18n="actionDate">动作日期</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="sample_date"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-8 control-label" style="padding-right:5px" data-i18n="actionTime">动作时间</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="sample_time"></label>
                    </div>
                </div>
                <div class="form-group" id="action_type_g">
                    <label class="col-sm-8 control-label" style="padding-right:5px" data-i18n="ActionType">动作类型</label>
                    <div class="col-sm-4">
                        <label class=" control-label" id="action_type" data-i18n="unknown">未知</label>
                    </div>
                </div>
                <div id="coilInfo_solo" class="hide">

                </div>
                <div id="coilInfo">
                    <div id="closeTime" class="hide">

                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px" for="a_close_time"
                                data-i18n="a_close_time">A相合闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                for="a_close_coil_charge_time" data-i18n="a_close_coil_charge_time">A相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_coil_charge_time"
                                    data-i18n="a_close_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_close_coil_cutout_time">A相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_close_max_current">A相合闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_close_hit_time">A相合闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_hit_time">26ms</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_close_subswitch_close_time">A相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_close_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_close_time">B相合闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                for="b_close_coil_charge_time" data-i18n="b_close_coil_charge_time">B相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                for="b_close_coil_cutout_time" data-i18n="b_close_coil_cutout_time">B相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_close_max_current">B相合闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_close_hit_time">B相合闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_hit_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_close_subswitch_close_time">B相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_close_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_time">C相合闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_coil_charge_time">C相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_coil_cutout_time">C相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_max_current">C相合闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_hit_time">C相合闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_hit_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_close_subswitch_close_time">C相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_close_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="close_sync">合闸同期性</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="close_sync"></label>
                            </div>
                        </div>
                        <div class="form-group-non hide">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="close_time">合闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="close_time"></label>
                            </div>
                        </div>
                    </div>

                    <div id="openTime" class="hide">
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_time">A相分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_coil_charge_time">A相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_coil_cutout_time">A相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_max_current">A相分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_hit_time">A相分闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_hit_time">26ms</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_open_subswitch_close_time">A相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_open_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_time">B相分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_coil_charge_time">B相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_coil_cutout_time">B相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_max_current">B相分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_hit_time">B相分闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_hit_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_open_subswitch_close_time">B相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_open_subswitch_close_time"></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_time">C相分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_coil_charge_time">C相线圈通电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_coil_cutout_time">C相线圈断电时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_max_current">C相分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_hit_time">C相分闸掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_hit_time">26ms</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_open_subswitch_close_time">C相辅助开关切换时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_open_subswitch_close_time"></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="open_sync">分闸同期性</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="open_sync"></label>
                            </div>
                        </div>
                        <div class="form-group-non hide">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="open_time">分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="open_time"></label>
                            </div>
                        </div>
                    </div>

                    <div id="twice_open_time" class="hide">
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_time">A相二次分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_coil_charge_time">A相二次分闸线圈带电时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_coil_charge_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_coil_cutout_time">A相二次分闸线圈断电时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_max_current">A相二次分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_hit_time">A相二分掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_hit_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_twice_open_subswitch_close_time">A相二次分闸辅助开关断开时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_twice_open_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_twice_open_time">B相二次分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_twice_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_twice_open_coil_cutout_time">B相二次分闸线圈断电时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_twice_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_twice_open_max_current">B相二次分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_twice_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_twice_open_hit_time">B相二分掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_twice_open_hit_time">26ms</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_twice_open_subswitch_close_time">B相二次分闸辅助开关断开时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_twice_open_subswitch_close_time"></label>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_twice_open_time">C相二次分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_twice_open_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_twice_open_coil_cutout_time">C相二次分闸线圈断电时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_twice_open_coil_cutout_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_twice_open_max_current">C相二次分闸线圈最大电流</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_twice_open_max_current"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_twice_open_hit_time">C相二分掣子脱扣时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_twice_open_hit_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_twice_open_subswitch_close_time">C相二次分闸辅助开关断开时刻</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_twice_open_subswitch_close_time"></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="twice_open_sync">二次分闸同期性</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="twice_open_sync"></label>
                            </div>
                        </div>
                        <div class="form-group-non hide">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="twice_open_time_text">二次分闸时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="twice_open_time_text"></label>
                            </div>
                        </div>
                    </div>
                    <div id="shot_time" class="hide">
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_switch_shot_time">A相金短时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_switch_shot_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_switch_shot_time">B相金短时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_switch_shot_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_switch_shot_time">C相金短时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_switch_shot_time"></label>
                            </div>
                        </div>
                    </div>
                    <div id="no_current_time" class="hide">
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="a_switch_no_current_time">A相无电流时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="a_switch_no_current_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="b_switch_no_current_time">B相无电流时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="b_switch_no_current_time"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-8 control-label" style="padding-right:5px"
                                data-i18n="c_switch_no_current_time">C相无电流时间</label>
                            <div class="col-sm-4">
                                <label class=" control-label" id="c_switch_no_current_time"></label>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="motorInfo" class="hide">
                    <!--<div class="form-group">-->
                    <!--<label class="col-sm-7 control-label">电机启动电流</label>-->
                    <!--<div class="col-sm-4">-->
                    <!--<label class=" control-label" id="motor_start_current">1.2A</label>-->
                    <!--</div>-->
                    <!--</div>-->
                    <!--<div class="form-group">-->
                    <!--<label class="col-sm-7 control-label">电机最大电流</label>-->
                    <!--<div class="col-sm-4">-->
                    <!--<label class=" control-label" id="motor_max_current">1.3A</label>-->
                    <!--</div>-->
                    <!--</div>-->
                    <!--<div class="form-group">-->
                    <!--<label class="col-sm-7 control-label">电机储能时间</label>-->
                    <!--<div class="col-sm-4">-->
                    <!--<label class=" control-label" id="storage_time">5s</label>-->
                    <!--</div>-->
                    <!--</div>-->
                </div>
            </form>
        </div>
        <div class="content-footer">
            <div class="col-sm-3">
                <button type="button" id="now_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="historyData">
                    历史数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="history_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="realData">实时数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                    style="width: 110px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $('.form-group').css('margin-left', '0px');
    $('.form-group').css('margin-right', '0px');
    initPageI18n();
    $(function () {
        //@ sourceURL=mechanical_chart.js
        $(window).resize(function () {
            if (!document.getElementById("mechDiv")) { //js判断元素是否存在
                return;
            }
            var width = $("#graph").width();
            var height = $("#graph").height() - 110;
            $("#mechDiv").height(height);

            var infoHeight = $("#graph").height() - 55;
            $("#info").height(infoHeight);
            var svgEls = document.querySelectorAll('svg');

            if (svgEls.length === 0)
                return;
            else if (svgEls.length === 2) {
                svgEls.forEach(function (tempsvgEl, index, array) {
                    tempsvgEl.setAttribute('width', width);
                    tempsvgEl.setAttribute('height', height / 2);
                });
            } else {
                svgEls.forEach(function (tempsvgEl, index, array) {
                    tempsvgEl.setAttribute('width', width);
                    tempsvgEl.setAttribute('height', height + 50);
                });
            }

        });
        $(window).resize();

        var preId = 0;
        var nextId = 0;
        var nowId;

        $("#showCoil").click(function () {
            //            nowId
            var mechDiv = $("#mechDiv");
            mechDiv.empty();
            //            $("#showCoil").addClass("disabled");
            //            $("#showMotor").removeClass("disabled");
            $("#showMotor").removeAttr("disabled");
            $("#showOrig").removeAttr("disabled");
            $("#showCoil").attr("disabled", true);
            $("#motorInfo").addClass("hide");
            $("#coilInfo").removeClass("hide");
            $("#coilInfo_solo").removeClass("hide");
            $("#action_type_g").removeClass("hide");

            $("#graph").removeClass("col-sm-12");
            $("#graph").addClass("col-sm-7");
            $("#infoDiv").removeClass("hide");

            mechDiv.append("<div class='row' id='coilDiv'></div>");
            mechDiv.append("<div class='row' id='switchDiv'></div>");
            //            mechDiv.append("<div class='row' id='origDiv'></div>");

            var coilDiv = document.getElementById('coilDiv');
            var switchDiv = document.getElementById('switchDiv');
            //            var origDiv= document.getElementById('origDiv');

            var waveChart = PDChart.Mech.draw(wave.chartBody, {
                title: getI18nName('showCoil'),
                showTriggerLine: false,
                showTitle: true,
                backgroundColor: "#ffffff",
                chartWidth: 900,
                chartHeight: 300,
                showYAxisLabel: true,
                autoColor: false
            });
            PDChart.loadChart(coilDiv, waveChart, {
                id: "wave",
                width: mechDiv.width(),
                height: mechDiv.height() / 2,
                append: true,
                saveAsImage: false
            });

            var switchChart = PDChart.Mech.draw(wave1.chartBody, {
                title: getI18nName('showSwitch'),
                showTriggerLine: false,
                showTitle: true,
                backgroundColor: "#ffffff",
                chartWidth: 900,
                chartHeight: 300,
                showYAxisLabel: false,
                autoColor: true
            });
            PDChart.loadChart(switchDiv, switchChart, {
                id: "wave",
                width: mechDiv.width(),
                height: mechDiv.height() / 2,
                append: true,
                saveAsImage: false
            });
            getMechData(showChartDataId, showChartPointId, 2);
        });

        var coilOrmotor;

        function getMechData(dataId, pointId, type) {
            var pointType = "MEU";
            coilOrmotor = type;
            var dataGet = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=" + pointType +
                "&dataType=" + type;
            poll('GET', 'GetChartData', dataGet, function (text) {
                var respData = text.result;

                //                preId = respData.preDataId ? respData.preDataId : 0;
                //                nextId = respData.nextDataId ? respData.nextDataId : 0;
                preId = respData.preDataId;
                nextId = respData.nextDataId;
                showChartDataId = dataId;
                //判断上一条/下一条按钮是否可用并改变状态
                if (preId === 0) {
                    $("#preData").attr("disabled", true);
                } else {
                    $("#preData").removeAttr("disabled");
                }
                if (nextId === 0) {
                    $("#nextData").attr("disabled", true);
                } else {
                    $("#nextData").removeAttr("disabled");
                }
                //开关状态标志 0代表非法；1代表分闸；2代表合闸
                var switchType = respData.params.switchType;
                if (type === 2) {
                    var coilDiv = document.getElementById('coilDiv');
                    var switchDiv = document.getElementById('switchDiv');

                    if (!respData.coilDataBody)
                        return;
                    $("#coilDiv").empty();
                    $("#switchDiv").empty();

                    var waveChart = PDChart.Mech.draw(respData.coilDataBody.coilCurrent, {
                        title: getI18nName('showCoil'),
                        showTriggerLine: false,
                        showTitle: true,
                        backgroundColor: "#ffffff",
                        chartWidth: 900,
                        chartHeight: 300,
                        showYAxisLabel: true,
                        autoColor: false
                    });
                    PDChart.loadChart(coilDiv, waveChart, {
                        id: "wave",
                        width: $("#mechDiv").width(),
                        height: $("#mechDiv").height() / 2,
                        append: true,
                        saveAsImage: false
                    });

                    var switchChart = PDChart.Mech.draw(respData.coilDataBody.switchStatus, {
                        title: getI18nName('showSwitch'),
                        showTriggerLine: false,
                        showTitle: true,
                        backgroundColor: "#ffffff",
                        chartWidth: 900,
                        chartHeight: 300,
                        showYAxisLabel: false,
                        autoColor: false
                    });
                    PDChart.loadChart(switchDiv, switchChart, {
                        id: "wave",
                        width: $("#mechDiv").width(),
                        height: $("#mechDiv").height() / 2,
                        append: true,
                        saveAsImage: false
                    });
                    //由页面进行Type判断转换中文，未知type直接显示
                    var sensorType = getI18nName(respData.params.sensorType);
                    $("#pointName").html(respData.params.pointName);
                    $("#sensorCode").html(respData.params.aduId);
                    $("#sensorType").html(sensorType);
                    $("#sample_date").html(respData.params.sample_date);
                    $("#sample_time").html(respData.params.sample_time);

                    $("#coilInfo_solo").removeClass("hide").addClass("hide");
                    if (respData.params.switchFunctionType === "0") {
                        $("#openTime").removeClass("hide").addClass("hide");
                        $("#closeTime").removeClass("hide").addClass("hide");
                        $("#shot_time").removeClass("hide").addClass("hide");
                        $("#no_current_time").removeClass("hide").addClass("hide");
                        $("#twice_open_time").removeClass("hide").addClass("hide");


                        $("#coilInfo_solo").empty();
                        $("#coilInfo_solo").removeClass("hide")
                        if (switchType === "1") { //分闸
                            $("#action_type").html(getI18nName('StateOpen'));
                            addCoilSoloData('open_coil_charge_time_solo', getI18nName(
                                    'coil_charge_time'), respData.params.a_open_coil_charge_time,
                                " ms")
                            addCoilSoloData('open_coil_cutout_time_solo', getI18nName(
                                    'coil_cutout_time'), respData.params.a_open_coil_cutout_time,
                                " ms")
                            addCoilSoloData('open_max_current_solo', getI18nName('max_current'),
                                respData.params.a_open_max_current, " A")
                            addCoilSoloData('open_hit_time_solo', getI18nName('hit_time'), respData
                                .params.a_open_hit_time, " ms")
                            addCoilSoloData('open_subswitch_close_time_solo', getI18nName(
                                    'subswitch_close_time'), respData.params
                                .a_open_subswitch_close_time, " ms")
                            addCoilSoloData('a_open_time_solo', getI18nName('a_open_time'), respData
                                .params.a_open_time, " ms")
                            addCoilSoloData('b_open_time_solo', getI18nName('b_open_time'), respData
                                .params.b_open_time, " ms")
                            addCoilSoloData('c_open_time_solo', getI18nName('c_open_time'), respData
                                .params.c_open_time, " ms")
                            addCoilSoloData('open_sync_solo', getI18nName('open_sync'), respData.params
                                .open_sync, " ms")

                        } else if (switchType === "2") { //合闸
                            $("#action_type").html(getI18nName('StateClose'));
                            addCoilSoloData('close_coil_charge_time_solo', getI18nName(
                                    'coil_charge_time'), respData.params.a_close_coil_charge_time,
                                " ms")
                            addCoilSoloData('close_coil_cutout_time_solo', getI18nName(
                                    'coil_cutout_time'), respData.params.a_close_coil_cutout_time,
                                " ms")
                            addCoilSoloData('close_max_current_solo', getI18nName('max_current'),
                                respData.params.a_close_max_current, " A")
                            addCoilSoloData('close_hit_time_solo', getI18nName('hit_time'), respData
                                .params.a_close_hit_time, " ms")
                            addCoilSoloData('close_subswitch_close_time_solo', getI18nName(
                                    'subswitch_close_time'), respData.params
                                .a_close_subswitch_close_time, " ms")
                            addCoilSoloData('a_close_time_solo', getI18nName('a_close_time'), respData
                                .params.a_close_time, " ms")
                            addCoilSoloData('b_close_time_solo', getI18nName('b_close_time'), respData
                                .params.b_close_time, " ms")
                            addCoilSoloData('c_close_time_solo', getI18nName('c_close_time'), respData
                                .params.c_close_time, " ms")
                            addCoilSoloData('close_sync_solo', getI18nName('close_sync'), respData
                                .params.close_sync, " ms")
                        }
                    } else {
                        $("#openTime").removeClass("hide");
                        $("#closeTime").removeClass("hide");
                        $("#shot_time").removeClass("hide");
                        $("#no_current_time").removeClass("hide");
                        $("#twice_open_time").removeClass("hide");
                        $("#action_type").html(getI18nName('Unknown'));
                        if (switchType === "1") { //分闸
                            $("#action_type").html(getI18nName('StateOpen'));
                            $("#closeTime").addClass("hide");
                            $("#shot_time").addClass("hide");
                            $("#no_current_time").addClass("hide");
                            $("#twice_open_time").addClass("hide");

                            setOpenTime(respData);
                        } else if (switchType === "2") { //合闸
                            $("#action_type").html(getI18nName('StateClose'));
                            $("#openTime").addClass("hide");
                            $("#shot_time").addClass("hide");
                            $("#no_current_time").addClass("hide");
                            $("#twice_open_time").addClass("hide");

                            setCloseTime(respData);
                        } else if (switchType === "3") { //分合闸
                            $("#shot_time").addClass("hide");
                            $("#twice_open_time").addClass("hide");

                            setOpenTime(respData);
                            setCloseTime(respData);
                            setNoCurrentTime(respData);
                        } else if (switchType === "4") { //合分闸
                            $("#no_current_time").addClass("hide");
                            $("#twice_open_time").addClass("hide");

                            setOpenTime(respData);
                            setCloseTime(respData);
                            setShotTime(respData);
                        } else if (switchType === "5") { //重合闸
                            setOpenTime(respData);
                            setCloseTime(respData);
                            setShotTime(respData);
                            setNoCurrentTime(respData);
                            setTwiceOpenTime(respData);
                        } else {
                            $("#openTime").addClass("hide");
                            $("#closeTime").addClass("hide");
                            $("#shot_time").addClass("hide");
                            $("#no_current_time").addClass("hide");
                            $("#twice_open_time").addClass("hide");
                        }
                    }

                } else if (type === 3) {
                    if (!respData.motorDataBody)
                        return;

                    $("#mechDiv").empty();
                    var chartDivMotor = document.getElementById('mechDiv');
                    var motorChart = PDChart.Mech.draw(respData.motorDataBody.motorCurrent, {
                        title: getI18nName('showMotor'),
                        showTriggerLine: false,
                        backgroundColor: "#ffffff",
                        chartWidth: 500,
                        chartHeight: 500,
                        showYAxisLabel: true,
                        autoColor: false
                    });
                    PDChart.loadChart(chartDivMotor, motorChart, {
                        id: "wave",
                        width: $("#mechDiv").width(),
                        height: $("#mechDiv").height() + 60,
                        append: true,
                        saveAsImage: false
                    });

                    //暂时由页面进行Type判断转换中文，未知type直接显示
                    var sensorType = getI18nName(respData.params.sensorType);
                    $("#pointName").html(respData.params.pointName);
                    $("#sensorCode").html(respData.params.aduId);
                    $("#sensorType").html(sensorType);
                    $("#sample_date").html(respData.params.sample_date);
                    $("#sample_time").html(respData.params.sample_time);
                    $("#motor_start_current").html(respData.params.Chan_A_motor_start_current + " A");
                    $("#motor_max_current").html(respData.params.Chan_A_motor_max_current + " A");
                    $("#storage_time").html(respData.params.Chan_A_storage_time + " s");
                    $("#motorInfo").html("");
                    var dataSeries = respData.motorDataBody.motorCurrent.dataSeries;
                    if (dataSeries[0].dataList.length > 0) {
                        var m = getI18nName('motor_start_current');
                        var n = getI18nName('motor_max_current');
                        var k = getI18nName('storage_time');
                        if (respData.params.switchFunctionType != "0") {
                            m = getI18nName('Chan_A_motor_start_current');
                            n = getI18nName('Chan_A_motor_max_current');
                            k = getI18nName('Chan_A_storage_time');
                        }
                        addMotorData('Chan_A_motor_start_current', m, respData.params
                            .Chan_A_motor_start_current, " A")
                        addMotorData('Chan_A_motor_max_current', n, respData.params
                            .Chan_A_motor_max_current, " A")
                        addMotorData('Chan_A_storage_time', k, respData.params.Chan_A_storage_time,
                            " s")
                    }
                    if (dataSeries[1].dataList.length > 0) {
                        addMotorData('Chan_B_motor_start_current', getI18nName(
                                'Chan_B_motor_start_current'), respData.params
                            .Chan_B_motor_start_current, " A")
                        addMotorData('Chan_B_motor_max_current', getI18nName(
                                'Chan_B_motor_max_current'), respData.params
                            .Chan_B_motor_max_current,
                            " A")
                        addMotorData('Chan_B_storage_time', getI18nName('Chan_B_storage_time'), respData
                            .params.Chan_B_storage_time, " s")
                    }
                    if (dataSeries[2].dataList.length > 0) {
                        addMotorData('Chan_C_motor_start_current', getI18nName(
                                'Chan_C_motor_start_current'), respData.params
                            .Chan_C_motor_start_current, " A")
                        addMotorData('Chan_C_motor_max_current', getI18nName(
                                'Chan_C_motor_max_current'), respData.params
                            .Chan_C_motor_max_current,
                            " A")
                        addMotorData('Chan_C_storage_time', getI18nName('Chan_C_storage_time'), respData
                            .params.Chan_C_storage_time, " s")
                    }

                } else if (type === 4) {
                    $("#mechDiv").empty();
                    var chartDiv = document.getElementById('mechDiv');

                    var origChart = PDChart.Mech.draw(respData.coilDataBody.orig, {
                        title: getI18nName('showOrig'),
                        showTriggerLine: false,
                        backgroundColor: "#ffffff",
                        chartWidth: 800,
                        chartHeight: 400,
                        showYAxisLabel: true,
                        autoColor: true
                    });
                    PDChart.loadChart(chartDiv, origChart, {
                        id: "wave",
                        width: $("#mechDiv").width(),
                        height: $("#mechDiv").height() + 60,
                        append: true,
                        saveAsImage: false
                    });
                }
            });
        }

        function addCoilSoloData(id, name, itemValue, unit) {
            if (validateStrLen(itemValue) != 0) {
                var item = getDataItem(id, name, itemValue + unit);
                $("#coilInfo_solo").append(item);
            }
        }

        function addMotorData(id, name, itemValue, unit) {
            var itemId = "#" + id;
            if (validateStrLen(itemValue) != 0) {
                var item = getDataItem(id, name, itemValue + unit);
                $("#motorInfo").append(item);
            }
        }

        function getDataItem(id, name, itemValue) {
            var item = [' <div class="form-group">',
                '                        <label class="col-sm-8 control-label" style="padding-right:5px">' +
                name + '</label>',
                '                        <div class="col-sm-4">',
                '                            <label class=" control-label" id="' + id + '">' + itemValue +
                '</label>',
                '                        </div>',
                '                    </div>'
            ].join("");
            return item;
        }

        function setOpenTime(respData) {
            //            $("#a_open_time").html(respData.params.a_open_time + " ms");
            //            $("#b_open_time").html(respData.params.b_open_time + " ms");
            //            $("#c_open_time").html(respData.params.c_open_time + " ms");

            setMecDataWithNullCheck("#a_open_time", respData.params.a_open_time, "ms")
            setMecDataWithNullCheck("#b_open_time", respData.params.b_open_time, "ms")
            setMecDataWithNullCheck("#c_open_time", respData.params.c_open_time, "ms")

            //            $("#a_open_coil_charge_time").html(respData.params.a_open_coil_charge_time + " ms");
            //            $("#a_open_coil_cutout_time").html(respData.params.a_open_coil_cutout_time + " ms");
            //            $("#a_open_max_current").html(respData.params.a_open_max_current + " A");
            //            $("#a_open_hit_time").html(respData.params.a_open_hit_time + " ms");
            //            $("#a_open_subswitch_close_time").html(respData.params.a_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#a_open_coil_charge_time", respData.params.a_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#a_open_coil_cutout_time", respData.params.a_open_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#a_open_max_current", respData.params.a_open_max_current, "A")
            setMecDataWithNullCheck("#a_open_hit_time", respData.params.a_open_hit_time, "ms")
            setMecDataWithNullCheck("#a_open_subswitch_close_time", respData.params.a_open_subswitch_close_time,
                "ms")

            //            $("#b_open_coil_cutout_time").html(respData.params.b_open_coil_cutout_time + " ms");
            //            $("#b_open_coil_charge_time").html(respData.params.b_open_coil_charge_time + " ms");
            //            $("#b_open_max_current").html(respData.params.b_open_max_current + " A");
            //            $("#b_open_hit_time").html(respData.params.b_open_hit_time + " ms");
            //            $("#b_open_subswitch_close_time").html(respData.params.b_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#b_open_coil_charge_time", respData.params.b_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#b_open_coil_cutout_time", respData.params.b_open_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#b_open_max_current", respData.params.b_open_max_current, "A")
            setMecDataWithNullCheck("#b_open_hit_time", respData.params.b_open_hit_time, "ms")
            setMecDataWithNullCheck("#b_open_subswitch_close_time", respData.params.b_open_subswitch_close_time,
                "ms")

            //            $("#c_open_coil_cutout_time").html(respData.params.c_open_coil_cutout_time + " ms");
            //            $("#c_open_coil_charge_time").html(respData.params.c_open_coil_charge_time + " ms");
            //            $("#c_open_max_current").html(respData.params.c_open_max_current + " A");
            //            $("#c_open_hit_time").html(respData.params.c_open_hit_time + " ms");
            //            $("#c_open_subswitch_close_time").html(respData.params.c_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#c_open_coil_charge_time", respData.params.c_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#c_open_coil_cutout_time", respData.params.c_open_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#c_open_max_current", respData.params.c_open_max_current, "A")
            setMecDataWithNullCheck("#c_open_hit_time", respData.params.c_open_hit_time, "ms")
            setMecDataWithNullCheck("#c_open_subswitch_close_time", respData.params.c_open_subswitch_close_time,
                "ms")


            //            $("#open_sync").html(respData.params.open_sync + " ms");
            //            $("#open_time").html(respData.params.open_time + " ms");
            setMecDataWithNullCheck("#open_sync", respData.params.open_sync, "ms")
            setMecDataWithNullCheck("#open_time", respData.params.open_time, "ms")

        }

        function setCloseTime(respData) {
            //            $("#a_close_time").html(respData.params.a_close_time + " ms");
            //            $("#a_close_coil_charge_time").html(respData.params.a_close_coil_charge_time + " ms");
            //            $("#a_close_coil_cutout_time").html(respData.params.a_close_coil_cutout_time + " ms");
            //            $("#a_close_max_current").html(respData.params.a_close_max_current + " A");
            //            $("#a_close_hit_time").html(respData.params.a_close_hit_time + " ms");
            //            $("#a_close_subswitch_close_time").html(respData.params.a_close_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#a_close_time", respData.params.a_close_time, "ms")
            setMecDataWithNullCheck("#a_close_coil_charge_time", respData.params.a_close_coil_charge_time, "ms")
            setMecDataWithNullCheck("#a_close_coil_cutout_time", respData.params.a_close_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#a_close_max_current", respData.params.a_close_max_current, "A")
            setMecDataWithNullCheck("#a_close_hit_time", respData.params.a_close_hit_time, "ms")
            setMecDataWithNullCheck("#a_close_subswitch_close_time", respData.params
                .a_close_subswitch_close_time, "ms")


            //            $("#b_close_time").html(respData.params.b_close_time + " ms");
            //            $("#b_close_coil_charge_time").html(respData.params.b_close_coil_charge_time + " ms");
            //            $("#b_close_coil_cutout_time").html(respData.params.b_close_coil_cutout_time + " ms");
            //            $("#b_close_max_current").html(respData.params.b_close_max_current + " A");
            //            $("#b_close_hit_time").html(respData.params.b_close_hit_time + " ms");
            //            $("#b_close_subswitch_close_time").html(respData.params.b_close_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#b_close_time", respData.params.b_close_time, "ms")
            setMecDataWithNullCheck("#b_close_coil_charge_time", respData.params.b_close_coil_charge_time, "ms")
            setMecDataWithNullCheck("#b_close_coil_cutout_time", respData.params.b_close_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#b_close_max_current", respData.params.b_close_max_current, "A")
            setMecDataWithNullCheck("#b_close_hit_time", respData.params.b_close_hit_time, "ms")
            setMecDataWithNullCheck("#b_close_subswitch_close_time", respData.params
                .b_close_subswitch_close_time, "ms")


            //            $("#c_close_time").html(respData.params.c_close_time + " ms");
            //            $("#c_close_coil_charge_time").html(respData.params.c_close_coil_charge_time + " ms");
            //            $("#c_close_coil_cutout_time").html(respData.params.c_close_coil_cutout_time + " ms");
            //            $("#c_close_max_current").html(respData.params.c_close_max_current + " A");
            //            $("#c_close_hit_time").html(respData.params.c_close_hit_time + " ms");
            //            $("#c_close_subswitch_close_time").html(respData.params.c_close_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#c_close_time", respData.params.c_close_time, "ms")
            setMecDataWithNullCheck("#c_close_coil_charge_time", respData.params.c_close_coil_charge_time, "ms")
            setMecDataWithNullCheck("#c_close_coil_cutout_time", respData.params.c_close_coil_cutout_time, "ms")
            setMecDataWithNullCheck("#c_close_max_current", respData.params.c_close_max_current, "A")
            setMecDataWithNullCheck("#c_close_hit_time", respData.params.c_close_hit_time, "ms")
            setMecDataWithNullCheck("#c_close_subswitch_close_time", respData.params
                .c_close_subswitch_close_time, "ms")

            //            $("#close_sync").html(respData.params.close_sync + " ms");
            //            $("#close_time").html(respData.params.close_time + " ms");

            setMecDataWithNullCheck("#close_sync", respData.params.close_sync, "ms")
            setMecDataWithNullCheck("#close_time", respData.params.close_time, "ms")
        }

        function setShotTime(respData) {
            //            $("#a_switch_shot_time").html(respData.params.a_switch_shot_time + " ms");
            //            $("#b_switch_shot_time").html(respData.params.b_switch_shot_time + " ms");
            //            $("#c_switch_shot_time").html(respData.params.c_switch_shot_time + " ms");
            setMecDataWithNullCheck("#a_switch_shot_time", respData.params.a_switch_shot_time, "ms")
            setMecDataWithNullCheck("#b_switch_shot_time", respData.params.b_switch_shot_time, "ms")
            setMecDataWithNullCheck("#c_switch_shot_time", respData.params.c_switch_shot_time, "ms")
        }

        function setNoCurrentTime(respData) {
            //            $("#a_switch_no_current_time").html(respData.params.a_switch_no_current_time + " ms");
            //            $("#b_switch_no_current_time").html(respData.params.b_switch_no_current_time + " ms");
            //            $("#c_switch_no_current_time").html(respData.params.c_switch_no_current_time + " ms");
            setMecDataWithNullCheck("#a_switch_no_current_time", respData.params.a_switch_no_current_time, "ms")
            setMecDataWithNullCheck("#b_switch_no_current_time", respData.params.b_switch_no_current_time, "ms")
            setMecDataWithNullCheck("#c_switch_no_current_time", respData.params.c_switch_no_current_time, "ms")
        }

        function setTwiceOpenTime(respData) {
            //            $("#a_twice_open_time").html(respData.params.a_twice_open_time + " ms");
            //            $("#a_twice_open_coil_charge_time").html(respData.params.a_twice_open_coil_charge_time + " ms");
            //            $("#a_twice_open_max_current").html(respData.params.a_twice_open_max_current + " A");
            //            $("#a_twice_open_hit_time").html(respData.params.a_twice_open_hit_time + " ms");
            //            $("#a_twice_open_subswitch_close_time").html(respData.params.a_twice_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#a_twice_open_time", respData.params.a_twice_open_time, "ms")
            setMecDataWithNullCheck("#a_twice_open_coil_charge_time", respData.params
                .a_twice_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#a_twice_open_max_current", respData.params.a_twice_open_max_current, "A")
            setMecDataWithNullCheck("#a_twice_open_hit_time", respData.params.a_twice_open_hit_time, "ms")
            setMecDataWithNullCheck("#a_twice_open_subswitch_close_time", respData.params
                .a_twice_open_subswitch_close_time, "ms")

            //            $("#b_twice_open_time").html(respData.params.b_twice_open_time + " ms");
            //            $("#b_twice_open_coil_charge_time").html(respData.params.b_twice_open_coil_charge_time + " ms");
            //            $("#b_twice_open_max_current").html(respData.params.b_twice_open_max_current + " A");
            //            $("#b_twice_open_hit_time").html(respData.params.b_twice_open_hit_time + " ms");
            //            $("#b_twice_open_subswitch_close_time").html(respData.params.b_twice_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#b_twice_open_time", respData.params.b_twice_open_time, "ms")
            setMecDataWithNullCheck("#b_twice_open_coil_charge_time", respData.params
                .b_twice_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#b_twice_open_max_current", respData.params.b_twice_open_max_current, "A")
            setMecDataWithNullCheck("#b_twice_open_hit_time", respData.params.b_twice_open_hit_time, "ms")
            setMecDataWithNullCheck("#b_twice_open_subswitch_close_time", respData.params
                .b_twice_open_subswitch_close_time, "ms")

            //            $("#c_twice_open_time").html(respData.params.c_twice_open_time + " ms");
            //            $("#c_twice_open_coil_charge_time").html(respData.params.c_twice_open_coil_charge_time + " ms");
            //            $("#c_twice_open_max_current").html(respData.params.c_twice_open_max_current + " A");
            //            $("#c_twice_open_hit_time").html(respData.params.c_twice_open_hit_time + " ms");
            //            $("#c_twice_open_subswitch_close_time").html(respData.params.c_twice_open_subswitch_close_time + " ms");

            setMecDataWithNullCheck("#c_twice_open_time", respData.params.c_twice_open_time, "ms")
            setMecDataWithNullCheck("#c_twice_open_coil_charge_time", respData.params
                .c_twice_open_coil_charge_time, "ms")
            setMecDataWithNullCheck("#c_twice_open_max_current", respData.params.c_twice_open_max_current, "A")
            setMecDataWithNullCheck("#c_twice_open_hit_time", respData.params.c_twice_open_hit_time, "ms")
            setMecDataWithNullCheck("#c_twice_open_subswitch_close_time", respData.params
                .c_twice_open_subswitch_close_time, "ms")


            //            $("#twice_open_sync").html(respData.params.twice_open_sync + " ms");
            //            $("#twice_open_sync").html(respData.params.twice_open_sync + " ms");
            setMecDataWithNullCheck("#twice_open_sync", respData.params.twice_open_sync, "ms")
            setMecDataWithNullCheck("#twice_open_time_text", respData.params.twice_open_time, "ms")

        }

        function setMecDataWithNullCheck(id, data, unit) {
            $(id).parent().parent().removeClass("hide").addClass("hide");
            if (validateStrLen(data) != 0) {
                $(id).html(data + " " + unit);
                $(id).parent().parent().removeClass("hide")
            }
        }

        $("#showMotor").click(function () {
            $("#mechDiv").empty();
            $("#showCoil").removeAttr("disabled");
            $("#showOrig").removeAttr("disabled");
            $("#showMotor").attr("disabled", true);
            $("#motorInfo").removeClass("hide");
            $("#coilInfo").addClass("hide");
            $("#coilInfo_solo").addClass("hide");
            $("#action_type_g").addClass("hide");

            $("#graph").removeClass("col-sm-12");
            $("#graph").addClass("col-sm-7");
            $("#infoDiv").removeClass("hide");

            var chartDiv = document.getElementById('mechDiv');

            var waveChart = PDChart.Mech.draw(wave.chartBody, {
                title: getI18nName('showMotor'),
                showTriggerLine: false,
                backgroundColor: "#ffffff",
                chartWidth: 500,
                chartHeight: 500,
                showYAxisLabel: true,
                autoColor: true
            });
            PDChart.loadChart(chartDiv, waveChart, {
                id: "wave",
                width: $("#mechDiv").width(),
                height: $("#mechDiv").height() + 60,
                append: true,
                saveAsImage: false
            });
            getMechData(showChartDataId, showChartPointId, 3);
        });

        $("#showOrig").click(function () {
            $("#mechDiv").empty();
            $("#showCoil").removeAttr("disabled");
            $("#showMotor").removeAttr("disabled");
            $("#showOrig").attr("disabled", true);

            $("#infoDiv").addClass("hide");
            $("#graph").removeClass("col-sm-7");
            $("#graph").addClass("col-sm-12");

            var chartDiv = document.getElementById('mechDiv');

            var origChart = PDChart.Mech.draw(wave.chartBody, {
                title: getI18nName('showOrig'),
                showTriggerLine: false,
                backgroundColor: "#ffffff",
                chartWidth: 800,
                chartHeight: 400,
                showYAxisLabel: true,
                autoColor: true
            });
            PDChart.loadChart(chartDiv, origChart, {
                id: "wave",
                width: $("#mechDiv").width(),
                height: $("#mechDiv").height() + 60,
                append: true,
                saveAsImage: false
            });
            getMechData(showChartDataId, showChartPointId, 4);
        });

        $('#showCoil').trigger("click");

        $("#preData").click(function () {
            if (preId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            if ($("#showMotor").attr("disabled")) {
                getMechData(preId, showChartPointId, 3);
                //                getMechData(preId, showChartPointId, coilOrmotor);
            } else if ($("#showCoil").attr("disabled"))
                getMechData(preId, showChartPointId, 2);
            else if ($("#showOrig").attr("disabled"))
                getMechData(preId, showChartPointId, 4);
        });
        $("#nextData").click(function () {
            if (nextId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            if ($("#showMotor").attr("disabled")) {
                getMechData(nextId, showChartPointId, 3);
                //                getMechData(nextId, showChartPointId, coilOrmotor);
            } else if ($("#showCoil").attr("disabled")) {
                getMechData(nextId, showChartPointId, 2);
            } else if ($("#showOrig").attr("disabled")) {
                getMechData(nextId, showChartPointId, 4);
            }
        });
    });

    function postLog(content) {
        var formData1 = "loginfo=" + content;
        poll('GET', 'Loginfo', formData1, function (text) {

        });
    }
</script>