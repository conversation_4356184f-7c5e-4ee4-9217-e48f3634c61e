/*
 * Skin: Red
 * ---------
 */
.skin-red-light .main-header .navbar {
  background-color: #dd4b39;
}
.skin-red-light .main-header .navbar .nav > li > a {
  color: #ffffff;
}
.skin-red-light .main-header .navbar .nav > li > a:hover,
.skin-red-light .main-header .navbar .nav > li > a:active,
.skin-red-light .main-header .navbar .nav > li > a:focus,
.skin-red-light .main-header .navbar .nav .open > a,
.skin-red-light .main-header .navbar .nav .open > a:hover,
.skin-red-light .main-header .navbar .nav .open > a:focus,
.skin-red-light .main-header .navbar .nav > .active > a {
  background: rgba(0, 0, 0, 0.1);
  color: #f6f6f6;
}
.skin-red-light .main-header .navbar .sidebar-toggle {
  color: #ffffff;
}
.skin-red-light .main-header .navbar .sidebar-toggle:hover {
  color: #f6f6f6;
  background: rgba(0, 0, 0, 0.1);
}
.skin-red-light .main-header .navbar .sidebar-toggle {
  color: #fff;
}
.skin-red-light .main-header .navbar .sidebar-toggle:hover {
  background-color: #d73925;
}
@media (max-width: 767px) {
  .skin-red-light .main-header .navbar .dropdown-menu li.divider {
    background-color: rgba(255, 255, 255, 0.1);
  }
  .skin-red-light .main-header .navbar .dropdown-menu li a {
    color: #fff;
  }
  .skin-red-light .main-header .navbar .dropdown-menu li a:hover {
    background: #d73925;
  }
}
.skin-red-light .main-header .logo {
  background-color: #dd4b39;
  color: #ffffff;
  border-bottom: 0 solid transparent;
}
.skin-red-light .main-header .logo:hover {
  background-color: #dc4735;
}
.skin-red-light .main-header li.user-header {
  background-color: #dd4b39;
}
.skin-red-light .content-header {
  background: transparent;
}
.skin-red-light .wrapper,
.skin-red-light .main-sidebar,
.skin-red-light .left-side {
  background-color: #f9fafc;
}
.skin-red-light .content-wrapper,
.skin-red-light .main-footer {
  border-left: 1px solid #d2d6de;
}
.skin-red-light .user-panel > .info,
.skin-red-light .user-panel > .info > a {
  color: #444444;
}
.skin-red-light .sidebar-menu > li {
  -webkit-transition: border-left-color 0.3s ease;
  -o-transition: border-left-color 0.3s ease;
  transition: border-left-color 0.3s ease;
}
.skin-red-light .sidebar-menu > li.header {
  color: #848484;
  background: #f9fafc;
}
.skin-red-light .sidebar-menu > li > a {
  border-left: 3px solid transparent;
  font-weight: 600;
}
.skin-red-light .sidebar-menu > li:hover > a,
.skin-red-light .sidebar-menu > li.active > a {
  color: #000000;
  background: #f4f4f5;
}
.skin-red-light .sidebar-menu > li.active {
  border-left-color: #dd4b39;
}
.skin-red-light .sidebar-menu > li.active > a {
  font-weight: 600;
}
.skin-red-light .sidebar-menu > li > .treeview-menu {
  background: #f4f4f5;
}
.skin-red-light .sidebar a {
  color: #444444;
}
.skin-red-light .sidebar a:hover {
  text-decoration: none;
}
.skin-red-light .treeview-menu > li > a {
  color: #777777;
}
.skin-red-light .treeview-menu > li.active > a,
.skin-red-light .treeview-menu > li > a:hover {
  color: #000000;
}
.skin-red-light .treeview-menu > li.active > a {
  font-weight: 600;
}
.skin-red-light .sidebar-form {
  border-radius: 3px;
  border: 1px solid #d2d6de;
  margin: 10px 10px;
}
.skin-red-light .sidebar-form input[type="text"],
.skin-red-light .sidebar-form .btn {
  box-shadow: none;
  background-color: #fff;
  border: 1px solid transparent;
  height: 35px;
}
.skin-red-light .sidebar-form input[type="text"] {
  color: #666;
  border-top-left-radius: 2px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 2px;
}
.skin-red-light .sidebar-form input[type="text"]:focus,
.skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  background-color: #fff;
  color: #666;
}
.skin-red-light .sidebar-form input[type="text"]:focus + .input-group-btn .btn {
  border-left-color: #fff;
}
.skin-red-light .sidebar-form .btn {
  color: #999;
  border-top-left-radius: 0;
  border-top-right-radius: 2px;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 0;
}
@media (min-width: 768px) {
  .skin-red-light.sidebar-mini.sidebar-collapse .sidebar-menu > li > .treeview-menu {
    border-left: 1px solid #d2d6de;
  }
}
