const mockDataFastSyncSensorTreeresult = {
  stationName: 'Substation A',
  stationGUID: '004d3c09-0977-4b52-9f56-2d10df5a1cf5',
  selectedPoints: ['b9c8d7e6-5f4a-3b2c-1d0e-9f8a7b6c5d4e'],
  devices: [
    {
      deviceNo: 1,
      deviceName: 'Main Transformer #1',
      deviceGUID: 'a7e8f5d2-3b6c-41a9-8d0e-5f2c7b1d9e3a',
      points: [
        {
          pointNo: 1,
          pointName: 'Transformer Phase A Temperature',
          pointGUID: 'b9c8d7e6-5f4a-3b2c-1d0e-9f8a7b6c5d4e',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AA:DC' }],
        },
        {
          pointNo: 2,
          pointName: 'Transformer Phase B Temperature',
          pointGUID: 'c1d2e3f4-5a6b-7c8d-9e0f-1a2b3c4d5e6f',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AA:DD' }],
        },
        {
          pointNo: 3,
          pointName: 'Transformer Phase C Temperature',
          pointGUID: 'd1e2f3g4-6b7c-8d9e-0f1a-2b3c4d5e6f7g',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AA:DE' }],
        },
        {
          pointNo: 4,
          pointName: 'Oil Temperature',
          pointGUID: 'e1f2g3h4-7c8d-9e0f-1a2b-3c4d5e6f7g8h',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AA:DF' }],
        },
        {
          pointNo: 5,
          pointName: 'Core Temperature',
          pointGUID: 'f1g2h3i4-8d9e-0f1a-2b3c-4d5e6f7g8h9i',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AA:E0' }],
        },
      ],
    },
    {
      deviceNo: 2,
      deviceName: 'Circuit Breaker #1',
      deviceGUID: 'b8f9g6e3-4c7d-52ba-9e1f-6g3d8c2e4a5b',
      points: [
        {
          pointNo: 1,
          pointName: 'Operating Time',
          pointGUID: 'g1h2i3j4-9e0f-1a2b-3c4d-5e6f7g8h9i0j',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AB:DC' }],
        },
        {
          pointNo: 2,
          pointName: 'Contact Temperature',
          pointGUID: 'h1i2j3k4-0f1a-2b3c-4d5e-6f7g8h9i0j1k',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AB:DD' }],
        },
        {
          pointNo: 3,
          pointName: 'SF6 Gas Pressure',
          pointGUID: 'i1j2k3l4-1a2b-3c4d-5e6f-7g8h9i0j1k2l',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AB:DE' }],
        },
        {
          pointNo: 4,
          pointName: 'Operating Counter',
          pointGUID: 'j1k2l3m4-2b3c-4d5e-6f7g-8h9i0j1k2l3m',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AB:DF' }],
        },
        {
          pointNo: 5,
          pointName: 'Mechanism Status',
          pointGUID: 'k1l2m3n4-3c4d-5e6f-7g8h-9i0j1k2l3m4n',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AB:E0' }],
        },
      ],
    },
    {
      deviceNo: 3,
      deviceName: 'Disconnector #1',
      deviceGUID: 'c9g0h7f4-5d8e-63cb-0f2g-7h4e9d3f5b6c',
      points: [
        {
          pointNo: 1,
          pointName: 'Position Status',
          pointGUID: 'l1m2n3o4-4d5e-6f7g-8h9i-0j1k2l3m4n5o',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AC:DC' }],
        },
        {
          pointNo: 2,
          pointName: 'Motor Current',
          pointGUID: 'm1n2o3p4-5e6f-7g8h-9i0j-1k2l3m4n5o6p',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AC:DD' }],
        },
        {
          pointNo: 3,
          pointName: 'Operating Time',
          pointGUID: 'n1o2p3q4-6f7g-8h9i-0j1k-2l3m4n5o6p7q',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AC:DE' }],
        },
        {
          pointNo: 4,
          pointName: 'Contact Temperature',
          pointGUID: 'o1p2q3r4-7g8h-9i0j-1k2l-3m4n5o6p7q8r',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AC:DF' }],
        },
        {
          pointNo: 5,
          pointName: 'Operation Counter',
          pointGUID: 'p1q2r3s4-8h9i-0j1k-2l3m-4n5o6p7q8r9s',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AC:E0' }],
        },
      ],
    },
    {
      deviceNo: 4,
      deviceName: 'Current Transformer #1',
      deviceGUID: 'd0h1i8g5-6e9f-74dc-1g3h-8i5f0e4g6c7d',
      points: [
        {
          pointNo: 1,
          pointName: 'Primary Current',
          pointGUID: 'q1r2s3t4-9i0j-1k2l-3m4n-5o6p7q8r9s0t',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AD:DC' }],
        },
        {
          pointNo: 2,
          pointName: 'Secondary Current',
          pointGUID: 'r1s2t3u4-0j1k-2l3m-4n5o-6p7q8r9s0t1u',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AD:DD' }],
        },
        {
          pointNo: 3,
          pointName: 'Oil Level',
          pointGUID: 's1t2u3v4-1k2l-3m4n-5o6p-7q8r9s0t1u2v',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AD:DE' }],
        },
        {
          pointNo: 4,
          pointName: 'Oil Temperature',
          pointGUID: 't1u2v3w4-2l3m-4n5o-6p7q-8r9s0t1u2v3w',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AD:DF' }],
        },
        {
          pointNo: 5,
          pointName: 'Insulation Status',
          pointGUID: 'u1v2w3x4-3m4n-5o6p-7q8r-9s0t1u2v3w4x',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AD:E0' }],
        },
      ],
    },
    {
      deviceNo: 5,
      deviceName: 'Voltage Transformer #1',
      deviceGUID: 'e1i2j9h6-7f0g-85ed-2h4i-9j6g1f5h7d8e',
      points: [
        {
          pointNo: 1,
          pointName: 'Primary Voltage',
          pointGUID: 'v1w2x3y4-4n5o-6p7q-8r9s-0t1u2v3w4x5y',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AE:DC' }],
        },
        {
          pointNo: 2,
          pointName: 'Secondary Voltage',
          pointGUID: 'w1x2y3z4-5o6p-7q8r-9s0t-1u2v3w4x5y6z',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AE:DD' }],
        },
        {
          pointNo: 3,
          pointName: 'Oil Temperature',
          pointGUID: 'x1y2z3a4-6p7q-8r9s-0t1u-2v3w4x5y6z7a',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AE:DE' }],
        },
        {
          pointNo: 4,
          pointName: 'Oil Level',
          pointGUID: 'y1z2a3b4-7q8r-9s0t-1u2v-3w4x5y6z7a8b',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AE:DF' }],
        },
        {
          pointNo: 5,
          pointName: 'Insulation Status',
          pointGUID: 'z1a2b3c4-8r9s-0t1u-2v3w-4x5y6z7a8b9c',
          sensors: [{ sensorNo: 1, sensorID: '00:00:00:00:2F:FE:AE:E0' }],
        },
      ],
    },
  ],
};

// 数据快速同步页面管理构造函数
function DataFastSyncManager() {
  this.sensorTree = null;
  this.sensorTable = null;
  this.isSyncing = false;
}

// 在原型上定义方法
DataFastSyncManager.prototype = {
  // 重新指定构造函数
  constructor: DataFastSyncManager,

  // 初始化页面
  init: function () {
    try {
      // 初始化传感器树
      this.sensorTree = new SensorTreeManager('fu-aduItems');
      // 将父容器实例挂载到sensorTree
      this.sensorTree.parentManager = this;
      // 将SensorTreeManager实例挂载到zTree的setting上
      this.sensorTree.setting.treeManager = this.sensorTree;
      this.sensorTree.init();

      // 初始化传感器表
      this.sensorTable = new SensorTableManager('tableFastSyncData');
      // 将父容器实例挂载到sensorTable
      this.sensorTable.parentManager = this;
      this.sensorTable.init();

      return this;
    } catch (error) {
      console.error('Error initializing DataFastSyncManager:', error);
      return null;
    }
  },

  // 刷新表格数据
  refreshTable: function (newData) {
    if (!this.sensorTable) return;
    try {
      this.sensorTable.refreshTable(newData);
    } catch (error) {
      console.error('Error refreshing table:', error);
    }
  },

  // 获取选中的传感器节点
  getCheckedSensors: function () {
    if (!this.sensorTree) return [];
    try {
      return this.sensorTree.getCheckedSensors();
    } catch (error) {
      console.error('Error getting checked sensors:', error);
      return [];
    }
  },

  // 刷新树数据
  refreshTree: function (newData) {
    if (!this.sensorTree) return;
    try {
      this.sensorTree.refreshTree(newData);
    } catch (error) {
      console.error('Error refreshing tree:', error);
    }
  },

  // 取消快速同步数据查找
  onFastSyncDataFindCancel: function () {
    try {
      layer.msg(getI18nName('FastDataSyncCancelMsg'));
      this.startFastSyncDataState(false);
    } catch (error) {
      console.error('Error canceling fast sync data find:', error);
    }
  },
  onPushFastSyncDataStatus: function (msg, code, key) {
    this.getSensorState();
  },
  getSensorState: function () {
    const _that = this;
    pollPost(
      FASTSYNCDATA_GETSTATUS,
      {
        pageIndex: 1,
        pageSize: 9999,
      },
      function (res) {
        _that.__updateGroupCodePageStatus(res.result);
      }
    );
  },
  __updateGroupCodePageStatus: function (sensorStateResp) {
    let { syncTask = {}, syncTaskStatus = {} } = sensorStateResp;
    // console.log('dataFastSyncM-refreshPageState', {
    //   sensorStateResp,
    // });

    if (syncTaskStatus.error === 'StartCommServiceFailed') {
      $('#lastFastSyncStatus').removeClass('hide')
      $('#lastFastSyncStatus').html(getI18nName('SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED'))
    } else {
      $('#lastFastSyncStatus').addClass('hide')
    }


    if (syncTaskStatus.isSyncing == true) {
      this.startFastSyncDataState(true);
    } else {
      this.startFastSyncDataState(false);
    }
    // 更新 tableData 中匹配的对象的 error 和 syncState
    if (
      syncTaskStatus.pointStatus &&
      this.sensorTable &&
      this.sensorTable.tableData
    ) {
      // 更新 daterangepicker 的值
      if (
        !checkIsEmpty(syncTask.startTime) &&
        !checkIsEmpty(syncTask.endTime)
      ) {
        const startTime = moment(syncTask.startTime, 'YYYY/MM/DD HH:mm:ss');
        const endTime = moment(syncTask.endTime, 'YYYY/MM/DD HH:mm:ss');

        //如果是正在同步，页面进入时初始化更新日期选择器范围
        if (syncTaskStatus.isSyncing == true) {
          $('#quick-data-sync-dateRange')
            .data('daterangepicker')
            .setStartDate(startTime);
          $('#quick-data-sync-dateRange')
            .data('daterangepicker')
            .setEndDate(endTime);
        }

        $('#lastFastSyncDateRange').parent().removeClass('hide');
        $('#lastFastSyncDateRange').html(
          `[${syncTask.startTime} - ${syncTask.endTime}]`
        );
      } else {
        $('#lastFastSyncDateRange').parent().addClass('hide');
      }
      const tableData = this.sensorTable.tableData;
      syncTaskStatus.pointStatus.forEach((status) => {
        const matchedItem = tableData.find(
          (item) =>
            item.id === status.aduId && item.pointGUID === status.pointId
        );
        if (matchedItem) {
          matchedItem.error = status.error;
          matchedItem.syncState = status.syncState;
          matchedItem.currentSyncedCount = status.currentSyncedCount;
          matchedItem.totalSyncCount = status.totalSyncCount;
        }
      });

      // 刷新表格
      this.sensorTable.refreshTable(tableData);
    }
    if (!hasInitFastSyncData) {
      hasInitFastSyncData = true;
    }
  },
  // 更新快速同步数据状态
  startFastSyncDataState: function (flag) {
    try {
      // 如果 flag 与当前状态一致，直接返回
      if (this.isSyncing === flag) return;

      // 更新 loading 状态
      this.isSyncing = flag;

      if (flag) {
        $('#quick-data-sync-dateRange').attr('disabled', 'disabled');
        $('#fu-fast-data-sync').addClass('hide');
        $('#fu-cancel').removeClass('hide');
        $('#fu-cancel img').removeClass('hide');

        // 禁用 zTree 操作
        if (this.sensorTree && this.sensorTree.zTreeObj) {
          // this.sensorTree.zTreeObj.setting.check.enable = false; // 禁用复选框
          // this.sensorTree.zTreeObj.refresh(); // 刷新树

          // 获取 zTreeContainer
          const zTreeContainer = $('#' + this.sensorTree.containerId);
          zTreeContainer.css('position', 'relative');

          // 获取当前滚动条位置
          const scrollTop = zTreeContainer.scrollTop();
          const scrollLeft = zTreeContainer.scrollLeft();

          // 增加灰色遮罩，高度为 100%，并根据当前滚动条位置设置初始位置
          zTreeContainer.append(
            `<div class="zTree-mask" style="position: absolute; top: ${scrollTop}px; left: ${scrollLeft}px; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.3); z-index: 1; cursor: not-allowed;"></div>`
          );

          // 增加悬浮提示的禁用图标，并根据当前滚动条位置设置初始位置
          zTreeContainer.append(
            `<div class="zTree-disabled-icon" style="position: absolute; top: calc(50% + ${scrollTop}px); left: calc(50% + ${scrollLeft}px); transform: translate(-50%, -50%); font-size: 24px; color: white; z-index: 1001;"><img src="dist/img/loading.gif" style="margin: 0 5px 0 0" class="img-loading" alt="" /></div>`
          );

          // 监听 zTree 容器滚动事件，动态调整遮罩和图标位置
          zTreeContainer.on('scroll', function () {
            const scrollTop = zTreeContainer.scrollTop();
            const scrollLeft = zTreeContainer.scrollLeft();
            zTreeContainer.find('.zTree-mask').css({
              top: scrollTop,
              left: scrollLeft,
            });
            zTreeContainer.find('.zTree-disabled-icon').css({
              top: `calc(50% + ${scrollTop}px)`,
              left: `calc(50% + ${scrollLeft}px)`,
            });
          });
        }
      } else {
        $('#fu-cancel').removeAttr('disabled');
        $('#quick-data-sync-dateRange').removeAttr('disabled');
        $('#fu-fast-data-sync').removeClass('hide');
        $('#fu-cancel').addClass('hide');
        $('#fu-cancel img').addClass('hide');

        // 启用 zTree 操作
        if (this.sensorTree && this.sensorTree.zTreeObj) {
          // this.sensorTree.zTreeObj.setting.check.enable = true; // 启用复选框
          // this.sensorTree.zTreeObj.refresh(); // 刷新树

          // 移除灰色遮罩和禁用图标
          const zTreeContainer = $('#' + this.sensorTree.containerId);
          zTreeContainer.find('.zTree-mask').remove();
          zTreeContainer.find('.zTree-disabled-icon').remove();

          // 移除滚动事件监听
          zTreeContainer.off('scroll');
        }
      }
    } catch (error) {
      console.error('Error updating fast sync data state:', error);
    }
  },
  getSyncDataADUTree: function () {
    var data = null;
    const _that = this;
    // _that.refreshTree(mockDataFastSyncSensorTreeresult);
    // $.ajax({
    //   async:true,
    //   type: 'GET',
    //   url: 'http://172.16.3.116:8003/' + FASTSYNCDATA_GETSTATIONDEVICETREE,
    //   dataType: 'json',
    //   success: function (respData) {
    //     _that.refreshTree(respData);
    //   },
    // });
    poll('GET', FASTSYNCDATA_GETSTATIONDEVICETREE, data, function (text) {
      var respData = text.result;
      _that.refreshTree(respData);
      _that.getSensorState();
    });
  },
};

function checkFastSyncDataPageStatus() {
  if (!hasInitFastSyncData) {
    hasInitFastSyncData = true;
    pollGet('GetFindAduGroupNoStatus', {}, function (res) {
      var statusRes = res.result;
      if (statusRes.isFindAduGroupNo) {
        updateGroupCodePageStatus(statusRes);
      }
    });
  }
}
