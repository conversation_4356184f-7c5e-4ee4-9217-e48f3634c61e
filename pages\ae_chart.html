<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div id="graph-AE" class="col-sm-7 border-left full-height">
            <div class="row border-left" id="aeDiv" style="min-height: 400px;height: 100%">
            </div>
        </div>
        <div class="col-sm-5 full-height bootstrap-dialog" style="border: 1px solid #f4f4f4;">
            <form class="form-horizontal">
                <br>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="pointName">测点名称:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="pointName"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sensorCode">传感器编码:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sensorCode"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sensorType">传感器类型:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sensorType">AE</label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="gain">增益:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="gain"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_date" for="sample_date">采样日期:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_date"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_time">采样时间:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_time"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="rms">有效值:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="rms"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="cycle_max">周期最大值:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="cycle_max"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label id="frequency1_n" class="col-sm-6 control-label" data-i18n="frequency1">频率成分1:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="frequency1"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label id="frequency2_n" class="col-sm-6 control-label" style="padding-left:0px"
                        data-i18n="frequency2">频率成分2:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="frequency2"></label>
                    </div>
                </div>
            </form>
        </div>
        <div class="content-footer">
            <div class="col-sm-3">
                <button type="button" id="now_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="realData">实时数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="history_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="historyData">
                    历史数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                    style="width: 110px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    initPageI18n();
    $(function () {
        //@ sourceURL=ae_chart.js
        $(window).resize(function () {
            if (!document.getElementById("aeDiv")) { //js判断元素是否存在
                return;
            }
            var height = $("#graph-AE").height() - $(".content-footer").height() - 20;
            $("#aeDiv").height(height);

            var svgEls = document.querySelector('svg');
            if (svgEls) {
                svgEls.setAttribute('width', $("#aeDiv").width());
                svgEls.setAttribute('height', $("#aeDiv").height());
            }
            //            loadAETest();
        });
        $(window).resize();

        $('.slider').slider();

        var preId = 0;
        var nextId = 0;
        var nowId;

        function loadAETest(dataId, pointId) {
            $("#aeDiv").empty();
            var chartDiv = document.getElementById('aeDiv');
            var amplitudeChart = PDChart.AMPLITUDE.draw(amplitude.chartBody, {
                title: getI18nName('aeChartTitle'),
                backgroundColor: "#ffffff"
            });
            PDChart.loadChart(chartDiv, amplitudeChart, {
                id: "amplitude",
                width: $("#aeDiv").width(),
                height: $("#aeDiv").height(),
                append: true,
                saveAsImage: false,
            });

            var data = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=AE&dataType=AE";
            poll('GET', 'GetChartData', data, function (text) {
                var respData = text.result;

                preId = respData.preDataId;
                nextId = respData.nextDataId;
                //判断上一条/下一条按钮是否可用并改变状态
                if (preId === 0) {
                    $("#preData").attr("disabled", true);
                } else {
                    $("#preData").removeAttr("disabled");
                }
                if (nextId === 0) {
                    $("#nextData").attr("disabled", true);
                } else {
                    $("#nextData").removeAttr("disabled");
                }
                $("#aeDiv").empty();
                //PRPD-2D
                //                //临时补正判断：更正图谱坐标轴名称，后续需要z058返回值正确
                //                if (xDesc === "RMS") {
                //                    xLabel = "  有效值"
                //                } else if (xDesc === "max") {
                //                    xLabel = "周期最大值";
                //                } else if (xDesc === "fre1Value") {
                //                    xLabel = "频率成分1  [50]Hz";
                //                } else if (xDesc === "fre2Value") {
                //                    xLabel = "频率成分2 [100]Hz";
                //                }
                //全局管理翻译文件，处理返回数据中的多语言
                for (var chartIndex = 0; chartIndex < respData.chartBody.length; chartIndex++) {
                    var chartData = respData.chartBody[chartIndex];
                    var xDesc = globalParms[chartData.axisInfo.xDesc];
                    if (validateStrLen(xDesc) != 0) {
                        if (chartData.axisInfo.xDesc === "fre1Value") {
                            xDesc += "[" + respData.params.frequency + "]Hz";
                            $('#frequency1_n').text(xDesc + ":");
                        } else if (chartData.axisInfo.xDesc === "fre2Value") {
                            xDesc += "[" + respData.params.frequency * 2 + "]Hz";
                            $('#frequency2_n').text(xDesc + ":");
                        }
                        chartData.axisInfo.xDesc = xDesc;
                    }
                }
                var amplitudeChart = PDChart.AMPLITUDE.draw(respData.chartBody, {
                    title: getI18nName('aeChartTitle'),
                    backgroundColor: "#ffffff"
                });
                PDChart.loadChart(chartDiv, amplitudeChart, {
                    id: "amplitude",
                    width: $("#aeDiv").width(),
                    height: $("#aeDiv").height(),
                    append: true,
                    saveAsImage: false
                });
                //暂时由页面进行Type判断转换中文，未知type直接显示
                var sensorType = getI18nName(respData.params.sensorType);
                $("#pointName").html(respData.params.pointName);
                $("#sensorCode").html(respData.params.aduId);
                $("#sensorType").html(sensorType);
                $("#gain").html(respData.params.gain);
                $("#sample_date").html(respData.params.sample_date);
                $("#sample_time").html(respData.params.sample_time);
                $("#rms").html(respData.params.rms);
                $("#cycle_max").html(respData.params.cycle_max);
                $("#frequency1").html(respData.params.frequency1);
                $("#frequency2").html(respData.params.frequency2);
            });
        }

        loadAETest(showChartDataId, showChartPointId);

        $("#preData").click(function () {
            if (preId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            loadAETest(preId, showChartPointId);
        });
        $("#nextData").click(function () {
            if (nextId === 0) {
                $.fn.alertMsg(
                    'warning',
                    getI18nName(''), [{
                        id: 'no',
                        text: getI18nName('confirm')
                    }]
                );
                return;
            }
            loadAETest(nextId, showChartPointId);
        });
    });
</script>