{"loginTime": "Time Logged In", "changerUser": "Switch User", "change": "Switch", "login": "<PERSON><PERSON>", "systemShutDownConfirmTip": "You've Been Idle For Too Long, You Will Be Automatically Logged Out Of The System If This Continues For 1 Minute. Do You Wish To Remain Logged In? If So, Please Select \"Continue.\"", "systemShutDownTip": "Your Session Has Ended and <PERSON> Been Automatically Logged Out Of The System", "systemShutDownCancel": "Cancel Shutdown", "loadingData": "Loading data, please wait...", "pageSize": "{1} per page", "pageSelecter": "Display: {1}-{2}, Total {3}.", "search": "Search", "noMatch": "No matching record was found.", "index": "Serial Number", "type": "Type", "aduId": "Sensor ID", "connectedState": "Network Status", "commType": "Communication Mode", "loraSignalLevel": "LoRa Signal Strength", "loraSNR": "Signal to Noise Ratio", "powerSupplyMode": "Power Mode", "byBattery": "On Battery", "byCable": "External Power", "battaryPercent": "Battery Level", "battaryLife": "Battery Life (Days)", "deviceNameTable": "Equipment Name", "pointType": "Sensor", "isconnected": "Status", "onlineState": "Online", "offlineState": "Lost Connection", "aduNeverConnectedState": "Not Connected", "updateTime": "Update Time", "data": "Data", "chart": "Chart", "operate": "Operation", "lookUp": "View", "tbSampleDate": "Time Sample Taken", "collect": "Collect Data", "errorTip": "Error: {1}", "AEamp": "AE Amplitude", "aeChartTitle": "AE Amplitude Spectrum", "humidityChartTitle": "Humidity Stat.", "temperatureChartTitle": "Temperature Stat.", "coreGroundingCurrentChartTitle": "Core Grounding Current Stat.", "TEVamp": "TEV Amplitude", "TEVYunit": "Unit[dB]", "UnitFomat": "Unit[{1}]", "unitParse": "Unit[{1}]", "maxValue": "Max", "minValue": "Min. Value", "average": "Average Value", "AT&T": "AT&T", "China Unicom": "Unicom", "China Mobile": "CMCC", "Transformer": "Transformer", "Breaker": "Breaker", "Disconnector": "Disconnecting Switch", "Isolator": "Switch", "Arrester": "Lightning Arrester", "PT": "Potential Transformer", "CT": "Current Transformer", "Busbar": "Bus Bar", "Circuit": "Buscouple", "Switchgear": "Switchgear", "Power Cable": "Electric Power Cable", "Lightning Rod": "Lightning Rod", "Wall Bushing": "<PERSON>", "Reactor": "Reactor", "Electric Conductor": "Electric Wire (diversion)", "Power Capacitor": "Power Capacitor", "Discharge Coil": "Coil", "Load Switch": "<PERSON>ad Switch", "Grounding Transformer": "Grounding Transformer", "Grounding Resistance": "Grounding Resistance", "Grounding Grid": "Grounding Grid", "Combined Filter": "Combined Filter", "Insulator": "Insulator", "Coupling Capacitor": "Coupling Capacitor", "Cabinet": "Cabinet", "Other": "Other", "Fuse": "<PERSON><PERSON>", "Using Transformer": "Substation Transformer", "Arc Suppression Device": "Arc Extinguishing Device", "Main Transformer": "Main Transformer", "Wave Trap": "Wave Trap", "Combined Electric Appliance": "Composite Apparatus", "Combined Transformer": "Combined Transformer", "monitor": "Real-time Monitor", "dataSelect": "Data Query", "themeSkin": "Theme Skin", "themeBlue": "Blue", "settings": "System Config", "datetime_set": "Time Setting", "language_set": "Language Setting", "soft_setting": "System Setting", "file_manage": "Asset Management", "instrument_set": "Sensor Config", "file_point_set": "Test Point Config", "alarm_param_set": "Alarm Configuration", "alarm_manager": "Alarm Manager", "audit_view": "Audit View", "modbus_setting": "Modbus Setting", "hardware_update": "Firmware Update", "collect_mode": "Collection Mode", "net_set": "Network Setting", "sync_data": "Data Sync", "syncingData": "Synchronizing Data...", "syncingDataCancel": "Cancel", "syncingDataFailed": "Synchronization Failed", "syncingDataSuccess": "Data Synchronization Completed", "syncingDataProgress": "Data Synchronization Progress: {1}", "syncingDataProgress2": "Data Synchronization Progress [{1}]", "export_data": "Data Export", "system_info": "System Info", "monitoringTable": "Test Table", "PD": "Internal PD Sensor", "PD_THREE": "Internal PD Sensor(Three in one)", "PD_FIVE": "Internal PD Sensor(Five in one)", "OLD_IS": "UHF Sensor", "MEU": "Mechanical Properties Sensor", "UHF_IS": "UHF Smart Sensor", "HFCT_IS": "HFCT Smart Sensor", "PD_IS": "Combo TUT Module", "TRANSFORMER_AE_IS": "Transformer AE Sensor", "GIS_AE_IS": "GIS AE Sensor", "ENV_IS": "Environmental Smart Sensor", "Arrester_U_IS": "Lightning Arrester Voltage Smart Sensor", "Arrester_I_IS": "Lightning Arrester Current Smart Sensor", "Vibration_IS": "Vibration Smart Sensor", "MECH_IS": "Mechanical Characteristic Smart Monitor", "TEMP_HUM_IS": "Combo TH Module", "MECH_PD_TEMP": "Built-in PD,Mechanical Characteristic, and Mtemperature three in one Sensor", "GIS_LOWTEN": "Low Pressure Sensor", "CHANNEL_OPTICAL_TEMP": "Fiber Optic Temperature Sensor", "VaisalaDTP145": "Vaisala DTP 145", "Wika_GDT20": "Wika GDT20", "Wika_GDHT20": "Wika GDHT20", "SHQiuqi_SC75D_SF6": "SHQiuqi SC75D", "SPTR_IS": "Core Grounding Current Sensor", "TEMPFC_OUT": "Temperature Sensor (CLMD)", "TEMPXP_OUT": "Temperature Sensor (SPS061)", "SF6YN_OUT": "SF6 Gas Pressure Sensor (FTP-18)", "FLOOD_IS": "Water Immersion Smart Sensor", "TEMPKY_OUT": "Temperature Sensor (RFC-01)", "SMOKERK_OUT": "Smoke Detector (RS-YG-N01)", "phase": "Phase", "period": "Period", "AMP": "Amplitude", "RMS": "RMS", "max": "Peak", "fre1Value": "Spectrum1", "fre2Value": "Spectrum2", "All-pass": "All Pass", "Low-pass": "Low Pass", "High-pass": "High Pass", "No-Record": "Not Recorded", "TEV": "Transient Earth Voltage", "AE": "AE", "TEMP": "Temperature", "HFCT": "HFCT", "UHF": "UHF", "Humidity": "<PERSON><PERSON><PERSON><PERSON>", "Noisy": "Sound Pressure", "ENVGas": "Atmosphere", "envgas": "Atmospheric Trend", "MP": "Mechanical Properties", "FLOOD": "Waterlogging", "SMOKE": "Smoke Detector", "SF6YN": "SF6 gas pressure", "ArresterU": "Arrester Voltage", "ArresterI": "Arrester Current", "Vibration": "Vibration", "FrostPointRaw": "Frost-point Temperature", "FrostPointATM": "Frost-point Temperature", "DewPointRaw": "Dew Point Temperature", "DewPointATM": "Dew-point Temperature", "Moisture": "PPM Moisture Content", "AbsolutePressure": "Absolute Pressure", "NormalPressure": "Pressure", "Density": "Intensity", "Oxygen": "Oxygen(%)", "SPTR": "Core Grounding Current", "confirm": "Confirm", "close": "Close", "yes": "Yes", "no": "No", "continue": "Continue", "none": "None", "DISK": "Storage", "MEMORY": "Memory", "noTestData": "No Data Stored", "connected": "Connected", "disconnected": "Disconnected", "virtualKeyBord": "Virtual Keyboard", "pleaseInput": "Please input", "pleaseInputFormat": "Please input {1}.", "tips": "Tips", "confirmTips": "Confirmation Prompt", "getDirFailed": "Directory acquisition failed", "backupSuccessTip": "Data Backup Has Completed Successfully. To View The Data, Please Go To The {1} Directory.", "backupFailedTip": "Failed To Backup The Data", "exportFailedTip": "Failed To Export The Data: {1}", "historyDataTypeNotSuport": "This Type Of Smart Sensor Does Not Support Query Of Historical Data", "stopExportTips": "Abort Exporting Data?", "stationNameTips": "Input Substation Name", "powerUnitNameTips": "Input Power Company Name", "selectDeviceTips": "Select The Asset", "selectPointTips": "Select The Test Point", "selectDeviceOfPointTips": "Select Power Asset To Add Test Point", "saveSuccess": "Successfully Saved", "saveFailed": "Save Failure", "operatFailed": "Operation Failed: {1}", "chosseSensorUpdate": "Select Sensor To Begin Update", "chooseFileUpdate": "Select File To Begin Update", "cancelUpdateSensorConfirm": "Abort Updating Sensor?", "enterServerUrlTips": "Please Enter The Server Address", "enterServerUrlError": "<PERSON><PERSON><PERSON>, The Server Address Used Is Incorrect, Please Try Again.", "enterServerPortTips": "Please Enter The Server Port.", "enterServerPortError": "Error, The Value Used For The Server Port Is Incorrect, Please Try Again and Enter a Range Of: [1,65535].", "NTPserverIpError": "<PERSON><PERSON><PERSON>, The Time Server Address Used Is Incorrect, Please Try Again.", "NTPserverPortError": "Error, The Value Used For The Time Server Port Is Incorrect, Please Try Again and Enter a Range Of: [1,65535]", "enterNetName": "Please Enter a Network Name.", "enterIP": "Please Enter The IP Address.", "enterIPError": "Error, The IP Address Used Is Invalid, Please Use a Valid Form As Shown In This Example: ***********", "enterNetMask": "Please Enter The Subnet Mask", "enterNetError": "<PERSON>rror, The Subnet Mask Used Is Invalid, Please Use a Valid Form As Shown In This Example: *************", "enterGateWay": "Please Enter The Gateway", "enterGateWayError": "Error, The Configuration Of The Gateway Is Invalid. Please Use a Valid Form As Shown In This Example: ***********", "enterAPN": "Select the APN", "pdSampleIntervalTip": "PD Sampling Interval Can Be Set Between 20s and 594099s(99h99m99s)", "meuSampleIntervalTip": "MEU Sampling Interval Can Be Set Between 10s and 86400s", "monitorAwakeTimeTip": "Sleep Interval Can Be Set Between 10s and 86400s", "monitorSleepSpaceTip": "Wake Up Time Can Be Set Between {1} Min and {2} Min", "uploadIntervalTip": "Upload Interval Can Be Set Between 60s and 86400s", "sampleIntervalTip": "The Sampling Interval Should Be an Integer Between 1 Hour and 72 Hours", "monitorSampleTimeTip": "The Initial Sampling Time of The Data Collection Unit Is a Number Between 00 and 23 Hours", "monitorSampleIntervalTip": "The Sampling Interval of The Data Collection Unit Is a Number Between 01 and 168 Hours", "updatingSensor": "Updating The Sensor...", "updatingSensorProgress": "Updating: {1}", "updatingSensorProgress2": "Updating [{1}]", "selectInstTip": "Please Select The Sensor That Needs to Be Modified", "selectChannelTip": "Select The Channel That Needs to Be Modified", "instCodeTip": "Input The Sensor ID", "commLinkTypeTip": "Select The Communication Type", "commLinkPortTip": "Select The Communication Port", "instSampleSpaceTip": "The Sampling Interval Can Be Set Between {1} Min and {2} Min", "instSampleStartTimeTip": "The Initial Sampling Time Is At The Top Of The Hour Beginning at 00 Hours and Ending at 23 Hours", "instNumberPattenTip": "It Can Only Contain Alphanumeric values and a Colon", "deleteInstTip": "Select the Sensor You Want to Delete", "selectSensorTip": "Select a Sensor", "deleteInstConfirmTip": "Are You Sure to Delete</Br>{1}</Br> Sensor ID: {2}?", "activeInstConfigTip": "Are You Sure You Want to Apply This Configuration to All Of The Same Sensor Type:</Br>{1}?", "activeBatchConfigsSuccess": "Batch Configuration Of Sensors Successful", "activeBatchConfigsFailed": "Batch Configuration Of Sensors Failed", "collectStartOnceTip": "Please Confirm to Start Collecting Data From All Of The Same Sensor Type: </Br>{1}?", "collectStartTip": "Please Confirm to Start Collecting Data From Sensor: </Br>{1}</Br> ID: {2} ?", "wakeUpInstrumentOnceTip": "Please Confirm to Start Waking Up All Of The Same Sensor Type: </Br>{1}?", "wakeUpInstrumentTip": "Please Confirm to Start Waking Up</Br>{1}</Br> Sensor: {2} ?", "emptySensorDataOnceTip": "Please Confirm to Clear All Data for All Of The Same Sensor Type: </Br>{1}?", "emptySensorDataTip": "Please Confirm to Clear All Data for</br>{1}</br>Sensor ID: {2}?", "emptySensorDataSuccess": "Successfully Cleared Sensor Data", "emptySensorDataFailed": "Failed To Clear Sensor Data.", "ae_chart": "Real-Time Monitoring - AE Spectrum", "uhf_chart": "Real-Time Monitoring - UHF Spectrum", "hfct_chart": "Real-Time Monitoring - HFCT Spectrum", "tev_chart": "Real-Time Monitoring - TEV Amplitude", "temperature_chart": "Real-Time Monitoring - Temperature", "humidity_chart": "Real-Time Monitoring - Hu<PERSON><PERSON><PERSON>", "mechanical_chart": "Real-Time Monitoring - Mechanical Properties", "arrester_chart": "Real-Time Monitoring - A<PERSON>ster", "vibration_pickup_chart": "Real-Time Monitoring - Vibration", "density_micro_water_history": "Real-Time Monitoring - Density Micro Water", "core_grounding_current": "Real-Time Monitoring - Core Grounding Current", "error": "Error", "warning": "Warning", "success": "Success", "userErrorTip": "User name and/or Password is invalid", "errorNoDeviceId": "Device ID dose not exist", "errorNoSensorId": "Sensor ID dose not exist", "errorExistSensorId": "The sensor ID has already existed", "errorNoChannelId": "Channel ID dose not exist", "errorNoPointId": "Test point does not exist", "errorDataExportErrDir": "Data export, file path error", "errorDataExportErrTime": "Data export, time error", "errorDataExportNoData": "Data export, no data.", "errorDataExportNoSpace": "Data export, U disk space is insufficient", "errorDataExportNoDisk": "Data export,  U disk not found", "errorDataExportNoInfo": "Data export, no station information.", "errorDataExportOther": "Data export, other error", "errorSetModeBus": "Modebus setting error", "errorIP": "IP error", "errorGPRSSet": "GPRS setting error", "errorSenorUpdateErrFile": "Sensor update file error", "errorSenorUpdateNotMatch": "Sensor update, sensor type and upgrade file do not match", "errorSenorUpdating": "Updating", "errorDataCleanFailed": "Clearing data failed!", "errorCodeWorkGroupNotExist": "Work group dose not exist", "errorCodeInvalidChannel": "illegal channel", "errorCodeInvalidWirignMode": "illegal wiring connection mode", "errorCodeInvalidAlarmMode": "Illegal alarm mode!", "errorNoPermission": "Restricted User Rights", "errorSameNamePoint": "point already exists", "illegalUser": "Invalid User!", "legalPattenMsg": "Can only contain the characters (nnot include spaces): Chinese characters letter number  Rome digital I - XII. ([]) - ` #, _", "leakageCurrent": "Full Current's <PERSON><PERSON><PERSON>", "leakageCurrentA": "Phase A Full Current", "leakageCurrentB": "Phase B Full Current", "leakageCurrentC": "Phase C Full Current", "ResistiveCurrent": "Resistive Current's <PERSON><PERSON><PERSON>", "ResistiveCurrentA": "A Phase Resistive Current", "ResistiveCurrentB": "B Phase Resistive Current", "ResistiveCurrentC": "C Phase Resistive Current", "resistiveCurrentA": "A Phase Resistive Current", "resistiveCurrentB": "B Phase Resistive Current", "resistiveCurrentC": "C Phase Resistive Current", "referenceVoltageA": "Phase A Reference Voltage", "referenceVoltageB": "Phase B Reference Voltage", "referenceVoltageC": "Phase C Reference Voltage", "timeDomain": "Time Domain Spectrum", "frequencyDomain": "Frequency Domain Spectrum", "characterParam": "Characteristic Parameters", "TimeDomainDataX": "X-Axis Vibration Signal", "TimeDomainDataY": "Y-Axis Vibration Signal", "TimeDomainDataZ": "Z-Axis Vibration Signal", "FrequencyDomainDataX": "Vibration Signal Spectrum - X Axis", "FrequencyDomainDataY": "Vibration Signal Spectrum - Y Axis", "FrequencyDomainDataZ": "Vibration Signal Spectrum - Z Axis", "ACCAVGX": "X-Axis Average Acceleration", "ACCAVGY": "Y-Axis Average Acceleration", "ACCAVGZ": "Z-Axis Average Acceleration", "ACCMAXX": "X-Axis Maximum Acceleration", "ACCMAXY": "Y-Axis Maximum Acceleration", "ACCMAXZ": "Z-Axis Maximum Acceleration", "AMPAVGX": "X-Axis Average Vibration Amplitude", "AMPAVGY": "Y-Axis Average Vibration Amplitude", "AMPAVGZ": "Z-Axis Average Vibration Amplitude", "AMPMAXX": "X-Axis Maximum Vibration Amplitude", "AMPMAXY": "Y-Axis Maximum Vibration Amplitude", "AMPMAXZ": "Z-Axis Maximum Vibration Amplitude", "MAXFreqX0": "X-Axis Extreme Point Frequency 1", "MAXFreqY0": "Y-Axis Extreme Point Frequency 1", "MAXFreqZ0": "Z-Axis Extreme Point Frequency 1", "MAXFreqX1": "X-Axis Extreme Point Frequency 2", "MAXFreqY1": "Y-Axis Extreme Point Frequency 2", "MAXFreqZ1": "Z-Axis Extreme Point Frequency 2", "MAXFreqX2": "X-Axis Extreme Point Frequency 3", "MAXFreqY2": "Y-Axis Extreme Point Frequency 3", "MAXFreqZ2": "Z-Axis Extreme Point Frequency 3", "sensorType": "Sensor Type", "sensorList": "Sensor List", "gain": "<PERSON><PERSON>", "trigger": "Trigger Value", "wave_filter": "Frequency Band", "sample_date": "Acquisition Date", "sample_time": "Acquisition Time", "rms": "RMS", "cycle_max": "Peak", "frequency1": "Spectrum1", "frequency2": "Spectrum2", "time_interval": "Time Interval", "realData": "Real-Time Data", "historyData": "Historical Data", "trendSync": "Trend Analysis", "preData": "Previous Data", "nextData": "Next Data", "systemDate": "System Date", "systemTime": "System Time", "backupType": "Backup Type", "plusBackup": "Plus Backup", "allBackup": "Full Backup", "timeRange": "Time Range", "exportPath": "Export Path", "getExportDir": "Retrieve Directories", "checkDir": "Check Directory", "exportingData": "Exporting Data", "cancel": "Cancel", "export": "Export", "stationConfig": "Station Config", "stationDelSuccess": "Successfully Deleted Substation", "stationDelFailed": "Failed To Delete", "deviceConfig": "Primary Equipment Config", "pointConfig": "Test Point Config", "stationName": "Station Name", "stationPMS": "Station Code", "stationLevel": "Station Voltage Level", "powerUnit": "Electric Power Unit", "save": "Save", "operationSuccess": "Operation Successfully", "deviceAddSuccessTips": "Successfully Added Asset", "deviceEditSuccessTips": "Successfully Modified Asset", "deviceDelSuccessTips": "Successfully Deleted Asset", "pointAddSuccessTips": "Successfully Added Test Point", "pointEditSuccessTips": "Successfully Modified Test Point", "pointDelSuccessTips": "Successfully Deleted Test Point", "deleteFailedTips": "Attempt to Delete Failed", "save_add": "Save/Add", "delete": "Delete", "deviceType": "Equipment Type", "deviceLevel": "Equipment Voltage Level", "add": "Add", "channelTypeAlarm": "Data Type", "alarmThreshold": "Alarm Threshold", "alarmRecoveryThreshold": "Alarm recovery Threshold", "alarmChannel": "Select Alarm Channel", "built_in_channel_1": "Built-in channel 1", "External_IO_module": "External IO Module", "not_associated": "Not Associated", "alarmExternalIOSN": "External IO Module Coding", "alarmExternalIOChannel": "External IO Module Channel", "wiringMode": "Wiring Mode", "alarmMode": "Alarm Mode", "alarmTime": "Alarm Timing", "alarmInterval": "Alarm Interval", "alarmDuration": "Alarm Duration", "normal_open": "Normally Open", "normal_close": "Normal Close", "continuous": "Continuous", "timing": "Timing", "interval": "Bay", "alarmSaveSuccess": "Alarm configuration saved successfully", "alarmDelSuccess": "Successfully Erased <PERSON><PERSON>'s Configuration", "deviceName": "Primary Equipment Name", "pointName": "Test Point Name", "testStation": "Tested Station", "device": "Primary Equipment", "pointList": "Test Point List", "sensorID": "Sensor ID", "sensorChannel": "Sensor Channel", "channelList": "Channel List", "showPoint": "Display Test Point", "fileSelect": "File Select", "selectPlease": "Select Please", "deviceList": "Device List", "updating": "Updating", "updatingFailed": "Failed To Update", "updatingCanceled": "Update Canceled", "updatingComplete": "Update Complete", "update": "Update", "sampleDate": "Acquisition Date", "sampleTime": "Acquisition Time", "pdMax": "<PERSON> Discharge Amplitude", "pdAvg": "Average Discharge Amplitude", "pdNum": "Number of Pulses", "acquisitionTime": "Acquisition Time", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Start Date", "endDate": "End Date", "refresh": "Refresh", "scanData": "Query", "sensorCode": "Sensor ID", "sensorTypeSet": "Sensor Type", "senorName": "Sensor Name", "aduAddress": "Sensor Address", "senorWorkMode": "Initiative Upload", "On": "On", "Off": "Off", "ADUMode": "Work Mode", "normalMode": "Maintenance Mode", "lowPowerMode": "Low Power Mode", "monitorMode": "Monitoring Mode", "artificialStateStartTime": "Manual Adjustment Of The State Start Time (Hour)", "artificialStateStartTimeTip": "Manual Adjustment Of The State Start Time (Hour):[0~23]", "artificialStateEndTime": "Manual Adjustment Of The State End Time (Hour)", "artificialStateEndTimeTip": "Manual Adjustment Of The State End Time (Hour):[0~23]", "artificialStateWakeUpSpace": "Manual Adjustment Of The State Wake-Up Period (Min)", "unartificialStateWakeUpSpace": "Automatic Adjustment Of The State Wake-Up Period (Min)", "isAutoChangeMode": "Automatic Mode Switch?", "monitorModeSampleSapce": "Monitoring Mode's Data Acquisition Period", "workGroup": "Workgroup Number", "warnTemp": "Temperature At Time Of Alarm(?)", "taskGroup": "Task Group Number", "commLinkType": "Communication Link Type", "commLinkPort": "Communication Port", "frequencyUnit": "Power Grid Frequency (Hz)", "numInGroup": "Group Number", "commSpeed": "Communication Rate", "commLoad": "Communication Channel", "sampleSpace": "Sampling Interval (min)", "sleepTime": "Power consumption strategy", "samleStartTime": "Initial Sampling Time (hourly)", "applyData": "Operation", "applyAllSensor": "Apply to the same ", "collectOnce": "Batch Acquisition", "collectOnceEnd": "Acquiring <PERSON> Batch Has Ended", "collectOnceProgress": "Batch Progress: {1} ({2})", "collectOnceProgress2": "Batch Progress [{1}] ({2})", "activeOnce": "Batch Application", "wakeUp": "Wake Up", "wakeUpInstrument": "Wake Up Sensor", "wakeUpInstrumentOnce": "<PERSON><PERSON>", "orderSend": "The Command Has Been Executed", "cleanData": "Clear Data", "sensorAddSuccess": "Sensor Was Added Successfully", "sensorEditSuccess": "Sensor Settings Modified Successfully", "sensorDelSuccess": "Sensor Was Removed Successfully", "cleanSensorData": "Clear Sensor Data", "getSensor": "Get the Sensor", "channelType": "Channel Type", "channelName": "Channel Name", "channelInfoSaveSuccess": "Channel Settings Saved Successfully", "bias": "Bias[dB]", "waveFilter": "Frequency Band Setting", "gainMode": "Gain Mode", "gain_unit": "<PERSON>ain[dB]", "samCycle": "Sampling Period", "samPointNum": "Sampling Numbers", "samRate": "Number of Samples Per Cycle", "ratio": "<PERSON><PERSON>", "channelPhase": "Phase", "mecLoopCurrentThred": "Coil Current Threshold [mA]", "mecMotorCurrentThred": "Motor Current Threshold [mA]", "mecSwitchState": "Switching Value Initial State", "awaysOpen": "Normally Open", "awaysClose": "Aways close", "mecBreakerType": "Circuit Breaker Mechanism Configuration", "oneMech": "Three-Phase Mechanical Transmission (One Sets of Operating Mechanisms) ", "threeMech": "Three-Phase Electrical Transmission (Three Sets of Operating Mechanisms) ", "mecMotorFunctionType": "Motor Work Type", "threePhaseAsynMotor": "Three Phase Asynchronous Motor", "onePhaseMotor": "Single Phase AC/DC Motor", "setCurrent": "Set the Current", "setAll": "Apply to the same", "showCoil": "Coil Current Spectrum", "showSwitch": "Switching Value Spectrum", "showMotor": "The Motor Current Spectrum", "showOrig": "Original Current Spectrum", "actionDate": "Action Date", "actionTime": "Actuation Time", "phaseA": "PhaseA", "phaseB": "PhaseB", "phaseC": "PhaseC", "coil_charge_time": "Coil Energization Time", "coil_cutout_time": "Coil Power Off Time", "max_current": "Maximum Current of Split Coil", "hit_time": "Trip Time of Detent During Opening", "subswitch_close_time": "Auxiliary Switch Switching Time", "a_close_time": "Phase A Closing Time", "a_close_coil_charge_time": "Phase A Coil Power On Time", "a_close_coil_cutout_time": "Phase A Coil Power Off Time", "a_close_max_current": "Maximum Current of Phase A Closing Coil", "a_close_hit_time": "Trip Time of Phase A Closing Detent", "a_close_subswitch_close_time": "Switching Time of Phase A Auxiliary Switch", "b_close_time": "B Phase Closing Time", "b_close_coil_charge_time": "Phase B Coil Power On Time", "b_close_coil_cutout_time": "Phase B Coil Power Off Time", "b_close_max_current": "Maximum Current of Phase B Closing Coil", "b_close_hit_time": "Trip Time of Phase B Closing Detent", "b_close_subswitch_close_time": "Switching Time of Phase B Auxiliary Switch", "c_close_time": "Phase C Closing Time", "c_close_coil_charge_time": "Phase C Coil Power On Time", "c_close_coil_cutout_time": "Phase C Coil Power Off Time", "c_close_max_current": "Maximum Current of Phase C Closing Coil", "c_close_hit_time": "Trip Time of Phase B Closing Detent", "c_close_subswitch_close_time": "Switching Time of Phase C Auxiliary Switch", "close_sync": "Close", "close_time": "Close time", "a_open_time": "A Phase Opening Time", "a_open_coil_charge_time": "Phase A Coil Power On Time", "a_open_coil_cutout_time": "Phase A Coil Power Off Time", "a_open_max_current": "Maximum Current of Phase A Opening Coil", "a_open_hit_time": "Trip Time of Phase A Opening Tweezer", "a_open_subswitch_close_time": "Switching Time of Phase A Auxiliary Switch", "b_open_time": "Phase B Open Time", "b_open_coil_charge_time": "Phase B Coil Power On Time", "b_open_coil_cutout_time": "Phase B Coil Power Off Time", "b_open_max_current": "Maximum Current of Phase B Opening Coil", "b_open_hit_time": "Trip Time of Phase B Opening Tweezer", "b_open_subswitch_close_time": "Switching Time of Phase B Auxiliary Switch", "c_open_time": "C Phase Opening Time", "c_open_coil_charge_time": "Phase C Coil Power On Time", "c_open_coil_cutout_time": "Phase C Coil Power Off Time", "c_open_max_current": "Maximum Current of Phase C Opening Coil", "c_open_hit_time": "Trip Time of Phase C Opening Tweezer", "c_open_subswitch_close_time": "Switching Time of Phase C Auxiliary Switch", "open_sync": "Synchronization of Open", "open_time": "Open Time", "a_twice_open_time": "Close Time of Phase A Coil During Second Opening", "a_twice_open_coil_charge_time": "Power On Time of Phase A Coil During Second Opening", "a_twice_open_coil_cutout_time": "Power Off Time of Phase A Coil During Second Opening", "a_twice_open_max_current": "Maximum Current of Phase A Coil During Second Opening", "a_twice_open_hit_time": "Trip Time of Phase A Detent During Second Opening", "a_twice_open_subswitch_close_time": "Phase A Auxiliary Switch Disconnection Time During Second Opening", "b_twice_open_time": "Close Time of Phase B Coil During Second Opening", "b_twice_open_coil_cutout_time": "Power Off Time of Phase B Coil During Second Opening", "b_twice_open_max_current": "Maximum Current of Phase B Coil During Second Opening", "b_twice_open_hit_time": "Trip Time of Phase B Detent During Second Opening", "b_twice_open_subswitch_close_time": "Phase B Auxiliary Switch Disconnection Time During Second Opening", "c_twice_open_time": "Phase C Second Opening Time", "c_twice_open_coil_cutout_time": "Power Off Time of Phase C Coil During Second Opening", "c_twice_open_max_current": "Maximum Current of Phase C Coil During Second Opening", "c_twice_open_hit_time": "Trip Time of Phase C Detent During Second Opening", "c_twice_open_subswitch_close_time": "Phase C Auxiliary Switch Disconnection Time During Second Opening", "twice_open_sync": "Second Opening Simultaneity", "twice_open_time_text": "Secondary Break-brake time", "a_switch_shot_time": "Phase A Metal Short Time", "b_switch_shot_time": "Phase B Metal Short Time", "c_switch_shot_time": "Phase C Metal Short Time", "a_switch_no_current_time": "Phase A with no current", "b_switch_no_current_time": "Phase B with no current time", "c_switch_no_current_time": "C Phase Dead Time", "motor_start_current": "Starting Current of Motor", "motor_max_current": "Maximum Current of Motor", "storage_time": "Energy Storage Time of Motor", "Chan_A_motor_start_current": "Starting Current of Phase A Motor", "Chan_A_motor_max_current": "Maximum Current Phase A Motor", "Chan_A_storage_time": "Energy Storage Time of Phase A Motor", "Chan_B_motor_start_current": "Starting Current of Phase B Motor", "Chan_B_motor_max_current": "Maximum Current Phase B Motor", "Chan_B_storage_time": "Energy Storage Time of Phase B Motor", "Chan_C_motor_start_current": "Starting Current of Phase C Motor", "Chan_C_motor_max_current": "Maximum Current Phase C Motor", "Chan_C_storage_time": "Energy Storage Time of Phase C Motor", "serialPort": "Serial Port", "baudRate": "Baud <PERSON>", "dataBit": "Data Bits", "stopBit": "Stop Bits", "checkBit": "Parity", "singleCollect": "Batch Acquisition", "sampling": "Sampling", "serverIP": "Server IP Address", "serverPort": "Server Port", "NTPserverIp": "NTP Server IP Address", "NTPserverPort": "NTP Server Port Number", "netType": "Network Type", "deviceIp": "Host IP Address", "subnetMask": "Subnet Mask", "gateway": "Default Gateway", "networkAPN": "Network Access to APN", "userName": "Username", "passWord": "Password", "deviceWorkGroup": "Host Workgroup Number", "frequency": "Power Grid Frequency", "pdSampleInterval": "PD-sampling Interval", "spaceSecond": "&nbsp;second", "meuSampleInterval": "MEU-Sampling Interval", "monitorAwakeTime": "<PERSON><PERSON><PERSON>", "monitorSleepSpace": "Awakening Time", "wakeStartTime": "Initial Awakening Time(hourly)", "intervalServer": "Upload Interval", "intervalMinus": "Sampling Interval", "startSampleTime": "Host Sampling Starting Time", "spacePoint": "&nbsp;point", "startSampleInterval": "Host Start Sampling Interval", "hour": "Hour", "poerOffSpace": "Turn off interval", "powerOnSpace": "Turn on interval", "dateRange": "Date Range", "synchronizing": "Synchronizing", "synchronize": "Synchronize", "amplitude": "Amplitude", "switch": "Switch", "copyRight": "©2020 PMDT Ltd.", "S1010Name": "Local Data Collecting Unit", "modbusCompanySelf": "PMDT", "logOut": "Exit", "logOutTitle": "Confirm Logout?", "logOutConfirm": "Exit?", "logOutTips": "Logged Out", "enterHost": "Please Enter The Data Collection Unit Name", "selectDevTip": "Please Select Device", "stopSyncDataTip": "You Are Currently Synchronizing Data, Do You Wish To Stop?", "modbusAddress": "ModBus Address", "modbusAddressCheckTip": "ModBus Address Range Is A Number Between 1~254", "deviceWorkGroupCheckTip": "Host Workgroup Number Range Is A Number Between 0~6", "emptyMonitorDatas": "Clear All Data Files", "emptyMonitorDatasTip": "Clear All The Historical Files Stored On The Data Collection Unit", "emptyMonitorDatasSuccess": "Files Cleared Successfully", "resetSoftSet": "Reset", "resetSoftSetTip": "Restore The Data Collection Unit To Default Factory Settings", "resetSoftSetSuccess": "Default Factory Settings Have been Successfully Restored", "continueConfirmTip": "This Operation Cannot Be Resumed. Are You Sure to Continue?", "bias_data": "Bias ranges from - 100 to 100", "back_ground_data": "Background values range from 0 to 100", "sam_point": "The range of sampling points per cycle is 20-2000", "sam_rate": "Sampling frequency ranges from 20 to 2000 per cycle", "smartSensorStatus": "Monitor of Sensor State", "upThreshold": "Upper Threshold", "lowerThreshold": "Lower Threshold", "changeThreshold": "Change Threshold", "changeThresholdLimit": "Threshold Settings support only one decimal", "upThresholdTip": "Upper Threshold Range", "lowerThresholdTip": "Lower Threshold Range", "changeThresholdTip": "Change Threshold Range", "upLowerThresholderrTip": "The lower threshold should not be greater than the upper threshold.", "monitorName": "Host Name", "monitorType": "Host Type", "MonitorTypeHanging": "LDCU-H", "MonitorType2U": "LDCU-U", "MonitorTypeCollectionNode": "MEU", "MonitorTypeLowPower": "Low Power Consumption", "devicePMSCode": "Equipment Code", "forceChange": "Force Change", "forceChangeSuccess": "Force Change Success", "currentVersion": "Current Version", "abnormalRecover": "Abnormal Self-recovery", "fromLabel": "Start Time", "toLabel": "End Time", "select": "Select", "sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "all": "All", "strong": "Strong", "weak": "Weak", "poor": "Poor", "min": "Min", "second": "Second", "uploadInterval": "Upload Interval(min)", "loginWelcome": "Welcome", "dateStartIsAfterDateEnd": "Start date is greater than end date, please select again", "maxCurrent": "<PERSON><PERSON> Current", "standMobus": "Sensor Manufacturer", "fWrnThreshold": "Warning Threshold", "fAlmThreshold": "Alarm Threshold", "regularDormancy": "Regular Do<PERSON>cy", "noDormancy": "No Dormancy", "soakingWater": "Soaking Water", "dry": "Dry", "normal": "Normal", "alarm": "Alarm", "HIKVIDEO_OUT": "Video Sensor (HIK)", "TEMPSB_OUT": "Temperature sensor (DS18B20)", "TEMP_HUM_JDRK_OUT": "Temperature and humidity sensor (RS-WS-N01-2)", "VIDEO": "Video Images", "instIPPattenTip": "Please enter the correct IP and port, such as 0.0.0.0:1", "water_immersion": "Real time monitoring - water immersion", "smoke_sensor": "Real time monitoring - fireworks", "video_sensor": "Real time monitoring - video", "config1": "Configuration 1", "config2": "Configuration 2", "lightGunCamera": "Visible Light Gun Machine", "lightBallCamera": "Visible Light Sphere Machine", "infraredGunCamera": "Infrared Binocular Bun Machine", "infraredBallCamera": "Infrared Binocular Ball Machine", "maxTemp": "Maximum Temperature", "maxTempPosition": "Maximum Temperature Position", "continuousAcquisitionTime": "Continuous Collection Time", "SF6_OUT": "SF6&O2 Sensor (WFS-S1P-SO)", "FAN_OUT": "Fan Controller", "LIGHT_OUT": "Lighting Controller", "NOISE_JDRK_OUT": "Noise Sensor (RS-WS-N01)", "IR_DETECTION_OUT": "Infrared Dual Detector", "SF6": "Sulfur Hexafluoride", "FAN": "Fan", "LIGHT": "Lighting", "NOISE_JDRK": "Noise", "IRDETECTION": "Infrared", "devIPError": "The IP is already occupied", "LoraFrequency": "Host Area", "AREA_CHINA": "China", "AREA_VIETNAM": "Vietnam (AS1)", "AREA_MALAYSIA": "Malaysia (AS1)", "AREA_EUROPE": "Europe", "AREA_US": "America", "AREA_INDONESIA": "Indonesia (AS2)", "AREA_INDIA": "India", "AREA_KOREA": "Korea", "AREA_CHINA_RSV": "China (backup)<2>", "noiseChartTitle": "Noise Curve", "main_station_params": "Master Station Configuration", "LeakageCurrent_IS": "Leakage Current Smart Sensor", "GrounddingCurrent_IS": "Grounding Current Intelligent Sensor", "TEMPWS_OUT": "Temperature and humidity sensor (WS-DMC100)", "FLOODWS_OUT": "Water immersion sensor (WS-DMC100)", "NOISEWS_OUT": "Noise Sensor (WS-DMC100)", "VibrationSY_OUT": "Rising Sun Vibration Sensor", "TEVPRPS_IS": "Transient Ground Voltage Smart Sensor", "SF6_IS": "SF6 Smart Sensor", "Decibels": "Decibel", "Noise": "Noise", "NOISE": "Noise", "LeakageCurrent": "Leakage Current", "GrounddingCurrent": "Grounding Current", "TEVPRPS": "Transient Earth Voltage PRPS", "continuSampTimeTip": "The continuous collection time is between {1} min and {2} min.", "continuSampTimeCompareTip": "The continuous collection time should be less than the sampling interval.", "activeBatchConfigsBegin": "Batch configuration of sensors begins", "tev_prps_chart": "Real time monitoring - Transient Ground Voltage PRPS Graph", "leakage_current_chart": "Real time monitoring - Leakage Current", "grounddingcurrent_chart": "Real time monitoring - Grounding Current", "noise_chart": "Real time monitoring - noise", "sf6_sensor": "Real time monitoring - SF6", "errorSenorUpdateSizeCheckFailed": "Firmware size verification failed", "errorSenorUpdateVersionCheckFailed": "Firmware version number verification failed", "errorSenorUpdateDeviceTypeCheckFailed": "Firmware device type verification failed", "errorSenorUpdateCRCCheckFailed": "Firmware CRC verification failed", "errorSenorUpdateFailed": "Sensor upgrade failed", "errorSenorUpdateConnectErr": "Abnormal firmware update communication", "groundCurrent": "Run Chart of Grounding Current", "groundCurrentA": "A-Phase Grounding Current", "groundCurrentB": "B-Phase Grounding Current", "groundCurrentC": "C-Phase Grounding Current", "grounddingCurrent": "Run Chart of Grounding Current", "grounddingCurrentA": "A-Phase Grounding Current", "grounddingCurrentB": "B-Phase Grounding Current", "grounddingCurrentC": "C-Phase Grounding Current", "sensorEditBegin": "Sensor modification start", "channelInfoSaveBegin": "Start of saving sensor channel information", "emptyMonitorDatasApply": "The application for clearing data has been submitted and is pending review", "unknown": "Unknown", "fault": "<PERSON><PERSON>", "lowPower": "Low Battery", "mediumPower": "Medium Power", "highPower": "High Battery", "slaveid": "Slave ID", "getLogFileError": "Failed to obtain sensor log file", "exportLogError": "The log file does not exist, export failed", "alertSyncProgress": "Alarm data synchronization update progress: {1}", "alertSyncProgress2": "Alarm data synchronization update progress [{1}]", "FIRMWARE_EXCEPTION": "Firmware Exception", "AD_INITIALIZATION_EXCEPTION": "AD Initialization Exception", "REFERENCE_VOLTAGE_EXCEPTION": "Abnormal Reference Voltage", "ONCHIP_FLASH_EXCEPTION": "On chip Flash exception", "OFFCHIP_FLASH_EXCEPTION": "Off chip Flash exception", "SYSTEM_PARAMETERS_EXCEPTION": "Abnormal System Parameters", "SAMPLE_PARAMETERS_EXCEPTION": "Abnormal Collection Parameters", "CALIBRATION_PARAMETERS_EXCEPTION": "Abnormal Calibration Parameters", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "System Parameter Abnormal Recovery", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "Abnormal Recovery of Collection Parameters", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "Abnormal Recovery of Calibration Parameters", "LORA_MODULE_EXCEPTION": "LoRa module abnormality", "PHASE_NUM": "Phase Number", "DISCONNECT_TIME": "Disconnect Time", "DATA_TOTAL_COUNT": "Total Data Volume", "ONLINE_RATE": "Online Rate", "aduInfoSetProgress": "Sensor Configuration: {1} ({2})", "aduInfoSetProgress2": "Sensor Configuration [{1}] ({2})", "aduInfoSetting": "Configuring <PERSON>sor", "aduInfoSetEnd": "Sensor configuration completed", "aduDataSample": "Data Acquisition", "aduInfoSet": "Sensor Configuration", "errorDataSampleRunning": "The sensor collection service is currently in progress. Please try again after completing it", "errorAduInfoSetRunning": "The sensor parameter setting service is currently in progress. Please try again after completing it", "SAMPLE_PERIOD": "Sampling Cycle Number", "SF6_Density": "SF6 Gas Density (P20)", "SF6_Temp": "SF6 Gas Temperature", "Device_env_Temp": "Equipment Ambient Temperature", "Alarm_Status": "Alarm Status Identification", "SF6_Alarm_Low_Pressure": "Low Pressure Alarm", "SF6_Alarm_Low_Voltage_Block": "Low Voltage Blocking", "SF6_Alarm_Over_Voltage": "Overvoltage Alarm", "AlarmType": "Alarm Type ", "AlarmData": "Alarm Content", "AlarmTime": "Alarm Time", "AlarmLevel": "Alarm Level", "AlarmStateWarning": "Early Warning", "WarningValue": "Warning Value", "AlarmValue": "Alarm Value", "SetSuccess": "Successfully Set", "AlarmDate": "Alarm Time", "AlarmConfirm": "Alarm Confirmation", "Confirm": "Confirm", "Confirmed": "Confirmed", "AllData": "All Data", "CheckManagerSponsor": "Initiator", "CheckManagerCheckType": "Audit Type", "CheckManagerDate": "Origination Date", "CheckManagerTarget": "Initiated by", "CheckManagerExtraInfo": "Additional Information", "CheckManagerResult": "Agree or not", "Refuse": "Refuse", "Agree": "Agree", "DataExport": "Data Export", "DataExportStep1": "Select the data to export", "DataExportStep1Title": "Export File Selection", "DataExportStep2": "Calculating data size", "DataExportStep2Title": "Calculate raw data size", "DataExportStep3": "Packaging compressed data", "DataExportStep3Title": "Packaging compressed data", "DataExportStep4": "Data compression completed", "DataExportStep4Title": "Compressed data completed", "DataExportCheckData": "Database [/media/data/database]", "DataExportCheckDataFile": "Data file [/media/data/datafile]", "DataExportCheckLog": "Log file [/media/data/log]", "DataExportCheckConfig": "Configuration file [/home/<USER>/config. xml]", "DataExportBegin": "Starting Export", "SelectAtLeastOneTips": "Select at least one item", "DataExportFileSizeError": "The file size exceeds 2000MB. Please use tools such as Winscp to export the data", "DataExportFileSizeZeroError": "The selected file size is 0", "DataExportCancelTips": "Export data cancelled", "DataExportDataCompressTips": "The original data size is {1} MB, and the estimated compression time is [{2} min {3} s]", "LogExportStep1": "Select sensor", "LogExportStep1Title": "Select sensor", "LogExportStep2": "Get Log File", "LogExportStep2Title": "Get Log Files", "LogExportStep3": "Get log file completed", "LogExportStep3Title": "Get log file completed", "LogExportStep4": "Pack and compress log files", "LogExportStep4Title": "Packaging compressed log files", "LogExportStep5": "Compression completed", "LogExportStep5Title": "Compression completed", "LogExportCancelTips": "Export log cancelled", "batteryVoltage": "Battery Voltage", "superCapvoltage": "Supercapacitor Voltage", "OverPressureThreshold": "Overvoltage threshold", "LowPressureThreshold": "Low voltage threshold", "ShutThreshold": "Lockout threshold", "PhysicalChannelType": "Physical channel type", "GasAbsPressure": "Gas absolute pressure", "GasGaugePressure": "Gas gauge pressure", "CameraType": "Camera Type", "DEFAULT_CHECK_Tips": "The numbering format is m: n, such as 1:100, where m ranges from [1 to 255] and n ranges from [0 to 65535]", "VibrationSY_OUT_CHECK_Tips": "The numbering format is address: modbus: id, such as 1:0:1917, where the address range is [1-255] and modbus/id is [0-65535]", "NOISEWS_OUT_CHECK_Tips": "The numbering format is ip: x: y, such as *********:1321:4512, where the ip format [xxx. xxx. xxx. xxx] and x/y are strings of length 4", "IR_DETECTION_OUT_IO_CHECK_Tips": "The numbering format is ip: port: ss: se: cs: ce, such as 1:100, where ip format [xxx. xxx. xxx. xxx] port range [0-65535] ss range [0-255], se range 0-255], cs range 0-255], ce range 0-255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "The numbering format is ip: port: x: y, such as 1:100, where ip format [xxx. xxx. xxx. xxx] port range [0-65535] x range [1-255], y range [0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "The numbering format is m: n, such as 1:100, where m ranges from [1 to 255] and n ranges from [0 to 65535]", "fWrnThreshold_CHECK_Tips": "Note that the threshold range is [10-1000] mA", "fAlmThreshold_CHECK_1_Tips": "The alarm threshold range is [50-5000] mA", "fAlmThreshold_CHECK_2_Tips": "The alarm threshold should be greater than the attention threshold", "AuditContent": "Audit Content", "OPTime": "Operation Time", "Executed": "Implement", "UserManager": "User Management ", "SystemManagement": "System Management", "BusinessManagement": "Business Management", "LanguageChange": "Language Switching", "AlarmManagement": "Alarm Management", "DataOperation": "Data Operation", "BackupRecovery": "Backup Recovery", "AddUser": "Add User", "AddUserCheck": "Add User Audit", "UserRightSet": "User permission settings", "UserRightSetCheck": "User permission setting review", "DeleteUser": "delete user", "DeleteUserConfirm": "Delete User Confirmation", "FreezeUser": "Freeze Users", "FreezeUserConfirm": "Freeze User Confirmation", "UnlockUser": "Unlock User", "UnlockUserConfirm": "Unlock User Confirmation", "FileStationSet": "Profile (Site) Settings", "FileDeviceSet": "Archive (Equipment) Settings", "SenserSet": "Sensor Settings", "AlarmSet": "Alarm Settings", "SavePointSet": "Save measurement point settings", "DelPointSet": "Delete measurement point settings", "SingleSample": "Single Acquisition", "DataBackup": "Data Backup", "DataRecovery": "Data Recovery", "ClearDataCheck": "Clear Data Audit", "RestoreFactorySettingsReview": "Factory reset audit", "SaveMainStation": "Save Master Station", "DataView": "Data Viewing", "DataSync": "Data Synchronization", "RightSet": "Permission Configuration", "GetUserList": "Obtain user list", "AuditData": "Audit Data", "AuditPermissions": "Audit Permissions", "MainStationSet": "Master Station Settings", "ChangePasswordSelfOnly": "Change password (for this user only)", "ChangePasswordForNormal": "Modifying a regular user password", "ChangeUserRight": "Modify/Set User Permissions", "ChangePassword": "Change Password", "ChangeLoginInfo": "Modify login information", "UserStatus": "User Status", "UserLanguage": "User Language", "HasLogin": "Have you logged in before", "RoleType": "Role Type", "IsPassTimeout": "Is the password timeout", "AuditsManagement": "Audit Management", "SensorOperation": "Sensor Operate", "SensorLogExport": "Log Export", "SensorAlarmDataSync": "Alarm Data Synchronize", "ViewSensorAlarmData": "View Alarm Data", "Block": "Freeze", "BlockPendingReview": "Freeze - Pending Review", "UnlockPendingReview": "Unfreeze - Pending Review", "DeletePendingReview": "Delete - Pending Review", "AddUserPendingReview": "New - Pending Review", "ChangePassPendingReview": "Change Password - Pending Review", "ChangeRightPendingReview": "Modify Permissions - Pending Review", "abnormal": "Abnormal", "DataQueryNotSupported": "Currently, this front-end type's historical data query is not supported", "DeviceCodeExists": "The device name or device code already exists", "OutputSwitchConfig": "Configuration of outgoing switch", "OutputSwitch": "Outgoing Switch", "MainStationAuxRtuID": "Auxiliary control RtuID", "MainStationIedRtuID": "Collect RtuID", "MainstationParams1": "Main station parameter 1", "MainstationParams2": "Main station parameter 2", "MainstationInterface1": "Master Station 1 Interface", "MainstationInterface2": "Master Station 2 Interface", "Port": "Port", "IPAddress": "IP address", "EnterUriTips": "Please enter the path URI", "ConnectUserName": "Communication Username", "EnterConnectUserNameTips": "Please enter the communication username", "ConnectPass": "Communication Password", "EnterConnectPassTips": "Please enter the communication password", "DataSubmissionInterval": "Data Upload Interval", "EnderDataSBIntervalTips": "Please enter the data submission interval", "HeartbeatInterval": "Heartbeat Interval ", "EnterHertbeatIntervalTips": "Please enter the heartbeat interval", "ConnectStatus": "Connection Status", "Reset": "Reset", "Submit": "Submit", "RtuidRepeatTips": "Auxiliary control RtuID and collection RtuID cannot be the same", "DataSubmissionIntervalTips": "The data submission interval cannot be less than 60", "HeartbeatIntervalTips": "Heartbeat interval cannot be less than 60", "MainstationParamsSaveSuccess": "Master station parameters saved successfully", "MainstationParamsSaveFailed": "Failed to save master station parameters", "OutSwitchSaveSuccess": "Successfully saved the configuration of the outgoing switch", "OutSwitchSaveFailed": "Failed to save the configuration of the outgoing switch", "StateLinkError": "Link Exception", "StateConnectError": "Communication Exception", "StateConnecting": "Connecting", "CannotConnectServerMSG": "Not connected to service, retry after 10 seconds", "LogOutOfTimeMSG": "Login timeout, please log in again", "PasswordChangedMSG": "Password changed successfully, please log in again", "ServerDisConnectMSG": "Service disconnected, please log in again", "GroupNumberScan": "Group Number Scan", "OldPassword": "Old Password", "OldPasswordError": "Old Password Error", "NewPassword": "New Password", "RepeatNewPassword": "Re-enter New Password", "OldPasswordPlaceholder": "Enter Old Password", "PasswordCompareTips": "The two new passwords entered must match.", "PasswordFormatTipes": "New Password Format: At least 8 characters, at least 1 uppercase letter, 1 lowercase letter, 1 number, and 1 special character.", "AlarmData1000": "Firmware CRC calculation result does not match firmware's own CRC check result.", "AlarmData1001": "AD initialization error, AD cannot work normally.", "AlarmData1002": "Battery voltage is {1}V.", "AlarmData1003": "Internal Flash read-write error.", "AlarmData1004": "External Flash read-write error.", "AlarmData1005": "Parameters were not read from storage area.", "AlarmData1006": "Parameter CRC check failed.", "AlarmData1007": "Read parameter exceeds legal range.", "AlarmData1008": "Parameters could be correctly read and they are within legal range.", "AlarmData1009": "Initialization return value error.", "NoiseReductionMode": "Noise Reduction Mode", "Manual": "Manual", "Automatic": "Automatic", "SensorThreshold": "<PERSON><PERSON><PERSON><PERSON> (%)", "AccessKey": "Access Key", "ControlMode": "Control Mode", "ConventionalSwitch": "Conventional Switch", "InchingSwitch": "Inching Switch", "SensorIOPlaceHolder": "IP address:port:status bit 1:status bit 2:control bit 1:control bit 2", "TEMPSB_OUT_ID_TIPS": "Number format is m:n:o, e.g., 1:1:100, where m ranges from [1-255], n ranges from [0-255], o ranges from [0-65535].", "OUT_NORMAL_ID_TIPS": "Number format is m:n, e.g., 1:100, where m ranges from [1-255], n ranges from [0-65535]", "SlaveIDRange": "Slave ID Range", "inputNameAndPsw": "Enter Username and Password", "PreStartCollect": "Prepare to Start Collection, Please Wait", "RefreshComplet": "Refresh Complete", "State": "Status", "DeleteSuccess": "Delete Success", "DeleteFailed": "Delete Failure", "DeleteUserConfirmTips": "Confirm Deletion of User [{1}]?", "FileSaveFailed": "File save failed", "GetConnectFailed": "Getting connection link failed", "WakeUpFailed": "Wakeup Failed", "ConnectFailed": "Connection Failed", "CollectDataFailed": "Collection data failed", "UnpackFailed": "Decompression failed, please check file format", "ConfigFileError": "Package does not contain data/configuration files, file type incorrect", "ProgramError": "Program Exception", "NoData": "No Data", "SensorOptRepeatTips": "The following sensors have duplicate operations, please wait for operation completion:", "StateOpen": "Open", "StateClose": "Closed", "Unknown": "Unknown", "ExportDataTimeRangeLimitTips": "Maximum export data time span is one week", "UserManagerAllowLoginTimeStart": "'Allowed login time - start (on the hour)", "UserManagerAllowLoginTimeEnd": "'Allowed login time - end (on the hour)", "UserManagerAllowLoginIPStart": "'Permitted login IP - starting point", "UserManagerAllowLoginIPEnd": "'Permitted login IP - ending point", "UserManagerErrorCount": "Number of error attempts", "UserManagerMaxErrorCount": "Maximum number of attempts", "UserManagerPasswordDuration": "Password validity duration", "UserManagerPasswordEffective": "Password effective date", "Day": "Days", "UserManagerInputUserNameTips": "Please enter username", "UserManagerAddUser": "Add new user", "AddSuccess": "Addition successful", "AddFailed": "Addition failed", "DataImport": "Data Import", "Upload": "Upload", "Uploading": "Uploading", "UploadingMsg": "Uploading file. Do not perform other operations.", "UploadProgress": "Upload Progress", "ImportConfigConfirmTips": "Executing data import requires restarting. Confirm to continue?", "UploadSuccess": "File upload successful", "UploadFailed": "File upload failed", "DataImportSuccess": "Data imported successfully", "DataImportSuccessTips": "Data import successful. You will be logged out and program will restart. Please refresh and try again later.", "SensorGroupNum": "Sensor Group Number", "SensorScanState": "Scan State", "Complete": "Completed", "Failed": "Failed", "ScanCancelMsg": "<PERSON><PERSON>", "GroupNumScaning": "Scanning Group Numbers", "SensorGroupNumScan": "Sensor Group Number Scan", "SensorScanID": "Sensor IDs to be scanned", "GroupNumRange": "Group Number Range", "PreSensorScan": "Preparing to scan", "SensorScan": "<PERSON><PERSON>", "SensorScanResult": "Scan Results", "SensorGroupNumScanEnd": "Sensor group number scan complete", "SensorGroupNumScanCancelTips": "Do you want to cancel the sensor group number scan?", "SensorGroupNumScanSelectTips": "Please select the sensors to be scanned", "GroupNumStartTips": "Starting group number cannot exceed end group number", "GroupNumEndTips": "Ending group number cannot be less than starting group number", "Apply": "Apply", "Date": "Date", "SearchType": "Search Type", "ActionType": "Action Type", "ConfirmChange": "Do you want to switch?", "None": "None", "ADUProgressInvalid": "Invalid Progress", "ADUProgressBEGIN": "<PERSON><PERSON>", "ADUProgressWAKEUP": "Wake Up", "ADUProgressCONNECT": "Connect", "ADUProgressREGULATE_SPEED": "Regulate Speed", "ADUProgressSAMPLE_DATA": "Sampling Data", "ADUProgressSUCCESS": "Success", "ADUProgressFAILED": "Failure", "ADUProgressEND": "End", "ADUProgressWRITE_SYSTEM_PARAM": "Writing system parameters", "ADUProgressWRITE_WORKER_MODEL_PARAM": "Writing worker model parameters", "ADUProgressWRITE_ADU_PARAM": "Writing sensor parameters", "ADUProgressWRITE_CHANNEL_PARAM": "Writing channel parameters", "ADUProgressREAD_MONITOR_INFO": "Reading device information", "ADUProgressREAD_SYSTEM_PARAM": "Reading system parameters", "ADUProgressREAD_CHANNEL_PARAM": "Reading channel parameters", "ADUPRogressWRITE_SYSTEM_PARAM_FAILED": "Writing system parameters failed", "ADUPRogressWRITE_WORKER_MODEL_PARAM_FAILED": "Writing worker model parameters failed", "ADUPRogressWRITE_ADU_PARAM_FAILED": "Writing sensor parameters failed", "ADUPRogressWRITE_CHANNEL_PARAM_FAILED": "Writing channel parameters failed", "ADUPRogressREAD_MONITOR_INFO_FAILED": "Reading device information failed", "ADUPRogressREAD_SYSTEM_PARAM_FAILED": "Reading system parameters failed", "ADUPRogressREAD_CHANNEL_PARAM_FAILED": "Reading channel parameters failed", "ADUPRogressCopyIDTips": "ID [{1}] copied", "ADUPRogressMsgIDTips": "Progress Message [{1}]", "CopyID": "Copy ID", "Wait": "Waiting", "SwitchToLine": "Switch to Line", "SwitchToBar": "Switch to Bar", "ManstationParams1": "Main Station Parameter 1", "ManstationParams2": "Main Station Parameter 2", "ConnectionProtocolConfiguration": "Connection Protocol Configuration", "CommunicatProtocol": "Communication Protocol", "BusiProtocol": "Business Protocol", "HeartbeatTransmissionInterval": "Heartbeat Transmission Interval", "DataUploadInterval": "Data Upload Interval", "Remark": "Remarks", "Enable": "Enabled", "Deactivate": "Disabled", "Edit": "Edit", "Details": "Details", "timeoutT0": "Timeout Time [t0]", "timeoutT1": "Timeout Time [t1]", "timeoutT2": "Timeout Time [t2]", "timeoutT3": "Timeout Time [t3]", "MaxNumOfClientConnect": "Maximum Number of Client Connections", "MMSprofile": "MMS Profile", "StartupProfile": "Startup Profile", "UsermapProfile": "Usermap Profile", "CIDfile": "CID Profile", "WorkerAddress": "Worker Address", "UnitConversion": "Unit Conversion", "PDStarsProtocol": "PDStars Protocol", "DistributionAutoProtocol": "Distribution Auto Protocol", "SGCCProtocol": "SGCC Protocol", "SouthPowerGridProtocol": "South Power Grid Protocol", "GuangZhouDistributionAutoProtocol": "GuangZhou Distribution Auto Protocol", "IDExists": "ID already exists", "VolumeRatio": "Volume Ratio", "ChartConfig": "Chart Configuration", "Measurement": "Measured Value", "SENSOR_TIMEOUT_COUNT": "Sensor Offline Determination Count", "OUTWARD_CONFIG_ENABLE_STATE": "Config Status", "OUTWARD_CONFIG_ENABLED": "Enabled", "OUTWARD_CONFIG_DISABLED": "Disabled ", "OUTWARD_CONFIG_ENABLING": "<PERSON><PERSON><PERSON>", "SENSOR_TIMEOUT_DISABLING": "Disabling", "ERROR_NO_CONFIG": "Configuration does not exist", "ERROR_INPUT_PARAMS": "Input parameter error", "ERROR_INTEGER": "Please enter an integer", "ERROR_TIP_RANGE": "Value Range [{1}~{2}]", "ERROR_IP_FORMAT": "The IP format is incorrect", "ERROR_FILE_NAME": "The file name must be {1}", "ERROR_FILE_SUFFIX": "The file suffix must be {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "The file must be less than {1} MB", "ERROR_T2_T1": "T2 must be less than T1", "LENGTH_COMM_LIMIT": "Please note, this field cannot exceed {1} characters.", "MANU_FAST_SYNC_DATA": "Quick Sync", "SYNC_DATA_STATE_LIST": "Sensor Sync Status List", "LAST_SYNC_DATE_RANGE": "Last Sync Date Range", "NoError": "No Error", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "Sync Timeout", "SYNC_DATA_ERROR_DISCONNECT": "Disconnect", "SYNC_DATA_STATUS_WAITING": "Waiting for Connection", "SYNC_DATA_STATUS_CONNECTED": "Connected", "SYNC_DATA_STATUS_SYNCING": "Syncing", "SYNC_DATA_STATUS_SUCCESS": "Sync Success", "SYNC_DATA_STATUS_FAILED": "Sync Failed", "SYNC_DATA_STATUS_CANCELED": "Sync Canceled", "Today": "Today", "Yesterday": "Yesterday", "LAST_7_DAYS": "Last 7 Days", "LAST_30_DAYS": "Last 30 Days", "THIS_MONTH": "This Month", "LAST_MONTH": "Last Month", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Start Communication Service Failed", "FastSyncDataCancelTips": "Are you sure to cancel data synchronization?", "FastSyncDataSelectTips": "Please select the sensors for data synchronization", "Band-pass": "Band Pass", "aeWaveChartTitle": "AE Waveform Spectrum", "aePhaseChartTitle": "AE Phase Spectrum", "aeFlyChartTitle": "AE Fly Spectrum", "LOCAL_PARAMS_TIME": "Time", "LOCAL_CHART_COLORDENSITY": "Color Density", "openTime": "Opent Time", "closeTime": "Close Time", "triggerUnit": "Trigger Amplitude[μV]", "openTimeUnit": "Open Time[μs]", "closeTimeUnit": "Close Time[μs]", "triggerUnitTips": "Trigger amplitude range: [{1},{2}]μV", "TOP_DISCHARGE_AMPLITUDE": "TOP3 Discharge Amplitude", "WS_901_ERROR_TIPS": "Command push service is registering, please retry after 2s", "dataType": "Data Type", "startTime": "Start Time", "endTime": "End Time", "dataDetails": "Data Details", "refreshTrend": "Refresh Trend Chart", "sf6TrendChart": "SF6 Data Trend", "trendChart": "Trend Chart", "noTrendData": "No trend data available", "loadTrendDataFailed": "Failed to load trend data", "startTimeAfterEndTime": "Start time cannot be later than end time", "SF6_Pressure": "SF6 Gas Pressure", "SF6_Temperature": "SF6 Gas Temperature", "SF6_EnvTemperature": "Equipment Ambient Temperature", "SF6_SuperCapVoltage": "Supercapacitor Voltage"}