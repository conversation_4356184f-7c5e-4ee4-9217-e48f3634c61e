//初始化树状图
function initDevTree() {
    var data = null;
    var zNodes = [];

    var setting = {
        data: {
            simpleData: {
                idKey: "id",
                pIdKey: "parentId",
                name: "name",
                enable: true
            }
        },
        async: {
            enable: true,
            url: serverUrl + GET_HISTORY_TREE_LIST,
            type: "GET",
            dataFilter: ajaxDataFilterList,
            autoParam: ["id", "parentId", "type"]
        },
        callback: {
            onExpand: zTreeOnExpandDMWSet,
            onAsyncSuccess: zTreeOnAsyncSuccess,
            onClick: zTreeOnclickDMWSet
        }
    };

    poll('GET', 'GetStation', data, function (text) {
        var respData = text.result;
        if (respData.stationName === null || respData.stationName.length === 0)
            return;
        var tempdata = {
            id: respData.id,
            parentId: 0,
            name: respData.stationName,
            open: true,
            type: 2,
            iconOpen: "./plugins/zTree/css/zTreeStyle/img/diy/1_open.png",
            iconClose: "./plugins/zTree/css/zTreeStyle/img/diy/1_close.png",
            isParent: true
        };
        zNodes.push(tempdata);
        selectedHistoryArchives.substation = respData.id;
        // $("#station").append($("<option />", {"value": "" + 0 + ""}).append(respData.stationName));
        $.fn.zTree.init($("#dev_tree"), setting, zNodes);
        var node = {
            substation: selectedHistoryArchives.substation,
            device: selectedHistoryArchives.device,
            testPoint: selectedHistoryArchives.testPoint,
            channelType: selectedHistoryArchives.channelType
        }
        // debugger;
        selectNodeDMW(node);
    });
}


function ajaxDataFilterList(treeId, parentNode, responseData) {
    // debugger;
    if (parentNode.level === 0) {
        for (var i = 0; i < responseData.result.length; i++) {
            responseData.result[i].type = 3;
        }
        return responseData.result;
    }
    else if (parentNode.level === 1) {
        for (var i = 0; i < responseData.result.length; i++) {
            responseData.result[i].type = 4;
            if (!responseData.result[i].isParent) {
                responseData.result[i].name = responseData.result[i].pointName;
            }
            if (validateStrLen(responseData.result[i].name) == 0) {
                responseData.result[i].name = responseData.result[i].pointName;
            }
        }
        return responseData.result;
    }
    else if (parentNode.level === 2) {
        for (var i = 0; i < responseData.result.length; i++) {
            if (validateStrLen(responseData.result[i].name) != 0) {
                responseData.result[i].name = getI18nName(responseData.result[i].name, responseData.result[i].name);
            }
        }
        return responseData.result;
    }
}


//树状菜单节点操作
var isExpendingSelect = false;
function selectNodeDMW(nodeItem) {
    // debugger;
    // var currentTreeNodeType = selectedHistoryArchives.treeNodeType;
    isExpendingSelect = true;
    var treeObj = $.fn.zTree.getZTreeObj('dev_tree');
    var typeNodeCallBack = function () {
        var node = treeObj.getNodeByParam('id', nodeItem.channelType);
        virtualClickNode(treeObj, node);
    }
    var substationNode = treeObj.getNodeByParam('id', nodeItem.substation);
    var deviceNode = treeObj.getNodeByParam('id', nodeItem.device);
    //若变电站层级未展开
    if (!substationNode.open) {
        substationNode.callBack = function () {
            // debugger;
            //添加zTree展开异步回调监听
            deviceNode = treeObj.getNodeByParam('id', nodeItem.device);
            if (deviceNode) {
                deviceNode.callBack = function () {
                    // debugger;
                    var pointNode = treeObj.getNodeByParam('id', nodeItem.testPoint);
                    if (pointNode) {
                        pointNode.callBack = function () {
                            // debugger;
                            var channelNode = treeObj.getNodeByParam('id', nodeItem.channelType);
                            if (channelNode) {
                                channelNode.callBack = function () {
                                    // debugger;
                                    isExpendingSelect = false;
                                }
                                virtualClickNode(treeObj, channelNode);
                                isExpendingSelect = false;
                            } else {
                                throw "列表树加载中";
                            }
                            isExpendingSelect = false;
                        }
                        virtualClickNode(treeObj, pointNode);
                    } else {
                        throw "列表树加载中";
                    }
                }
                virtualClickNode(treeObj, deviceNode);
            } else {
                throw "列表树加载中";
            }
            // virtualClickNode(treeObj, deviceNode);
        }
        virtualClickNode(treeObj, substationNode);
    } else {
        //若设备层级未展开
        if (deviceNode == undefined || !deviceNode.open) {
            deviceNode = treeObj.getNodeByParam('id', nodeItem.device);
            if (deviceNode) {
                deviceNode.callBack = function () {
                    var pointNode = treeObj.getNodeByParam('id', nodeItem.testPoint);
                    if (pointNode) {
                        pointNode.callBack = function () {
                            var channelNode = treeObj.getNodeByParam('id', nodeItem.channelType);
                            if (channelNode) {
                                channelNode.callBack = function () {
                                    isExpendingSelect = false;
                                }
                                virtualClickNode(treeObj, channelNode);
                                isExpendingSelect = false;
                            } else {
                                throw "列表树加载中";
                            }
                            isExpendingSelect = false;
                        };
                        virtualClickNode(treeObj, pointNode);
                    } else {
                        throw "列表树加载中";
                    }
                }
                virtualClickNode(treeObj, deviceNode);
            } else {
                throw "列表树加载中";
            }
            // deviceNode.callBack = typeNodeCallBack;
            // virtualClickNode(treeObj, deviceNode);
        }
    }
}

/**
 * 导航树，模拟点击树节点
 * @param {*} treeObj
 * @param {*} node
 * @param {*} flag
 */
function virtualClickNode(treeObj, node, flag) {
    var clickAble = flag;
    if (clickAble == undefined) {
        clickAble = true;
    }
    treeObj.selectNode(node);//选中指定节点
    if (node != undefined && !node.open) {
        treeObj.expandNode(node);//展开指定节点
    }
    if (clickAble) {
        treeObj.setting.callback.onClick(null, treeObj.setting.treeId, node);//触发函数
    }
}

//节点单击，回调函数
function zTreeOnclickDMWSet(event, treeId, treeNode) {
    // debugger;
    var treeObj = $.fn.zTree.getZTreeObj('dev_tree');
    treeObj.selectNode(treeNode);//选中指定节点
    zTreeOnExpandDMWSet(event, treeId, treeNode);
    if (treeNode != undefined) {
        if (!treeNode.open) {
            treeObj.expandNode(treeNode);//展开指定节点
        }
    }
}
/**
 * 导航树，异步加载，成功后回调
 * @param {*} event
 * @param {*} treeId
 * @param {*} treeNode
 * @param {*} msg
 */
function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
    if (treeNode != undefined && treeNode.callBack != undefined) {
        treeNode.callBack();
        treeNode.callBack = undefined;
    }
}
//节点展开，回调函数
function zTreeOnExpandDMWSet(event, treeId, treeNode) {
    // debugger;
    if (treeNode == undefined) {
        return;
    }
    //节点类型
    var treeNodeType = treeNode.level;
    selectedHistoryArchives.treeNodeType = treeNodeType;
    //根据节点类型不同，给全局变量selectedHistoryArchives赋值。
    if (treeNodeType == '0') {
        // selectedHistoryArchives.substation = treeNode.id;
        // selectedHistoryArchives.device = '';
        // selectedHistoryArchives.testPoint = '';
    } else if (treeNodeType == '1') { //一次设备
        // selectedHistoryArchives.substation = treeNode.parentId;
        // selectedHistoryArchives.device = treeNode.id;
    } else if (treeNodeType == '2') { //测点
        // selectedHistoryArchives.substation = treeNode.getParentNode().parentId;
        // selectedHistoryArchives.device = treeNode.parentId;
        // selectedHistoryArchives.testPoint = treeNode.id;
    } else {
        // debugger;
        selectedHistoryArchives.substation = treeNode.getParentNode().getParentNode().parentId;
        selectedHistoryArchives.device = treeNode.getParentNode().parentId;
        selectedHistoryArchives.testPoint = treeNode.parentId;
        selectedHistoryArchives.channelType = treeNode.id;
        if (checkDMWChannelType(treeNode.id)) {
            if (currentState == TREND) {
                loadDMWCharts();
            } else {
                refeshHistoryTable();
            }
        } else {
            layer.open({
                title: getI18nName('tips')
                , content: getI18nName('DataQueryNotSupported')
            });
        }
    }
    if (treeNode.callBack != undefined) {
        treeNode.callBack();
        treeNode.callBack = undefined;
    }
}

function getHistoryBeginDate() {
    var dateArray = $('#da-dateRange').val().split('-');
    var beginDate = dateArray[0].trim();
    return beginDate;
}
function getHistoryEndDate() {
    var dateArray = $('#da-dateRange').val().split('-');
    var endDate = dateArray[1].trim();
    return endDate;
}