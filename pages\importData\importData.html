<style>
    .import-data-content {
        background: white;
        height: calc(100% - 50px);
        width: calc(100% - 40px);
        margin: 20px;
        border-radius: 5px;
    }

    .import-data-content .header {
        width: 100%;
        height: 50px;
        border-bottom: 1px solid lightskyblue;
        padding: 15px;
        color: #5098c5;
    }

    .import-data-config {
        display: flex;
        padding-left: 10px;
        z-index: 10;
        width: 100%;
    }

    .import-data-config .ck-content {
        margin: 5px 8px;
        display: flex;
        z-index: 10;
        position: relative;
    }

    .import-data-config .ck-content label {
        z-index: 10;
    }

    .import-data-config .ck-content input {
        margin-left: 5px;
        z-index: 10;
        position: absolute;
        right: 5px;
    }

    .import-data-content .box,
    .import-data-content .container {
        clear: both;
        position: relative;
    }

    .import-data-content pre {
        background: #f2f2f2;
        color: #000;
        width: 800px;
        margin: 0 auto;
        border: 1px solid #dee0e1;
        -webkit-box-shadow: inset 1px 1px 2px #ddd;
        -moz-box-shadow: inset 1px 1px 2px #ddd;
        box-shadow: inset 1px 1px 2px #ddd;
    }

    .import-data-content em {
        color: red;
    }

    .import-data-content .block {
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 3;
    }

    .import-data-btn-gray {
        background: lightgray;
        border-color: lightgray;
    }

</style>
<div style="height: 100%;">
    <div class="import-data-content">
        <div class="header">
            <div data-i18n="DataImport">数据导入</div>
        </div>
        <div class="input-group file-caption-main" style="margin: 2rem;width: 30%;min-width: 400px;">
            <div id="import-data-input" tabindex="500" class="form-control file-caption  kv-fileinput-caption">
                <div style="color: #999" data-i18n="selectPlease">请选择</div>
            </div>
            <div class="input-group-btn" >
                <div id="import-data-input-btn" tabindex="500" class="btn btn-primary btn-file">
                    <i class="glyphicon glyphicon-folder-open"></i>&nbsp; <span class="hidden-xs"
                        data-i18n="selectPlease">请选择</span>
                    <input id="import-data-upload" type="file" class="file" data-upload-url="#">
                </div>
            </div>
            
        </div>
        <div class="import-data-config">
            <button id="beginImportData" class="btn btn-primary table-item-btn" style="margin-left: 1rem;width: 15rem;height: 3.95rem;"
                onclick="requestImportData()" data-i18n="Upload">上传</button>
            <div id="import-da-progress" class="progress-group hide" style="width: 37rem;margin-left: 3rem;">
                <span class="progress-text" data-i18n="Uploading">正在上传</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- <div id="import-data-loading" style="display: none;">
                <h2>正在解压缩和校验数据...</h2>
                <img src="./dist/img/loading1.gif" style="margin-left: 8px;"></img>
            </div> -->
        </div>
        <div id="import-upload-tips" class="testarea hide" style="padding-left: 2rem;">
            <h3 style="color:red" data-i18n="UploadingMsg">正在上传文件，请勿进行其他操作</h3>
        </div>
    </div>
</div>
<script>
    //文件上传组件
    $('#import-data-upload').change(function () {
        var files = $(this)[0].files;
        var types = [".tar.gz"];
        inputFile(files, types,function (params) {
            // debugger
        },1024 * 1024 * 200)
    });

    function showOnUplading(flag){
        if(flag){
            $('#beginImportData').attr('disabled','disabled');
            $('#import-data-upload').attr('disabled','disabled');
            $('#import-data-input').attr('disabled','disabled');
            $('#import-data-input-btn').addClass('import-data-btn-gray');
            $('#import-upload-tips').removeClass('hide');
        }else{
            $('#beginImportData').removeAttr('disabled');
            $('#import-data-upload').removeAttr('disabled');
            $('#import-data-input').removeAttr('disabled');
            $('#import-data-input-btn').removeClass('import-data-btn-gray');
            $('#import-upload-tips').addClass('hide');
        }
    }


    function importDataProgress(value,progress) {
        var progress = $('#import-da-progress');
        var progressBar = $('#import-da-progress>.progress>.progress-bar');
        var progressText = $('#import-da-progress>.progress-text');
        var progressNum = $('#import-da-progress>.progress-number');

        progress.removeClass("hide");
        progressBar.css({
            "width": value + "%"
        });
        progressText.text(stringFormat.format(getI18nName('UploadProgress'), progress));
        progressNum.text(value + "%");
    }

    const chunkSize = 1024 * 1024 * 3;
    // const chunkSize = 1024;
    function requestImportData() {
        var fileEle = $("#import-data-upload")[0];
        // debugger
        if (fileEle != undefined && fileEle.value != "") {
            showOnUplading(true);
            function beginFileImport(){
                layer.confirm(getI18nName('ImportConfigConfirmTips'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('ok'), getI18nName('cancel')]
                }, function (index) {
                    var message = {
                    "errorCode": 0,
                    "errormsg": getI18nName('success'),
                    "result": {
                        "ImportData": {
                            "beginImportData": true,
                            }
                        },
                        "success": "true"
                    }
                    webSocketSend(message);
                    layer.close(index);
                }, function () {

                });
            }

            var file = fileEle.files[0];
            // var tempUploadConfigDataChunkCount = file.size/chunkSize;
            /* if(file.size/chunkSize<1){
                tempUploadConfigDataChunkCount = 1;
            } else if(file.size%chunkSize!=0){
                tempUploadConfigDataChunkCount = parseInt(tempUploadConfigDataChunkCount)+1;
            } else {
                tempUploadConfigDataChunkCount = parseInt(tempUploadConfigDataChunkCount);
            } */
            var tempUploadConfigDataChunkCount = Math.ceil(file.size / chunkSize);
            function uploadFileChunk(index){
                let fileNameArr = file.name.split(".");
                let fname = fileNameArr[0]+'.'+fileNameArr[1];
                let fext = fileNameArr[2];
                let start = index * chunkSize;
                let end = start + chunkSize >= file.size ? file.size : start + chunkSize;
                if(tempUploadConfigDataChunkCount == 1){
                    end = file.size;
                }
                if (start > file.size) {
                    return;
                }
                let blob;
                let blobName; 
                if(tempUploadConfigDataChunkCount > 1){
                    blob= file.slice(start, end);
                    blobName= `${fname}.${index}.${fext}`; // 文件名：文件的名称+index+后缀命名;
                } else{
                    blob= file.slice(0, chunkSize);
                    blobName= `${fname}.${fext}`; // 文件名：文件的名称+index+后缀命名;
                }

                function startUpload(md5){
                    let blobFile = new File([blob], blobName);
                    let formData = new FormData();
                    formData.append("file_data", blobFile);
                    formData.append("index", index);
                    formData.append("chunkCount", tempUploadConfigDataChunkCount);
                    formData.append("fileSize", end - start);
                    formData.append("fileMD5", md5);

                    var progressPercent=(index/tempUploadConfigDataChunkCount*100).toFixed(2);
                    var progressMsg=`${index}/${tempUploadConfigDataChunkCount}`;
                    importDataProgress(progressPercent,progressMsg);

                    importData(formData,function(index){
                        if(tempUploadConfigDataChunkCount>1){
                            var tempIndex=index+1;
                            if(tempIndex == tempUploadConfigDataChunkCount){
                                importDataProgress(100,`${tempIndex}/${tempUploadConfigDataChunkCount}`);
                                showOnUplading(false);
                                $('.notifications').notify({
                                    message: {
                                        text: getI18nName('UploadSuccess') //'Please select upgrade file!'
                                    },
                                    type: "success"
                                }).show();
                                beginFileImport();
                            }else {
                                uploadFileChunk(tempIndex);
                            }
                            console.log({
                                blobName,
                                index,
                                tempUploadConfigDataChunkCount,
                                progressPercent,
                                progressMsg,
                                formData,
                            })
                        }else{
                            importDataProgress(100,`${tempIndex-1}/${tempUploadConfigDataChunkCount}`);
                            showOnUplading(false);
                            $('.notifications').notify({
                                message: {
                                    text: getI18nName('UploadSuccess') //'Please select upgrade file!'
                                },
                                type: "success"
                            }).show();
                            beginFileImport();
                        }
                    },index);
                }
                calculateMd5(blob,startUpload);
            }
            uploadFileChunk(0);
        } else {
            $('.notifications').notify({
                message: {
                    text: getI18nName('chooseFileUpdate') //'Please select upgrade file!'
                },
                type: "warning"
            }).show();
        }
    }

    function calculateMd5(blob, callback) {
        var reader = new FileReader();
        reader.readAsArrayBuffer(blob);
        reader.onloadend = function () {
        var wordArray = CryptoJS.lib.WordArray.create(reader.result),
            hash = CryptoJS.MD5(wordArray).toString();
        // or CryptoJS.SHA256(wordArray).toString(); for SHA-2
        console.log("MD5 Checksum", hash);
        callback(hash);
        };
    }

    function importData(formData,uploadFileCallback,index){
        $.ajax({
        url: serverUrl + "ImportConfigFile",
        type: 'POST',
        data: formData,
        // 告诉jQuery不要去处理发送的数据
        processData: false,
        // 告诉jQuery不要去设置Content-Type请求头
        contentType: false,
        success: function (responseStr) {
            if (responseStr.success == "true" && responseStr.errorCode == 0) {
                uploadFileCallback(index);
            }else {
                $('.notifications').notify({
                    message: {
                        text: getI18nName('UploadFailed') //'Please select upgrade file!'
                    },
                    type: "warning"
                }).show();
            }
        },
        error: function (responseStr) {
            console.log(responseStr);
            // $('#fu-update').removeClass('hide');
            // $('#fu-cancel').addClass('hide').prop('disabled', false);
        }
    });
    }

    function onImportData(msg,code){
        if(code==0){
            layer.open({
                title: getI18nName('DataImportSuccess'),
                content: getI18nName('DataImportSuccessTips'),
                end:function(){
                    logOut();
                }
            });
        }else{
            $('.notifications').notify({
                        fadeOut: {
                            enabled: false
                        },
                        message: {
                            text: stringFormat.format(getI18nName('errorTip'), convertErrorCode(code))
                        },
                        type: "danger",
                    }).show();
        }
    }
</script>