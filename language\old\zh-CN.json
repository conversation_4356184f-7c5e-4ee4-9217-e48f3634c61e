{"loginTime": "登录时间", "changerUser": "用户切换", "change": "切换", "systemShutDownConfirmTip": "系统将在1分钟内关机，是否取消操作？", "systemShutDownTip": "1分钟时间到，自动关机！", "systemShutDownCancel": "取消关机", "loadingData": "正在努力地加载数据中，请稍候……", "pageSize": "每页 {1} 条", "pageSelecter": "显示：{1}-{2}，共 {3} 条", "search": "搜索", "noMatch": "没有找到匹配的记录", "index": "序号", "type": "类型", "aduId": "编码", "connectedState": "网络状态", "commType": "通讯方式", "loraSignalLevel": "lora信号强度(dBm)", "loraSNR": "信噪比(dB)", "powerSupplyMode": "供电方式", "byBattery": "内置电池", "byCable": "外接电源", "battaryPercent": "电量", "battaryLife": "续航时间(天)", "deviceNameTable": "设备名称", "pointType": "传感器", "isconnected": "状态", "onlineState": "在线", "offlineState": "失联", "aduNeverConnectedState": "未连接", "updateTime": "更新时间", "data": "数据", "chart": "图谱", "operate": "操作", "lookUp": "查看", "tbSampleDate": "采集时间", "collect": "采集", "errorTip": "错误：{1}", "aeChartTitle": "AE幅值图谱", "humidityChartTitle": "湿度曲线", "temperatureChartTitle": "温度曲线", "noiseChartTitle": "噪声曲线", "TEVamp": "TEV幅值", "TEVYunit": "单位[dB]", "UnitFomat": "单位[{1}]", "unitParse": "单位[{1}]", "maxValue": "最大值", "minValue": "最小值", "average": "平均值", "AT&T": "美国AT&T", "China Unicom": "中国联通", "China Mobile": "中国移动", "Transformer": "变压器", "Breaker": "断路器", "Disconnector": "隔离开关", "Isolator": "刀闸", "Arrester": "避雷器", "PT": "电压互感器", "CT": "电流互感器", "Busbar": "母线", "Circuit": "母联", "Switchgear": "开关柜", "Power Cable": "电力电缆", "Lightning Rod": "避雷针", "Wall Bushing": "穿墙套管", "Reactor": "电抗器", "Electric Conductor": "电力导线（导流排）", "Power Capacitor": "电力电容器", "Discharge Coil": "放电线圈", "Load Switch": "负荷开关", "Grounding Transformer": "接地变", "Grounding Resistance": "接地电阻", "Grounding Grid": "接地网", "Combined Filter": "结合滤波器", "Insulator": "绝缘子", "Coupling Capacitor": "耦合电容器", "Cabinet": "屏柜", "Other": "其他", "Fuse": "熔断器", "Using Transformer": "所用变", "Arc Suppression Device": "消弧装置", "Main Transformer": "主变压器", "Wave Trap": "阻波器", "Combined Electric Appliance": "组合电器", "Combined Transformer": "组合互感器", "monitor": "实时监测", "dataSelect": "数据查询", "themeSkin": "皮肤", "themeBlue": "蓝色", "settings": "系统配置", "datetime_set": "时间设置", "language_set": "语言设置", "soft_setting": "系统设置", "file_manage": "档案管理", "instrument_set": "前端配置", "file_point_set": "测点配置", "alarm_param_set": "报警配置", "modbus_setting": "Modbus设置", "hardware_update": "固件更新", "collect_mode": "采集模式", "net_set": "网络设置", "sync_data": "同步数据", "syncingData": "正在同步数据...", "syncingDataCancel": "取消同步", "syncingDataFailed": "同步失败", "syncingDataSuccess": "同步数据完成", "syncingDataProgress": "同步数据进度：{1}", "syncingDataProgress2": "同步数据进度[{1}]", "export_data": "导出数据", "system_info": "系统信息", "monitoringTable": "检测表", "PD": "内置局放传感器", "OLD_IS": "特高频智能传感器", "MEU": "机械特性传感器", "UHF_IS": "特高频智能传感器", "HFCT_IS": "高频智能传感器", "PD_IS": "外置三合一智能传感器", "TRANSFORMER_AE_IS": "变压器超声传感器", "GIS_AE_IS": "GIS超声传感器", "ENV_IS": "环境智能传感器", "Arrester_U_IS": "避雷器电压智能传感器", "Arrester_I_IS": "避雷器电流智能传感器", "Vibration_IS": "振动智能传感器", "MECH_IS": "机械特性智能监视仪", "TEMP_HUM_IS": "温湿度传感器", "MECH_PD_TEMP": "内置局放机械特性温度三合一传感器", "GIS_LOWTEN": "低压传感器", "CHANNEL_OPTICAL_TEMP": "光纤测温传感器", "VaisalaDTP145": "维萨拉DTP145", "Wika_GDT20": "威卡GDT20", "Wika_GDHT20": "威卡GDHT20", "SHQiuqi_SC75D_SF6": "上海虬祺SC75D", "phase": "  相位", "period": "  周期", "AMP": "  幅值", "RMS": "  有效值", "max": "  周期最大值", "fre1Value": "频率成分1", "fre2Value": "频率成分2", "All-pass": "全通", "Low-pass": "低通", "High-pass": "高通", "No-Record": "未记录", "TEV": "暂态地电压", "AE": "超声波", "TEMP": "温度", "HFCT": "高频电流", "UHF": "特高频", "Humidity": "湿度", "Decibels": "分贝", "Noisy": "声压", "ENVGas": "大气", "envgas": "大气趋势", "MP": "机械特性", "ArresterU": "避雷器电压", "ArresterI": "避雷器电流", "Vibration": "振动", "FrostPointRaw": "霜点", "FrostPointATM": "霜点(标准大气压)", "DewPointRaw": "露点", "DewPointATM": "露点(标准大气压)", "Moisture": "微水", "AbsolutePressure": "绝对压力", "NormalPressure": "标准压力", "Density": "密度", "Oxygen": "氧含量", "SF6": "SF6含量", "confirm": "确定", "close": "关闭", "yes": "是", "no": "否", "continue": "继续", "none": "无", "DISK": "存储", "MEMORY": "内存", "noTestData": "无测试数据", "connected": "连接", "disconnected": "断开", "virtualKeyBord": "虚拟键盘", "pleaseInput": "请输入", "pleaseInputFormat": "请输入 {1}.", "tips": "提示", "confirmTips": "确认提示", "getDirFailed": "获取目录失败：", "backupSuccessTip": "数据数据已成功备份，请至{1}目录下查看", "backupFailedTip": "数据备份失败", "exportFailedTip": "数据导出失败：{1}", "historyDataTypeNotSuport": "暂不支持该前端类型历史数据查询", "stopExportTips": "是否需要停止导出数据?", "stationNameTips": "请填写站点名称", "powerUnitNameTips": "请填写电力单位", "selectDeviceTips": "请选择一次设备", "selectPointTips": "请选择测点", "selectDeviceOfPointTips": "请选择需要添加测点的一次设备", "saveSuccess": "保存成功", "saveFailed": "保存失败：", "operatFailed": "操作失败：{1}", "chosseSensorUpdate": "请选择需要更新的前端", "chooseFileUpdate": "请选择更新文件", "cancelUpdateSensorConfirm": "是否确认取消更新前端程序?", "enterServerUrlTips": "请输入服务器地址 ", "enterServerUrlError": "服务器地址输入错误，请重新输入.", "enterServerPortTips": "请输入服务器端口.", "enterServerPortError": "服务器端口输入错误，端口输入范围： [1,65535].", "NTPserverIpError": "对时服务器地址输入错误，请重新输入.", "NTPserverPortError": "对时服务器端口输入错误，端口输入范围： [1,65535].", "enterNetName": "请输入网络名称.", "enterIP": "请输入IP.", "enterIPError": "IP地址输入错误，如: ***********", "enterNetMask": "请输入子网掩码", "enterNetError": "子网掩码输入错误，如: *************", "enterGateWay": "请输入网关", "enterGateWayError": "配置网关错误, 如: ***********", "enterAPN": "请选择APN", "bias_data": "'偏置值范围为-100-100'", "back_ground_data": "'背景值范围为0-100'", "sam_point": "每周期采样频率范围为20-2000", "sam_rate": "每周期采样频率范围为20-2000", "pdSampleIntervalTip": "PD-采样间隔在10s至86400之间", "meuSampleIntervalTip": "MEU-采样间隔在10s至86400之间", "monitorAwakeTimeTip": "休眠间隔在1s至86400之间", "monitorSleepSpaceTip": "苏醒时间在1min至86400之间", "uploadIntervalTip": "上传间隔在60s至86400之间", "sampleIntervalTip": "采样间隔在1小时至72小时之间的整数", "monitorSampleTimeTip": "主机起始采样时刻在0点至23点之间的整点时刻", "monitorSampleIntervalTip": "主机采样间隔在1小时至168小时之间的整数", "updatingSensor": "正在更新前端...", "updatingSensorProgress": "前端更新进度:{1}", "updatingSensorProgress2": "前端更新进度[{1}]", "selectInstTip": "请选择需要修改的前端", "selectChannelTip": "请选择需要修改的通道", "instCodeTip": "前端编码不能为空", "commLinkTypeTip": "请选择通讯链路类型", "commLinkPortTip": "请选择通讯端口", "instSampleSpaceTip": "采样间隔在6min至100000min之间。", "instSampleStartTimeTip": "起始采样时刻在0点至23点之间的整点时刻。", "instNumberPattenTip": " 只能包含0-9数字、a-zA-Z字母及 ：.", "deleteInstTip": "请选择需要删除的前端", "selectSensorTip": "请选择前端", "deleteInstConfirmTip": "确定删除</br>{1}</br>前端编码:{2}？", "activeInstConfigTip": "确定将此配置应用到</br>{1} 的同类前端？", "activeBatchConfigsSuccess": "批量配置前端成功", "activeBatchConfigsFailed": "批量配置前端失败", "collectStartOnceTip": "确定开始采集</br>{1} 同类前端的数据？", "collectStartTip": "确定开始采集</br>{1}</br>前端编码:{2} 的数据？", "wakeUpInstrumentOnceTip": "确定开始唤醒</br>{1} 同类前端？", "wakeUpInstrumentTip": "确定开始唤醒</br>{1}</br>前端:{2} ？", "emptySensorDataOnceTip": "确定清空</br>{1} 同类前端的所有数据？", "emptySensorDataTip": "确定清空</br>{1}</br>前端编码:{2} 的所有数据？", "emptySensorDataSuccess": "前端数据清除成功", "emptySensorDataFailed": "前端数据清除失败：", "ae_chart": "实时监测-AE图谱", "uhf_chart": "实时监测-UHF图谱", "hfct_chart": "实时监测-HFCT图谱", "tev_chart": "实时监测-TEV幅值", "temperature_chart": "实时监测-温度", "humidity_chart": "实时监测-湿度", "mechanical_chart": "实时监测-机械特性", "arrester_chart": "实时监测-避雷器", "vibration_pickup_chart": "实时监测-振动", "density_micro_water_history": "实时监测-密度微水", "error": "错误", "warning": "警告", "success": "成功", "userErrorTip": "用户名或密码错误", "errorNoDeviceId": "设备ID不存在", "errorNoSensorId": "前端ID不存在", "errorExistSensorId": "前端ID已存在", "errorNoChannelId": "通道ID不存在", "errorNoPointId": "测点ID不存在", "errorDataExportErrDir": "数据导出，文件路径错误。", "errorDataExportErrTime": "数据导出 时间错误", "errorDataExportNoData": "数据导出，无数据。", "errorDataExportNoSpace": "数据导出，U盘已满", "errorDataExportNoDisk": "数据导出，无U盘。", "errorDataExportNoInfo": "数据导出，无站点信息。", "errorDataExportOther": "数据导出，其他错误。", "errorSetModeBus": "modebus设置错误", "errorIP": "IP错误", "errorGPRSSet": "GPRS设置错误", "errorSenorUpdateErrFile": "前端更新，文件错误", "errorSenorUpdateNotMatch": "前端更新，前端类型与升级文件不匹配", "errorSenorUpdating": "前端更新，正在更新", "errorDataCleanFailed": "数据清空失败！", "errorCodeWorkGroupNotExist": "工作组不存在！", "errorCodeInvalidChannel": "非法通道！", "errorCodeInvalidWirignMode": "非法接线方式！", "errorCodeInvalidAlarmMode": "非法报警方式！", "errorNoPermission": "非法权限！", "illegalUser": "非法用户!", "legalPattenMsg": " 只能包含字符（不含空格）：汉字 字母 数字 罗马数字Ⅰ-Ⅻ ` . - () [] # 、 _", "leakageCurrent": "全电流趋势图", "leakageCurrentA": "A相全电流", "leakageCurrentB": "B相全电流", "leakageCurrentC": "C相全电流", "ResistiveCurrent": "阻性电流趋势图", "ResistiveCurrentA": "A相阻性电流", "ResistiveCurrentB": "B相阻性电流", "ResistiveCurrentC": "C相阻性电流", "resistiveCurrentA": "A相阻性电流", "resistiveCurrentB": "B相阻性电流", "resistiveCurrentC": "C相阻性电流", "referenceVoltageA": "A相参考电压", "referenceVoltageB": "B相参考电压", "referenceVoltageC": "C相参考电压", "timeDomain": "时域图", "frequencyDomain": "频域图", "characterParam": "特征参数", "TimeDomainDataX": "X轴振动信号", "TimeDomainDataY": "Y轴振动信号", "TimeDomainDataZ": "Z轴振动信号", "FrequencyDomainDataX": "振动信号频谱图-X轴", "FrequencyDomainDataY": "振动信号频谱图-Y轴", "FrequencyDomainDataZ": "振动信号频谱图-Z轴", "ACCAVGX": "X轴加速度平均值", "ACCAVGY": "Y轴加速度平均值", "ACCAVGZ": "Z轴加速度平均值", "ACCMAXX": "X轴加速度最大值", "ACCMAXY": "Y轴加速度最大值", "ACCMAXZ": "Z轴加速度最大值", "AMPAVGX": "X轴振幅平均值", "AMPAVGY": "Y轴振幅平均值", "AMPAVGZ": "Z轴振幅平均值", "AMPMAXX": "X轴振幅最大值", "AMPMAXY": "Y轴振幅最大值", "AMPMAXZ": "Z轴振幅最大值", "MAXFreqX0": "X轴极值点频率1", "MAXFreqY0": "Y轴极值点频率1", "MAXFreqZ0": "Z轴极值点频率1", "MAXFreqX1": "X轴极值点频率2", "MAXFreqY1": "Y轴极值点频率2", "MAXFreqZ1": "Z轴极值点频率2", "MAXFreqX2": "X轴极值点频率3", "MAXFreqY2": "Y轴极值点频率3", "MAXFreqZ2": "Z轴极值点频率3", "sensorType": "传感器类型", "sensorList": "传感器列表", "gain": "增益", "trigger": "触发幅值", "wave_filter": "频带", "sample_date": "采样日期", "sample_time": "采样时间", "rms": "有效值", "cycle_max": "周期最大值", "frequency1": "频率成分1", "frequency2": "频率成分2", "time_interval": "时间间隔", "realData": "实时数据", "historyData": "历史数据", "trendSync": "趋势分析", "preData": "上一条", "nextData": "下一条", "systemDate": "系统日期", "systemTime": "系统时间", "backupType": "备份类型", "plusBackup": "增量备份", "allBackup": "全量备份", "timeRange": "时间范围", "exportPath": "导出路径", "getExportDir": "获取目录", "checkDir": "检查目录", "exportingData": "正在导出...", "cancel": "取消", "export": "导出", "stationConfig": "站点配置", "stationDelSuccess": "站点删除成功", "stationDelFailed": "站点删除失败：", "deviceConfig": "一次设备配置", "pointConfig": "测点配置", "stationName": "站点名称", "stationPMS": "站点PMS编码", "stationLevel": "站点电压等级", "powerUnit": "电力单位", "save": "保存", "operationSuccess": "操作成功", "deviceAddSuccessTips": "一次设备增加成功", "deviceEditSuccessTips": "一次设备修改成功", "deviceDelSuccessTips": "一次设备删除成功", "pointAddSuccessTips": "测点增加成功", "pointEditSuccessTips": "测点修改成功", "pointDelSuccessTips": "测点删除成功", "deleteFailedTips": "删除失败", "save_add": "保存/增加", "delete": "删除", "deviceType": "设备类型", "deviceLevel": "设备电压等级", "add": "增加", "channelTypeAlarm": "数据类型", "alarmThreshold": "报警阈值", "alarmRecoveryThreshold": "报警恢复阈值", "alarmChannel": "报警通道选择", "built_in_channel_1": "内置通道1", "External_IO_module": "外置IO模块                            ", "not_associated": "不关联", "alarmExternalIOSN": "外置IO模块编码", "alarmExternalIOChannel": "外置IO模块通道", "wiringMode": "接线方式", "alarmMode": "报警方式", "alarmTime": "报警定时", "alarmInterval": "报警间隔", "alarmDuration": "报警时长", "normal_open": "常开", "normal_close": "常闭", "continuous": "连续", "timing": "定时", "interval": "间隔", "alarmSaveSuccess": "报警配置保存成功", "alarmDelSuccess": "报警配置删除成功", "deviceName": "一次设备名称", "pointName": "测点名称", "testStation": "测试站点", "device": "一次设备", "pointList": "测点列表", "sensorID": "前端ID", "sensorChannel": "前端通道", "channelList": "通道列表", "showPoint": "显示测点", "fileSelect": "文件选择", "selectPlease": "请选择", "deviceList": "设备列表", "updating": "正在更新...", "updatingFailed": "更新失败：", "updatingCanceled": "取消更新", "updatingComplete": "完成更新", "update": "更新", "sampleDate": "采样日期", "sampleTime": "采样时间", "pdMax": "最大放电幅值", "pdAvg": "平均放电幅值", "pdNum": "脉冲个数", "acquisitionTime": "采集时间", "humidity": "湿度", "startDate": "起始日期", "endDate": "结束日期", "refresh": "刷新", "scanData": "查询", "sensorCode": "前端编码", "sensorTypeSet": "前端类型", "senorName": "前端名称", "aduAddress": "前端地址", "senorWorkMode": "主动上送", "On": "开", "Off": "关", "ADUMode": "工作模式", "normalMode": "维护模式", "lowPowerMode": "低功耗模式", "monitorMode": "监测模式", "artificialStateStartTime": "人工干预状态开始时刻（整点）", "artificialStateStartTimeTip": "人工干预状态开始时刻范围0-23", "artificialStateEndTime": "人工干预状态结束时刻（整点）", "artificialStateEndTimeTip": "人工干预状态结束时刻范围0-23", "artificialStateWakeUpSpace": "人工干预状态苏醒间隔（分）", "unartificialStateWakeUpSpace": "非人工干预状态苏醒间隔（分）", "isAutoChangeMode": "是否自动切换模式", "monitorModeSampleSapce": "监测模式采集间隔", "workGroup": "工作组号", "warnTemp": "告警温度", "taskGroup": "任务组号", "commLinkType": "通讯链路类型", "commLinkPort": "通讯端口", "frequencyUnit": "电网频率(Hz)", "numInGroup": "组内编号", "commSpeed": "通讯速率", "commLoad": "通讯信道", "sampleSpace": "采样间隔(min)", "sleepTime": "休眠时间(min)", "samleStartTime": "起始采样时间(整点)", "applyData": "操作", "applyAllSensor": "应用到同种", "collectOnce": "批量采集", "collectOnceEnd": "批量采集结束", "collectOnceProgress": "批量采集进度：{1}% ({2})", "collectOnceProgress2": "批量采集进度[{1}% ({2})]", "activeOnce": "批量应用", "wakeUp": "唤醒", "wakeUpInstrument": "唤醒前端", "wakeUpInstrumentOnce": "批量唤醒前端", "orderSend": "命令已下发", "cleanData": "清空数据", "sensorAddSuccess": "传感器增加成功", "sensorEditSuccess": "传感器修改成功", "sensorDelSuccess": "前端删除成功", "cleanSensorData": "清空前端数据", "getSensor": "获取前端", "channelType": "通道类型", "channelName": "通道名称", "channelInfoSaveSuccess": "通道信息保存成功", "bias": "偏置", "waveFilter": "频带设置", "gainMode": "增益模式", "gain_unit": "增益[dB]", "samCycle": "采样周期数", "samPointNum": "每周期采样点数", "samRate": "每周期采样点数", "ratio": "变比", "channelPhase": "相别", "mecLoopCurrentThred": "线圈电流阈值[mA]", "mecMotorCurrentThred": "电机电流阈值[mA]", "mecSwitchState": "开关量初始状态", "awaysOpen": "常开", "awaysClose": "常闭", "mecBreakerType": "断路器机构配置", "oneMech": "三相机械联动（一台机构）", "threeMech": "三相电气联动（三台机构）", "mecMotorFunctionType": "电机工作类型", "threePhaseAsynMotor": "三相异步电机", "onePhaseMotor": "单相交/直流电机", "setCurrent": "设置当前", "setAll": "应用到同种传感器", "showCoil": "线圈电流图谱", "showSwitch": "开关量图谱", "showMotor": "电机电流图谱", "showOrig": "原始电流图谱", "actionDate": "动作日期", "actionTime": "动作时间", "phaseA": "A相", "phaseB": "B相", "phaseC": "C相", "coil_charge_time": "线圈通电时间", "coil_cutout_time": "线圈断电时间", "max_current": "分闸线圈最大电流", "hit_time": "分闸掣子脱扣时间", "subswitch_close_time": "辅助开关切换时间", "a_close_time": "A相合闸时间", "a_close_coil_charge_time": "A相线圈通电时间", "a_close_coil_cutout_time": "A相线圈断电时间", "a_close_max_current": "A相合闸线圈最大电流", "a_close_hit_time": "A相合闸掣子脱扣时间", "a_close_subswitch_close_time": "A相辅助开关切换时间", "b_close_time": "B相合闸时间", "b_close_coil_charge_time": "B相线圈通电时间", "b_close_coil_cutout_time": "B相线圈断电时间", "b_close_max_current": "B相合闸线圈最大电流", "b_close_hit_time": "B相合闸掣子脱扣时间", "b_close_subswitch_close_time": "B相辅助开关切换时间", "c_close_time": "C相合闸时间", "c_close_coil_charge_time": "C相线圈通电时间", "c_close_coil_cutout_time": "C相线圈断电时间", "c_close_max_current": "C相合闸线圈最大电流", "c_close_hit_time": "C相合闸掣子脱扣时间", "c_close_subswitch_close_time": "C相辅助开关切换时间", "close_sync": "合闸同期性", "close_time": "合闸时间", "a_open_time": "A相分闸时间", "a_open_coil_charge_time": "A相线圈通电时间", "a_open_coil_cutout_time": "A相线圈断电时间", "a_open_max_current": "A相分闸线圈最大电流", "a_open_hit_time": "A相分闸掣子脱扣时间", "a_open_subswitch_close_time": "A相辅助开关切换时间", "b_open_time": "B相分闸时间", "b_open_coil_charge_time": "B相线圈通电时间", "b_open_coil_cutout_time": "B相线圈断电时间", "b_open_max_current": "B相分闸线圈最大电流", "b_open_hit_time": "B相分闸掣子脱扣时间", "b_open_subswitch_close_time": "B相辅助开关切换时间", "c_open_time": "C相分闸时间", "c_open_coil_charge_time": "C相线圈通电时间", "c_open_coil_cutout_time": "C相线圈断电时间", "c_open_max_current": "C相分闸线圈最大电流", "c_open_hit_time": "C相分闸掣子脱扣时间", "c_open_subswitch_close_time": "C相辅助开关切换时间", "open_sync": "分闸同期性", "open_time": "分闸时间", "a_twice_open_time": "A相二次分闸时间", "a_twice_open_coil_charge_time": "A相二次分闸线圈带电时刻", "a_twice_open_coil_cutout_time": "A相二次分闸线圈断电时刻", "a_twice_open_max_current": "A相二次分闸线圈最大电流", "a_twice_open_hit_time": "A相二分掣子脱扣时间", "a_twice_open_subswitch_close_time": "A相二次分闸辅助开关断开时刻", "b_twice_open_time": "B相二次分闸时间", "b_twice_open_coil_cutout_time": "B相二次分闸线圈断电时刻", "b_twice_open_max_current": "B相二次分闸线圈最大电流", "b_twice_open_hit_time": "B相二分掣子脱扣时间", "b_twice_open_subswitch_close_time": "B相二次分闸辅助开关断开时刻", "c_twice_open_time": "C相二次分闸时间", "c_twice_open_coil_cutout_time": "C相二次分闸线圈断电时刻", "c_twice_open_max_current": "C相二次分闸线圈最大电流", "c_twice_open_hit_time": "C相二分掣子脱扣时间", "c_twice_open_subswitch_close_time": "C相二次分闸辅助开关断开时刻", "twice_open_sync": "二次分闸同期性", "twice_open_time_text": "二次分闸时间", "a_switch_shot_time": "A相金短时间", "b_switch_shot_time": "B相金短时间", "c_switch_shot_time": "C相金短时间", "a_switch_no_current_time": "A相无电流时间", "b_switch_no_current_time": "B相无电流时间", "c_switch_no_current_time": "C相无电流时间", "motor_start_current": "电机启动电流", "motor_max_current": "电机最大电流", "storage_time": "电机储能时间", "Chan_A_motor_start_current": "A相电机启动电流", "Chan_A_motor_max_current": "A相电机最大电流", "Chan_A_storage_time": "A相电机储能时间", "Chan_B_motor_start_current": "B相电机启动电流", "Chan_B_motor_max_current": "B相电机最大电流", "Chan_B_storage_time": "B相电机储能时间", "Chan_C_motor_start_current": "C相电机启动电流", "Chan_C_motor_max_current": "C相电机最大电流", "Chan_C_storage_time": "C相电机储能时间", "serialPort": "串口端口", "baudRate": "波特率", "dataBit": "数据位", "stopBit": "停止位", "checkBit": "校验位", "singleCollect": "批量采集", "sampling": "正在采集...", "serverIP": "服务器IP", "serverPort": "服务器端口", "NTPserverIp": "NTP服务器IP", "NTPserverPort": "NTP服务器端口", "netType": "网络类型", "deviceIp": "主机IP", "subnetMask": "子网掩码", "gateway": "默认网关", "networkAPN": "网络接入APN", "userName": "用户名", "passWord": "密码", "deviceWorkGroup": "主机工作组号", "frequency": "电网频率", "pdSampleInterval": "PD-采样间隔", "spaceSecond": "&nbsp;秒", "meuSampleInterval": "MEU-采样间隔", "monitorAwakeTime": "休眠间隔", "monitorSleepSpace": "苏醒时间", "intervalServer": "上传间隔（服务器）", "intervalMinus": "采样间隔", "startSampleTime": "主机起始采样时刻", "spacePoint": "&nbsp;点", "startSampleInterval": "主机起始采样间隔", "hour": "小时", "poerOffSpace": "关机间隔", "powerOnSpace": "开机间隔", "dateRange": "日期范围", "synchronizing": "正在同步...", "synchronize": "同步", "amplitude": "幅值", "switch": "切换", "copyRight": "版权所有(c)2018华乘电气科技股份有限公司", "S1010Name": "开关设备状态监测系统", "logOut": "退出", "logOutTitle": "登出确认", "logOutConfirm": "退出当前用户登录？", "logOutTips": "已退出登录", "enterHost": "请输入主机名称", "selectDevTip": "请选择设备", "stopSyncDataTip": "是否需要停止同步数据?", "modbusAddressCheckTip": "ModBus地址范围为1~254整数", "emptyMonitorDatas": "清空主机数据", "emptyMonitorDatasTip": "清空采集主机内所有历史数据", "emptyMonitorDatasSuccess": "清空数据成功", "resetSoftSet": "恢复出厂设置", "resetSoftSetTip": "将采集主机内所有配置恢复至出厂状态", "resetSoftSetSuccess": "出厂设置恢复成功", "continueConfirmTip": "该操作不可恢复，还要继续吗？"}