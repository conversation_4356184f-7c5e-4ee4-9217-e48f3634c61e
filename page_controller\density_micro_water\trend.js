var myChart;
function initTendChartWidth() {
    var width = $('#dmwContent').width() - 30;
    var height=$('#dmwContent').height() - 30;
    $("#trendChart").width(width);
    $("#trendChart").height(height);
}

var dataXValue = [" "],
    dataYValue = [0];

window.addEventListener("resize", function () {
    if (myChart)
        myChart.resize();
});

$(window).resize(function () {
    var height = $("#maingraph").height() - 120;
    $("#graph-water").height(height);
    initTendChartWidth();
});

$(window).resize();
var option = {
    tooltip: {
        trigger: 'axis',
        axisPointer: { // 坐标轴指示器，坐标轴触发有效
            type: 'line' // 默认为直线，可选为：'line' | 'shadow'
        },
        confine: true
    },
    title: {
        text: '',
        x: 'center',
        y: 'top',
        textAlign: 'left'
    },
    calculable: true,
    xAxis: [
        {
            type: 'category',
            data: dataXValue,
            boundaryGap: false,
            axisTick: {
                alignWithLabel: false
            }
        }
    ],
    yAxis: [
        {
            type: 'value',
            name: stringFormat.format(getI18nName('UnitFomat'),''),
        }
    ],
    series: [
        {
            name: '',
            type: 'line',
            data: dataYValue,
            itemStyle: {
                normal: {
                    color: '#3c8dbc'
                }
            },
            markPoint: {
                data: [
                    {type: 'max', name: getI18nName('maxValue')},
                    {type: 'min', name: getI18nName('minValue')}
                ]
            }
//                    markLine: {
//                        data: [
//                            {type: 'average', name: '平均值'}
//                        ]
//                    }
        }
    ]
};

function loadDMWCharts() {
    var pointType = selectedHistoryArchives.channelType;
    var dataGet = "pointId=" + selectedHistoryArchives.testPoint + "&pointType=" + pointType +
        "&startDate=" + getHistoryBeginDate() + "&endDate=" + getHistoryEndDate();
    poll('GET', GET_TEND_DATA, dataGet, function (text) {
        var respData = text.result;

        if (!respData)
            return;
        var sensorType = getI18nName(respData.sensorType);
        myChart = echarts.init(document.getElementById('trendChart'));
        if (option) {
            option.title.text = sensorType;
            option.xAxis[0].data = respData.dataChart.xValue;
            option.yAxis[0].name = stringFormat.format(getI18nName('UnitFomat'), respData.unit);
            option.series[0].data = respData.dataChart.yValue;
            option.series[0].name = sensorType;
            myChart.setOption(option);
        }
        $(window).resize();
        //
        // if (respData.startDate) {
        //     $('#dateStart').daterangepicker({
        //         "opens": "right",
        //         "autoApply": true,
        //         "timePicker": false,
        //         "singleDatePicker": true,
        //         "format": 'yyyy/MM/dd',
        //         "startDate": new Date(respData.startDate)
        //     });
        // }
        // if (respData.endDate) {
        //     $('#dateEnd').daterangepicker({
        //         "opens": "right",
        //         "autoApply": true,
        //         "timePicker": false,
        //         "singleDatePicker": true,
        //         "format": 'yyyy/MM/dd',
        //         "startDate": new Date(respData.endDate)
        //     });
        // }
    });
}