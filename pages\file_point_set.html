<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div class="col-sm-3 full-height zTreeDemoBackground left" style="overflow:auto;border: 1px solid #f4f4f4;">
            <div id="station_tree" class="ztree"></div>
        </div>
        <div class="col-sm-9 full-height ">
            <div class="col-sm-6">
                <form class="form-horizontal full-height">
                    <br>
                    <div class="form-group">
                        <label for="station" class="col-sm-4 control-label" data-i18n="testStation">测试站点</label>
                        <div class="col-sm-8">
                            <select id="station" onchange="selTestPoint_onChange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="dev" class="col-sm-4 control-label" data-i18n="device">一次设备</label>
                        <div class="col-sm-8">
                            <select id="dev" onchange="selTestPoint_onChange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="test_point" class="col-sm-4 control-label" data-i18n="pointName">测点名称</label>
                        <div class="col-sm-8">
                            <select id="test_point" onchange="selPoint_onchange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease">
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="sel_test_point" class="col-sm-4 control-label" data-i18n="pointList">测点列表</label>
                        <div class="col-sm-8  full-height">
                            <select name="from[]" id="sel_test_point" class="form-control" style="height: 180px;"
                                multiple="multiple"></select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-sm-6">
                <form class="form-horizontal full-height">
                    <br>
                    <div class="form-group">
                        <label for="inst_type" class="col-sm-4 control-label" data-i18n="sensorType">前端类型</label>
                        <div class="col-sm-8">
                            <select id="inst_type" onchange="selAduType_onChange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inst_id" class="col-sm-4 control-label" data-i18n="sensorID">前端ID</label>
                        <div class="col-sm-8">
                            <select id="inst_id" onchange="selAdu_onChange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="inst_chns" class="col-sm-4 control-label" data-i18n="sensorChannel">前端通道</label>
                        <div class="col-sm-8">
                            <select id="inst_chns" onchange="selChns_onchange();" class="form-control select2"
                                data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="sel_chn" class="col-sm-4 control-label" data-i18n="channelList">通道列表</label>
                        <div class="col-sm-8 full-height">
                            <select name="to[]" id="sel_chn" onclick="clRemoveChn()" class="form-control"
                                style="height: 180px;" multiple="multiple"></select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-sm-12">
                <form class="form-horizontal full-height">
                    <div class="form-group">
                        <label class="col-sm-2 control-label" for="test_point_name" data-i18n="showPoint">显示测点</label>
                        <div class="col-sm-10">
                            <input id="test_point_name" vk="true" type="text" class="form-control"
                                data-i18n-placeholder="pleaseInput" value="">
                        </div>
                    </div>
                </form>
            </div>
            <div class="content-footer col-sm-12">
                <button type="button" id="save_inst_set" class="btn btn-block btn-primary"
                    style="width: 80px; margin: 5px 20px 5px auto" data-i18n="save">保存
                </button>
            </div>
        </div>
    </div>
</div>
<script>
    initPageI18n();
    checkNeedKeyboard();

    var selectedArchives = {
        substation: '',
        device: '',
        testPoint: '',
        treeNodeType: '0'
    };


    $('.select2').select2({
        minimumResultsForSearch: Infinity,
        language: "zh-CN",
    });


    //    function onMouseDown(item) {
    //        item.sindex = item.selectedIndex;
    //        item.selectedIndex = -1;
    //    }
    //    function onMouseMove(item) {
    //        if (item.sindex)
    //            this.selectedIndex = this.sindex;
    //    }
    //    $('#sel_chn').focus(function () {
    //        $(this)[0].selectedIndex = -1;
    //    });
    //    $('#inst_type').focus(function () {
    //        $(this)[0].selectedIndex = -1;
    //    });
    GetAduTypeListRelation();
    getFileTestPointTree();

    function selTestPoint_onChange() {
        var data = "id=" + $("#dev ").val() + "&type=3";
        poll('GET', 'GetRelationList', data, function (text) {
            $("#test_point").empty();
            var respData = text.result;
            if (respData === null || respData.length === 0)
                return;

            respData.forEach(function (value, index) {
                var option = $("<option />", {
                    "value": "" + value.pointId + ""
                }).append(value.pointName);
                $("#test_point").append(option);
            });
            selectedArchives.device = $("#dev ").val();
            selPoint_onchange();
        });
    }

    var mCallback;
    var mCallData;

    function selAduType_onChange(callback, callData) {
        var data = "aduType=" + $("#inst_type ").val();
        mCallback = callback;
        mCallData = callData;
        poll('GET', 'GetADUVersionList', data, function (text) {
            $("#inst_id").empty();
            var respData = text.result;
            if (respData === null || respData.length === 0)
                return;
            respData.forEach(function (value, index) {
                var option = $("<option />", {
                    "value": "" + value.aduId + ""
                }).append(value.aduId);
                $("#inst_id").append(option);
            });
            GetChannelListRelation($("#inst_id ").val())
        });
    }

    function selAdu_onChange() {
        GetChannelListRelation($("#inst_id ").val());
    }

    function selPoint_onchange() {
        $("#sel_test_point").empty();
        var value = $("#test_point ").val();
        var _name = $("#test_point").find("option:selected").text();
        //        var option = $("<option />", {"value": "" + item.aduId + ""}).append(item.aduId + "-" + item.aduName);
        var option = $("<option />", {
            "value": "" + value + ""
        }).append(_name);
        $("#sel_test_point").append(option);

        var dataP = "testPointId=" + value;
        poll('GET', 'GetRelation', dataP, function (text) {
            var respData = text.result;
            if (respData === null || respData.length === 0)
                return;
            if (respData.aduChannels[0]) {
                $("#inst_type").val(respData.aduChannels[0].aduType).select2();
            }
            selAduType_onChange(function (respData) {
                var point_name = respData.testPointName;
                if (validateStrLen(point_name) == 0) {
                    point_name = respData.testPoint.testPointNameDoc;
                }
                //                debugger;
                $("#test_point_name").val(point_name);
                $("#inst_id").val(respData.aduChannels[0].aduId).select2();
                //                $("#inst_chns").val(respData.aduChannels[0].channelType).select2();
                $("#inst_chns").val(respData.aduChannels[0].channelIndex).select2();
            }, respData);
            var point_name = respData.testPointName;
            if (validateStrLen(point_name) == 0) {
                point_name = respData.testPoint.testPointNameDoc;
            }
            $("#test_point_name").val(point_name);
            $("#sel_chn").empty();
            for (var i = 0; i < respData.aduChannels.length; i++) {
                var channel = respData.aduChannels[i];
                var chnsValue = channel.aduId + "+" + channel.channelIndex;

                var _name = channel.channelType + "-" + channel.aduId;
                var option = $("<option />", {
                    "value": "" + chnsValue + ""
                }).append(_name);
                $("#sel_chn").append(option);
            }
            //            debugger;
            var type = '3';
            var nodeItem = {
                id: value,
                type: type
            };
            selectNode(nodeItem);
        });
    }

    function selChns_onchange() {
        if ($("#inst_chns").val() == "-99") {
            return;
        }
        var chnsValue = $("#inst_id").val() + "+" + $("#inst_chns").val();
        var hasValus = false;
        var tempOptions = $("#sel_chn")[0].options;
        var tempLength = 0;
        if (tempOptions != undefined) {
            tempLength = tempOptions.length;
        }
        for (var i = 0; i < tempLength; i++) {
            if (tempOptions[i].value == chnsValue) {
                hasValus = true;
            }
        }
        if (!hasValus) {
            var _name = $("#inst_chns").find("option:selected").text() + "-" + $("#inst_id ").val();
            var option = $("<option />", {
                "value": "" + chnsValue + ""
            }).append(_name);
            $("#sel_chn").append(option);
        }
    }

    function clRemoveChn() {
        var fromObj = document.getElementById("sel_chn");
        for (var i = 0; i < fromObj.options.length; i++) {
            var option = fromObj.options[i];
            if (option.selected === true) {
                fromObj.options.remove(i);
            }
        }
    }

    $('#save_inst_set').click(function () {
        var formData = {};
        formData.testPointName = $('#test_point_name').val();
        formData.testPoint = [];
        formData.aduChannels = [];
        checkLegalNameValue(formData.testPointName, 'test_point_name');
        var pointList = document.getElementById("sel_test_point");
        for (var i = 0; i < pointList.options.length; i++) {
            var tempPoint = {};
            tempPoint.testPointId = pointList.options[i].value;
            formData.testPoint.push(tempPoint);
        }
        //        debugger;
        var chnList = document.getElementById("sel_chn");
        for (var i = 0; i < chnList.options.length; i++) {
            var tempChn = {};
            var chnids = chnList.options[i].value.split("+");
            tempChn.aduId = chnids[0];

            tempChn.channelIndex = chnids[1];
            tempChn.channelType = chnList.options[i].text.split("-")[0].split("_")[
                0]; //取区分值首位的Type类型，如UHF-1B:AD:D5:1D
            tempChn.channelTypeName = chnList.options[i].text;
            formData.aduChannels.push(tempChn);
        }
        formData.testPointLength = pointList.length;
        formData.aduChannelsLength = chnList.length;

        poll('POST', 'SaveRelation', formData, function (text) {
            var textshow = getI18nName('saveSuccess');
            getFileTestPointTree();

            $('.notifications').notify({
                message: {
                    text: textshow
                },
                type: "success"
            }).show();
        }, function (errorCode) {
            $('.notifications').notify({
                fadeOut: {
                    enabled: false
                },
                message: {
                    text: stringFormat.format('operatFailed', convertErrorCode(errorCode))
                },
                type: "danger"
            }).show();
        });
    });

    //树状菜单节点操作
    function selectNode(nodeItem) {
        // debugger;
        var currentTreeNodeType = selectedArchives.treeNodeType;
        var treeObj = $.fn.zTree.getZTreeObj('station_tree');
        var typeNodeCallBack = function () {
            var node = treeObj.getNodeByParam('id', nodeItem.id);
            virtualClickNode(treeObj, node);
        }
        if (currentTreeNodeType == '0') {
            var substationNode = treeObj.getNodeByParam('id', selectedArchives.substation);

            var deviceNode = treeObj.getNodeByParam('id', selectedArchives.device);
            //若变电站层级未展开
            if (!substationNode.open) {
                substationNode.callBack = function () {
                    // debugger;
                    //添加zTree展开异步回调监听
                    deviceNode = treeObj.getNodeByParam('id', selectedArchives.device);
                    deviceNode.callBack = typeNodeCallBack;
                    virtualClickNode(treeObj, deviceNode);
                }

                virtualClickNode(treeObj, substationNode);
            }
            //若设备层级未展开
            if (deviceNode == undefined || !deviceNode.open) {
                deviceNode = treeObj.getNodeByParam('id', selectedArchives.device);
                //添加zTree展开异步回调监听
                deviceNode.callBack = typeNodeCallBack;
                virtualClickNode(treeObj, deviceNode);
            } else {
                deviceNode.callBack = typeNodeCallBack;
                virtualClickNode(treeObj, deviceNode);
            }
        } else if (currentTreeNodeType == '1') {
            var deviceNode = treeObj.getNodeByParam('id', selectedArchives.device);
            //添加zTree展开异步回调监听
            deviceNode.callBack = typeNodeCallBack;
            virtualClickNode(treeObj, deviceNode);
        }
    }

    function virtualClickNode(treeObj, node, flag) {
        var clickAble = flag;
        if (clickAble == undefined) {
            clickAble = true;
        }
        treeObj.selectNode(node); //选中指定节点
        if (node != undefined && !node.open) {
            treeObj.expandNode(node); //展开指定节点
        }
        if (clickAble && treeObj.setting.callback) {
            treeObj.setting.callback.onClick(null, treeObj.setting.treeId, node); //触发函数
        }
    }

    //节点单击，回调函数
    function zTreeOnclickPointSet(event, treeId, treeNode) {
        // debugger;
        var treeObj = $.fn.zTree.getZTreeObj('station_tree');
        treeObj.selectNode(treeNode); //选中指定节点
        zTreeOnExpandPointSet(event, treeId, treeNode);
        if (treeNode != undefined) {
            if (!treeNode.open) {
                treeObj.expandNode(treeNode); //展开指定节点
            }
            if (treeNode.callBack != undefined) {
                treeNode.callBack();
                treeNode.callBack = undefined;
            }
        }
    }
    //节点展开，回调函数
    function zTreeOnExpandPointSet(event, treeId, treeNode) {
        //        debugger;
        //节点类型
        if (treeNode == undefined) {
            return;
        }
        var treeNodeType = treeNode.level;
        //根据节点类型不同，给全局变量selectedArchives赋值。
        if (treeNodeType == '0') {
            selectedArchives.substation = treeNode.id;
            selectedArchives.device = '';
            selectedArchives.testPoint = '';
        } else if (treeNodeType == '1') { //一次设备
            selectedArchives.substation = treeNode.getParentNode().pId;
            selectedArchives.device = treeNode.id;
            selectedArchives.testPoint = '';
            $("#dev").val(treeNode.id).select2();
        } else if (treeNodeType == '2') { //测点
            selectedArchives.substation = treeNode.getParentNode().getParentNode().pId;
            selectedArchives.device = treeNode.pId;
            selectedArchives.testPoint = treeNode.id;
            $("#dev").val(treeNode.pId).select2();
            var data = "id=" + $("#dev ").val() + "&type=3";
            poll('GET', 'GetRelationList', data, function (text) {
                $("#test_point").empty();
                var respData = text.result;
                if (respData === null || respData.length === 0)
                    return;
                //                debugger;
                respData.forEach(function (value, index) {
                    var option = $("<option />", {
                        "value": "" + value.pointId + ""
                    }).append(value.pointName);
                    $("#test_point").append(option);
                });

                $("#test_point").val(selectedArchives.testPoint).select2();

                selectedArchives.device = $("#dev ").val();
                selPoint_onchange();
            });
            //            var dataP = "testPointId=" + treeNode.id;
            //            poll('GET', 'GetRelation', dataP, function (text) {
            //                var respData = text.result;
            //                if (respData === null || respData.length === 0)
            //                    return;
            ////                debugger;
            //                var point_name = respData.testPointName;
            //                if (validateStrLen(point_name) == 0) {
            //                    point_name = respData.testPoint.testPointNameDoc;
            //                }
            //                selAduType_onChange(function (respData) {
            //                    $("#test_point_name").val(point_name);
            //                    $("#inst_id").val(respData.aduChannels[0].aduId).select2();
            ////                $("#inst_chns").val(respData.aduChannels[0].channelType).select2();
            //                    $("#inst_chns").val(respData.aduChannels[0].channelIndex).select2();
            ////                debugger;
            //                    $("#inst_type").val(respData.aduChannels[0].aduType).select2();
            //                });
            //                $("#sel_chn").empty();
            //                for (var i = 0; i < respData.aduChannels.length; i++) {
            //                    var channel = respData.aduChannels[i];
            //                    var chnsValue = channel.aduId + "+" + channel.channelIndex;
            //
            //                    var _name = channel.channelType + "-" + channel.aduId;
            //                    var option = $("<option />", {"value": "" + chnsValue + ""}).append(_name);
            //                    $("#sel_chn").append(option);
            //                }
            //            });
        } else {
            return;
        }
    }
</script>