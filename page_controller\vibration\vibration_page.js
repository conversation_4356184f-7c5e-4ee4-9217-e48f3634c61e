var preVibrationId;
var nextVibrationId;

function refreshPreNextButton(respData, dataId) {
    preVibrationId = respData.preDataId;
    nextVibrationId = respData.nextDataId;
    showChartDataId = dataId;
    //判断上一条/下一条按钮是否可用并改变状态
    if (preVibrationId === 0) {
        $("#preData").attr("disabled", true);
    } else {
        $("#preData").removeAttr("disabled");
    }
    if (nextVibrationId === 0) {
        $("#nextData").attr("disabled", true);
    } else {
        $("#nextData").removeAttr("disabled");
    }
}

function getVibrationTendDatas(contentId, dataId, chartIndex) {
    var dataGet = "pointId=" + showChartPointId + "&dataId=" + dataId + "&pointType=" + showChartPointType + "&dataType=" + chartIndex;
    var chartDivId = '#chart-' + chartIndex;
    // if ($(chartDivId).length == 0) {
    var width = $('.tab-content').width() + 'px';
    var height = $('.tab-content').height() / 3 + 'px';
    $('#' + contentId).append(
        "<div id='"
        + chartDivId
        + "' style='width:" + width + ";height:" + height + "'>"
        + "</div>"
    );
    // }
    poll('GET', GET_CHART_DATA, dataGet, function (response) {
        var respData = response.result;
        if (!respData) {
            return
        }
        refreshPreNextButton(respData, dataId);
        // debugger;
        var index = 0;
        for (var i = 0; i < respData.charts.length; i++) {
            initVibrationTendChart(respData.charts[i], chartDivId, chartIndex);
        }
    }, function () {

    }, false);
}

function initVibrationTendChart(chartData, chartID, chartIndex) {
    var myChart = echarts.init(document.getElementById(chartID));
    // var myChart = initChart(chartID);
    var colors = ['orange', '#00CC00', 'red'];
    var dataXValue = [" "];
    console.log(chartIndex)
    var legendData = chartData.legendData;
    for (var i = 0; i < legendData.length; i++) {
        legendData[i] = getI18nName(legendData[i]);
    }
    var option = {
        color: [colors[chartIndex % 3]],
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            },
            confine: true
        },
        legend: {
            data: legendData
        },
        title: {
            text: getI18nName(chartData.chartName),
            x: 'left',
            y: 'top',
            textAlign: 'left'
        },
        calculable: true,
        xAxis: [
            {
                type: 'category',
                data: dataXValue,
                boundaryGap: false,
                triggerEvent: true
            }
        ],
        yAxis: [
            {
                type: 'value'
            }
        ],
        series: []
    };
    if (option) {
        var xvalue = chartData.xValue;
        if (chartData.xUnit) {
            option.xAxis[0].name = stringFormat.format(getI18nName('unitParse'), chartData.xUnit);
        }
        if (chartData.yUnit) {
            option.yAxis[0].name = stringFormat.format(getI18nName('unitParse'), chartData.yUnit);
        }
        for (var j = 0; j < chartData.dataList.length; j++) {
            chartData.dataList[j].name = getI18nName(chartData.dataList[j].name);
            var data = chartData.dataList[j];
            data.showSymbol = false;
        }
        // debugger;
        option.xAxis[0].data = xvalue;
        option.series = chartData.dataList;
        myChart.setOption(option);
    }

}
var vibrationParams = {
    "pointName": "",
    "sensorCode": "",
    "sample_date": "",
    "sample_time": "",
    "sensorType": "",
    "AMPAVGX": "μm",
    "AMPAVGY": "μm",
    "AMPAVGZ": "μm",
    "AMPMAXX": "μm",
    "AMPMAXY": "μm",
    "AMPMAXZ": "μm",
    "ACCAVGX": "g",
    "ACCAVGY": "g",
    "ACCAVGZ": "g",
    "ACCMAXX": "g",
    "ACCMAXY": "g",
    "ACCMAXZ": "g",
    "MAXFreqX0": "Hz",
    "MAXFreqY0": "Hz",
    "MAXFreqZ0": "Hz",
    "MAXFreqX1": "Hz",
    "MAXFreqY1": "Hz",
    "MAXFreqZ1": "Hz",
    "MAXFreqX2": "Hz",
    "MAXFreqY2": "Hz",
    "MAXFreqZ2": "Hz",
};
function initVibrationParams() {
    var content = '';
    for (var item in vibrationParams) {
        var name = getI18nName(item);
        if (item == "sensorCode") {
            item = "aduId";
        }
        content += '      <div class="col-sm-4">' +
            '                    <div class="form-group">' +
            '                    <label class="col-sm-7 control-label" style="padding-right:5px">' + name + '</label>' +
            '                    <div class="col-sm-5">' +
            '                    <label class=" control-label" id="' + item + '"></label>' +
            '                    </div>' +
            '                    </div>' +
            '                </div>';
    }
    $('#characterParamContent').html(content);
}
function getVibrationParams(dataId) {
    var dataGet = "pointId=" + showChartPointId + "&dataId=" + dataId + "&pointType=" + showChartPointType;
    pollGet(GET_CHART_PARAM_DATA, dataGet, function (response) {
        var result = response.result;
        refreshPreNextButton(result, dataId);
        for (var param in result) {
            if (param.indexOf("MAXFreq") == -1) {
                $('#' + param).html(result[param] + " " + (vibrationParams[param] ? vibrationParams[param] : ''));
            } else {
                var array = result[param];
                for (var i = 0; i < array.length; i++) {
                    var freqID = param + i;
                    $('#' + freqID).html(array[i] + " " + vibrationParams[freqID]);
                }
            }
        }
        $('#sensorType').html(getI18nName(result.sensorType));
    }, function () {

    })
}


function initChart(id) {
    var chartDiv = document.getElementById(id);
    if (chartDiv) {
        var chart = echarts.getInstanceByDom(chartDiv);
        if (chart === undefined) {
            chart = echarts.init(chartDiv, null);
        }
        return chart;
    }
    return null;
}