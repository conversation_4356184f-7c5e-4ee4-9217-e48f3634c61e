{"loginTime": "<PERSON><PERSON><PERSON><PERSON> gian đ<PERSON>ng nh<PERSON>p", "changerUser": "Chuyển đổi người dùng", "change": "<PERSON><PERSON><PERSON><PERSON> đổi", "login": "<PERSON><PERSON><PERSON>", "systemShutDownConfirmTip": "<PERSON><PERSON> thống sẽ tắt trong vòng 1 phú<PERSON>, bạn có hủy hoạt động không?", "systemShutDownTip": "Hết 1 phút, tự động tắt máy!", "systemShutDownCancel": "Hủy bỏ Shutdown", "loadingData": "<PERSON><PERSON> cố gắng tải dữ li<PERSON>u, h<PERSON><PERSON> đợi...", "pageSize": "{1} b<PERSON><PERSON> vi<PERSON>t trên mỗi trang", "pageSelecter": "<PERSON><PERSON><PERSON> thị: {1}-{2}, tổng số {3}}", "search": "<PERSON><PERSON><PERSON>", "noMatch": "<PERSON><PERSON><PERSON><PERSON> tìm thấ<PERSON> hồ sơ phù hợp", "index": "Số sê-ri", "type": "<PERSON><PERSON><PERSON>", "aduId": "Mã hóa", "connectedState": "<PERSON>r<PERSON><PERSON> thái mạng", "commType": "<PERSON><PERSON><PERSON><PERSON> thức liên lạc", "loraSignalLevel": "<PERSON>ư<PERSON>ng độ tín hiệu <PERSON>", "loraSNR": "Tỷ lệ tín hiệu nhi<PERSON>u", "powerSupplyMode": "<PERSON><PERSON><PERSON> cung cấp đi<PERSON>n", "byBattery": "<PERSON>n t<PERSON><PERSON> h<PERSON>p", "byCable": "<PERSON><PERSON><PERSON><PERSON> điện bên ngoài", "battaryPercent": "<PERSON><PERSON><PERSON>", "battaryLife": "<PERSON><PERSON><PERSON><PERSON> gian k<PERSON>o dài (ngày)", "deviceNameTable": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> bị", "pointType": "<PERSON><PERSON><PERSON>", "isconnected": "<PERSON><PERSON><PERSON><PERSON> thái", "onlineState": "<PERSON><PERSON><PERSON><PERSON>", "offlineState": "<PERSON><PERSON><PERSON> li<PERSON> l<PERSON>", "aduNeverConnectedState": "<PERSON><PERSON><PERSON> k<PERSON>", "updateTime": "<PERSON><PERSON><PERSON><PERSON> gian c<PERSON><PERSON> nh<PERSON>t", "data": "<PERSON><PERSON> liệu", "chart": "Sơ đồ", "operate": "<PERSON><PERSON><PERSON> đ<PERSON>", "lookUp": "<PERSON><PERSON>", "tbSampleDate": "<PERSON><PERSON><PERSON><PERSON> gian thu thập", "collect": "<PERSON>hu thập", "errorTip": "Sai lầm: {1}", "AEamp": "G<PERSON>á trị AE", "aeChartTitle": "Bản đồ AE", "humidityChartTitle": "<PERSON><PERSON><PERSON><PERSON> cong độ <PERSON>m", "temperatureChartTitle": "<PERSON><PERSON><PERSON><PERSON> cong nhiệt độ", "noiseChartTitle": "<PERSON><PERSON><PERSON><PERSON> cong nhi<PERSON>u", "coreGroundingCurrentChartTitle": "<PERSON><PERSON><PERSON><PERSON> cong lõi nối đất hiện tại", "TEVamp": "Định dạng TEV", "TEVYunit": "Đơn vị [dB]", "UnitFomat": "Đơn vị [{1}]", "unitParse": "Đơn vị [{1}]", "maxValue": "<PERSON><PERSON><PERSON> đa", "minValue": "<PERSON><PERSON><PERSON> thi<PERSON>u", "average": "<PERSON>rung bình", "AT&T": "Mỹ AT&T", "China Unicom": "Trung Quốc Unicom", "China Mobile": "Trung Quốc Mobile", "Transformer": "<PERSON><PERSON><PERSON>", "Breaker": "<PERSON><PERSON> m<PERSON>ch", "Disconnector": "<PERSON><PERSON><PERSON> tắc c<PERSON>ch ly", "Isolator": "Khóa dao", "Arrester": "<PERSON><PERSON><PERSON>", "PT": "<PERSON><PERSON><PERSON><PERSON> áp điện áp", "CT": "<PERSON><PERSON><PERSON><PERSON> dòng điện", "Busbar": "<PERSON><PERSON> bu<PERSON><PERSON>", "Circuit": "<PERSON><PERSON><PERSON> kết mẹ", "Switchgear": "<PERSON><PERSON> chuyển đổi", "Power Cable": "<PERSON><PERSON><PERSON>", "Lightning Rod": "<PERSON><PERSON><PERSON> thu lôi", "Wall Bushing": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON> tay <PERSON>o", "Reactor": "Điện trở", "Electric Conductor": "<PERSON><PERSON><PERSON> dẫn điện (Conductor Row)", "Power Capacitor": "Điện Container", "Discharge Coil": "Cuộn xả", "Load Switch": "<PERSON><PERSON><PERSON> tắc tải", "Grounding Transformer": "Thay đổi mặt đất", "Grounding Resistance": "Điện trở mặt đất", "Grounding Grid": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i đất", "Combined Filter": "<PERSON><PERSON> l<PERSON><PERSON> kế<PERSON> h<PERSON>p", "Insulator": "<PERSON><PERSON><PERSON> c<PERSON>ch đi<PERSON>n", "Coupling Capacitor": "<PERSON><PERSON> đi<PERSON>", "Cabinet": "<PERSON><PERSON> màn hình", "Other": "K<PERSON><PERSON><PERSON>", "Fuse": "<PERSON><PERSON><PERSON> ch<PERSON>", "Using Transformer": "<PERSON><PERSON> đổi sử dụng", "Arc Suppression Device": "<PERSON><PERSON><PERSON><PERSON> bị chống <PERSON>", "Main Transformer": "<PERSON><PERSON><PERSON><PERSON>", "Wave Trap": "Chặn sóng", "Combined Electric Appliance": "<PERSON><PERSON><PERSON><PERSON> bị kết hợp", "Combined Transformer": "<PERSON><PERSON> biến đổi kết hợp", "monitor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực", "dataSelect": "<PERSON><PERSON><PERSON> vấn dữ liệu", "themeSkin": "Trang chủ", "themeBlue": "<PERSON><PERSON><PERSON>", "settings": "<PERSON><PERSON><PERSON> hình hệ thống", "datetime_set": "Cài đặt thời gian", "language_set": "<PERSON><PERSON><PERSON><PERSON> lập ngôn ngữ", "soft_setting": "<PERSON><PERSON><PERSON><PERSON> lập hệ thống", "file_manage": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ", "instrument_set": "<PERSON><PERSON><PERSON> hình cảm biến", "file_point_set": "<PERSON><PERSON><PERSON> hình điểm đo", "alarm_param_set": "<PERSON><PERSON><PERSON> hình báo động", "alarm_manager": "<PERSON><PERSON><PERSON><PERSON> lý báo động", "audit_view": "<PERSON><PERSON><PERSON> to<PERSON> xem", "modbus_setting": "Cài đặt Modbus", "hardware_update": "<PERSON><PERSON><PERSON> nh<PERSON>t firmware", "collect_mode": "<PERSON><PERSON> độ thu thập", "net_set": "<PERSON><PERSON><PERSON><PERSON> lập mạng", "main_station_params": "Cấu h<PERSON>nh Home Station", "sync_data": "<PERSON><PERSON><PERSON> bộ hóa dữ liệu", "syncingData": "<PERSON><PERSON> đồng bộ hóa dữ liệu...", "syncingDataCancel": "<PERSON><PERSON><PERSON> đồng bộ", "syncingDataFailed": "Lỗi đồng bộ", "syncingDataSuccess": "<PERSON><PERSON><PERSON> thành dữ liệu đồng bộ", "syncingDataProgress": "Ti<PERSON>n trình dữ liệu đồng bộ: {1}", "syncingDataProgress2": "<PERSON><PERSON><PERSON> bộ hóa tiến trình dữ liệu [{1}]", "export_data": "<PERSON><PERSON><PERSON> dữ liệu", "system_info": "<PERSON><PERSON><PERSON><PERSON> tin hệ thống", "monitoringTable": "<PERSON><PERSON><PERSON> dữ liệu", "PD": "<PERSON>ộ cảm biến đặt cục bộ tích hợp", "PD_THREE": "<PERSON><PERSON> cảm biến đặt cục bộ tích hợp (3 trong 1)", "PD_FIVE": "<PERSON><PERSON> cảm biến đặt cục bộ tích hợp (5 trong 1)", "OLD_IS": "<PERSON><PERSON><PERSON> biến thông minh tần số cao", "MEU": "<PERSON><PERSON><PERSON> biến đặc điểm c<PERSON> học", "UHF_IS": "<PERSON><PERSON><PERSON> biến thông minh tần số cao", "HFCT_IS": "<PERSON><PERSON><PERSON> biến thông minh tần số cao", "PD_IS": "<PERSON><PERSON><PERSON> biến thông minh 3 trong 1 bên ngo<PERSON>i", "TRANSFORMER_AE_IS": "<PERSON><PERSON><PERSON> biến siêu âm Transformer", "GIS_AE_IS": "<PERSON><PERSON><PERSON> bi<PERSON>n siêu âm G<PERSON>", "ENV_IS": "<PERSON><PERSON><PERSON> biến thông minh môi trường", "Arrester_U_IS": "<PERSON><PERSON><PERSON> biến thông minh điện áp chống sét", "Arrester_I_IS": "<PERSON><PERSON><PERSON> biến thông minh chống sét hiện tại", "LeakageCurrent_IS": "<PERSON><PERSON><PERSON> biến thông minh hiện tại rò rỉ", "Vibration_IS": "<PERSON><PERSON><PERSON> biến thông minh rung", "MECH_IS": "Đặc điểm cơ khí Giám sát thông minh", "TEMP_HUM_IS": "<PERSON><PERSON><PERSON> biến nhi<PERSON>t độ và độ ẩm", "GrounddingCurrent_IS": "<PERSON><PERSON><PERSON> biến thông minh hiện tại mặt đất", "MECH_PD_TEMP": "Đặc điểm cơ học đặt cục bộ tích hợ<PERSON> độ cảm biến 3 trong 1", "GIS_LOWTEN": "<PERSON><PERSON><PERSON> biến điện áp thấp", "CHANNEL_OPTICAL_TEMP": "<PERSON><PERSON><PERSON> biến nhiệt độ sợi quang", "VaisalaDTP145": "Mã sản phẩm: DTP145", "Wika_GDT20": "Trang chủ/GDT20", "Wika_GDHT20": "Trang chủ » GDHT20", "SHQiuqi_SC75D_SF6": "<PERSON><PERSON><PERSON><PERSON><PERSON> SC75D", "SPTR_IS": "<PERSON><PERSON><PERSON> biến dòng điện mặt đất lõi", "TEMPFC_OUT": "<PERSON><PERSON><PERSON> n<PERSON> độ (CLMD)", "TEMPXP_OUT": "<PERSON><PERSON><PERSON> độ (SPS061)", "SF6YN_OUT": "<PERSON><PERSON><PERSON> bi<PERSON> á<PERSON> suất khí SF6 (FTP-18)", "FLOOD_IS": "<PERSON><PERSON><PERSON> biến thông minh ngâm nước", "TEMPKY_OUT": "<PERSON><PERSON><PERSON> n<PERSON> độ (RFC-01)", "SMOKERK_OUT": "<PERSON><PERSON><PERSON> đ<PERSON> kh<PERSON> (RS-YG-N01)", "HIKVIDEO_OUT": "<PERSON><PERSON><PERSON> video (HIK)", "TEMPSB_OUT": "<PERSON><PERSON><PERSON> n<PERSON> độ (DS18B20)", "TEMP_HUM_JDRK_OUT": "<PERSON><PERSON><PERSON> biến nhi<PERSON>t độ và độ ẩm (RS-WS-N01-2)", "SF6_OUT": "<PERSON><PERSON>m biến SF6&O2 (WFS-S1P-SO)", "FAN_OUT": "<PERSON><PERSON> điều khiển quạt", "LIGHT_OUT": "<PERSON><PERSON> điều khiển chiếu sáng", "NOISE_JDRK_OUT": "<PERSON><PERSON><PERSON> bi<PERSON>n tiếng <PERSON> (RS-WS-N01)", "IR_DETECTION_OUT": "<PERSON><PERSON><PERSON> dò gi<PERSON>m định đôi hồng ngoại", "TEMPWS_OUT": "<PERSON><PERSON><PERSON> biến nhi<PERSON>t độ và độ ẩm (WS-DMC100)", "FLOODWS_OUT": "<PERSON><PERSON><PERSON> biến ngâm n<PERSON>ớ<PERSON> (WS-DMC100)", "NOISEWS_OUT": "<PERSON><PERSON><PERSON> bi<PERSON>n tiếng <PERSON>n (WS-DMC100)", "VibrationSY_OUT": "<PERSON><PERSON><PERSON> bi<PERSON>n rung Ascending", "TEVPRPS_IS": "<PERSON><PERSON><PERSON> biến thông minh điện áp tạm thời", "SF6_IS": "<PERSON><PERSON><PERSON> biến thông minh SF6", "phase": "<PERSON><PERSON><PERSON>", "period": "<PERSON>", "AMP": "<PERSON><PERSON><PERSON> d<PERSON>ng", "RMS": "<PERSON><PERSON><PERSON> tr<PERSON> hợp lệ", "max": "<PERSON><PERSON><PERSON> đa chu kỳ", "fre1Value": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>n tần số 1", "fre2Value": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> tần s<PERSON> 2", "All-pass": "<PERSON><PERSON><PERSON> bộ", "Low-pass": "<PERSON><PERSON><PERSON><PERSON> thấp", "High-pass": "<PERSON>", "No-Record": "<PERSON><PERSON><PERSON><PERSON> ghi lại", "TEV": "<PERSON><PERSON><PERSON><PERSON> tạm thời", "AE": "<PERSON><PERSON><PERSON>", "TEMP": "Nhiệt độ", "HFCT": "<PERSON>ần số cao hiện tại", "UHF": "Tần số đặc biệt cao", "Humidity": "<PERSON><PERSON> <PERSON><PERSON>", "Decibels": "Name", "Noisy": "<PERSON><PERSON> su<PERSON>", "Noise": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ENVGas": "<PERSON><PERSON><PERSON> quy<PERSON>n", "envgas": "<PERSON> h<PERSON> khí quyển", "MP": "Đặc t<PERSON>h cơ học", "FLOOD": "<PERSON><PERSON><PERSON><PERSON>", "SMOKE": "<PERSON><PERSON><PERSON>", "SF6": "<PERSON><PERSON><PERSON> h<PERSON> hexafluoride", "FAN": "Quạt", "LIGHT": "<PERSON><PERSON><PERSON>", "NOISE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IRDETECTION": "<PERSON><PERSON><PERSON> ng<PERSON>", "SF6YN": "SF6 áp suất khí", "ArresterU": "<PERSON><PERSON><PERSON><PERSON> chống <PERSON>t", "ArresterI": "<PERSON><PERSON><PERSON> s<PERSON>t hiện tại", "LeakageCurrent": "rò rỉ hiện tại", "Vibration": "<PERSON><PERSON> đ<PERSON>", "FrostPointRaw": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> giá", "FrostPointATM": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> gi<PERSON> (áp suất khí quyển tiêu chuẩn)", "DewPointRaw": "<PERSON><PERSON><PERSON><PERSON>", "DewPointATM": "<PERSON><PERSON><PERSON><PERSON> (á<PERSON> su<PERSON>t khí quyển tiêu chuẩn)", "Moisture": "Micro nước", "AbsolutePressure": "<PERSON><PERSON> <PERSON><PERSON> t<PERSON> đ<PERSON>i", "NormalPressure": "<PERSON><PERSON> su<PERSON>t ti<PERSON><PERSON>", "Density": "<PERSON><PERSON><PERSON>", "Oxygen": "<PERSON><PERSON><PERSON> oxy", "SPTR": "l<PERSON>i đất hiện tại", "GrounddingCurrent": "Mặt đất hiện tại", "VIDEO": "<PERSON><PERSON><PERSON> video", "TEVPRPS": "<PERSON><PERSON><PERSON><PERSON> <PERSON>p tạm thời PRPS", "confirm": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "yes": "Vâng", "no": "K<PERSON>ô<PERSON>", "continue": "<PERSON><PERSON><PERSON><PERSON> tục đi.", "none": "K<PERSON>ô<PERSON>", "DISK": "<PERSON><PERSON><PERSON> tr<PERSON>", "MEMORY": "Bộ nhớ", "noTestData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu thử nghiệm", "connected": "<PERSON><PERSON><PERSON>", "disconnected": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "virtualKeyBord": "Bàn ph<PERSON><PERSON>", "pleaseInput": "<PERSON><PERSON> lòng nh<PERSON>p", "pleaseInputFormat": "<PERSON><PERSON> lòng nhập {1}", "tips": "Mẹo", "confirmTips": "Mẹo xác nhận", "getDirFailed": "Lỗi l<PERSON>y thư mục:", "backupSuccessTip": "<PERSON><PERSON> liệu đã đư<PERSON><PERSON> sao lưu thành công, vui lòng kiểm tra trong thư mục {1}", "backupFailedTip": "<PERSON><PERSON> lưu dữ liệu thất bại", "exportFailedTip": "<PERSON><PERSON>t dữ liệu không thành công: {1}", "historyDataTypeNotSuport": "<PERSON>hông hỗ trợ truy vấn dữ liệu lịch sử loại cảm biến này", "stopExportTips": "<PERSON><PERSON> cần ngừng xuất dữ liệu không?", "stationNameTips": "<PERSON><PERSON> lòng điền tên trang web", "powerUnitNameTips": "<PERSON><PERSON> lòng điền đơn vị điện", "selectDeviceTips": "<PERSON><PERSON> lòng chọn thiết bị một lần", "selectPointTips": "<PERSON><PERSON> lòng chọn điểm đo", "selectDeviceOfPointTips": "<PERSON><PERSON> lòng chọn thiết bị một lần cần thêm điểm đo", "saveSuccess": "<PERSON><PERSON><PERSON> thành công", "saveFailed": "Lỗi lưu:", "operatFailed": "<PERSON><PERSON><PERSON> động thất bại: {1}", "chosseSensorUpdate": "<PERSON><PERSON> lòng chọn cảm biến cần cập nhật", "chooseFileUpdate": "<PERSON><PERSON> lòng chọn Update File", "cancelUpdateSensorConfirm": "<PERSON><PERSON><PERSON> nhận hủy cập nhật chương trình cảm biến không?", "enterServerUrlTips": "<PERSON><PERSON> lòng nhập địa chỉ máy chủ", "enterServerUrlError": "Đ<PERSON><PERSON> chỉ máy chủ nhập sai, vui lòng nhập lại.", "enterServerPortTips": "<PERSON><PERSON> lòng nh<PERSON>p cổng máy chủ.", "enterServerPortError": "<PERSON><PERSON><PERSON> má<PERSON> chủ nhập sai, ph<PERSON><PERSON> vi cổng nhập: [1,65535].", "NTPserverIpError": "<PERSON><PERSON> nhập sai địa chỉ máy chủ, vui lòng nhập lại.", "NTPserverPortError": "<PERSON><PERSON><PERSON> máy chủ thời gian đ<PERSON><PERSON><PERSON>h<PERSON> sai, phạ<PERSON> vi đầu vào cổng: [1,65535].", "enterNetName": "<PERSON><PERSON> lòng nhập tên mạng.", "enterIP": "<PERSON><PERSON> l<PERSON> nhập IP.", "enterIPError": "<PERSON>hập sai địa chỉ IP như: ***********", "enterNetMask": "<PERSON><PERSON> lòng nh<PERSON>p Subnet Mask", "enterNetError": "Mặt nạ mạng con nh<PERSON><PERSON>, chẳng hạn như *************", "enterGateWay": "<PERSON><PERSON> lòng nh<PERSON>p <PERSON>", "enterGateWayError": "Lỗi cấu hình cổng nh<PERSON>: ***********", "enterAPN": "<PERSON><PERSON> lòng chọn APN", "pdSampleIntervalTip": "PD - <PERSON><PERSON><PERSON><PERSON> thời gian lấy mẫu từ 20s đến 594099s (99h99m99s)", "meuSampleIntervalTip": "MEU - <PERSON><PERSON><PERSON><PERSON> thời gian lấy mẫu từ 10s đến 86400", "monitorAwakeTimeTip": "<PERSON><PERSON><PERSON><PERSON> thời gian ngủ đông từ 1s đến 86400", "monitorSleepSpaceTip": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian thức d<PERSON>y: [{1}, {2}]min", "uploadIntervalTip": "<PERSON><PERSON><PERSON><PERSON> thời gian tải lên từ 60s đến 86400", "sampleIntervalTip": "<PERSON><PERSON> nguyên đư<PERSON><PERSON> lấy mẫu từ 1 giờ đến 72 giờ", "monitorSampleTimeTip": "Thời điểm lấy mẫu bắt đầu của máy chủ là toàn bộ thời điểm từ 0 giờ đến 23 giờ", "monitorSampleIntervalTip": "<PERSON><PERSON> nguyên đư<PERSON><PERSON> lấy mẫu bởi máy chủ trong khoảng thời gian từ 1 giờ đến 168 giờ", "updatingSensor": "<PERSON><PERSON> cập nhật cảm biến...", "updatingSensorProgress": "<PERSON><PERSON><PERSON><PERSON> độ cập nhật cảm biến: {1}", "updatingSensorProgress2": "<PERSON><PERSON><PERSON><PERSON> trình cập nhật cảm biến [{1}]", "selectInstTip": "<PERSON><PERSON> lòng chọn cảm biến cần sửa đổi", "selectChannelTip": "<PERSON><PERSON> lòng chọn kênh cần sửa đổi", "instCodeTip": "<PERSON><PERSON> hóa cảm biến không thể để trống", "commLinkTypeTip": "<PERSON><PERSON> lòng chọn loại liên kết", "commLinkPortTip": "<PERSON><PERSON> lòng chọn cổng giao tiếp", "continuSampTimeTip": "<PERSON>h<PERSON><PERSON> gian thu thập liên tục là từ {1} phú<PERSON> đến {2} phút.", "continuSampTimeCompareTip": "Thời gian thu thập liên tục phải nhỏ hơn khoảng thời gian lấy mẫu.", "instSampleSpaceTip": "<PERSON>h<PERSON><PERSON> gian lấy mẫu là từ {1} phút đến {2} phút.", "instSampleStartTimeTip": "Thời điểm lấy mẫu bắt đầu là toàn bộ thời điểm từ 0 giờ đến 23 giờ.", "instNumberPattenTip": "Chỉ có thể chứa các <PERSON> 0-9, các chữ cái a-zA-Z và:", "instIPPattenTip": "IP và cổng định dạng như: 0.0.0.0:1", "deleteInstTip": "<PERSON><PERSON> lòng chọn cảm biến cần xóa", "selectSensorTip": "<PERSON><PERSON> lòng chọ<PERSON>", "deleteInstConfirmTip": "<PERSON><PERSON><PERSON> đ<PERSON> lo<PERSON> bỏ</br>{1}</br><PERSON><PERSON> hóa cảm biến: {2}?", "activeInstConfigTip": "<PERSON><PERSON><PERSON> đ<PERSON>nh cảm biến tương tự áp dụng cấu hình này cho</br>{1}?", "activeBatchConfigsSuccess": "<PERSON><PERSON><PERSON> hình hàng loạt cảm biến thành công", "activeBatchConfigsBegin": "<PERSON><PERSON><PERSON> hình hàng loạt cảm biến B<PERSON>t đầu", "activeBatchConfigsFailed": "<PERSON><PERSON><PERSON> biến cấu hình hàng loạt không thành công", "collectStartOnceTip": "<PERSON><PERSON><PERSON> chắc chắn để bắt đầu thu thập dữ liệu từ các cảm biến tương tự?", "collectStartTip": "<PERSON><PERSON><PERSON> chắc chắn để bắt đầu thu thập dữ liệu</br>{1}</br><PERSON><PERSON> hóa cảm biến: {2}?", "wakeUpInstrumentOnceTip": "Bạn có chắc chắn để bắt đầu đ<PERSON>h thức các cảm biến tương tự?", "wakeUpInstrumentTip": "<PERSON><PERSON><PERSON> chắc chắn để bắt đầu đ<PERSON>h thức</br>{1}</br><PERSON><PERSON><PERSON> biến: {2}?", "emptySensorDataOnceTip": "<PERSON><PERSON><PERSON> định làm trống tất cả dữ liệu của cảm biến tương tự</br>{1}?", "emptySensorDataTip": "<PERSON><PERSON><PERSON> định tất cả dữ liệu để trống</br>{1}</br><PERSON><PERSON> hóa cảm biến: {2}?", "emptySensorDataSuccess": "<PERSON><PERSON><PERSON> dữ liệu cảm biến thành công", "emptySensorDataFailed": "<PERSON><PERSON><PERSON> dữ liệu cảm biến thất bại:", "ae_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - Sơ đồ AE", "uhf_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - <PERSON><PERSON><PERSON> đồ UHF", "hfct_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - Sơ đồ HFCT", "tev_chart": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - Đ<PERSON><PERSON> dạng TEV", "tev_prps_chart": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - <PERSON><PERSON><PERSON><PERSON> đồ PRPS điện áp tạm thời", "temperature_chart": "<PERSON> thời gian thực - <PERSON><PERSON><PERSON> độ", "humidity_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - <PERSON><PERSON>", "mechanical_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - Đặc t<PERSON>h cơ học", "arrester_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thự<PERSON> - <PERSON><PERSON><PERSON>", "leakage_current_chart": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - Rò rỉ hiện tại", "grounddingcurrent_chart": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - Dòng điện mặt đất", "vibration_pickup_chart": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - Rung", "density_micro_water_history": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - <PERSON><PERSON><PERSON> độ vi nước", "core_grounding_current": "<PERSON><PERSON><PERSON><PERSON> sát thời gian thực - lõi nối đất hiện tại", "noise_chart": "<PERSON> thời gian thực - <PERSON><PERSON><PERSON><PERSON>n", "water_immersion": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - ngâm nước", "smoke_sensor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - <PERSON><PERSON><PERSON> gi<PERSON>c kh<PERSON>i", "video_sensor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - video", "sf6_sensor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t thời gian thực - SF6", "error": "Lỗi", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "success": "<PERSON><PERSON><PERSON><PERSON> công", "userErrorTip": "<PERSON>ên người dùng hoặc mật khẩu sai", "errorNoDeviceId": "<PERSON> thi<PERSON>t bị không tồn tại", "errorNoSensorId": "ID cảm biến không tồn tại", "errorExistSensorId": "Sensor ID đã có", "errorNoChannelId": "Channel ID không tồn tại", "errorNoPointId": "<PERSON><PERSON><PERSON><PERSON> đo không tồn tại", "errorDataExportErrDir": "<PERSON><PERSON><PERSON> d<PERSON> li<PERSON>, đ<PERSON><PERSON><PERSON> dẫn tập tin sai.", "errorDataExportErrTime": "Lỗi thời gian xuất dữ liệu", "errorDataExportNoData": "<PERSON><PERSON><PERSON> dữ liệu, kh<PERSON><PERSON> có dữ liệu.", "errorDataExportNoSpace": "<PERSON><PERSON><PERSON> li<PERSON>u, <PERSON> đ<PERSON>y", "errorDataExportNoDisk": "<PERSON><PERSON><PERSON> d<PERSON> liệu, <PERSON><PERSON><PERSON><PERSON> có <PERSON>.", "errorDataExportNoInfo": "<PERSON><PERSON><PERSON> dữ liệu, kh<PERSON>ng có thông tin trang web.", "errorDataExportOther": "<PERSON><PERSON><PERSON> dữ liệu, c<PERSON>c lỗi khác", "errorSetModeBus": "Lỗi thiết lập modebus", "errorSameNamePoint": "<PERSON><PERSON><PERSON><PERSON> đo cùng tên đã tồn tại", "errorIP": "Lỗi IP", "errorGPRSSet": "Lỗi thi<PERSON>t lập GPRS", "errorSenorUpdateErrFile": "Lỗi cập nhật tập tin cảm biến", "errorSenorUpdateNotMatch": "<PERSON><PERSON><PERSON> nh<PERSON>t cảm biến, lo<PERSON><PERSON> cảm biến không khớp với tệp nâng cấp", "errorSenorUpdating": "<PERSON><PERSON><PERSON> biến cập nhật, đang cập nhật", "errorSenorUpdateSizeCheckFailed": "<PERSON>ểm tra kích thước firmware không thành công", "errorSenorUpdateVersionCheckFailed": "<PERSON><PERSON>m tra số phiên bản firmware không thành công", "errorSenorUpdateDeviceTypeCheckFailed": "Loại thiết bị firmware Kiểm tra không thành công", "errorSenorUpdateCRCCheckFailed": "Kiểm tra CRC firmware không thành công", "errorSenorUpdateFailed": "<PERSON><PERSON><PERSON> cấp cảm biến thất bại", "errorSenorUpdateConnectErr": "Bản tin cập nhật firmware bất thường", "errorDataCleanFailed": "<PERSON><PERSON>a dữ liệu thất bại!", "errorCodeWorkGroupNotExist": "<PERSON><PERSON><PERSON><PERSON> làm việc không tồn tại!", "errorCodeInvalidChannel": "<PERSON><PERSON><PERSON> đi bất hợp pháp!", "errorCodeInvalidWirignMode": "<PERSON><PERSON><PERSON> kết n<PERSON>i bất hợp pháp!", "errorCodeInvalidAlarmMode": "<PERSON><PERSON><PERSON> g<PERSON>i cảnh sát bất hợp pháp!", "errorNoPermission": "User kh<PERSON>ng có quyền này", "illegalUser": "<PERSON><PERSON><PERSON><PERSON> dùng bất hợp pháp!", "legalPattenMsg": "Chỉ có thể chứa các ký tự (không có dấu cách): chữ cái và số kanji chữ số La Mã Ⅰ-Ⅻ`. () [] # 、 _", "groundCurrent": "<PERSON><PERSON><PERSON><PERSON> đồ xu hướng hiện tại mặt đất", "groundCurrentA": "<PERSON><PERSON><PERSON> pha đất hiện tại", "groundCurrentB": "B <PERSON>iai đoạn đất hiện tại", "groundCurrentC": "C <PERSON><PERSON><PERSON> đoạn nối đất hiện tại", "leakageCurrent": "<PERSON><PERSON><PERSON><PERSON> đồ xu hướng hiện tại đầy đủ", "leakageCurrentA": "A pha đầy đủ hiện tại", "leakageCurrentB": "B pha đầy đủ hiện tại", "leakageCurrentC": "C pha đầy đủ hiện tại", "ResistiveCurrent": "<PERSON><PERSON><PERSON><PERSON> đồ xu hướng kh<PERSON>g hiện tại", "ResistiveCurrentA": "A <PERSON>iai đoạn kháng hiện tại", "ResistiveCurrentB": "B Giai đo<PERSON>n kháng hiện tại", "ResistiveCurrentC": "C G<PERSON><PERSON> đo<PERSON>n kháng hiện tại", "resistiveCurrentA": "A <PERSON>iai đoạn kháng hiện tại", "resistiveCurrentB": "B Giai đo<PERSON>n kháng hiện tại", "resistiveCurrentC": "C G<PERSON><PERSON> đo<PERSON>n kháng hiện tại", "referenceVoltageA": "<PERSON><PERSON><PERSON><PERSON> áp tham chiếu pha A", "referenceVoltageB": "<PERSON><PERSON><PERSON><PERSON> áp tham chiếu pha B", "referenceVoltageC": "<PERSON><PERSON><PERSON><PERSON> áp tham chiếu pha <PERSON>", "grounddingCurrent": "<PERSON><PERSON><PERSON><PERSON> đồ xu hướng hiện tại mặt đất", "grounddingCurrentA": "<PERSON><PERSON><PERSON> pha đất hiện tại", "grounddingCurrentB": "B <PERSON>iai đoạn đất hiện tại", "grounddingCurrentC": "C <PERSON><PERSON><PERSON> đoạn nối đất hiện tại", "timeDomain": "<PERSON><PERSON> đồ miền thời gian", "frequencyDomain": "<PERSON><PERSON> đồ miền tần số", "characterParam": "<PERSON><PERSON> số t<PERSON>h n<PERSON>ng", "TimeDomainDataX": "<PERSON><PERSON> hiệu rung trục X", "TimeDomainDataY": "<PERSON><PERSON> hiệu rung trục Y", "TimeDomainDataZ": "<PERSON><PERSON> hiệu rung trục Z", "FrequencyDomainDataX": "Phổ tín hiệu rung <PERSON><PERSON> thị trục X", "FrequencyDomainDataY": "<PERSON>ổ tín hiệu rung <PERSON><PERSON> thị trục Y", "FrequencyDomainDataZ": "Phổ tín hiệu rung <PERSON><PERSON> thị trục Z", "ACCAVGX": "<PERSON><PERSON> tốc trục X trung bình", "ACCAVGY": "Trung bình gia tốc trục Y", "ACCAVGZ": "Trung bình gia tốc trục <PERSON>", "ACCMAXX": "<PERSON><PERSON> tốc trục X <PERSON>", "ACCMAXY": "<PERSON><PERSON> tốc tr<PERSON><PERSON>", "ACCMAXZ": "<PERSON><PERSON> tốc tr<PERSON><PERSON>", "AMPAVGX": "Trung bình biên độ trục X", "AMPAVGY": "Trung bình biên độ trục Y", "AMPAVGZ": "Trung bình biên độ trục Z", "AMPMAXX": "<PERSON><PERSON><PERSON><PERSON> độ trục X <PERSON>", "AMPMAXY": "<PERSON><PERSON><PERSON><PERSON> độ trục Y <PERSON>ối đa", "AMPMAXZ": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> trụ<PERSON>", "MAXFreqX0": "<PERSON>ần số điểm cực trị trục X 1", "MAXFreqY0": "<PERSON>ần số điểm cực trị trục Y 1", "MAXFreqZ0": "<PERSON>ần số điểm cực trị trục Z 1", "MAXFreqX1": "<PERSON>ần số điểm cực trị trục X 2", "MAXFreqY1": "<PERSON>ần số điểm cực trị trục Y 2", "MAXFreqZ1": "<PERSON>ần số điểm cực trị trục Z 2", "MAXFreqX2": "<PERSON>ần số điểm cực trị trục X 3", "MAXFreqY2": "<PERSON>ần số điểm cực trị trục Y 3", "MAXFreqZ2": "<PERSON><PERSON>n số điểm cực trị trục Z 3", "sensorType": "<PERSON><PERSON><PERSON> cảm <PERSON>n", "sensorList": "<PERSON><PERSON> s<PERSON>ch cảm bi<PERSON>n", "gain": "<PERSON><PERSON><PERSON>", "trigger": "<PERSON><PERSON>nh dạng kích ho<PERSON>t", "wave_filter": "<PERSON><PERSON><PERSON>n", "sample_date": "<PERSON><PERSON><PERSON> l<PERSON> mẫu", "sample_time": "Th<PERSON>i gian mẫu", "rms": "<PERSON><PERSON><PERSON> tr<PERSON> hợp lệ", "cycle_max": "<PERSON><PERSON><PERSON> đa chu kỳ", "frequency1": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>n tần số 1", "frequency2": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> tần s<PERSON> 2", "time_interval": "<PERSON><PERSON><PERSON><PERSON> thời gian", "realData": "<PERSON><PERSON> liệu thời gian thực", "historyData": "<PERSON><PERSON> li<PERSON>u l<PERSON>ch sử", "trendSync": "<PERSON><PERSON> tích xu hư<PERSON>ng", "preData": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> tr<PERSON>", "nextData": "<PERSON><PERSON><PERSON><PERSON> theo", "systemDate": "<PERSON><PERSON><PERSON> thống", "systemTime": "<PERSON><PERSON><PERSON><PERSON> gian h<PERSON> thống", "backupType": "Loại backup", "plusBackup": "<PERSON>o lưu gia tăng", "allBackup": "<PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON> đủ", "timeRange": "<PERSON><PERSON><PERSON><PERSON> thời gian", "exportPath": "Đường dẫn xuất", "getExportDir": "<PERSON><PERSON><PERSON><PERSON> thư mục", "checkDir": "<PERSON><PERSON><PERSON> tra thư mục", "exportingData": "<PERSON><PERSON><PERSON> k<PERSON>...", "cancel": "Hủy bỏ", "export": "<PERSON><PERSON><PERSON>", "stationConfig": "<PERSON><PERSON><PERSON> hình trang web", "stationDelSuccess": "<PERSON>óa trang web thành công", "stationDelFailed": "Xóa trang web không thành công:", "deviceConfig": "<PERSON><PERSON><PERSON> hình thiết bị ch<PERSON>h", "pointConfig": "<PERSON><PERSON><PERSON> hình điểm đo", "stationName": "<PERSON><PERSON><PERSON> trang <PERSON>", "stationPMS": "Mã hóa trang web", "stationLevel": "<PERSON><PERSON><PERSON> hạng điện áp trang web", "powerUnit": "Đơn vị điện", "save": "<PERSON><PERSON><PERSON>", "operationSuccess": "<PERSON><PERSON><PERSON> động thành công", "deviceAddSuccessTips": "<PERSON><PERSON><PERSON> thi<PERSON>t bị tăng thành công", "deviceEditSuccessTips": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> thiết bị thành công", "deviceDelSuccessTips": "<PERSON><PERSON><PERSON> lần x<PERSON>a thiết bị thành công", "pointAddSuccessTips": "<PERSON><PERSON><PERSON><PERSON> đo tăng thành công", "pointEditSuccessTips": "Thay đổi điểm kiểm tra thành công", "pointDelSuccessTips": "<PERSON><PERSON><PERSON> điểm kiểm tra thành công", "deleteFailedTips": "Lỗi xóa", "save_add": "Ti<PERSON><PERSON> kiệm/Tăng", "delete": "Xoá", "deviceType": "<PERSON><PERSON><PERSON> thiết bị", "deviceLevel": "<PERSON><PERSON><PERSON> đi<PERSON>n áp thiết bị", "add": "<PERSON><PERSON><PERSON>", "channelTypeAlarm": "<PERSON><PERSON><PERSON> dữ liệu", "alarmThreshold": "Ngưỡng báo động", "alarmRecoveryThreshold": "Ngưỡng phục hồi báo động", "alarmChannel": "Lựa chọn kênh báo động", "built_in_channel_1": "<PERSON><PERSON><PERSON> tích hợ<PERSON> 1", "External_IO_module": "<PERSON><PERSON><PERSON><PERSON><PERSON> bên ngo<PERSON>i", "not_associated": "<PERSON><PERSON><PERSON><PERSON> liên quan", "alarmExternalIOSN": "<PERSON>ã hóa mô-đun IO bên ngoài", "alarmExternalIOChannel": "<PERSON><PERSON><PERSON> mô-đun I<PERSON> bên ngo<PERSON>i", "wiringMode": "<PERSON><PERSON><PERSON> k<PERSON>", "alarmMode": "<PERSON><PERSON><PERSON> b<PERSON>o động", "alarmTime": "<PERSON><PERSON><PERSON><PERSON> gian báo động", "alarmInterval": "<PERSON><PERSON><PERSON><PERSON> báo động", "alarmDuration": "<PERSON><PERSON><PERSON><PERSON> gian báo động", "normal_open": "Thường mở", "normal_close": "<PERSON><PERSON><PERSON><PERSON><PERSON> đóng", "continuous": "<PERSON><PERSON><PERSON>", "timing": "<PERSON><PERSON><PERSON><PERSON> gian", "interval": "<PERSON><PERSON><PERSON><PERSON>", "alarmSaveSuccess": "<PERSON><PERSON><PERSON> hình báo động đã lưu thành công", "alarmDelSuccess": "<PERSON><PERSON><PERSON> hình báo động đã xóa thành công", "deviceName": "Name", "pointName": "Name", "testStation": "<PERSON><PERSON> <PERSON> thử nghi<PERSON>m", "device": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON>", "pointList": "<PERSON><PERSON> s<PERSON>ch các điểm đo", "sensorID": "<PERSON> cảm bi<PERSON>n", "sensorChannel": "<PERSON><PERSON><PERSON> c<PERSON>n", "channelList": "<PERSON><PERSON> s<PERSON> k<PERSON>nh", "showPoint": "<PERSON><PERSON><PERSON> thị điểm đo", "fileSelect": "<PERSON><PERSON><PERSON> tập tin", "selectPlease": "<PERSON><PERSON> lòng ch<PERSON>n", "deviceList": "<PERSON><PERSON> s<PERSON>ch cảm bi<PERSON>n", "updating": "<PERSON><PERSON> cập nhật...", "updatingFailed": "<PERSON><PERSON><PERSON> nhật thất bại:", "updatingCanceled": "<PERSON><PERSON><PERSON> c<PERSON> nh<PERSON>t", "updatingComplete": "<PERSON><PERSON><PERSON> thành cập nh<PERSON>t", "update": "<PERSON><PERSON><PERSON>", "sampleDate": "<PERSON><PERSON><PERSON> l<PERSON> mẫu", "sampleTime": "Th<PERSON>i gian mẫu", "pdMax": "<PERSON><PERSON><PERSON> dạng xả tối đa", "pdAvg": "Định dạng xả trung bình", "pdNum": "<PERSON><PERSON> xung", "acquisitionTime": "<PERSON><PERSON><PERSON><PERSON> gian thu thập", "humidity": "<PERSON><PERSON> <PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "refresh": "<PERSON><PERSON><PERSON>", "scanData": "<PERSON><PERSON><PERSON> c<PERSON>", "sensorCode": "<PERSON><PERSON> hóa cảm bi<PERSON>n", "sensorTypeSet": "<PERSON><PERSON><PERSON> cảm <PERSON>n", "senorName": "<PERSON><PERSON><PERSON> cả<PERSON>n", "aduAddress": "<PERSON><PERSON><PERSON> chỉ cảm biến", "senorWorkMode": "<PERSON><PERSON> động giao hàng", "On": "Mở", "Off": "Tắt", "ADUMode": "<PERSON><PERSON> độ làm việc", "normalMode": "<PERSON><PERSON> độ bảo trì", "lowPowerMode": "<PERSON><PERSON> độ tiêu thụ điện năng thấp", "monitorMode": "<PERSON>ế độ giám s<PERSON>t", "artificialStateStartTime": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu can thiệp của con người.", "artificialStateStartTimeTip": "<PERSON>h<PERSON><PERSON> điểm b<PERSON><PERSON> đầu can thi<PERSON><PERSON> củ<PERSON> con người 0-23", "artificialStateEndTime": "Thời điểm kết thúc trạng thái can thiệp của con người (toàn bộ điểm)", "artificialStateEndTimeTip": "<PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> can thi<PERSON><PERSON> của con người <PERSON> vi thời gian kết thúc 0-23", "artificialStateWakeUpSpace": "Disable (adj): <PERSON><PERSON><PERSON><PERSON><PERSON> tậ<PERSON> (", "unartificialStateWakeUpSpace": "Disable (adj): <PERSON><PERSON><PERSON><PERSON><PERSON> tậ<PERSON> (", "isAutoChangeMode": "Tự động chuyển đổi chế độ", "monitorModeSampleSapce": "<PERSON><PERSON> độ gi<PERSON>m s<PERSON><PERSON> thời gian thu thập", "workGroup": "Số nhóm làm việc", "warnTemp": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> n<PERSON> độ", "taskGroup": "Số nhóm nhiệm vụ", "commLinkType": "<PERSON><PERSON><PERSON> liên kết tru<PERSON>ền thông", "commLinkPort": "<PERSON><PERSON><PERSON> giao ti<PERSON>", "frequencyUnit": "Tần s<PERSON> l<PERSON> (Hz)", "numInGroup": "Số trong nhóm", "commSpeed": "<PERSON><PERSON><PERSON> độ tru<PERSON>ền thông", "commLoad": "<PERSON><PERSON><PERSON> t<PERSON> thông", "sampleSpace": "<PERSON><PERSON><PERSON><PERSON> thời gian lấy mẫu (phút)", "sleepTime": "<PERSON><PERSON><PERSON> lư<PERSON><PERSON> tiêu thụ năng lượng", "samleStartTime": "<PERSON>h<PERSON><PERSON> gian lấy mẫu bắt đầu (toàn bộ điểm)", "applyData": "<PERSON><PERSON><PERSON> đ<PERSON>", "applyAllSensor": "<PERSON><PERSON> dụng cho cùng một lo<PERSON>i", "collectOnce": "<PERSON><PERSON> thập hàng <PERSON>", "collectOnceEnd": "Data Acquisition Đ<PERSON> kết thúc left", "collectOnceProgress": "<PERSON><PERSON> thập dữ liệu: {1}({2})", "collectOnceProgress2": "Thu thập dữ liệu", "activeOnce": "Ứng dụng hàng lo<PERSON>t", "wakeUp": "<PERSON><PERSON><PERSON>", "wakeUpInstrument": "<PERSON><PERSON><PERSON> bi<PERSON>n đ<PERSON> thức", "wakeUpInstrumentOnce": "<PERSON><PERSON><PERSON>", "orderSend": "<PERSON><PERSON>nh đã đư<PERSON><PERSON> đưa ra.", "cleanData": "<PERSON><PERSON><PERSON> dữ liệu", "sensorAddSuccess": "<PERSON><PERSON><PERSON> biến tăng thành công", "sensorEditSuccess": "<PERSON><PERSON> đ<PERSON>i cảm biến thành công", "sensorEditBegin": "<PERSON><PERSON><PERSON> đầu sửa đổi cảm biến", "sensorDelSuccess": "<PERSON><PERSON><PERSON> cảm biến thành công", "cleanSensorData": "<PERSON><PERSON><PERSON> dữ liệu cảm bi<PERSON>n", "getSensor": "<PERSON><PERSON><PERSON> c<PERSON>n", "channelType": "<PERSON><PERSON><PERSON> k<PERSON>", "channelName": "<PERSON><PERSON><PERSON> k<PERSON>", "channelInfoSaveSuccess": "Thông tin kênh được lưu thành công", "channelInfoSaveBegin": "<PERSON><PERSON><PERSON> thông tin kênh cảm biến B<PERSON>t đầu", "bias": "<PERSON><PERSON><PERSON><PERSON> vị [dB]", "waveFilter": "<PERSON><PERSON><PERSON> đặt băng tần", "gainMode": "<PERSON><PERSON> độ tăng", "gain_unit": "Tăng [dB]", "samCycle": "<PERSON><PERSON> chu kỳ lấy mẫu", "samPointNum": "<PERSON><PERSON> điểm lấy mẫu mỗi chu kỳ", "samRate": "<PERSON><PERSON> điểm lấy mẫu mỗi chu kỳ", "ratio": "Tỷ l<PERSON> bi<PERSON>n", "channelPhase": "Phân bi<PERSON>t", "mecLoopCurrentThred": "Ngưỡng hiện tại cuộn dây [mA]", "mecMotorCurrentThred": "Ngưỡng dòng động cơ [mA]", "mecSwitchState": "Chuyển đổi số lượng trạng thái ban đầu", "awaysOpen": "Thường mở", "awaysClose": "<PERSON><PERSON><PERSON><PERSON><PERSON> đóng", "mecBreakerType": "<PERSON><PERSON><PERSON> h<PERSON>nh cơ chế ngắt mạch", "oneMech": "<PERSON><PERSON><PERSON> kết cơ khí ba pha (một cơ chế)", "threeMech": "<PERSON><PERSON><PERSON> kết điện ba pha (ba c<PERSON> chế)", "mecMotorFunctionType": "<PERSON><PERSON><PERSON> công vi<PERSON><PERSON> động cơ", "threePhaseAsynMotor": "Đ<PERSON>ng cơ không đồng bộ ba pha", "onePhaseMotor": "Động c<PERSON> giao nhau đơn/DC", "setCurrent": "Đặt hiện tại", "setAll": "<PERSON><PERSON> dụng cho cùng một loại cảm biến", "showCoil": "<PERSON><PERSON><PERSON> đồ hiện tại của cuộn dây", "showSwitch": "<PERSON><PERSON> đồ khối lư<PERSON><PERSON> chuyển đổi", "showMotor": "Sơ đồ dòng điện động cơ", "showOrig": "Bản đồ Original Current", "actionDate": "<PERSON><PERSON><PERSON> h<PERSON>nh đ<PERSON>ng", "actionTime": "<PERSON><PERSON><PERSON><PERSON> gian hành động", "phaseA": "Giai đ<PERSON>n <PERSON>", "phaseB": "Giai <PERSON> B", "phaseC": "G<PERSON>i <PERSON>", "coil_charge_time": "Cuộn dây Power Up Thời gian", "coil_cutout_time": "<PERSON><PERSON><PERSON><PERSON> dây tắt thời gian", "max_current": "<PERSON><PERSON><PERSON><PERSON> dây tối đa hiện tại", "hit_time": "Bỏ qua thời gian.", "subswitch_close_time": "Thời gian chuyển đổi công tắc phụ", "a_close_time": "<PERSON><PERSON> gian đ<PERSON>g c<PERSON>a", "a_close_coil_charge_time": "<PERSON><PERSON><PERSON><PERSON> gian bật nguồn cho cuộn dây pha A", "a_close_coil_cutout_time": "A giai đoạn cuộn dây thời gian tắt nguồn", "a_close_max_current": "<PERSON><PERSON><PERSON> cuộn dây tương thích tối đa hiện tại", "a_close_hit_time": "<PERSON><PERSON> gian ng<PERSON>t kết n<PERSON>i", "a_close_subswitch_close_time": "A pha hỗ trợ chuyển đổi thời gian", "b_close_time": "<PERSON><PERSON><PERSON><PERSON><PERSON> gian đ<PERSON>g c<PERSON>a", "b_close_coil_charge_time": "<PERSON><PERSON><PERSON><PERSON> gian bật nguồn cho cuộn dây pha B", "b_close_coil_cutout_time": "<PERSON>h<PERSON><PERSON> gian tắt nguồn cho cuộn dây giai đoạn B", "b_close_max_current": "<PERSON> <PERSON><PERSON><PERSON><PERSON> thích <PERSON>st<PERSON> tối đa hiện tại", "b_close_hit_time": "<PERSON><PERSON><PERSON><PERSON> gian ng<PERSON>t kết n<PERSON>i", "b_close_subswitch_close_time": "B pha hỗ trợ chuyển đổi thời gian", "c_close_time": "<PERSON>h<PERSON><PERSON> gian đóng cửa pha <PERSON>", "c_close_coil_charge_time": "C Giai đoạn Coil Power Up Thời gian", "c_close_coil_cutout_time": "C giai đoạn cuộn dây mất điện thời gian", "c_close_max_current": "<PERSON> cuộn dây tương thích tối đa hiện tại", "c_close_hit_time": "<PERSON><PERSON><PERSON> gian ng<PERSON>t kết n<PERSON>i", "c_close_subswitch_close_time": "C pha hỗ trợ chuyển đổi thời gian", "close_sync": "<PERSON><PERSON><PERSON> thời đ<PERSON>g c<PERSON>a", "close_time": "<PERSON><PERSON><PERSON><PERSON> gian đ<PERSON>g c<PERSON>a", "a_open_time": "<PERSON>hời gian chia pha A", "a_open_coil_charge_time": "<PERSON><PERSON><PERSON><PERSON> gian bật nguồn cho cuộn dây pha A", "a_open_coil_cutout_time": "A giai đoạn cuộn dây thời gian tắt nguồn", "a_open_max_current": "A pha chia cuộn dây tối đa hiện tại", "a_open_hit_time": "a) <PERSON>h<PERSON><PERSON> gian tách rời", "a_open_subswitch_close_time": "A pha hỗ trợ chuyển đổi thời gian", "b_open_time": "<PERSON>hời gian chia pha B", "b_open_coil_charge_time": "<PERSON><PERSON><PERSON><PERSON> gian bật nguồn cho cuộn dây pha B", "b_open_coil_cutout_time": "<PERSON>h<PERSON><PERSON> gian tắt nguồn cho cuộn dây giai đoạn B", "b_open_max_current": "B pha chia cuộn dây tối đa hiện tại", "b_open_hit_time": "<PERSON><PERSON><PERSON><PERSON> gian tách rời", "b_open_subswitch_close_time": "B pha hỗ trợ chuyển đổi thời gian", "c_open_time": "<PERSON>hời gian chia pha <PERSON>", "c_open_coil_charge_time": "C Giai đoạn Coil Power Up Thời gian", "c_open_coil_cutout_time": "C giai đoạn cuộn dây mất điện thời gian", "c_open_max_current": "C pha chia cuộn dây tối đa hiện tại", "c_open_hit_time": "<PERSON><PERSON> gian tách rời", "c_open_subswitch_close_time": "C pha hỗ trợ chuyển đổi thời gian", "open_sync": "<PERSON>a sẻ cùng kỳ", "open_time": "<PERSON>h<PERSON>i gian chia", "a_twice_open_time": "<PERSON><PERSON> <PERSON><PERSON><PERSON> đo<PERSON>n thứ hai chia thời gian", "a_twice_open_coil_charge_time": "A giai đoạn thứ cấp chia cuộn dây thời gian sống", "a_twice_open_coil_cutout_time": "A giai đoạn thứ cấp ngắt điện cuộn dây", "a_twice_open_max_current": "A pha thứ cấp chia cuộn dây tối đa hiện tại", "a_twice_open_hit_time": "a) Th<PERSON>i gian lo<PERSON>i bỏ", "a_twice_open_subswitch_close_time": "A pha thứ cấp chia công tắc phụ trợ thời điểm ngắt kết nối", "b_twice_open_time": "<PERSON><PERSON> <PERSON><PERSON><PERSON> đ<PERSON>n thứ cấp chia thời gian", "b_twice_open_coil_cutout_time": "B giai đoạn thứ cấp ngắt điện cuộn dây", "b_twice_open_max_current": "B pha thứ cấp chia cuộn dây tối đa hiện tại", "b_twice_open_hit_time": "b) <PERSON>h<PERSON><PERSON> gian tách rời", "b_twice_open_subswitch_close_time": "<PERSON><PERSON><PERSON> tắc phụ trợ ngắt kết nối giai đoạn B", "c_twice_open_time": "<PERSON><PERSON><PERSON> gian chia tách thứ cấp", "c_twice_open_coil_cutout_time": "C giai đoạn thứ cấp ngắt điện cuộn dây", "c_twice_open_max_current": "<PERSON> <PERSON>ha thứ cấp chia cuộn dây tối đa hiện tại", "c_twice_open_hit_time": "<PERSON><PERSON> gian tách rời", "c_twice_open_subswitch_close_time": "C pha thứ cấp chia công tắc phụ trợ thời điểm ngắt kết nối", "twice_open_sync": " <PERSON><PERSON> chia đồng thời", "twice_open_time_text": "<PERSON><PERSON><PERSON>i gian chia tách thứ cấp", "a_switch_shot_time": "<PERSON><PERSON> gian <PERSON>", "b_switch_shot_time": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON>n", "c_switch_shot_time": "<PERSON><PERSON> gian <PERSON>", "a_switch_no_current_time": "A pha không có thời gian hiện tại", "b_switch_no_current_time": "B pha không có thời gian hiện tại", "c_switch_no_current_time": "C pha không có thời gian hiện tại", "motor_start_current": "<PERSON>ộng cơ khởi động hiện tại", "motor_max_current": "Tối đa hiện tại của động cơ", "storage_time": "<PERSON>h<PERSON><PERSON> gian lưu trữ năng lượng động cơ", "Chan_A_motor_start_current": "A pha động cơ bắt đầu hiện tại", "Chan_A_motor_max_current": "<PERSON>òng điện tối đa cho động cơ A pha", "Chan_A_storage_time": "Th<PERSON><PERSON> gian lưu trữ năng lượng của động cơ A pha", "Chan_B_motor_start_current": "B pha động c<PERSON> bắt đ<PERSON>u hiện tại", "Chan_B_motor_max_current": "<PERSON>òng điện tối đa của động cơ pha B", "Chan_B_storage_time": "Thời gian lưu trữ năng lượng của động cơ giai đoạn B", "Chan_C_motor_start_current": "C pha động c<PERSON> bắt đầu hiện tại", "Chan_C_motor_max_current": "Dòng điện tối đa của động cơ C pha", "Chan_C_storage_time": "Th<PERSON><PERSON> gian lưu trữ năng lượng của động cơ C pha", "serialPort": "<PERSON><PERSON><PERSON> n<PERSON>i ti<PERSON>", "baudRate": "<PERSON><PERSON><PERSON> độ tru<PERSON>n", "dataBit": "bit dữ liệu", "stopBit": "Dừng bit", "checkBit": "<PERSON><PERSON><PERSON> tra bit", "singleCollect": "<PERSON><PERSON> thập hàng <PERSON>", "sampling": "<PERSON><PERSON> thu thập......", "serverIP": "IP máy chủ", "serverPort": "C<PERSON>ng m<PERSON>y chủ", "NTPserverIp": "IP máy chủ NTP", "NTPserverPort": "Cổng máy chủ NTP", "netType": "<PERSON><PERSON><PERSON> m<PERSON>ng", "deviceIp": "M<PERSON>y chủ IP", "subnetMask": "Mặt nạ mạng con", "gateway": "Cổng mặc định", "networkAPN": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> mạng APN", "userName": "<PERSON><PERSON><PERSON> dùng", "passWord": "<PERSON><PERSON><PERSON>", "deviceWorkGroup": "Số nhóm làm việc của máy chủ", "frequency": "<PERSON>ần số lưới điện", "pdSampleInterval": "PD - K<PERSON><PERSON><PERSON> lấy mẫu", "spaceSecond": "  giây", "meuSampleInterval": "MEU - <PERSON><PERSON><PERSON><PERSON> thời gian l<PERSON> mẫu", "monitorAwakeTime": "<PERSON><PERSON><PERSON><PERSON> thời gian ngủ đông", "monitorSleepSpace": "<PERSON><PERSON><PERSON><PERSON> thời gian thức tỉnh", "wakeStartTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> (toàn bộ)", "intervalServer": "<PERSON><PERSON><PERSON><PERSON> thời gian tải lên (máy chủ)", "intervalMinus": "Khoảng mẫu", "continuousAcquisitionTime": "<PERSON><PERSON><PERSON><PERSON> gian thu thập liên tục", "startSampleTime": "<PERSON><PERSON><PERSON> chủ bắt đầu lấy mẫu thời điểm", "spacePoint": "  <PERSON><PERSON><PERSON><PERSON>", "startSampleInterval": "<PERSON><PERSON><PERSON><PERSON> thời gian lấy mẫu bắt đầu của máy ch<PERSON>h", "hour": "Giờ", "poerOffSpace": "<PERSON><PERSON><PERSON><PERSON> thời gian t<PERSON>t", "powerOnSpace": "<PERSON><PERSON><PERSON><PERSON> thời gian khởi động", "dateRange": "Phạm vi ngày", "synchronizing": "<PERSON><PERSON> đồng bộ......", "synchronize": "<PERSON><PERSON><PERSON> bộ", "amplitude": "<PERSON><PERSON><PERSON> d<PERSON>ng", "switch": "<PERSON><PERSON><PERSON><PERSON> đổi", "copyRight": "© 2020 PMDT Ltd.", "S1010Name": "<PERSON><PERSON><PERSON> thu thập dữ liệu", "modbusCompanySelf": "<PERSON>a thừa", "logOut": "<PERSON><PERSON><PERSON><PERSON>", "logOutTitle": "<PERSON><PERSON><PERSON> xu<PERSON>t x<PERSON>c <PERSON>n", "logOutConfirm": "<PERSON><PERSON><PERSON><PERSON> đăng nhập người dùng hiện tại?", "logOutTips": "<PERSON><PERSON> tho<PERSON>t đ<PERSON>ng <PERSON>p", "enterHost": "<PERSON><PERSON> lòng nhập tên máy", "selectDevTip": "<PERSON><PERSON> lòng chọn thiết bị", "stopSyncDataTip": "Bạn có cần ngừng đồng bộ hóa dữ liệu không?", "modbusAddress": "Địa chỉ ModBus", "modbusAddressCheckTip": "Phạm vi địa chỉ ModBus là 1~254 số nguyên", "deviceWorkGroupCheckTip": "Số nhóm làm việc của máy chủ là số nguyên, phạm vi: [{1}~{2}]", "emptyMonitorDatas": "<PERSON><PERSON>a dữ liệu máy chủ", "emptyMonitorDatasTip": "<PERSON><PERSON><PERSON> tất cả dữ liệu lịch sử trong máy chủ", "emptyMonitorDatasSuccess": "<PERSON><PERSON><PERSON> dữ liệu thành công", "emptyMonitorDatasApply": "<PERSON><PERSON><PERSON> cầu dữ liệu trống đã đư<PERSON><PERSON> nộp và đang chờ xem xét", "resetSoftSet": "<PERSON><PERSON><PERSON><PERSON> phục cài đặt gốc", "resetSoftSetTip": "<PERSON><PERSON><PERSON><PERSON> phục tất cả các cấu hình trong máy chủ thu thập về trạng thái xuất xưởng", "resetSoftSetSuccess": "<PERSON><PERSON><PERSON> cầu khôi phục cài đặt gốc đã được gửi và đang chờ xem xét", "continueConfirmTip": "<PERSON><PERSON> tác này không thể khôi phục, còn muốn tiếp tục sao?", "bias_data": "Phạm vi giá trị bù đắp -100-100", "back_ground_data": "Phạm vi giá trị nền 0-100", "sam_point": "<PERSON><PERSON><PERSON> tần số lấy mẫu 20-2000 mỗi chu kỳ", "sam_rate": "<PERSON><PERSON><PERSON> tần số lấy mẫu 20-2000 mỗi chu kỳ", "smartSensorStatus": "<PERSON><PERSON>ng trạng thái", "upThreshold": "<PERSON><PERSON><PERSON><PERSON> hạn ngưỡng trên", "lowerThreshold": "G<PERSON><PERSON>i hạn dưới ngưỡng", "changeThreshold": "Thay đổi ngưỡng", "changeThresholdLimit": "<PERSON><PERSON><PERSON>t lập ngưỡng chỉ hỗ trợ một số thập phân", "upThresholdTip": "<PERSON><PERSON><PERSON>i hạn trên ngưỡng", "lowerThresholdTip": "G<PERSON><PERSON>i hạn dưới ngưỡng", "changeThresholdTip": "Phạm vi ngưỡng thay đổi", "upLowerThresholderrTip": "Giới hạn dưới ngưỡng không được lớn hơn giới hạn trên ngưỡng", "monitorName": "<PERSON><PERSON><PERSON>", "monitorType": "Loại Hosting", "MonitorTypeHanging": "treo t<PERSON>", "MonitorType2U": "2U", "MonitorTypeCollectionNode": "<PERSON><PERSON><PERSON> c<PERSON>", "MonitorTypeLowPower": "<PERSON><PERSON><PERSON> chủ năng lượng mặt trời tiêu thụ thấp", "devicePMSCode": "<PERSON><PERSON> hóa thiết bị", "forceChange": "<PERSON>y<PERSON><PERSON> đổi chế độ", "forceChangeSuccess": "<PERSON><PERSON> độ chuyển đổi thành công", "currentVersion": "<PERSON><PERSON><PERSON> bản hiện tại", "abnormalRecover": "<PERSON><PERSON> phục hồi bất thường", "fromLabel": "<PERSON><PERSON><PERSON><PERSON> gian b<PERSON><PERSON> đầu", "toLabel": "<PERSON><PERSON><PERSON><PERSON> gian kết thúc", "select": "<PERSON><PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON><PERSON>", "tuesday": "<PERSON>", "wednesday": "Ba", "thursday": "Bốn.", "friday": "Năm.", "saturday": "<PERSON><PERSON><PERSON>", "January": "<PERSON><PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON>áng Ba", "April": "<PERSON><PERSON><PERSON><PERSON>", "May": "<PERSON><PERSON><PERSON><PERSON>", "June": "<PERSON><PERSON><PERSON><PERSON>", "July": "<PERSON><PERSON><PERSON><PERSON>", "August": "<PERSON><PERSON><PERSON><PERSON>", "September": "<PERSON><PERSON><PERSON><PERSON>", "October": "<PERSON><PERSON><PERSON><PERSON>", "November": "<PERSON><PERSON><PERSON><PERSON>", "December": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "strong": "Mạnh mẽ", "weak": "<PERSON><PERSON><PERSON>", "poor": "<PERSON><PERSON><PERSON>", "min": "Phân chia", "second": "giây", "uploadInterval": "<PERSON><PERSON><PERSON><PERSON> thời gian tải lên (min)", "loginWelcome": "<PERSON><PERSON><PERSON> mừng đăng nh<PERSON>p", "dateStartIsAfterDateEnd": "<PERSON><PERSON><PERSON> bắt đầu lớn hơn ngày kết thúc, vui lòng chọn lại", "maxCurrent": "<PERSON><PERSON>i đa hiện tại", "standMobus": "<PERSON><PERSON><PERSON> sản xuất cảm biến", "config1": "Cấu hình 1", "config2": "Cấu h<PERSON> 2", "fWrnThreshold": "<PERSON><PERSON> <PERSON> đến ngưỡng", "fAlmThreshold": "Ngưỡng cảnh báo", "regularDormancy": "<PERSON>h<PERSON><PERSON> gian ngủ đông", "noDormancy": "<PERSON><PERSON><PERSON>ng ngủ đông", "soakingWater": "<PERSON><PERSON><PERSON>", "dry": "<PERSON><PERSON><PERSON>", "normal": "<PERSON><PERSON><PERSON>", "alarm": "<PERSON><PERSON><PERSON> b<PERSON>o", "lightGunCamera": "<PERSON><PERSON> thể nhìn thấy ánh sáng bu lông", "lightBallCamera": "<PERSON><PERSON><PERSON> quang cầu có thể nhìn thấy", "infraredGunCamera": "<PERSON><PERSON><PERSON> hồng ngoại hai mắt", "infraredBallCamera": "<PERSON><PERSON><PERSON> b<PERSON>g hồng ngoại hai mắt", "maxTemp": "Nhiệt độ tối đa", "maxTempPosition": "<PERSON><PERSON> trí nhiệt độ tối đa", "devIPError": "<PERSON> n<PERSON>y bị chi<PERSON>m", "unknown": "Không rõ", "fault": "Lỗi", "lowPower": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> thấp", "mediumPower": "<PERSON><PERSON><PERSON><PERSON> trung bình", "highPower": "<PERSON><PERSON><PERSON> l<PERSON> cao", "slaveid": "ID nô lệ", "LoraFrequency": "<PERSON><PERSON> v<PERSON><PERSON> m<PERSON>y chủ", "AREA_CHINA": "<PERSON><PERSON><1>", "AREA_VIETNAM": "<PERSON><PERSON><PERSON><PERSON> (AS1)", "AREA_MALAYSIA": "<PERSON><PERSON><PERSON><PERSON> (AS1)", "AREA_EUROPE": "Châu Âu", "AREA_US": "<PERSON><PERSON><PERSON>", "AREA_INDONESIA": "<PERSON><PERSON><PERSON><PERSON> (AS2)", "AREA_INDIA": "Ấn độ", "AREA_KOREA": "<PERSON><PERSON><PERSON>", "AREA_CHINA_RSV": "<PERSON><PERSON> (dự phòng)<2>", "getLogFileError": "Lỗi lấy tập tin nhật ký cảm biến", "exportLogError": "Tập tin nhật ký không tồn tại và xuất không thành công", "alertSyncProgress": "<PERSON><PERSON><PERSON> nh<PERSON>t đồng bộ dữ liệu cảnh báo: {1}", "alertSyncProgress2": "<PERSON><PERSON><PERSON><PERSON> báo cập nhật đồng bộ dữ liệu [{1}]", "FIRMWARE_EXCEPTION": "firmware bất thường", "AD_INITIALIZATION_EXCEPTION": "AD khởi tạo ngoại lệ", "REFERENCE_VOLTAGE_EXCEPTION": "<PERSON><PERSON><PERSON><PERSON> áp tham chiếu bất thường", "ONCHIP_FLASH_EXCEPTION": "<PERSON> b<PERSON>t th<PERSON>", "OFFCHIP_FLASH_EXCEPTION": "<PERSON> b<PERSON>t th<PERSON>", "SYSTEM_PARAMETERS_EXCEPTION": "<PERSON><PERSON><PERSON><PERSON> số hệ thống bất thường", "SAMPLE_PARAMETERS_EXCEPTION": "<PERSON>ham số thu thập bất thường", "CALIBRATION_PARAMETERS_EXCEPTION": "<PERSON><PERSON><PERSON><PERSON> số hiệu chuẩn bất thường", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "<PERSON><PERSON><PERSON> hồi thông số hệ thống bất thường", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "<PERSON><PERSON> thập thông số phục hồi bất thường", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "<PERSON>h<PERSON><PERSON> số hiệu chu<PERSON>n <PERSON> hồ<PERSON> bất thường", "LORA_MODULE_EXCEPTION": "LoRa Module ngo<PERSON> l<PERSON>", "PHASE_NUM": "<PERSON><PERSON><PERSON>", "DISCONNECT_TIME": "<PERSON><PERSON><PERSON><PERSON> gian ng<PERSON>t kết n<PERSON>i", "DATA_TOTAL_COUNT": "Tổng số dữ liệu", "ONLINE_RATE": "Tỷ lệ trực tuyến", "aduInfoSetProgress": "<PERSON><PERSON><PERSON> hình cảm biến: {1}({2})", "aduInfoSetProgress2": "<PERSON><PERSON> cảm bi<PERSON> (Sensor Configuration)", "aduInfoSetting": "<PERSON><PERSON> c<PERSON>u hình cảm biến...", "aduInfoSetEnd": "<PERSON><PERSON><PERSON> hình cảm biến đã hoàn thành", "aduDataSample": "<PERSON>hu thập dữ liệu", "aduInfoSet": "<PERSON><PERSON><PERSON> hình cảm biến", "errorDataSampleRunning": "<PERSON><PERSON><PERSON> đang kinh doanh thu thập cảm bi<PERSON>n, vui lòng thử lại khi hoàn thành", "errorAduInfoSetRunning": "<PERSON><PERSON><PERSON> đang kinh doanh thiết lập tham số cảm biến, vui lòng thử lại khi hoàn thành", "SAMPLE_PERIOD": "Số sóng lấy mẫu hàng tuần", "SF6_Density": "<PERSON><PERSON><PERSON> độ khí SF6 (P20)", "SF6_Temp": "SF6 Nhiệt độ khí", "Device_env_Temp": "<PERSON><PERSON><PERSON><PERSON> bị <PERSON>t độ môi trường", "Alarm_Status": "<PERSON>hận dạng trạng thái cảnh báo", "SF6_Alarm_Low_Pressure": "<PERSON><PERSON><PERSON> động điện áp thấp", "SF6_Alarm_Low_Voltage_Block": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>n áp thấp", "SF6_Alarm_Over_Voltage": "<PERSON><PERSON><PERSON> động quá áp", "AlarmType": "<PERSON><PERSON><PERSON> cảnh báo", "AlarmData": "<PERSON><PERSON><PERSON> dung cảnh báo", "AlarmTime": "<PERSON><PERSON><PERSON><PERSON> gian cảnh b<PERSON>o", "AlarmLevel": "<PERSON><PERSON><PERSON> b<PERSON>o động", "AlarmStateWarning": "<PERSON><PERSON><PERSON> b<PERSON>o sớm", "WarningValue": "<PERSON><PERSON><PERSON> trị cảnh báo sớm", "AlarmValue": "<PERSON><PERSON><PERSON> trị báo động", "SetSuccess": "<PERSON><PERSON><PERSON><PERSON> lập thành công", "AlarmDate": "<PERSON><PERSON><PERSON><PERSON> gian báo động", "AlarmConfirm": "<PERSON><PERSON><PERSON> đ<PERSON>ng x<PERSON>c <PERSON>n", "Confirm": "<PERSON><PERSON><PERSON>", "Confirmed": "<PERSON><PERSON><PERSON>", "AllData": "<PERSON><PERSON>t cả dữ liệu", "CheckManagerSponsor": "Khởi tạo", "CheckManagerCheckType": "<PERSON><PERSON><PERSON>t", "CheckManagerDate": "Ngày khởi xướng", "CheckManagerTarget": "Người khởi xướng", "CheckManagerExtraInfo": "<PERSON><PERSON><PERSON><PERSON> tin bổ sung", "CheckManagerResult": "Đồng ý", "Refuse": "<PERSON><PERSON> chối", "Agree": "Đồng ý", "DataExport": "<PERSON><PERSON><PERSON> dữ liệu", "DataExportStep1": "<PERSON><PERSON><PERSON> dữ liệu cần xuất", "DataExportStep1Title": "<PERSON><PERSON><PERSON> tập tin xuất", "DataExportStep2": "<PERSON><PERSON><PERSON> to<PERSON> kích thước dữ liệu...", "DataExportStep2Title": "<PERSON><PERSON><PERSON> to<PERSON> kích thước dữ liệu thô", "DataExportStep3": "<PERSON><PERSON> đóng gói dữ liệu nén...", "DataExportStep3Title": "<PERSON><PERSON><PERSON> gói dữ liệu nén", "DataExportStep4": "<PERSON><PERSON> liệu xong", "DataExportStep4Title": "<PERSON><PERSON> liệu xong", "DataExportCheckData": "Cơ sở dữ liệu [/media/data/database]", "DataExportCheckDataFile": "Tập tin dữ liệu [/media/data/datafile]", "DataExportCheckLog": "<PERSON>ậ<PERSON> tin nhật ký [/media/data/log]", "DataExportCheckConfig": "Tập tin cấu hình [/home/<USER>/config.xml]", "DataExportBegin": "<PERSON><PERSON><PERSON> đ<PERSON>u xuất", "SelectAtLeastOneTips": "<PERSON><PERSON><PERSON> ít nhất một mục", "DataExportFileSizeError": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp lớn hơn 2000MB, vui lòng sử dụng các công cụ như Winscp để xuất dữ liệu", "DataExportFileSizeZeroError": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tập tin đã chọn là 0", "DataExportCancelTips": "<PERSON><PERSON> liệu đã xuất bị hủy", "DataExportDataCompressTips": "<PERSON><PERSON><PERSON> thước dữ liệu thô là {1}MB và thời gian nén dự kiến [{2}min{3}s]", "LogExportStep1": "<PERSON><PERSON><PERSON>", "LogExportStep1Title": "<PERSON><PERSON><PERSON>", "LogExportStep2": "<PERSON><PERSON><PERSON> tập tin nhật ký......", "LogExportStep2Title": "<PERSON><PERSON><PERSON> tập tin nhật ký", "LogExportStep3": "<PERSON><PERSON><PERSON> thành tập tin nhật ký", "LogExportStep3Title": "<PERSON><PERSON><PERSON> thành tập tin nhật ký", "LogExportStep4": "<PERSON><PERSON><PERSON> gói tập tin nhật ký nén......", "LogExportStep4Title": "<PERSON><PERSON><PERSON> gói tập tin nhật ký nén", "LogExportStep5": "<PERSON><PERSON>", "LogExportStep5Title": "<PERSON><PERSON>", "LogExportCancelTips": "<PERSON><PERSON><PERSON><PERSON> ký xuất bị hủy", "batteryVoltage": "<PERSON><PERSON><PERSON><PERSON> pin", "superCapvoltage": "<PERSON><PERSON><PERSON> đi<PERSON>n dung", "OverPressureThreshold": "Ngưỡng quá áp", "LowPressureThreshold": "Ngưỡng đi<PERSON>n áp thấp", "ShutThreshold": "Ngưỡng khóa", "PhysicalChannelType": "<PERSON><PERSON><PERSON> kênh vật lý", "GasAbsPressure": "<PERSON><PERSON> su<PERSON>t kh<PERSON> n<PERSON>", "GasGaugePressure": "<PERSON><PERSON><PERSON> hồ đo khí", "CameraType": "Loại camera", "DEFAULT_CHECK_Tips": "<PERSON><PERSON><PERSON> dạng đánh số là m: n, chẳng hạn như 1: 100, trong đó m phạm vi [1-255] và n phạm vi [0-65535]", "VibrationSY_OUT_CHECK_Tips": "<PERSON><PERSON><PERSON> dạng số là address:modbus:id, chẳng hạn như 1:0:1917, trong đó phạm vi địa chỉ, là [1-255] và modbus/id đều là [0-65535]", "NOISEWS_OUT_CHECK_Tips": "Đ<PERSON><PERSON> dạng đư<PERSON><PERSON> đ<PERSON>h số là ip: x: y, chẳng hạn như *********: 1321: 4512, trong đó định dạng ip [xxx.xxx.xxx.xxx.xxx] và x/y đều là chuỗi có độ dài 4", "IR_DETECTION_OUT_IO_CHECK_Tips": "<PERSON><PERSON><PERSON> dạng đư<PERSON><PERSON> đ<PERSON>h số là ip: port: ss: se: cs: ce, chẳng hạn như 1: 100, trong đó định dạng ip [xxx.xxx.xxx.xxx.xxx] phạm vi cổng [0-65535] phạm vi ss [0-255], phạm vi se 0-255], phạm vi cs 0-255], phạm vi ce 0-255", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "Định dạng đư<PERSON><PERSON> đ<PERSON>h số là ip:port:x:y, chẳng hạn như 1:100, nơi định dạng ip [xxx.xxx.xxx.xxx] Phạm vi cổng cổng [0-65535] Phạm vi x [1-255], phạm vi y [0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "<PERSON><PERSON><PERSON> dạng đánh số là m: n, chẳng hạn như 1: 100, trong đó m phạm vi [1-255] và n phạm vi [0-65535]", "fWrnThreshold_CHECK_Tips": "Phạm vi ngưỡng chú ý là [10-1000] mA", "fAlmThreshold_CHECK_1_Tips": "Ngưỡng cảnh báo là [50-5000] mA", "fAlmThreshold_CHECK_2_Tips": "Ngưỡng cảnh báo phải lớn hơn ngưỡng chú ý", "AuditContent": "<PERSON><PERSON><PERSON> dung kiểm toán", "OPTime": "<PERSON><PERSON><PERSON><PERSON> gian ho<PERSON>t động", "Executed": "<PERSON><PERSON><PERSON><PERSON>", "UserManager": "<PERSON><PERSON><PERSON><PERSON> lý ng<PERSON><PERSON> dùng", "SystemManagement": "<PERSON><PERSON><PERSON><PERSON> lý hệ thống", "BusinessManagement": "<PERSON><PERSON><PERSON>n tr<PERSON> kinh doanh", "LanguageChange": "<PERSON>y<PERSON>n đổi ngôn ngữ", "AlarmManagement": "<PERSON><PERSON><PERSON><PERSON> lý cảnh báo", "DataOperation": "<PERSON><PERSON> tác dữ liệu", "BackupRecovery": "<PERSON><PERSON><PERSON> backup", "AddUser": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON> dùng", "AddUserCheck": "<PERSON><PERSON><PERSON><PERSON> kiểm toán người dùng", "UserRightSet": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> quyền người dùng", "UserRightSetCheck": "<PERSON><PERSON><PERSON> tra thi<PERSON>t lập quyền người dùng", "DeleteUser": "<PERSON><PERSON><PERSON> ng<PERSON>ời dùng", "DeleteUserConfirm": "<PERSON><PERSON><PERSON> x<PERSON> n<PERSON>n người dùng", "FreezeUser": "<PERSON><PERSON><PERSON> băng ng<PERSON>ời dùng", "FreezeUserConfirm": "<PERSON><PERSON><PERSON> băng xác nhận người dùng", "UnlockUser": "Mở khóa người dùng", "UnlockUserConfirm": "Mở khóa xác nhận người dùng", "FileStationSet": "<PERSON><PERSON><PERSON><PERSON> lập tập tin (site)", "FileDeviceSet": "<PERSON><PERSON><PERSON><PERSON> lập tập tin (thiết bị)", "SenserSet": "<PERSON><PERSON><PERSON><PERSON> lập cảm biến", "AlarmSet": "<PERSON>ài đặt cảnh báo", "SavePointSet": "<PERSON><PERSON><PERSON> ca<PERSON>c thiết đặt điểm kiểm tra", "DelPointSet": "<PERSON><PERSON><PERSON> cài đặt đo điểm", "SingleSample": "<PERSON><PERSON> s<PERSON>u tập đ<PERSON>n", "DataBackup": "<PERSON><PERSON> lưu dữ liệu", "DataRecovery": "<PERSON><PERSON><PERSON> hồi dữ liệu", "ClearDataCheck": "<PERSON><PERSON><PERSON> tra dữ liệu trống", "RestoreFactorySettingsReview": "<PERSON><PERSON><PERSON><PERSON> phục kiểm tra cài đặt gốc", "SaveMainStation": "<PERSON><PERSON><PERSON> tr<PERSON><PERSON>", "DataView": "<PERSON><PERSON> liệu", "DataSync": "<PERSON><PERSON><PERSON> bộ hóa dữ liệu", "RightSet": "<PERSON><PERSON><PERSON> hình quyền", "GetUserList": "<PERSON><PERSON><PERSON> danh s<PERSON>ch ng<PERSON><PERSON>i dùng", "AuditData": "<PERSON><PERSON> liệu ki<PERSON>m toán", "AuditPermissions": "<PERSON><PERSON><PERSON><PERSON> quyền kiểm toán", "MainStationSet": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>p Home Station", "ChangePasswordSelfOnly": "<PERSON>hay đổi mật khẩu (chỉ người dùng này)", "ChangePasswordForNormal": "Thay đổi mật khẩu người dùng thông thường", "ChangeUserRight": "<PERSON><PERSON><PERSON> đ<PERSON>/thiế<PERSON> lập quyền người dùng", "ChangePassword": "<PERSON>hay đ<PERSON>i mật kh<PERSON>u", "ChangeLoginInfo": "<PERSON><PERSON><PERSON> đổi thông tin đăng nhập", "UserStatus": "<PERSON>r<PERSON>ng thái người dùng", "UserLanguage": "<PERSON><PERSON><PERSON> ngữ người dùng", "HasLogin": "Đã hạ cánh chưa?", "RoleType": "<PERSON><PERSON><PERSON> role", "IsPassTimeout": "<PERSON><PERSON><PERSON><PERSON>u hết giờ", "AuditsManagement": "<PERSON><PERSON><PERSON><PERSON> lý k<PERSON> toán", "SensorOperation": "<PERSON><PERSON><PERSON> độ<PERSON> cảm biến", "SensorLogExport": "<PERSON><PERSON><PERSON> nh<PERSON>t ký cảm biến", "SensorAlarmDataSync": "<PERSON><PERSON><PERSON> bộ hóa dữ liệu cảnh báo cảm biến", "ViewSensorAlarmData": "<PERSON>em d<PERSON> liệu cảnh b<PERSON>o cảm biến", "Block": "<PERSON><PERSON><PERSON> b<PERSON>", "BlockPendingReview": "<PERSON><PERSON><PERSON> b<PERSON>ng - đ<PERSON> đư<PERSON>c xem xét", "UnlockPendingReview": "<PERSON><PERSON> chờ x<PERSON> - To be reviewed", "DeletePendingReview": "Remove - <PERSON><PERSON><PERSON><PERSON> xem xét", "AddUserPendingReview": "<PERSON><PERSON> chờ x<PERSON> - To be reviewed", "ChangePassPendingReview": "Thay đổi mật khẩu - đ<PERSON> được xem xét", "ChangeRightPendingReview": "<PERSON><PERSON><PERSON>n sử<PERSON> đổi - <PERSON><PERSON><PERSON><PERSON> xem xét", "abnormal": "<PERSON><PERSON><PERSON>", "DataQueryNotSupported": "Không hỗ trợ truy vấn dữ liệu lịch sử kiểu front-end", "DeviceCodeExists": "<PERSON><PERSON><PERSON> thiết bị hoặc mã hóa thiết bị đã tồn tại", "OutputSwitchConfig": "<PERSON><PERSON><PERSON> hình công tắc kết nối", "OutputSwitch": "<PERSON><PERSON><PERSON> tắc kết n<PERSON>i", "MainStationAuxRtuID": "<PERSON><PERSON><PERSON><PERSON> khiển RtuID", "MainStationIedRtuID": "<PERSON><PERSON><PERSON>", "MainstationParams1": "Thông số trạm chính 1", "MainstationParams2": "Thông số trạm ch<PERSON> 2", "MainstationInterface1": "Trạm chính 1 giao diện", "MainstationInterface2": "Trạm chín<PERSON> 2 giao diện", "Port": "Cổng", "IPAddress": "Địa chỉ IP", "EnterUriTips": "<PERSON><PERSON> lòng nhập đường dẫn URI", "ConnectUserName": "Name", "EnterConnectUserNameTips": "<PERSON><PERSON> lòng nhập tên người dùng liên lạc", "ConnectPass": "<PERSON><PERSON><PERSON><PERSON> liên lạc", "EnterConnectPassTips": "<PERSON><PERSON> lòng nhập mật khẩu liên lạc", "DataSubmissionInterval": "<PERSON><PERSON><PERSON><PERSON> thời gian gửi dữ liệu", "EnderDataSBIntervalTips": "<PERSON><PERSON> lòng nhập kho<PERSON>ng thời gian gửi dữ liệu", "HeartbeatInterval": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON><PERSON> tim", "EnterHertbeatIntervalTips": "<PERSON><PERSON> lòng nh<PERSON><PERSON> k<PERSON> c<PERSON>ch nh<PERSON>p tim", "ConnectStatus": "<PERSON><PERSON><PERSON><PERSON> thái kết n<PERSON>i", "Reset": "Đặt lại", "Submit": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "RtuidRepeatTips": "RtuID phụ trợ và RtuID tổng hợp không giống nhau", "DataSubmissionIntervalTips": "<PERSON><PERSON><PERSON><PERSON> cách dữ liệu không được nhỏ hơn 60.", "HeartbeatIntervalTips": "<PERSON><PERSON><PERSON><PERSON> tim không đ<PERSON> nhỏ hơn 60", "MainstationParamsSaveSuccess": "Thông số trạm ch<PERSON>h đ<PERSON><PERSON><PERSON> lưu thành công", "MainstationParamsSaveFailed": "<PERSON><PERSON><PERSON> tham số trạm ch<PERSON>h không thành công", "OutSwitchSaveSuccess": "<PERSON><PERSON><PERSON> hình công tắc kết n<PERSON>i đ<PERSON><PERSON><PERSON> lưu thành công", "OutSwitchSaveFailed": "<PERSON><PERSON><PERSON> hình công tắc kết nối không lưu đ<PERSON>", "ChartConfig": "<PERSON><PERSON><PERSON> hình <PERSON>ồ", "Measurement": "<PERSON><PERSON>", "SENSOR_TIMEOUT_COUNT": "S<PERSON> lần x<PERSON>c đ<PERSON>nh cảm biến ngoại tuyến", "OUTWARD_CONFIG_ENABLE_STATE": "<PERSON><PERSON><PERSON><PERSON> thái cấu hình", "OUTWARD_CONFIG_ENABLED": "<PERSON><PERSON> bật", "OUTWARD_CONFIG_DISABLED": "Đã tắt", "OUTWARD_CONFIG_ENABLING": "<PERSON><PERSON> b<PERSON>t", "SENSOR_TIMEOUT_DISABLING": "<PERSON><PERSON>", "ERROR_NO_CONFIG": "<PERSON><PERSON><PERSON> hình không tồn tại", "ERROR_INPUT_PARAMS": "Lỗi tham số đầu vào", "ERROR_INTEGER": "<PERSON><PERSON> lòng nh<PERSON>p số nguyên", "ERROR_TIP_RANGE": "Phạm vi giá trị [{1}~{2}]", "ERROR_IP_FORMAT": "Định dạng IP không chính xác", "ERROR_FILE_NAME": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> ph<PERSON>i là {1}", "ERROR_FILE_SUFFIX": "Phần mở rộng tệp phải là {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "<PERSON><PERSON><PERSON> ph<PERSON>i nhỏ hơn {1} MB", "ERROR_T2_T1": "T2 phải nhỏ hơn T1", "LENGTH_COMM_LIMIT": "<PERSON><PERSON><PERSON> <PERSON>, trư<PERSON><PERSON> này không đ<PERSON><PERSON><PERSON> vư<PERSON><PERSON> quá {1} ký tự.", "MANU_FAST_SYNC_DATA": "<PERSON><PERSON><PERSON> bộ n<PERSON>h", "SYNC_DATA_STATE_LIST": "<PERSON><PERSON> s<PERSON>ch trạng thái đồng bộ cảm biến", "LAST_SYNC_DATE_RANGE": "Phạm vi ngày đồng bộ cuối cùng", "NoError": "<PERSON><PERSON><PERSON>ng có lỗi", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "<PERSON><PERSON><PERSON> th<PERSON>i gian đồng bộ", "SYNC_DATA_ERROR_DISCONNECT": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "SYNC_DATA_STATUS_WAITING": "<PERSON><PERSON> chờ kết nối", "SYNC_DATA_STATUS_CONNECTED": "<PERSON><PERSON> kết nối", "SYNC_DATA_STATUS_SYNCING": "<PERSON><PERSON> đồng bộ", "SYNC_DATA_STATUS_SUCCESS": "<PERSON><PERSON>ng bộ thành công", "SYNC_DATA_STATUS_FAILED": "<PERSON>ồng bộ thất bại", "SYNC_DATA_STATUS_CANCELED": "<PERSON><PERSON>ng bộ đã hủy", "Today": "<PERSON><PERSON><PERSON> nay", "Yesterday": "<PERSON><PERSON><PERSON> qua", "LAST_7_DAYS": "7 ngày qua", "LAST_30_DAYS": "30 ngày qua", "THIS_MONTH": "<PERSON><PERSON><PERSON><PERSON>", "LAST_MONTH": "<PERSON><PERSON><PERSON><PERSON>", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "<PERSON><PERSON><PERSON>ng thể khởi động dịch vụ liên lạc", "FastSyncDataCancelTips": "Bạn có chắc chắn muốn hủy đồng bộ dữ liệu không?", "FastSyncDataSelectTips": "<PERSON><PERSON> lòng chọn cảm biến để đồng bộ dữ liệu", "Band-pass": "<PERSON><PERSON><PERSON> thông", "aeWaveChartTitle": "Phổ dạng sóng AE", "aePhaseChartTitle": "Phổ pha AE", "aeFlyChartTitle": "Phổ bay AE", "LOCAL_PARAMS_TIME": "<PERSON><PERSON><PERSON><PERSON> gian", "LOCAL_CHART_COLORDENSITY": "<PERSON><PERSON><PERSON> độ màu", "openTime": "Th<PERSON>i gian mở", "closeTime": "<PERSON><PERSON><PERSON><PERSON> gian đóng", "triggerUnit": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> kích ho<PERSON>[μV]", "openTimeUnit": "Thời gian mở[μs]", "closeTimeUnit": "<PERSON>h<PERSON><PERSON> gian đóng[μs]", "triggerUnitTips": "Phạm vi biên độ kích hoạt: [{1}, {2}] μV", "TOP_DISCHARGE_AMPLITUDE": "<PERSON><PERSON><PERSON><PERSON> xả TOP3", "WS_901_ERROR_TIPS": "<PERSON><PERSON><PERSON> vụ đẩy lệnh đang đư<PERSON><PERSON> đ<PERSON>ng ký, vui lòng thử lại sau 2 gi<PERSON>y"}