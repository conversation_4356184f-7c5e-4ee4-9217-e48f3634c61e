// 传感器树管理构造函数
function SensorTreeManager(containerId) {
  this.containerId = containerId;
  this.zTreeObj = null;
}

// 在原型上定义方法
SensorTreeManager.prototype = {
  // 重新指定构造函数
  constructor: SensorTreeManager,

  // zTree配置
  setting: {
    check: {
      enable: true,
      chkStyle: 'checkbox',
      radioType: 'all',
    },
    data: {
      simpleData: {
        enable: true,
        idKey: 'id',
        pIdKey: 'pId',
        rootPId: 0,
      },
    },
    callback: {
      onClick: function (event, treeId, treeNode) {
        if (treeNode.level != 4) {
          const zTree = $.fn.zTree.getZTreeObj('fu-aduItems');
          zTree.checkNode(treeNode, !treeNode.checked, true, true);
        }
      },
      onCheck: function (event, treeId, treeNode) {
        const zTree = $.fn.zTree.getZTreeObj(treeId);
        const checkedNodes = zTree.getCheckedNodes(true);

        // 通过treeId获取SensorTreeManager实例
        const sensorTreeManager = zTree.setting.treeManager;
        if (sensorTreeManager && sensorTreeManager.parentManager && sensorTreeManager.parentManager.sensorTable) {
          // 生成表格数据
          const tableData = generateTableDataFromNodes(checkedNodes);
          // 刷新表格
          sensorTreeManager.parentManager.sensorTable.refreshTable(tableData);
        }
      },
    },
  },

  // 数据转换方法
  convertToZTreeNodes: function (result) {
    try {
      if (
        !result ||
        typeof result !== 'object' ||
        !result.stationGUID ||
        !Array.isArray(result.devices)
      ) {
        console.error('Invalid result data:', result);
        return [];
      }

      const nodes = [];
      const selectedDevices = new Set();
      let selectedStation = false;

      nodes.push({
        id: result.stationGUID,
        pId: null,
        name: result.stationName || 'Unknown Station',
        open: true,
        checked: false,
      });

      result.devices.forEach((device) => {
        if (!device.deviceGUID || !device.deviceName) {
          console.warn('Skipping invalid device:', device);
          return;
        }

        let deviceChecked = false;

        nodes.push({
          id: device.deviceGUID,
          pId: result.stationGUID,
          name: device.deviceName,
          open: false,
          checked: false,
        });

        (device.points || []).forEach((point) => {
          if (!point.pointGUID || !point.pointName) {
            console.warn('Skipping invalid point:', point);
            return;
          }

          const isSelected =
            Array.isArray(result.selectedPoints) &&
            result.selectedPoints.includes(point.pointGUID);
          if (isSelected) {
            deviceChecked = true;
            selectedDevices.add(device.deviceGUID);
          }

          nodes.push({
            id: point.pointGUID,
            pId: device.deviceGUID,
            name: point.pointName,
            open: false,
            checked: isSelected,
          });

          (point.sensors || []).forEach((sensor) => {
            if (!sensor.sensorID) {
              console.warn('Skipping invalid sensor:', sensor);
              return;
            }

            nodes.push({
              id: sensor.sensorID,
              pId: point.pointGUID,
              name: sensor.sensorID,
              checked: isSelected,
            });
          });
        });

        if (deviceChecked) {
          selectedStation = true;
          nodes.forEach((node) => {
            if (node.id === device.deviceGUID) {
              node.checked = true;
            }
          });
        }
      });

      if (selectedStation) {
        nodes.forEach((node) => {
          if (node.id === result.stationGUID) {
            node.checked = true;
          }
        });
      }

      return nodes;
    } catch (error) {
      console.error('Error converting result to zTree nodes:', error);
      return [];
    }
  },

  // 检查并更新表格数据
  checkAndUpdateTable: function () {
    if (!this.zTreeObj) return;
    try {
      // 获取所有选中的节点
      const checkedNodes = this.zTreeObj.getCheckedNodes(true);
      // 生成表格数据
      const tableData = generateTableDataFromNodes(checkedNodes);
      // 刷新表格
      if (this.parentManager && this.parentManager.sensorTable) {
        this.parentManager.sensorTable.refreshTable(tableData);
      }
    } catch (error) {
      console.error('Error checking and updating table:', error);
    }
  },

  // 初始化方法
  init: function (data) {
    try {
      const treeNodes = data != undefined ? this.convertToZTreeNodes(data) : [];
      this.zTreeObj = $.fn.zTree.init(
        $('#' + this.containerId),
        this.setting,
        treeNodes
      );

      // 默认展开第一个设备节点
      const rootNodes = this.zTreeObj.getNodes();
      if (rootNodes.length > 0) {
        const firstDeviceNode = rootNodes[0].children?.[0];
        if (firstDeviceNode) {
          this.zTreeObj.expandNode(firstDeviceNode, true, false, true);
        }
      }

      // 检查并更新表格数据
      this.checkAndUpdateTable();

      return this.zTreeObj;
    } catch (error) {
      console.error('Error initializing zTree:', error);
      return null;
    }
  },

  // 获取当前树实例
  getTreeObj: function () {
    return this.zTreeObj;
  },

  // 获取所有选中的节点
  getCheckedNodes: function () {
    if (!this.zTreeObj) return [];
    return this.zTreeObj.getCheckedNodes(true);
  },

  // 获取选中的传感器节点
  getCheckedSensors: function () {
    if (!this.zTreeObj) return [];
    return this.zTreeObj.getCheckedNodes(true).filter((node) => !node.isParent);
  },

  // 展开/折叠指定节点
  expandNode: function (nodeId, expand = true) {
    if (!this.zTreeObj) return;
    const node = this.zTreeObj.getNodeByParam('id', nodeId);
    if (node) {
      this.zTreeObj.expandNode(node, expand);
    }
  },

  // 展开/折叠所有节点
  expandAll: function (expand = true) {
    if (!this.zTreeObj) return;
    this.zTreeObj.expandAll(expand);
  },

  // 刷新树数据
  refreshTree: function (newData) {
    if (!this.zTreeObj) return;
    const treeNodes = this.convertToZTreeNodes(newData);
    this.zTreeObj.destroy();
    this.init(newData);
  },
};

// 使用示例：
// $(document).ready(function() {
//     // 创建实例
//     const sensorTree = new SensorTreeManager('fu-aduItems');
//     // 初始化树
//     sensorTree.init(mockDataFastSyncSensorTreeresult);
// });

// 示例：获取新数据后刷新树
// $.ajax({
//     url: 'your-api-endpoint',
//     success: function(newData) {
//         sensorTree.refreshTree(newData);
//     }
// });

// 生成表格数据
function generateTableDataFromNodes(nodes) {
  const tableData = [];
  const addedPointIds = new Set(); // 用于记录已经添加的测点 ID
  let index = 1; // 序号从1开始

  function processNode(node) {
    if (node.level === 3) { // 传感器节点（level从0开始，3表示传感器节点）
      const pointId = node.getParentNode().id; // 获取测点 ID
      if (node.checked && !addedPointIds.has(pointId)) { // 只处理选中的传感器节点，且测点未被添加过
        tableData.push({
          index: index++, // 添加序号并自增
          id: node.id,
          deviceGUID: node.getParentNode().getParentNode().id,
          deviceName: node.getParentNode().getParentNode().name,
          pointGUID:node.getParentNode().id,
          pointName: node.getParentNode().name,
          syncState: '-' // 修改为 syncState
        });
        addedPointIds.add(pointId); // 标记测点 ID 为已添加
      }
    } else if (node.children) { // 其他层级节点，递归处理子节点
      node.children.forEach(processNode);
    }
  }

  nodes.forEach(processNode);
  return tableData;
}
