<style>
    .noiseParam {
        float: left;
        margin: 0 50px;
    }

    .noiseParam label {
        margin: 0 15px;
    }
</style>
<div class="col-sm-12 full-height" id="maingraph">
    <div class="form-group" style="height: 55px;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <div class="noiseParam">
            <label class="control-label" style="margin-top: 15px" data-i18n="pointName">测点名称:</label>
            <label class="control-label" style="margin-top: 15px;text-align: left" id="pointName" data-i18n=""></label>
        </div>
        <div class="noiseParam">
            <label class="control-label" style="margin-top: 15px" data-i18n="sensorCode">传感器编码:</label>
            <label class="control-label" style="margin-top: 15px;text-align: left" id="sensorCode" data-i18n=""></label>
        </div>
        <div class="noiseParam">
            <label class="control-label" style="margin-top: 15px" data-i18n="sensorType">传感器类型:</label>
            <label class="control-label" style="margin-top: 15px;text-align: left" id="sensorType"
                data-i18n="">噪声</label>
        </div>
        <div class="noiseParam">
            <label class="control-label" style="margin-top: 15px" data-i18n="acquisitionTime">采集时间:</label>
            <label class="control-label" style="margin-top: 15px;text-align: left" id="simpleTime">2017/07/07
                12:12:12</label>
        </div>
        <div class="noiseParam">
            <label class="control-label" style="margin-top: 15px; text-align: right;" data-i18n="Decibels">分贝:
            </label>
            <label class="control-label" style="margin-top: 15px;text-align: left" id="amp"></label>
        </div>
    </div>
    <div class="form-group"
        style="height: 80px;border-left: 1px solid #b9b7b7;border-right: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <div class="col-sm-5" style="">
            <label for="dateStart" class="col-sm-12 control-label" style="margin-top: 7px;"
                data-i18n="startDate">起始日期</label>
            <div class="col-sm-12">
                <div class="bootstrap-datepicker">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="dateStart" type="text" class="form-control pull-right" readonly="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-5" style="">
            <label for="dateEnd" class="col-sm-12 control-label" style="margin-top: 7px;"
                data-i18n="endDate">结束日期</label>
            <div class="col-sm-12">
                <div class="bootstrap-datepicker">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="dateEnd" type="text" class="form-control pull-right" readonly="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-2" style="z-index: 3;">
            <button type="button" id="refreshBtn" class="btn btn-block btn-primary"
                style="width: 80px; margin: 10px 0px 10px auto" data-i18n="refresh">刷新
            </button>
        </div>
    </div>
    <div class="form-group" id="graph"
        style="height: 80%;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <div id="humchart" style="width:100%;height:100%"></div>
    </div>
</div>
<!--</div>-->

<script type="text/javascript">
    initPageI18n();
    $(function () {
        // 基于准备好的dom，初始化echarts图表
        //@ sourceURL=tev_chart.js

        //Daterangepicker
        $('#dateStart').daterangepicker({
            "opens": "right",
            "autoApply": true,
            "timePicker": true,
            "singleDatePicker": true,
            "timePicker24Hour": true,
            locale: {
                'format': 'YYYY/MM/DD HH'
            },
            "startDate": new Date(new Date().toDateString()).pattern('yyyy/MM/dd HH')
        });

        //Daterangepicker
        $('#dateEnd').daterangepicker({
            "opens": "right",
            "autoApply": true,
            "timePicker": true,
            "singleDatePicker": true,
            "timePicker24Hour": true,
            locale: {
                'format': 'YYYY/MM/DD HH'
            },
            "startDate": new Date().pattern('yyyy/MM/dd HH')
        });

        $('#dateStart').click(function () {
            $('.minuteselect').attr('disabled', 'disabled');
            $('.minuteselect').css('background', 'lightgray');
        });

        $('#dateEnd').click(function () {
            $('.minuteselect').attr('disabled', 'disabled');
            $('.minuteselect').css('background', 'lightgray');
        });

        window.addEventListener("resize", function () {
            if (myChart)
                myChart.resize();
        });

        $(window).resize(function () {

            if (!document.getElementById("humchart")) { //js判断元素是否存在
                return;
            }
            var height = $("#maingraph").height() - 120;
            $("#graph").height(height);
        });
        $(window).resize();

        var myChart = echarts.init(document.getElementById('humchart'));

        var dataXValue = [" "],
            dataYValue = [0];

        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: { // 坐标轴指示器，坐标轴触发有效
                    type: 'line' // 默认为直线，可选为：'line' | 'shadow'
                },
                confine: true
            },
            title: {
                text: getI18nName('noiseChartTitle'),
                x: 'center',
                y: 'top',
                textAlign: 'left'
            },
            calculable: true,
            xAxis: [{
                type: 'category',
                data: dataXValue,
                boundaryGap: false
            }],
            yAxis: [{
                type: 'value'
            }],
            series: [{
                name: getI18nName('Humidity'),
                type: 'line',
                data: dataYValue,
                itemStyle: {
                    normal: {
                        color: '#3c8dbc'
                    }
                },
                markPoint: {
                    data: [{
                            type: 'max',
                            name: getI18nName('maxValue')
                        },
                        {
                            type: 'min',
                            name: getI18nName('maxValue')
                        }
                    ]
                },
                markLine: {
                    precision: 0,
                    data: [{
                        type: 'average',
                        name: getI18nName('average')
                    }]
                }
            }]
        };
        myChart.setOption(option);

        $("#refreshBtn").click(function () {
            var dateStartValue = $("#dateStart").val();
            var dateEndValue = $("#dateEnd").val();
            if (moment(dateStartValue + ':00:00').isAfter(moment(dateEndValue + ':00:00'))) {
                layer.msg('<div style="text-align:center">' + getI18nName('dateStartIsAfterDateEnd') +
                    '</div>')
            } else {
                loadNoise(showChartPointId, showChartDataId, $("#dateStart").val() + ':00:00', $(
                    "#dateEnd").val() + ':00:00');
            }
        });

        loadNoise(showChartPointId, showChartDataId, "", "");

        function loadNoise(pointId, dataId, startDate, endDate) {
            var pointType = "NOISE";
            var dataGet = "pointId=" + pointId + "&dataId=" + dataId + "&pointType=" + pointType +
                "&startDate=" + startDate + "&endDate=" + endDate;
            poll('GET', 'GetTendData', dataGet, function (text) {
                var respData = text.result;

                if (!respData)
                    return;

                drawChart(respData);

                var sensorType = getI18nName(respData.sensorType);
                $("#pointName").html(respData.pointName);
                $("#sensorCode").html(respData.aduId);
                $("#sensorType").html(sensorType);
                $("#simpleTime").html(respData.sample_time);
                $("#amp").html(respData.AMP != undefined ? respData.AMP +
                    respData.unit : "0" + respData.unit);
                if (startDate !== "" && endDate !== "") {
                    return;
                }
                var tempStartD = new Date(respData.startDate.split(' ')[0]);
                var tempEndD = new Date(respData.endDate.split(' ')[0]);
                tempEndD.setDate(tempEndD.getDate() + 1);
                $('#dateStart').daterangepicker({
                    "opens": "right",
                    "autoApply": true,
                    "timePicker": true,
                    "singleDatePicker": true,
                    "timePicker24Hour": true,
                    locale: {
                        'format': 'YYYY/MM/DD HH'
                    },
                    "startDate": tempStartD.pattern('yyyy/MM/dd HH')
                });

                //Daterangepicker
                $('#dateEnd').daterangepicker({
                    "opens": "right",
                    "autoApply": true,
                    "timePicker": true,
                    "singleDatePicker": true,
                    "timePicker24Hour": true,
                    locale: {
                        'format': 'YYYY/MM/DD HH'
                    },
                    "startDate": tempEndD.pattern('yyyy/MM/dd HH')
                });
            });
        }


        function drawChart(respData) {
            if (option) {
                option.xAxis[0].data = respData.dataChart.xValue;
                //                respData.dataChart.chartInit = undefined;
                if (respData.dataChart.chartInit) {
                    option.series[0] = getDataItem({
                        name: '原始值',
                        data: respData.dataChart.chartInit,
                        color: '#3c8dbc'
                    });
                } else {
                    option.legend = {
                        data: [getI18nName('maxValue'), getI18nName('average')],
                        top: 15,
                        left: 180
                    };
                    option.series = [];
                    option.series.push(getDataItem({
                        name: getI18nName('maxValue'),
                        data: respData.dataChart.chartMax,
                        color: '#3c8dbc'
                    }));
                    option.series.push(getDataItem({
                        name: getI18nName('average'),
                        data: respData.dataChart.chartAvg,
                        lineStyleType: 'dotted',
                        color: '#f39c12'
                    }));
                }

                myChart.setOption(option);
            }
        }

        function getDataItem(config) {
            return {
                name: config.name,
                type: 'line',
                data: config.data,
                itemStyle: {
                    normal: {
                        color: config.color,
                        lineStyle: {
                            type: config.lineStyleType ? config.lineStyleType : 'solid'
                        }
                    }
                },
                markPoint: {
                    data: [{
                            type: 'max',
                            name: getI18nName('maxValue')
                        },
                        {
                            type: 'min',
                            name: getI18nName('minValue')
                        }
                    ]
                },
                markLine: {
                    precision: 1,
                    data: [{
                        type: 'average',
                        name: getI18nName('average')
                    }]
                }
            }
        }
    });
</script>