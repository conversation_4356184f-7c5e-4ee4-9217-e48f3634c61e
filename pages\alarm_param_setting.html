<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div class="col-sm-3 full-height zTreeDemoBackground left"
             style="overflow:auto;border: 1px solid #f4f4f4;">
            <div id="alarm_tree" class="ztree"></div>
        </div>
        <div id="devInfo" class="col-sm-9 full-height ">
            <form class="form-horizontal full-height" style="padding:0px">
                <div class="form-group" style="padding:1px;margin:0px">
                    <label class="col-sm-4 control-label" for="taskGroup"
                           data-i18n="taskGroup">任务组号</label>
                    <!--<div class="col-sm-7">-->
                    <!--<input id="taskGroup" vk="true" type="text" class="col-sm-4 form-control"-->
                    <!--data-i18n-placeholder="pleaseInput" value="">-->
                    <!--</div>-->
                    <div class="col-sm-7">
                        <select id="taskGroup"
                                class="form-control select2"
                                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
                    </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                    <label for="channelType" class="col-sm-4 control-label"
                           data-i18n="channelTypeAlarm">数据类型</label>
                    <div class="col-sm-7">
                        <select id="channelType"
                                class="form-control select2"
                                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
                    </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                    <label class="col-sm-4 control-label" for="alarmThreshold"
                           data-i18n="alarmThreshold">报警阈值</label>
                    <div class="col-sm-7">
                        <input id="alarmThreshold" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="">
                    </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                    <label class="col-sm-4 control-label" for="alarmRecoveryThreshold"
                           data-i18n="alarmRecoveryThreshold">报警恢复阈值</label>
                    <div class="col-sm-7">
                        <input id="alarmRecoveryThreshold" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="">
                    </div>
                </div>
                <div class="form-group mec-set-non" style="padding:1px;margin:0px">
                    <label for="alarmChannel" class="col-sm-4 control-label"
                           data-i18n="alarmChannel">报警通道选择</label>
                    <div class="col-sm-7">
                        <select id="alarmChannel"
                                class="form-control select2"
                                onchange="alarmChannelOnChange()"
                                data-i18n-placeholder="selectPlease" style="width: 100%;">
                            <option value="built_in_channel_1" selected="selected"
                                    data-i18n="built_in_channel_1">
                                内置通道1
                            </option>
                            <option value="External_IO_module" data-i18n="External_IO_module">
                                外置IO模块
                            </option>
                            <option value="not_associated" data-i18n="not_associated">
                                不关联
                            </option>
                        </select>
                    </div>
                </div>
                <div class="form-group mec-set" style="padding:1px;margin:0px">
                    <label for="alarmExternalIOSN" class="col-sm-4 control-label"
                           data-i18n="alarmExternalIOSN">外置IO模块编码</label>
                    <div class="col-sm-7">
                        <input id="alarmExternalIOSN" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="0">
                    </div>
                </div>
                <div class="form-group g1 g2 g3 g4 g5" style="padding:1px;margin:0px">
                    <label for="alarmExternalIOChannel" class="col-sm-4 control-label"
                           data-i18n="alarmExternalIOChannel">外置IO模块通道</label>
                    <div class="col-sm-7">
                        <select id="alarmExternalIOChannel" class="form-control select2"
                                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
                    </div>
                </div>
                <div class="form-group g5" style="padding:1px;margin:0px">
                    <label for="wiringMode" class="col-sm-4 control-label"
                           data-i18n="wiringMode">接线方式</label>
                    <div class="col-sm-7">
                        <select id="wiringMode" class="form-control select2"
                                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
                    </div>
                </div>
                <div class="form-group g5" style="padding:1px;margin:0px">
                    <label for="alarmMode" class="col-sm-4 control-label"
                           data-i18n="alarmMode">报警方式</label>
                    <div class="col-sm-7">
                        <select id="alarmMode" class="form-control select2"
                                onchange="alarmModeOnChange();"
                                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
                    </div>
                </div>
                <div class="form-group mec-set" style="padding:1px;margin:0px">
                    <label for="alarmTime" class="col-sm-4 control-label"
                           data-i18n="alarmTime">报警定时</label>
                    <div class="col-sm-7">
                        <input id="alarmTime" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="0">
                    </div>
                </div>
                <div class="form-group mec-set" style="padding:1px;margin:0px">
                    <label for="alarmInterval" class="col-sm-4 control-label"
                           data-i18n="alarmInterval">报警间隔</label>
                    <div class="col-sm-7">
                        <input id="alarmInterval" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="0">
                    </div>
                </div>
                <div class="form-group mec-set" style="padding:1px;margin:0px">
                    <label for="alarmDuration" class="col-sm-4 control-label"
                           data-i18n="alarmDuration">报警时长</label>
                    <div class="col-sm-7">
                        <input id="alarmDuration" vk="true" type="text"
                               class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="0">
                    </div>
                </div>
            </form>
            <div class="content-footer">
                <!--<div class="col-sm-3">-->
                <!--<button type="button" id="add_alarm" class="btn btn-block btn-primary"-->
                <!--style="width: 80px; margin: 10px auto 10px auto" data-i18n="add">增加-->
                <!--</button>-->
                <!--</div>-->
                <div class="col-sm-5">
                    <button type="button" id="save_alarm" class="btn btn-block btn-primary"
                            style="width: 100px; margin: 10px auto 10px auto" data-i18n="save_add">
                        保存/增加
                    </button>
                </div>
                <div class="col-sm-3">
                    <button type="button" id="del_alarm" class="btn btn-block btn-primary"
                            style="width: 80px; margin: 10px auto 10px auto" data-i18n="delete">删除
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    var currentTreeNode = {
        group: '',
        channelType: ''
    }
    //@ sourceURL=instrument_set.js
    initPageI18n();
    $("input[vk=true]").focus(function () {
        $.fn.keysoft($(this));
    });

    $('.select2').select2({
        minimumResultsForSearch: Infinity,
        language: "zh-CN"
    });

    function alarmModeOnChange() {
        var alarmMode = $('#alarmMode').val();
        var alarmTime = $('#alarmTime');
        var alarmInterval = $('#alarmInterval');
        var alarmDuration = $('#alarmDuration');
        alarmTime.attr('disabled', false);
        alarmInterval.attr('disabled', false);
        alarmDuration.attr('disabled', false);
        switch (alarmMode) {
            case "continuous":
                alarmTime.attr('disabled', true);
                alarmInterval.attr('disabled', true);
                alarmDuration.attr('disabled', true);
                break;
            case "timing":
                alarmInterval.attr('disabled', true);
                alarmDuration.attr('disabled', true);
                break;
            case "interval":
                alarmTime.attr('disabled', true);
                break;
        }
    }
    function alarmChannelOnChange() {
        var alarmChannel = $('#alarmChannel').val();
        var alarmExternalIOSN = $('#alarmExternalIOSN');
        var alarmExternalIOChannel = $('#alarmExternalIOChannel');
        alarmExternalIOSN.attr('disabled', false);
        alarmExternalIOChannel.attr('disabled', false);
        switch (alarmChannel) {
            case "External_IO_module":
                break;
            case "built_in_channel_1":
            case "not_associated":
                alarmExternalIOSN.attr('disabled', true);
                alarmExternalIOChannel.attr('disabled', true);
                break;
        }
    }

    $('#save_alarm').click(function () {
        var formdata = {}
        formdata.taskGroup = $('#taskGroup').val();
        formdata.channelType = $('#channelType').val();
        formdata.alarmThreshold = $('#alarmThreshold').val();
        formdata.alarmRecoveryThreshold = $('#alarmRecoveryThreshold').val();
        formdata.alarmChannel = $('#alarmChannel').val();
        formdata.alarmExternalIOSN = $('#alarmExternalIOSN').val();
        formdata.alarmExternalIOChannel = $('#alarmExternalIOChannel').val();
        formdata.wiringMode = $('#wiringMode').val();
        formdata.alarmMode = $('#alarmMode').val();
        formdata.alarmTime = $('#alarmTime').val();
        formdata.alarmInterval = $('#alarmInterval').val();
        formdata.alarmDuration = $('#alarmDuration').val();
        poll("POST", SAVE_ALARM_CONFIG, formdata, function (response) {
            $.notifyUtil.commonNotify(getI18nName('alarmSaveSuccess'));
            currentTreeNode.group = formdata.taskGroup;
            currentTreeNode.channelType = formdata.channelType;
            poll("GET", GET_ALARM_CONFIG_LIST, "", function (response) {
                var respData = response.result;
                initAlarmTree(respData);
                selectNode(currentTreeNode);
            }, null);
        }, null);
    });
    $('#del_alarm').click(function () {
        var formdata = {}
//        formdata.taskGroup = $('#taskGroup').val();
//        formdata.channelType = $('#channelType').val();
        var data = 'taskGroup=' + $('#taskGroup').val() + '&channelType=' + $('#channelType').val();
        poll("GET", REMOVE_ALARM_CONFIG, data, function (response) {
            commonNotify(getI18nName('alarmDelSuccess'));
            poll("GET", GET_ALARM_CONFIG_LIST, "", function (response) {
                var respData = response.result;
                initAlarmTree(respData);
                var group;
                var channelType;
                for (var i = 0; i < respData.length; i++) {
                    if (!group && respData[i].arrtType == 0) {
                        group = respData[i].id;
                    }
                    if (!channelType && respData[i].arrtType == 1) {
                        channelType = respData[i].id;
                    }
                    if (group && channelType) {
                        break;
                    }
                }
                currentTreeNode.group = group;
                currentTreeNode.channelType = channelType;
                selectNode(currentTreeNode);
            }, null);
        }, null);
    });

    !function initSelect() {
        poll("GET", GET_ALARM_CONFIG_INFO_PARAM, "", function (response) {
            var respData = response.result;
            for (var item in respData) {
//                if (item == 'taskGroup')
//                    continue;
                $("#" + item).empty();
                var data = respData[item];
                if (data.length > 0) {
                    data.forEach(function (value, index) {
                        var option = $("<option />", {"value": "" + value + ""}).append(getI18nName(value));
                        $("#" + item).append(option);
                    });
                }
            }
            poll("GET", GET_ALARM_CONFIG_LIST, "", function (response) {
                var respData = response.result;
                initAlarmTree(respData);
                var group;
                var channelType;
                for (var i = 0; i < respData.length; i++) {
                    if (!group && respData[i].arrtType == 0) {
                        group = respData[i].id;
                    }
                    if (!channelType && respData[i].arrtType == 1) {
                        channelType = respData[i].id;
                    }
                    if (group && channelType) {
                        break;
                    }
                }
                currentTreeNode.group = group;
                currentTreeNode.channelType = channelType;
                selectNode(currentTreeNode);
            }, null);
        }, null);
    }();

    function initAlarmTree(respData) {
        var setting = {
            data: {
                simpleData: {
                    idKey: "id",
                    pIdKey: "parentId",
                    name: "name",
                    enable: true
                }
            },
            // async: {
            //     enable: true,
            //     url: "GetChannelList",
            //     type: "GET",
            //     dataFilter: ajaxDataFilterShowDevNode,
            //     autoParam: ["id=aduId"]
            // },
            callback: {
                onExpand: zTreeOnExpand,
                onClick: zTreeOnclick
            }
        };
        var zNodes = [{
            id: 0,
            pId: -1,
            name: "",
            open: true,
            iconOpen: "./plugins/zTree/css/zTreeStyle/img/diy/1_open.png",
            iconClose: "./plugins/zTree/css/zTreeStyle/img/diy/1_close.png",
            isParent: true
        }];
//        debugger;
        if (respData.length <= 0) {
            $.fn.zTree.init($("#alarm_tree"), setting, zNodes);
            return;
        }
        for (var i = 0; i < respData.length; i++) {
            respData[i].name = getI18nName(respData[i].name, respData[i].name);
        }
        zNodes = respData;
        $.fn.zTree.init($("#alarm_tree"), setting, zNodes);
    }

    /**
     * 导航树，节点单击，回调函数
     * @param {*} event
     * @param {*} treeId
     * @param {*} treeNode
     */
    function zTreeOnclick(event, treeId, treeNode) {
        var treeObj = $.fn.zTree.getZTreeObj('alarm_tree');
        treeObj.selectNode(treeNode);//选中指定节点
        zTreeOnExpand(event, treeId, treeNode);
        if (treeNode != undefined) {
            if (!treeNode.open) {
                treeObj.expandNode(treeNode);//展开指定节点
            }
            if (treeNode.callBack != undefined) {
                treeNode.callBack();
                treeNode.callBack = undefined;
            }
        }
        if (treeNode.arrtType == 1) {
            $('#taskGroup').val(treeNode.parentId).select2();
            for (var item in treeNode) {
                var view = $("#" + item);
                if (item == 'id') {
                    view = $("#channelType");
                }
                if (view.prop("tagName") != undefined) {
                    var data = treeNode[item];
                    if (view.prop("tagName") == 'SELECT') {
//                        debugger;
                        view.val(data).select2();
                        view.trigger('onchange');
                    } else {
                        view.val(data);
                    }
                }
            }
        }
    }

    /**
     * 导航树，节点展开，回调函数
     * @param {*} event
     * @param {*} treeId
     * @param {*} treeNode
     */
    function zTreeOnExpand(event, treeId, treeNode) {
        if (treeNode == undefined) {
            return;
        }
    }

    /**
     * 导航树，树状菜单节点操作
     * @param {*} nodeItem
     */
    function selectNode(nodeItem) {
//        isExpendingSelect = true;
        var treeObj = $.fn.zTree.getZTreeObj('alarm_tree');
//        var rootNode = treeObj.getNodeByParam('id', '-1');
//        virtualClickNode(treeObj, rootNode);
        var groupNode = treeObj.getNodeByParam('id', nodeItem.group);
        groupNode.callBack = function () {
            //添加zTree展开异步回调监听
            var typeNode = treeObj.getNodeByParam('id', nodeItem.channelType);
            if (typeNode) {
                virtualClickNode(treeObj, typeNode);
            } else {
                throw "列表树加载中";
            }
        }
        virtualClickNode(treeObj, groupNode);
    }

    /**
     * 导航树，模拟点击树节点
     * @param {*} treeObj
     * @param {*} node
     * @param {*} flag
     */
    function virtualClickNode(treeObj, node, flag) {
        var clickAble = flag;
        if (clickAble == undefined) {
            clickAble = true;
        }
        treeObj.selectNode(node);//选中指定节点
        if (node != undefined && !node.open) {
            treeObj.expandNode(node);//展开指定节点
        }
        if (clickAble) {
            treeObj.setting.callback.onClick(null, treeObj.setting.treeId, node);//触发函数
        }
    }

</script>