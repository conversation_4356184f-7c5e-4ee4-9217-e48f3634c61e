<nav class="navbar navbar-default  " role="navigation" style="background: rgba(22, 35, 72, 0.377);
            height: 50px;
            border: 0px;
            border-radius: 0px;
            margin: 0;
            position: absolute;
            top: 0;
            z-index: 999;
            width: 100%;">
    <div class="navbar-header" style="width: 100%">
        <div class="center-block" id="loginWelcome" style="color: white;
            font-size: 25px;
            margin-top: 7px;
            display: table;">
        </div>
    </div>
</nav>
<div
    style="width:100%;height:100%;background:url(./dist/img/lock-screen-background.jpg);background-attachment:fixed;background-repeat:no-repeat;background-size:cover;-moz-background-size:cover;-webkit-background-size:cover;position: absolute;">
    <div class="container "
        style="width:443px;height:289px; margin:auto;top:0;left: 0;bottom: 0;right:0; background:rgba(0, 0, 0, 0.188) ;border-radius: 5px;border-color:grey;position: absolute">

        <div id="loginForm" class="form-horizontal" style="margin:30px 20px 40px 20px" onkeydown="onFormClick()">
            <div class="form-group">
                <input type="text" id="userName" class="form-control" vk="true"
                    style="height: 55px;border-radius: 0px;width:400px; background:#ffffff85"
                    data-i18n-placeholder="userName" value="">
            </div>
            <div class="form-group">
                <i></i>
                <!--<span class="glyphicon glyphicon-lock form-control-feedback"></span>-->
                <!--<input type="password" class="form-control" id="password" name="password" placeholder="密码">-->
                <!--<img id="EyeImage" class="ViewPassword" src="./dist/img/eye.png">-->
                <input type="password" id="passWord" class="form-control " vk="true"
                    style="height: 55px;border-radius: 0px;width:400px; background:#ffffff70;"
                    data-i18n-placeholder="passWord" placeholder="" value=""/>
            </div>
            <div class="form-group">
                <div class="checkbox" style="display: none; border-color: grey;float:right;margin-top:-7px;">
                    <label style="color: grey">
                        <input id="showPassword" type="checkbox"
                            style="border-radius: 2px;border-color: red; background:#ffffff85"><span
                            id="showPasswordText">显示密码</span>
                    </label>
                </div>
            </div>
            <div class="form-group">
                <button id="login" class="btn btn-info" style="width: 400px;height:55px" data-i18n="login">
                    登录123
                </button>
            </div>
            <div class="form-group">
                <span style="color:red;display:none" id="ErrorMsg"></span>
            </div>
        </div>
    </div>
    <div style="z-index: 999;
                position: absolute;
                bottom: 20px;
                color: wheat;
                background: rgba(0, 0, 0, 0.377);
                padding: 14px;
                border-radius: 5px;
                right: 50px;">
        <div id='loginVInfo'></div>
        <div id='loginSN'></div>
    </div>
</div>

<script>
    initPageI18n();

    $('#loginWelcome').html(MONITOR_TYPE + " " + MONITOR_NAME);
    $('#loginVInfo').html(version);
    $('#loginSN').html('SN:' + versionSN);

    var checkSnIn = setInterval(function () {
        if (versionSN != '0' && $('#loginSN').html() == 'SN:0') {
            $('#loginWelcome').html(MONITOR_TYPE + " " + MONITOR_NAME);
            $('#loginVInfo').html(version);
            $('#loginSN').html('SN:' + versionSN);
            clearInterval(checkSnIn);
        }
    }, 500);

    checkNeedKeyboard();

    function onFormClick() {
        if (window.event.key == 'Enter') {
            $('#login').click();
        }
    }


    $('#login').on('click', function () {
        var name = $('#userName')[0].value;
        var password = $('#passWord')[0].value;
        if (name == '' || password == '') {
            $('#login').removeClass('disabled');
            $('#ErrorMsg').show();
            $('#ErrorMsg').html(getI18nName('inputNameAndPsw') == 'inputNameAndPsw' ? 'Please Input' :
                getI18nName('inputNameAndPsw'));
            return false;
        }
        //        var b = new Base64();
        //        name = b.encode(name);
        //        password = b.encode(password);
        //        name = encodeSM2(name, publicKey);
        //        password = encodeSM2(password, publicKey);
        // var tempName = name;
        // var tempPassword = password;

        var tempName = RSAUtil.encode(name);
        tempPassword = RSAUtil.encode(password);

        var postForm = {
            userName: tempName,
            password: tempPassword
        };
        $('#login').attr('disabled', 'disabled');
        pollPost(POST_LOGIN, postForm, function (text) {
            var respData = text.result;
            if (respData.loginResult === "loginSuccess") {
                $.session.clear();
                $.session.set('user', name);
                $.session.set('token', tempPassword);
                $.session.set('isLogOut', false);
                $.session.set('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));
                setCookie('user', name);
                setCookie('token', tempPassword);
                setCookie('isLogOut', null);
                setCookie('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));

                var localName = RSAUtil.encode(name);
                var localPassword = tempPassword;
                initWSonLogin(localName, localPassword);
                getADUStateColor(1, function () {
                    initUser();
                    $('.sidebar-menu').find('a')[0].click();
                    $('#projectContent').hide()
                });
            } else {
                $('#login').removeAttr('disabled')
                $('#ErrorMsg').show();
                $('#ErrorMsg').html(getI18nName('userErrorTip') || '用户名或密码错误');
            }
        }, function (errorCode) {
            $('#login').removeAttr('disabled')
            modals.error(this, '', "error");
        });
    });
</script>