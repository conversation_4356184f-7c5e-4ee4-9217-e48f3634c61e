<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<div class="col-sm-12 full-height">
  <div class="nav-tabs-custom full-height">
    <div id="graph-AE" class="col-sm-7 border-left full-height">
      <div class="row border-left" id="aeDiv" style="min-height: 400px;height: 100%">
      </div>
    </div>
    <div class="col-sm-5 full-height bootstrap-dialog" style="border: 1px solid #f4f4f4;">
      <form class="form-horizontal">
        <br>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="pointName">测点名称:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="pointName"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sensorCode">传感器编码:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="sensorCode"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sensorType">传感器类型:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="sensorType">AE</label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="gain">增益:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="gain"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sample_date" for="sample_date">采样日期:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="sample_date"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sample_time">采样时间:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="sample_time"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="rms">有效值:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="rms"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="cycle_max">周期最大值:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="cycle_max"></label>
          </div>
        </div>
        <div class="form-group">
          <label id="frequency1_n" class="col-sm-6 control-label" data-i18n="frequency1">频率成分1:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="frequency1"></label>
          </div>
        </div>
        <div class="form-group">
          <label id="frequency2_n" class="col-sm-6 control-label" style="padding-left:0px"
            data-i18n="frequency2">频率成分2:</label>
          <div class="col-sm-6">
            <label class=" control-label" id="frequency2"></label>
          </div>
        </div>
      </form>
    </div>
    <div class="content-footer">
      <div class="col-sm-3">
        <button type="button" id="now_data" class="btn btn-block btn-primary hide"
          style="width: 100px; margin: 10px 0px 10px auto" data-i18n="realData">实时数据
        </button>
      </div>
      <div class="col-sm-3">
        <button type="button" id="history_data" class="btn btn-block btn-primary hide"
          style="width: 100px; margin: 10px 0px 10px auto" data-i18n="historyData">
          历史数据
        </button>
      </div>
      <div class="col-sm-3">
        <button type="button" id="preData" class="btn btn-block btn-primary"
          style="width: 140px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
        </button>
      </div>
      <div class="col-sm-3">
        <button type="button" id="nextData" class="btn btn-block btn-primary"
          style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
        </button>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  initPageI18n();
  $(function () {
    //@ sourceURL=ae_chart.js
    $(window).resize(function () {
      if (!document.getElementById("aeDiv")) { //js判断元素是否存在
        return;
      }
      var height = $("#graph-AE").height() - $(".content-footer").height() - 20;
      $("#aeDiv").height(height);

      var svgEls = document.querySelector('svg');
      if (svgEls) {
        svgEls.setAttribute('width', $("#aeDiv").width());
        svgEls.setAttribute('height', $("#aeDiv").height());
      }
      //            loadAETest();
    });
    $(window).resize();

    $('.slider').slider();

    var preId = 0;
    var nextId = 0;
    var nowId;

    function loadAETest (dataId, pointId) {
      // $("#aeDiv").empty();
      var data = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=AE&dataType=AE";
      poll('GET', 'GetChartData', data, function (text) {
        var respData = text.result;
        // respData = tempData.result;
        renderChartAndParameter(respData)
      });
    }

    function renderChartAndParameter (respData) {
      var chartDiv = document.getElementById('aeDiv');
      // var amplitudeChart = PDChart.AMPLITUDE.draw(amplitude.chartBody, {
      //   title: getI18nName('aeChartTitle'),
      //   backgroundColor: "#ffffff"
      // });
      preId = respData.preDataId;
      nextId = respData.nextDataId;
      //判断上一条/下一条按钮是否可用并改变状态
      if (preId === 0) {
        $("#preData").attr("disabled", true);
      } else {
        $("#preData").removeAttr("disabled");
      }
      if (nextId === 0) {
        $("#nextData").attr("disabled", true);
      } else {
        $("#nextData").removeAttr("disabled");
      }
      // $("#aeDiv").empty();

      var pdChartBody = getAEChartBody(respData);

      // var amplitudeChart = PDChart.AMPLITUDE.draw(respData.chartBody, {
      //   title: getI18nName('aeChartTitle'),
      //   backgroundColor: "#ffffff"
      // });
      // PDChart.loadChart(chartDiv, amplitudeChart, {
      //   id: "amplitude",
      //   width: $("#aeDiv").width(),
      //   height: $("#aeDiv").height(),
      //   append: true,
      //   saveAsImage: false
      // });
      pdcharts.draw(chartDiv, {
        width: $("#aeDiv").width(),
        height: $("#aeDiv").height(),
        type: pdcharts.chartType.amplitude,
        data: pdChartBody,
      })

      //暂时由页面进行Type判断转换中文，未知type直接显示
      var sensorType = getI18nName(respData.params.sensorType);
      $("#pointName").html(respData.params.pointName);
      $("#sensorCode").html(respData.params.aduId);
      $("#sensorType").html(sensorType);
      $("#gain").html(respData.params.gain);
      $("#sample_date").html(respData.params.sample_date);
      $("#sample_time").html(respData.params.sample_time);
      $("#rms").html(respData.params.rms);
      $("#cycle_max").html(respData.params.cycle_max);
      $("#frequency1").html(respData.params.frequency1);
      $("#frequency2").html(respData.params.frequency2);
    }

    loadAETest(showChartDataId, showChartPointId);
    // loadAETest('126', 'e47601d4-ad25-4694-82d6-3a3d3727de96');

    $("#preData").click(function () {
      if (preId === 0) {
        layer.alert(getI18nName('noTestData'), {
          title: getI18nName('tips'),
          btn: [getI18nName('close')]
        });
        return;
      }
      loadAETest(preId, showChartPointId);
    });
    $("#nextData").click(function () {
      if (nextId === 0) {
        $.fn.alertMsg(
          'warning',
          getI18nName(''), [{
            id: 'no',
            text: getI18nName('confirm')
          }]
        );
        return;
      }
      loadAETest(nextId, showChartPointId);
    });
  });

  function getAEChartBody (respData) {
    // 导入ChartConfigUtil模块
    var chartConfigUtil = pdcharts.ChartConfigUtil;
    // 定义配置类型常量
    var CONFIG_TYPE = chartConfigUtil.CONFIG_TYPE;
    // 获取图表数据的单位
    let unit = respData.chartBody[0] ? respData.chartBody[0].axisInfo.xUnit : 'dB';
    // 初始化图表配置对象
    let chartBody = {
      "title": getI18nName('aeChartTitle'),
      "needBgData": false,
      "axisInfo": {
        "valDesc": getI18nName('Measurement'),
        "valColor": "#00b099",
        // "valMaxColor": "rgba(0, 220, 162, 1)",
        "bgValDesc": "",
        "bgColor": "#00b09933",
        "unit": unit,
        "frequecy": respData.params.frequency
      },
      "series": []
    }

    // 检查AE图表值的函数
    function checkAEChartValue (value) {
      // 根据配置类型和值获取对应的显示值
      var resultObj = chartConfigUtil.getValueByConfig({
        dataType: CONFIG_TYPE.AE,
        value: value,
        unit: unit,
      })
      return resultObj.valueStr;
    }

    // 需要检查的参数名列表
    let checkParamsNameList = ['rms', 'cycle_max', 'frequency1', 'frequency2'];
    // 全局管理翻译文件，处理返回数据中的多语言
    for (var chartIndex = 0; chartIndex < respData.chartBody.length; chartIndex++) {
      var chartData = respData.chartBody[chartIndex];
      var xDesc = globalParms[chartData.axisInfo.xDesc];
      if (validateStrLen(xDesc) != 0) {
        chartData.axisInfo.xDesc = xDesc;
      }
      var seriesItem = {
        max: chartData.axisInfo.xRangeMax,
        min: chartData.axisInfo.xRangeMin,
        dataList: [
          chartData.dataList[1].x,
          chartData.dataList[0].x
        ],
        name: xDesc
      }
      // 更新参数值
      respData.params[checkParamsNameList[chartIndex]] = checkAEChartValue(chartData.dataList[0].x);
      // 添加到图表配置对象的series数组中
      chartBody.series.push(seriesItem);
    }
    // 返回图表配置对象
    return chartBody;
  }

  var tempData = {
    "errorCode": 0,
    "errormsg": "成功",
    "result": {
      "chartBody": [
        {
          "axisInfo": {
            "xDesc": "RMS",
            "xRangeMax": 30,
            "xRangeMin": -15,
            "xUnit": "dB"
          },
          "dataList": [
            {
              "color": "#008000",
              "x": -8.313
            },
            {
              "color": "#ff0000",
              "x": -15
            }
          ]
        },
        {
          "axisInfo": {
            "xDesc": "max",
            "xRangeMax": 30,
            "xRangeMin": -15,
            "xUnit": "dB"
          },
          "dataList": [
            {
              "color": "#008000",
              "x": -4
            },
            {
              "color": "#ff0000",
              "x": -15
            }
          ]
        },
        {
          "axisInfo": {
            "xDesc": "fre1Value",
            "xRangeMax": 30,
            "xRangeMin": -15,
            "xUnit": "dB"
          },
          "dataList": [
            {
              "color": "#008000",
              "x": -15
            },
            {
              "color": "#ff0000",
              "x": -15
            }
          ]
        },
        {
          "axisInfo": {
            "xDesc": "fre2Value",
            "xRangeMax": 30,
            "xRangeMin": -15,
            "xUnit": "dB"
          },
          "dataList": [
            {
              "color": "#008000",
              "x": -15
            },
            {
              "color": "#ff0000",
              "x": -15
            }
          ]
        }
      ],
      "nextDataId": 126,
      "params": {
        "aduId": "00:00:00:00:D5:A7:13:5F",
        "battaryLife": "33",
        "battaryPercent": "733",
        "cycle_max": "-4dB",
        "frequency": 50,
        "frequency1": "-15dB",
        "frequency2": "-15dB",
        "gain": "100dB",
        "pointName": "AE-5F",
        "rms": "-8dB",
        "sample_date": "2024/02/21",
        "sample_time": "06:00:01",
        "sensorType": "AE"
      },
      "preDataId": 124
    },
    "success": "true"
  }
</script>