<div class="col-sm-12 full-height">
  <div class="nav-tabs-custom full-height">
    <div class="col-sm-3 full-height zTreeDemoBackground left" style="overflow:auto;border: 1px solid #f4f4f4;">
      <div id="dev_tree" class="ztree"></div>
    </div>
    <div id="devInfo" class="col-sm-9 full-height " style="height:96%">
      <form class="form-horizontal full-height" style="padding:0px">
        <div class="col-sm-12" style="height: 73%;overflow: auto;padding:0px">
          <div class="form-group hide" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">旧前端编码(保存用，界面不显示)</label>
            <div class="col-sm-8">
              <input id="inst_code_old" vk="true" type="text" class="col-sm-4 form-control" value="">
            </div>
          </div>
          <div class="form-group g1 g2 g3 g4 g5 g6" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" for="inst_code" data-i18n="sensorCode">前端编码</label>
            <div class="col-sm-8">
              <input id="inst_code" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g1 g2 g3 g4 g5 g6" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" for="inst_type" data-i18n="sensorTypeSet">前端类型</label>
            <div class="col-sm-8">
              <select id="inst_type" onchange="aduType_onchange();" class="form-control select2"
                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
            </div>
          </div>
          <div class="form-group g1 g2 g3 g4 g5 g6" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" for="inst_name" data-i18n="senorName">前端名称</label>
            <div class="col-sm-8">
              <input id="inst_name" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="" />
            </div>
          </div>
          <div id="expandParams" class="expand-params">
            <div class="form-group" style="padding:1px;margin:0px;">
              <label class="col-sm-3 control-label" for="cameraType" data-i18n="CameraType">摄像头类型</label>
              <div class="col-sm-8">
                <select id="cameraType" class="form-control select2" data-placeholder="" style="width: 100%;">
                  <option value="1" data-i18n="lightGunCamera" selected="selected">
                    可见光枪机
                  </option>
                  <option value="2" data-i18n="lightBallCamera">
                    可见光球机
                  </option>
                  <option value="3" data-i18n="infraredGunCamera">
                    红外双目枪机
                  </option>
                  <option value="4" data-i18n="infraredBallCamera">
                    红外双目球机
                  </option>
                </select>
              </div>
            </div>
            <div class="form-group" style="padding:1px;margin:0px;">
              <label class="col-sm-3 control-label" for="enterKey" data-i18n="AccessKey">访问秘钥</label>
              <div class="col-sm-8">
                <input id="enterKey" vk="true" type="text" class="col-sm-4 form-control"
                  data-i18n-placeholder="pleaseInput" value="" />
              </div>
            </div>
            <div class="form-group" style="padding:1px;margin:0px;">
              <label class="col-sm-3 control-label" for="controlType" data-i18n="ControlMode">控制方式</label>
              <div class="col-sm-8">
                <select id="controlType" class="form-control select2" data-placeholder="" style="width: 100%;">
                  <option value="1" data-i18n="ConventionalSwitch" selected="selected">
                    常规开关
                  </option>
                  <option value="2" data-i18n="InchingSwitch">
                    点动开关
                  </option>
                </select>
              </div>
            </div>
          </div>

          <!--<div class="form-group g5" style="padding:1px;margin:0px">-->
          <!--<label class="col-sm-3 control-label" for="adu_address"-->
          <!--data-i18n="aduAddress">前端地址</label>-->
          <!--<div class="col-sm-8">-->
          <!--<input id="adu_address" vk="true" type="text" class="col-sm-4 form-control"-->
          <!--data-i18n-placeholder="pleaseInput" value="">-->
          <!--</div>-->
          <!--</div>-->
          <!-- // 20230808 1840 新增降噪 -->
          <div class="ThresholdModeContent hide">
            <div class="form-group" style="padding:1px;margin:0px">
              <label class="col-sm-3 control-label" for="ThresholdMode" data-i18n="NoiseReductionMode">降噪模式</label>
              <div class="col-sm-8">
                <select id="ThresholdMode" class="form-control select2" data-placeholder="" style="width: 100%;"
                  onchange="thresholdMode_onchange(this)">
                  <option value="0" data-i18n="Manual">
                    手动
                  </option>
                  <option value="1" data-i18n="Automatic" selected="selected">
                    自动
                  </option>
                </select>
              </div>
            </div>
            <!-- oninput="debugger;if(!/^[0-9]+$/.test(value)) debugger;value=value.replace(/^(\-)*(\d+)\.(\d\d).*$/,'$1$2.$3');debugger;if(value>100)value=100;if(value<0)value=0;if(value<0)value=0;if((value[0] == 0 && value[1] > 0) || value == '00')value=value.slice(1);" -->
            <div id="Threshold-g" class="form-group" style="padding:1px;margin:0px;display: none;">
              <label for="Threshold" class="col-sm-3 control-label" data-i18n="SensorThreshold">阈值(%)</label>
              <div class="col-sm-8">
                <input id="Threshold" vk="true" type="number" class="col-sm-4 form-control"
                  oninput="onThresholdInputTest(this)" onchange="onThresholdChange(this)"
                  data-i18n-placeholder="pleaseInput" value="0" style="padding-right:40px;">
                <div id="ThresholdUnit"
                  style=" position: absolute;right: 15px;border: 1px solid #dddddd;padding: 6px;text-align: center;">
                  %
                </div>
              </div>
            </div>
          </div>
          <div class="form-group g3" style="padding:0px;margin:0px">
            <div class="ADUModeContent">
              <div class="form-group" style="height: 0px;overflow: hidden;;padding:1px;margin:0px">
                <label class="col-sm-3 control-label" for="ADUMode" data-i18n="ADUMode">工作模式</label>
                <div class="col-sm-8">
                  <select id="ADUMode" class="form-control select2" data-placeholder="" style="width: 100%;"
                    onchange="aduMode_onchange(this)">
                    <option value="normalMode" data-i18n="normalMode">
                      维护模式
                    </option>
                    <option value="lowPowerMode" data-i18n="lowPowerMode" selected="selected">
                      低功耗模式
                    </option>
                    <!-- <option value="monitorMode"
                                                data-i18n="monitorMode">
                                            监测模式
                                        </option> -->
                  </select>
                </div>
              </div>
              <div id="sleepTime-g" class="form-group g3 mec-set-non" style="padding:1px;margin:0px">
                <label for="sleepTime" class="col-sm-3 control-label" data-i18n="sleepTime">功耗策略</label>
                <div class="col-sm-8">
                  <select id="sleepTime" class="form-control select2" disabled="disabled"
                    data-i18n-placeholder="selectPlease" style="width: 100%;">
                    <option value="0" data-i18n="noDormancy">不休眠</option>
                    <option value="1" selected="selected" data-i18n="regularDormancy">定时休眠
                    </option>
                  </select>
                </div>
              </div>
              <div id="lowPowerModeParams" class="form-group" style="padding:0px;margin:0px">
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="artificialStateStartTime" class="col-sm-3 control-label"
                    data-i18n="artificialStateStartTime">人工干预状态开始时刻（整点）</label>
                  <div class="col-sm-8">
                    <input id="artificialStateStartTime" vk="true" type="text" class="col-sm-4 form-control"
                      disabled="disabled" data-i18n-placeholder="pleaseInput" value="8">
                  </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="artificialStateEndTime" class="col-sm-3 control-label"
                    data-i18n="artificialStateEndTime">人工干预状态结束时刻（整点）</label>
                  <div class="col-sm-8">
                    <input id="artificialStateEndTime" vk="true" type="text" class="col-sm-4 form-control"
                      disabled="disabled" data-i18n-placeholder="pleaseInput" value="20">
                  </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="artificialStateWakeUpSpace" class="col-sm-3 control-label"
                    data-i18n="artificialStateWakeUpSpace">
                    人工干预状态苏醒间隔（分）</label>
                  <div class="col-sm-8">
                    <select id="artificialStateWakeUpSpace" class="form-control select2" disabled="disabled"
                      data-i18n-placeholder="selectPlease" style="width: 100%;">
                    </select>
                  </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="unartificialStateWakeUpSpace" class="col-sm-3 control-label"
                    data-i18n="unartificialStateWakeUpSpace">
                    非人工干预状态苏醒间隔（分）</label>
                  <div class="col-sm-8">
                    <select id="unartificialStateWakeUpSpace" class="form-control select2" disabled="disabled"
                      data-i18n-placeholder="selectPlease" style="width: 100%;">
                    </select>
                  </div>
                </div>
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="isAutoChangeMode" class="col-sm-3 control-label"
                    data-i18n="isAutoChangeMode">是否自动切换模式</label>
                  <div class="col-sm-8">
                    <select id="isAutoChangeMode" class="form-control select2" disabled="disabled"
                      data-i18n-placeholder="selectPlease" style="width: 100%;">
                      <option value="0" data-i18n="no">否</option>
                      <option value="1" selected="selected" data-i18n="yes">
                        是
                      </option>
                    </select>
                  </div>
                </div>
              </div>
              <div id="monitorModeParams" class="form-group" style="padding:0px;margin:0px">
                <div class="form-group" style="padding:1px;margin:0px">
                  <label for="monitorModeSampleSapce" class="col-sm-3 control-label"
                    data-i18n="monitorModeSampleSapce">监测模式采集间隔</label>
                  <div class="col-sm-8">
                    <input id="monitorModeSampleSapce" vk="true" type="text" class="col-sm-4 form-control"
                      data-i18n-placeholder="pleaseInput" value="0">
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="form-group mec-set g3 g4" style="padding:1px;margin:0px">
            <label for="inst_work_group" class="col-sm-3 control-label" data-i18n="workGroup">工作组号</label>
            <div class="col-sm-8">
              <input id="inst_work_group" oninput="onWorkGroupTest(this)" autocomplete="off" vk="true" type="number"
                class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput" value="0">
            </div>
          </div>
          <div class="form-group mec-set-non g3" style="padding:1px;margin:0px">
            <label for="inst_work_mode" class="col-sm-3 control-label" data-i18n="senorWorkMode">主动上送</label>
            <div class="col-sm-8">
              <select id="inst_work_mode" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">
                <option value="0" selected="selected" data-i18n="On">开</option>
                <option value="1" data-i18n="Off">
                  关
                </option>
              </select>
            </div>
          </div>
          <div id="uploadInterval-g" class="form-group g3" style="padding:1px;margin:0px">
            <label for="uploadInterval" class="col-sm-3 control-label" data-i18n="uploadInterval">上传间隔</label>
            <div class="col-sm-8">
              <input id="uploadInterval" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="60">
            </div>
          </div>
          <div class="form-group mec-set g3" style="padding:1px;margin:0px">
            <label for="warnTemp" class="col-sm-3 control-label" data-i18n="warnTemp">告警温度</label>
            <div class="col-sm-8">
              <input id="warnTemp" vk="true" type="number" step="0.0001" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="0">
            </div>
          </div>
          <div class="form-group mec-set g5" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" data-i18n="taskGroup">任务组号</label>
            <div class="col-sm-8">
              <input id="inst_task_group" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="0">
            </div>
          </div>
          <div class="form-group g1 g2 g3 g4 g5 g6" style="padding:1px;margin:0px">
            <label for="comm_link_type" class="col-sm-3 control-label" data-i18n="commLinkType">通讯链路类型</label>
            <div class="col-sm-8">
              <select id="comm_link_type" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;"></select>
            </div>
          </div>
          <div class="form-group g5" style="padding:1px;margin:0px">
            <label for="comm_link_port" class="col-sm-3 control-label" data-i18n="commLinkPort">通讯端口</label>
            <div class="col-sm-8">
              <select id="comm_link_port" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;"></select>
            </div>
          </div>

          <!--<div id="frequency-g" class="form-group mec-set">-->
          <!--<label for="numInGroup" class="col-sm-4 control-label">电网频率(Hz)</label>-->
          <!--<div class="col-sm-7">-->
          <!--<input id="frequency" vk="true" type="number" class="col-sm-4 form-control"-->
          <!--data-i18n-placeholder="pleaseInput" value="50">-->
          <!--</div>-->
          <!--</div>-->
          <div id="frequency-g" class="form-group mec-set g3 g4" style="padding:1px;margin:0px">
            <label for="frequency" class="col-sm-3 control-label" data-i18n="frequencyUnit">电网频率(Hz)</label>
            <div class="col-sm-8">
              <select id="frequency" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">
                <option value="50" selected="selected">50 Hz</option>
                <option value="60">60 Hz</option>
              </select>
            </div>
          </div>
          <div id="slaveid-g" class="form-group mec-set g3 g4" style="padding:1px;margin:0px">
            <label for="slaveid" class="col-sm-3 control-label" data-i18n="slaveid">从机ID</label>
            <div class="col-sm-8">
              <input id="slaveid" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="0">
            </div>
          </div>
          <div id="numInGroup-g" class="form-group  mec-set" style="display: none;padding:1px;margin:0px">
            <label for="numInGroup" class="col-sm-3 control-label" data-i18n="numInGroup">组内编号</label>
            <div class="col-sm-8">
              <input disabled="disabled" id="numInGroup" vk="true" type="number" class="col-sm-4 form-control"
                placeholder="" value="">
            </div>
          </div>
          <div id="commSpeed-g" class="form-group mec-set-hide hide" style="padding:1px;margin:0px">
            <label for="commSpeed" class="col-sm-3 control-label" data-i18n="commSpeed">通讯速率</label>
            <div class="col-sm-8">
              <input id="commSpeed" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="1">
            </div>
          </div>
          <div id="commLoad-g" class="form-group mec-set-hide hide" style="padding:1px;margin:0px">
            <label for="commLoad" class="col-sm-3 control-label" data-i18n="commLoad">通讯信道</label>
            <div class="col-sm-8">
              <input id="commLoad" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="1">
            </div>
          </div>
          <div id="sampleSpace-g" class="form-group mec-set-non g3 g5 g6" style="padding:1px;margin:0px">
            <label for="sampleSpace" class="col-sm-3 control-label" data-i18n="intervalMinus">采样间隔</label>
            <div class="col-sm-8">
              <input id="sampleSpace" oninput="onSampleSpaceInputTest(this)" vk="true" type="number"
                class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput" value="1440"
                style="padding-right:40px;">
              <div id="intervalMinusUnit" data-i18n="min"
                style=" position: absolute;right: 15px;border: 1px solid #dddddd;padding: 6px;text-align: center;">
                分
              </div>
            </div>
          </div>
          <div id="continuSampTime-g" class="form-group mec-set-non g3 g5 g6" style="padding:1px;margin:0px">
            <label for="continuSampTime" class="col-sm-3 control-label"
              data-i18n="continuousAcquisitionTime">连续采集时间</label>
            <div class="col-sm-8">
              <input id="continuSampTime" vk="true" type="number" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="3" style="padding-right:40px;">
              <div id="continuSampTimeMinusUnit" data-i18n="min"
                style=" position: absolute;right: 15px;border: 1px solid #dddddd;padding: 6px;text-align: center;">
                分
              </div>
            </div>
          </div>
          <div id="sampleStartTime-g" class="form-group mec-set-non g3 g5" style="padding:1px;margin:0px">
            <label for="sampleStartTime" class="col-sm-3 control-label" data-i18n="samleStartTime">起始采样时间(整点)</label>
            <div class="col-sm-8">
              <input id="sampleStartTime" oninput="onSampleStartTimeInputTest(this)" vk="true" type="number"
                class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput" value="0">
            </div>
          </div>

          <!--<div class="form-group  g2 g3 g4 g5" id="clearData_g"-->
          <!--style="padding:1px;margin:0px">-->
          <!--<label for="clearData" class="col-sm-4 control-label"-->
          <!--data-i18n="cleanData">清空数据</label>-->
          <!--<div class="col-sm-7">-->
          <!--<button type="button" id="clearData" class="btn btn-block btn-primary"-->
          <!--style="margin: 0px 60px 10px auto" data-i18n="cleanSensorData">-->
          <!--清空前端数据-->
          <!--</button>-->
          <!--</div>-->
          <!--</div>-->


          <p style="margin-left: 3px" class="form-group g10">基本设置</p>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" for="inst_code_bus" data-i18n="sensorCode">前端编码</label>
            <div class="col-sm-8">
              <input id="inst_code_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>

          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label" for="inst_name_bus" data-i18n="senorName">前端名称</label>
            <div class="col-sm-8">
              <input id="inst_name_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_type_bus" class="col-sm-4 control-label" data-i18n="sensorTypeSet">前端类型</label>
            <div class="col-sm-8">
              <select id="inst_type_bus" onchange="aduType_onchange_bus();" class="form-control select2"
                data-i18n-placeholder="selectPlease" style="width: 100%;"></select>
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">ip地址/波特率</label>
            <div class="col-sm-8">
              <input id="inst_ip_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">
              端口/位</label>
            <div class="col-sm-8">
              <input id="inst_port_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">机器编号(slaveID)</label>
            <div class="col-sm-8">
              <input id="inst_slave_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_connect_bus" class="col-sm-4 control-label">通信方式</label>
            <div class="col-sm-8">
              <select id="inst_connect_bus" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">

                <option value="ModbusTcp" data-i18n="MODBUS_TCP" selected="selected">
                  MODBUS_TCP
                </option>
                <option value="ModbusOverTcp" data-i18n="MODBUS_OVER_TCP" selected="selected">
                  MODBUS_OVER_TCP
                </option>
                <option value="Modbus485" data-i18n="ModbusRTU" selected="selected">
                  ModbusRTU
                </option>

              </select>
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_sample_bus" class="col-sm-4 control-label">采集间隔</label>
            <div class="col-sm-8">
              <input id="inst_sample_bus" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="6">
            </div>
          </div>
          <div class="form-group  g10" style="padding:0px;margin:0px">
            <label for="inst_ADUModelType_bus" class="col-sm-4 control-label">传感器型号</label>
            <div class="col-sm-8">
              <select id="inst_ADUModelType_bus" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">
                <option value="100" data-i18n="未知" selected="selected">
                  未知
                </option>
                <option value="0" data-i18n="QP202-WM" selected="selected">
                  低压QP202-WM
                </option>
                <option value="1" data-i18n="WMD-300" selected="selected">
                  低压WMD300
                </option>
                <option value="2" data-i18n="SVG_SH_ELE">
                  SVG上海电力
                </option>
                <option value="3" data-i18n="SVG_ZHIDA">
                  SVG致达
                </option>
                <option value="4" data-i18n="海康普通摄像头">
                  hikNormal
                </option>
                <option value="5" data-i18n="海康红外摄像头">
                  hikRed
                </option>
                <option value="6" data-i18n="负荷电流">
                  loadI
                </option>
              </select>
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_moren_bus" class="col-sm-4 control-label">默认读写方式</label>
            <div class="col-sm-8">
              <select id="inst_moren_bus" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">


                <option value="0" selected="selected">
                  非系统默认
                </option>
                <option value="1" selected="selected">
                  系统默认
                </option>

              </select>
            </div>
          </div>

        </div>
        <div class="form-group g10" id="readInfo">
          <hr />
          <p style="margin-left: 3px" class="form-group g10">读取设置</p>


          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_type_bus_1" class="col-sm-3 control-label">读取方式</label>
            <div class="col-sm-8">
              <select id="inst_readType_bus_1" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">

                <option value="2" data-i18n="DISCRETE_INPUTS(2)" selected="selected">
                  DISCRETE_INPUTS(2)
                </option>
                <option value="4" data-i18n="INPUT_REGISTERS(4)" selected="selected">
                  INPUT_REGISTERS(4)
                </option>
                <option value="3" data-i18n="HOLD_REGISTERS(3)" selected="selected">
                  HOLD_REGISTERS(3)
                </option>
              </select>
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">地址</label>
            <div class="col-sm-8">
              <input id="inst_addr_bus_1" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">字段</label>
            <div class="col-sm-8">
              <input id="inst_key_bus_1" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
        </div>
        <div class="form-group g10" id="writeInfo">
          <hr />
          <p style="margin-left: 3px" class="form-group g10">控制设置</p>

          <div class="form-group g10" style="padding:1px;margin:0px">
            <label for="inst_type_bus_1" class="col-sm-3 control-label">控制方式</label>
            <div class="col-sm-8">
              <select id="inst_readType_w_bus_1" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;">
                <option value="6" data-i18n="SINGLE_REGISTER(6)" selected="selected">
                  SINGLE_REGISTER(6)
                </option>
                <option value="5" data-i18n="SINGLE_COIL(5)" selected="selected">
                  SINGLE_COIL(5)
                </option>

              </select>
            </div>
          </div>


          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">地址</label>
            <div class="col-sm-8">
              <input id="inst_addr_w_bus_1" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group g10" style="padding:1px;margin:0px">
            <label class="col-sm-3 control-label">字段</label>
            <div class="col-sm-8">
              <input id="inst_key_w_bus_1" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>

        </div>


        <!--</div>-->
        <div class="form-group g1 g2 g3 g4 g5 hide" id="applyAllSensor_g"
          style="padding:1px;margin:0px;margin-top: 8px">
          <label class="col-sm-3 control-label" data-i18n="applyData">操作</label>
          <div class="col-sm-8" style="padding-left:15px;padding-right:15px">
            <div class="col-lg-3 col-sm-6" style="padding: 0px">
              <button type="button" id="applyAllSensor" class="btn btn-block btn-primary"
                style="margin: 0px 10px 10px auto;" data-i18n="applyAllSensor">
                应用到同种
              </button>
            </div>
            <div class="col-lg-3 col-sm-6" style="padding-left:2px;padding-right:2px">
              <button type="button" id="collectOnce" class="btn btn-block btn-primary"
                style="margin: 0px 10px 10px auto" data-i18n="collectOnce">
                批量采集
              </button>
            </div>
            <div class="form-group col-lg-3 col-sm-6 g2 g3 g4 g5"
              style="padding-left:2px;margin: 0px;padding-right: 0px">
              <button type="button" id="clearData" class="btn btn-block btn-primary" style="margin: 0px 10px 10px auto"
                data-i18n="cleanData">
                清空数据
              </button>
            </div>
            <!--<div class="form-group col-lg-3 col-sm-6 hide"-->
            <div class="form-group col-lg-3 col-sm-6 g3" style="padding-left:2px;margin: 0px;padding-right: 0px;">
              <button type="button" id="wakeUpInstrument" class="btn btn-block btn-primary"
                data-i18n="wakeUpInstrumentOnce" style="margin: 0px 10px 10px auto">
                唤醒前端
              </button>
            </div>
            <div class="form-group col-lg-3 col-sm-6 g3" style="padding-left:2px;margin: 0px;padding-right: 0px">
              <button type="button" id="forceChange" class="btn btn-block btn-primary" data-i18n="forceChange"
                style="margin: 0px 10px 10px auto" onclick="onforceChangeClick();">
                强制切换
              </button>
            </div>
          </div>
        </div>
        <div class="col-sm-12">
          <div class="col-sm-3 hide">
            <button type="button" id="get_inst" class="btn btn-block btn-primary"
              style="width: 80px; margin: 10px auto 10px auto" data-i18n="getSensor">
              获取前端
            </button>
          </div>
          <div class="col-sm-3">
            <button type="button" id="add_inst" class="btn btn-block btn-primary"
              style="width: 80px; margin: 10px auto 10px auto" data-i18n="add">增加
            </button>
          </div>
          <div class="col-sm-3">
            <button type="button" id="save_inst" class="btn btn-block btn-primary"
              style="width: 80px; margin: 10px auto 10px auto" data-i18n="save">保存
            </button>
          </div>
          <div class="col-sm-3">
            <button type="button" id="del_inst" class="btn btn-block btn-primary"
              style="width: 80px; margin: 10px auto 10px auto" data-i18n="delete">
              删除
            </button>
          </div>
        </div>
      </form>
    </div>

    <div id="chnInfo" class="col-sm-8 full-height hide">
      <form id="chnInfoForm" class="form-horizontal full-height">
        <br>
        <input id="channel_index" vk="true" type="text" class="col-sm-4 form-control hide" placeholder="" value=""
          disabled="disabled">
        <input id="channel_adutype" vk="true" type="text" class="col-sm-4 form-control hide" placeholder="" value="">
        <div class="form-group" id="channel_type_g">
          <label for="channel_type" class="col-sm-4 control-label" data-i18n="channelType">通道类型</label>
          <div class="col-sm-7">
            <input id="channel_type" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="" disabled="disabled">
          </div>
        </div>
        <div class="form-group" id="channel_name_g">
          <label for="channel_name" class="col-sm-4 control-label" data-i18n="channelName">通道名称</label>
          <div class="col-sm-7">
            <input id="channel_name" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group tev hide">
          <label class="col-sm-4 control-label" for="back_ground_data" data-i18n="bias">偏置</label>
          <div class="col-sm-7">
            <input id="back_ground_data" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group" id="wave_filter_g">
          <label for="wave_filter" class="col-sm-4 control-label" data-i18n="waveFilter">频带设置</label>
          <div class="col-sm-7">
            <select id="wave_filter" class="form-control select2" data-i18n-placeholder="selectPlease"></select>
          </div>
        </div>
        <div class="form-group hide" id="gain_mode_g" style="display:none">
          <label for="gain_mode" class="col-sm-4 control-label" data-i18n="gainMode">增益模式</label>
          <div class="col-sm-7">
            <select id="gain_mode" class="form-control select2" data-i18n-placeholder="selectPlease"
              onchange="gainType_onchange();"></select>
          </div>
        </div>
        <div class="form-group" id="gain_g">
          <label for="gain" class="col-sm-4 control-label" data-i18n="gain_unit">增益[dB]</label>
          <div class="col-sm-7">
            <select id="gain" class="form-control select2" data-i18n-placeholder="selectPlease"></select>
          </div>
        </div>
        <div class="form-group" id="triggerThreshold_g">
          <label for="triggerThreshold" class="col-sm-4 control-label" data-i18n="triggerUnit">触发幅值</label>
          <div class="col-sm-7">
            <!-- <select id="triggerThreshold" class="form-control select2" data-i18n-placeholder="selectPlease"></select> -->
            <input id="triggerThreshold" vk="true" type="number" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group" id="openTime_g">
          <label for="openTime" class="col-sm-4 control-label" data-i18n="openTimeUnit">开门时间</label>
          <div class="col-sm-7">
            <select id="openTime" class="form-control select2" data-i18n-placeholder="selectPlease"></select>
          </div>
        </div>
        <div class="form-group" id="closeTime_g">
          <label for="closeTime" class="col-sm-4 control-label" data-i18n="closeTimeUnit">关门时间</label>
          <div class="col-sm-7">
            <select id="closeTime" class="form-control select2" data-i18n-placeholder="selectPlease"></select>
          </div>
        </div>
        <div class="form-group" id="sam_cycle_g">
          <label for="sam_cycle" class="col-sm-4 control-label" data-i18n="samCycle">采样周期</label>
          <div class="col-sm-7">
            <select id="sam_cycle" class="form-control select2" data-i18n-placeholder="selectPlease"></select>
          </div>
        </div>
        <div class="form-group" id="sam_point_g" style="display: none">
          <label for="sam_point" class="col-sm-4 control-label" data-i18n="samPointNum">每周期采样点数</label>
          <div class="col-sm-7">
            <input id="sam_point" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group" id="sam_rate_g">
          <label for="sam_rate" class="col-sm-4 control-label" data-i18n="samRate">每周期采样频率</label>
          <div class="col-sm-7">
            <input id="sam_rate" vk="true" type="text" class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput"
              value="">
          </div>
        </div>
        <div class="form-group" id="u_ratio_g">
          <label class="col-sm-4 control-label" data-i18n="ratio">变比</label>
          <div class="col-sm-7">
            <input id="u_ratio" vk="true" type="text" class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput"
              value="">
          </div>
        </div>
        <div class="form-group" id="u_phase_g">
          <label for="u_phase" class="col-sm-4 control-label" data-i18n="channelPhase">相别</label>
          <div class="col-sm-7">
            <select id="u_phase" class="form-control select2" style="width: 100%" data-i18n-placeholder="selectPlease">
              <option value="0" selected="selected" data-i18n="phaseA">A相</option>
              <option value="1" data-i18n="phaseB">
                B相
              </option>
              <option value="2" data-i18n="phaseC">
                C相
              </option>
            </select>
          </div>
        </div>
        <div id="sptr_g">
          <div class="form-group" style="margin-bottom: 15px;">
            <label class="col-sm-4 control-label" data-i18n="fWrnThreshold">注意阈值</label>
            <div class="col-sm-7">
              <input id="fWrnThreshold" name="fWrnThreshold" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-4 control-label" data-i18n="fAlmThreshold">告警阈值</label>
            <div class="col-sm-7">
              <input id="fAlmThreshold" name="fAlmThreshold" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
        </div>
        <div id="sf6_g">
          <div class="form-group" style="margin-bottom: 15px;">
            <label for="PhysicalChannelType" class="col-sm-4 control-label"
              data-i18n="PhysicalChannelType">物理通道类型</label>
            <div class="col-sm-7">
              <select id="PhysicalChannelType" class="form-control select2" style="width: 100%" disabled="disabled"
                data-i18n-placeholder="selectPlease">
                <option value="0" selected="selected" data-i18n="GasAbsPressure">气体绝压</option>
                <option value="1" data-i18n="GasGaugePressure">
                  气体表压
                </option>
                <option value="-999" data-i18n="unknown">
                  未知
                </option>
              </select>
            </div>
          </div>
          <div class="form-group" style="margin-bottom: 15px;">
            <label class="col-sm-4 control-label" data-i18n="OverPressureThreshold">过压阈值</label>
            <div class="col-sm-7 input-group" style="padding: 0px 15px;">
              <input id="OverPressureThreshold" name="OverPressureThreshold" vk="true" type="number"
                class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput" value="">
              <span class="input-group-btn"><label class="btn btn-default btn-flat">&nbspMPa</label></span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-4 control-label" data-i18n="LowPressureThreshold">低压阈值</label>
            <div class="col-sm-7 input-group" style="padding: 0px 15px;">
              <input id="LowPressureThreshold" name="LowPressureThreshold" vk="true" type="number"
                class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput" value="">
              <span class="input-group-btn"><label class="btn btn-default btn-flat">&nbspMPa</label></span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-4 control-label" data-i18n="ShutThreshold">闭锁阈值</label>
            <div class="col-sm-7 input-group" style="padding: 0px 15px;">
              <input id="ShutThreshold" name="ShutThreshold" vk="true" type="number" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
              <span class="input-group-btn"><label class="btn btn-default btn-flat">&nbspMPa</label></span>
            </div>
          </div>
          <div class="form-group">
            <label class="col-sm-4 control-label" data-i18n="VolumeRatio">体积比</label>
            <div class="col-sm-7 input-group" style="padding: 0px 15px;">
              <input id="VolumeRatio" vk="true" type="number" step="0.1" oninput="onVolumeRatioInputTest(this)"
                onchange="onVolumeRatioChange(this)" class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput"
                value="">
              <span class="input-group-btn"><label
                  class="btn btn-default btn-flat">%&nbsp&nbsp&nbsp&nbsp&nbsp</label></span>
            </div>
          </div>
        </div>
        <div class="form-group" id="sample_point_g">
          <label for="sample_point" class="col-sm-4 control-label" data-i18n="PHASE_NUM">相位周期数</label>
          <div class="col-sm-7">
            <select id="sample_point" class="form-control select2" style="width: 100%"
              data-i18n-placeholder="selectPlease">
              <option value="120" selected="selected">120</option>
              <option value="180">
                180
              </option>
            </select>
          </div>
        </div>
        <div class="form-group" id="samplePeriod_g">
          <label for="samplePeriod_g" class="col-sm-4 control-label" data-i18n="SAMPLE_PERIOD">采样周波数</label>
          <div class="col-sm-7">
            <select id="samplePeriod" disabled="disabled" class="form-control select2" style="width: 100%"
              data-i18n-placeholder="selectPlease">
              <option value="50" selected="selected">50</option>
              <option value="60">60</option>
            </select>
          </div>
        </div>

        <div class="form-group mec-sensor" id="mec_loop_current_thred_g">
          <label for="mec_loop_current_thred" class="col-sm-4 control-label"
            data-i18n="mecLoopCurrentThred">线圈电流阈值[mA]</label>
          <div class="col-sm-7">
            <input id="mec_loop_current_thred" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group mec-sensor" id="mec_motor_current_thred_g">
          <label class="col-sm-4 control-label" data-i18n="mecMotorCurrentThred">电机电流阈值[mA]</label>
          <div class="col-sm-7">
            <input id="mec_motor_current_thred" vk="true" type="text" class="col-sm-4 form-control"
              data-i18n-placeholder="pleaseInput" value="">
          </div>
        </div>
        <div class="form-group mec-sensor" id="mec_switch_state_g">
          <label for="mec_switch_state" class="col-sm-4 control-label" data-i18n="mecSwitchState">开关量初始状态</label>
          <div class="col-sm-7">
            <select id="mec_switch_state" class="form-control select2" data-i18n-placeholder="selectPlease">
              <option value="0" selected="selected" data-i18n="awaysOpen">常开</option>
              <option value="1" data-i18n="awaysClose">常闭</option>
            </select>
          </div>
        </div>
        <div class="form-group mec-sensor" id="mec_breaker_type_g">
          <label for="mec_switch_state" class="col-sm-4 control-label" data-i18n="mecBreakerType">断路器机构配置</label>
          <div class="col-sm-7">
            <select id="mec_breaker_type" class="form-control select2" data-i18n-placeholder="selectPlease">
              <option value="0" data-i18n="oneMech">三相机械联动（一台机构）</option>
              <option value="1" selected="selected" data-i18n="threeMech">
                三相电气联动（三台机构）
              </option>
            </select>
          </div>
        </div>
        <div class="form-group mec-sensor_hide hide" id="mec_motor_function_type_g">
          <label for="mec_switch_state" class="col-sm-4 control-label" data-i18n="mecMotorFunctionType">电机工作类型</label>
          <div class="col-sm-7">
            <select id="mec_motor_function_type" class="form-control select2" data-i18n-placeholder="selectPlease">
              <option value="0" data-i18n="threePhaseAsynMotor">三相异步电机</option>
              <option value="1" selected="selected" data-i18n="onePhaseMotor">
                单相交/直流电机
              </option>
            </select>
          </div>
        </div>
        <!-- 温湿度传感器参数 -->
        <div class="temp-hum-sensor-g">
          <div class="form-group">
            <label for="upThreshold" class="col-sm-4 control-label" data-i18n="upThreshold">阀值上限</label>
            <div class="col-sm-7">
              <input id="upThreshold" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group">
            <label for="lowerThreshold" class="col-sm-4 control-label" data-i18n="lowerThreshold">阀值下限</label>
            <div class="col-sm-7">
              <input id="lowerThreshold" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
          <div class="form-group">
            <label for="changeThresholdTip" class="col-sm-4 control-label" data-i18n="changeThreshold">变化阀值</label>
            <div class="col-sm-7">
              <input id="changeThreshold" vk="true" type="text" class="col-sm-4 form-control"
                data-i18n-placeholder="pleaseInput" value="">
            </div>
          </div>
        </div>
      </form>
      <div class="content-footer col-sm-11">
        <div class="col-sm-4">
          <button type="button" id="save_curr_inst_set" class="btn btn-block btn-primary"
            style="width: 140px; margin: 10px 0px 10px auto" data-i18n="setCurrent">
            设置当前
          </button>
        </div>
        <div class="col-sm-6">
          <button type="button" id="save_all_inst_set" class="btn btn-block btn-primary"
            style="width: 150px; margin: 10px 60px 10px auto" data-i18n="setAll">
            应用到同种传感器
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  //@ sourceURL=instrument_set.js
  initPageI18n();
  checkNeedKeyboard();

  $('.select2').select2({
    minimumResultsForSearch: Infinity,
    language: "zh-CN"
  });

  $('label').click(function (e) {
    e.stopPropagation();
    return false;
  });

  $('#comm_link_type').on('change', function (param) {
    if (param.currentTarget.value == 'IO') {
      $('#inst_code').attr('placeholder', getI18nName('SensorIOPlaceHolder'));
    } else {
      $('#inst_code').attr('placeholder', getI18nName('pleaseInput'));
    }
    if ($('#inst_type').val() == 'MECH_IS') {
      if (param.currentTarget.value == 'Modbus485') {
        $('#slaveid-g').show();
      } else {
        $('#slaveid-g').hide();
      }
    }


  });

  $('#inst_work_group').on('input', function (obj) {
    if (obj.target.value == "") {
      obj.target.value = "";
    }
  })

  $('#save_inst').hide();
  $("#get_inst").click(function () {
    GetAduCodeList();
  });

  $("div.form-group.mec-set").addClass("hide");
  $("div.form-group.mec-sensor").addClass("hide");
  $("#numInGroup-g").addClass("hide");
  $("#clearData_g").addClass("hide");
  $("#del_inst").parent().removeClass("hide").addClass("hide");

  GetAduTypeList();

  GetAduCodeList();
  //    $('#applyAllSensor_g').hide();
  function gainType_onchange () {
    var type = $('#gain_mode').children('option:selected').val();
    $("#gain_g").removeClass("hide");
    if (type == 1) {
      $("#gain_g").addClass("hide");
    }
  }

  function aduType_onchange () {
    var instType = $('#inst_type').val();
    if (isModbusADU(instType)) {
      $('#inst_type_bus').val(instType)
      $('#select2-inst_type_bus-container')[0].innerHTML = $('#inst_type').find("option:selected").text();
      $('#select2-inst_type_bus-container').attr('title', $('#inst_type').find("option:selected").text());
    }
    checkInstrumentType(instType);
    //2.0.0版本强制初始化为normalMode 维护模式
    $('#ADUMode').val('normalMode').select2();
    $('#ADUMode').trigger('change');
    $('#comm_link_type').trigger('change');
  }

  // 20230808 1840 新增降噪-降噪切换start
  function thresholdMode_onchange (thresholdModDom) {
    var tempMode = thresholdModDom.value;
    if (tempMode == 1) {
      $('#Threshold-g').hide();
    } else {
      $('#Threshold-g').show();
    }
  }
  //禁止输入小数.和负号-
  $('#Threshold').on('keypress', function (event) {
    if (event.key === '.' || event.key === '-') {
      event.preventDefault();
    }
  });

  function onSampleStartTimeInputTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (value > 23) dom.value = 23; if (value == '') dom.value = 0; if (value < 0) dom.value = 0; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }
  function onSampleSpaceInputTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (value == '') dom.value = 1; if (value < 0) dom.value = 1; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }

  function onWorkGroupTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (value < 0) dom.value = 0; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }

  function onThresholdInputTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3');
    if (value > 100) dom.value = 100; if (value == '') dom.value = 0; if (value < 0) dom.value = 0; if (value < 0) dom.value = 0; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }

  function onThresholdChange (dom) {
    dom.value = calcNum(dom.value, true, 0);
    if (dom.value > 100) {
      dom.value = 100;
    } else if (dom.value == '') {
      dom.value = 0;
    }
  }

  function onVolumeRatioInputTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d).*$/, '$1$2.$3');
    if (value > 100) dom.value = 100; if (value == '') dom.value = 0; if (value < 0) dom.value = 0; if (value < 0) dom.value = 0; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }

  function onVolumeRatioChange (dom) {
    dom.value = calcNum(dom.value, true, 3);
    if (dom.value > 100) {
      dom.value = 100;
    } else if (dom.value == '') {
      dom.value = 0;
    }
  }

  function onSF6ParamsInputTest (dom) {
    var value = dom.value;
    if (!/^[0-9]+$/.test(value)) dom.value = value.replace(/^(\-)*(\d+)\.(\d\d\d).*$/, '$1$2.$3');
    if (value == '') dom.value = 0; if (value < 0) dom.value = 0; if (value < 0) dom.value = 0; if ((value[0] == 0 && value[1] > 0) || value == '00') dom.value = value.slice(1);
  }

  function onSF6ParamsChange (dom) {
    dom.value = calcNum(dom.value, true, 3);
    if (dom.value == '') {
      dom.value = 0;
    }
  }

  function calcNum (num, isZerro = true, num2) {
    num = num.replace(/[^\d]/g, ''); // 清除“数字”以外的字符
    if (num.indexOf('.') < 0 && num !== '') {
      // 以上已经过滤，此处控制的是如果没有小数点，首位不能为类似于 01、02的金额
      num = parseFloat(num);
    }
    if (num === 0 && !isZerro) {
      num = '';
    }
    // num2,输入的数不能大于的位数
    if (num2) {
      if (num > Number(Math.pow(10, num2))) {
        num = num.toString().slice(0, num2);
      }
    }
    return num;
  }
  // 20230808 1840 新增降噪-降噪切换end

  function aduMode_onchange (aduModeDom) {
    var tempMode = aduModeDom.value;
    var aduType = $("#inst_type").val();
    var $lowPowerModeParams = $('#lowPowerModeParams');
    var $monitorModeParams = $('#monitorModeParams');
    var $wakeUpInstrument = $('#wakeUpInstrument').parent();
    var $sleepTime_g = $('#sleepTime-g');
    switch (tempMode) {
      case 'normalMode':
        $lowPowerModeParams.hide();
        $monitorModeParams.hide();
        $wakeUpInstrument.hide();
        if (aduType != 'TEMP_HUM_IS'
          && aduType != 'SF6_IS'
          && aduType != 'FLOOD_IS'
          && aduType != 'FLOODWS_OUT') {
          $sleepTime_g.show();
        } else {
          $sleepTime_g.hide();
        }
        break;
      case 'lowPowerMode':
        $monitorModeParams.hide();
        $sleepTime_g.hide();
        if (aduType != 'TEMP_HUM_IS'
          && aduType != 'SF6_IS'
          && aduType != 'FLOOD_IS'
          && aduType != 'FLOODWS_OUT') {
          $wakeUpInstrument.show();
          $lowPowerModeParams.show();
        } else {
          $wakeUpInstrument.hide();
          $lowPowerModeParams.hide();
        }

        break;
      case 'monitorMode':
        $lowPowerModeParams.hide();
        $monitorModeParams.show();
        $wakeUpInstrument.hide();
        $sleepTime_g.hide();
        break;
    }
  }

  $("#ADUMode").trigger('change');
  // $("#ADUMode").parent().parent().hide();
  // $(".ADUModeContent").hide();
  //切换模式取反
  function aduModeReverseChange (aduMode) {
    var tempMode = aduMode === "lowPowerMode" ? "normalMode" : "lowPowerMode";
    $("#ADUMode").val(tempMode);
    $("#ADUMode").trigger('change');
    return tempMode
  }

  function onforceChangeClick () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();
    var selectNode = selectNodes[0];
    var data = {
      aduId: selectNode.id,
      aduType: $('#inst_type').val(),
      workMode: aduModeReverseChange($('#ADUMode').val()),
      sleepTime: $('#sleepTime').val()
    };
    pollGet(GET_FORCE_CHANGE_ADUMODEL, data, function () {
      $.notifyUtil.notifySuccess(getI18nName('forceChangeSuccess'))
    })
  }

  var monitorModeMinSampleSapce = 1;
  //保存前端
  $("#save_inst").click(function () {
    if (isModbusADU($('#inst_type_bus').val())) {
      inst_code = $("#inst_code_bus").val();
      if (validateStrLen(inst_code) == 0) {
        $.showMsgText(getI18nName('instCodeTip'));
        return;
      }
      var formData = {};
      formData.aduOldId = $('#inst_code_bus').val();
      saveModbusAdu(formData);
    } else {
      var formData = {};
      var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
      var selectNodes = treeObj.getSelectedNodes();
      if (selectNodes[0].level !== 1) {
        $.showMsgText(getI18nName('selectInstTip'));
        return;
      }

      var inst_code = $('#inst_code').val();
      var inst_code_old = $('#inst_code_old').val();
      var comm_link_type = $('#comm_link_type').val();
      var comm_link_port = $('#comm_link_port').val();

      formData.aduType = $('#inst_type').val();
      if (formData.aduType == 'HIKVIDEO_OUT') {
        comm_link_type = '';
        formData.extendInfo = JSON.stringify({
          cameraType: $("#cameraType").val(),
          enterKey: $("#enterKey").val()
        })
      }

      if (validateStrLen(inst_code) == 0) {
        $.showMsgText(getI18nName('instCodeTip'));
        return;
      }

      if (comm_link_type == 'Invalid') {
        $.showMsgText(getI18nName('commLinkTypeTip'));
        return;
      }
      if (comm_link_port == 'Invalid') {
        $.showMsgText(getI18nName('commLinkPortTip'));
        return;
      }

      formData.aduId = inst_code;

      if (validateStrLen(inst_code_old) == 0) {
        inst_code_old = inst_code;
      }
      formData.aduOldId = inst_code_old;
      formData.aduName = $('#inst_name').val();
      formData.workMode = $('#inst_work_mode').val();
      formData.workGroup = $('#inst_work_group').val();
      formData.warnTemp = $('#warnTemp').val();
      formData.taskGroup = $('#inst_task_group').val();
      formData.commType = comm_link_type;
      formData.commPort = comm_link_port;
      formData.sampleSpace = $("#sampleSpace").val();
      formData.sampleStartTime = $("#sampleStartTime").val();
      formData.frequency = $("#frequency").val();
      formData.commSpeed = $("#commSpeed").val();
      formData.commLoad = $("#commLoad").val();

      if (!$("#slaveid-g").is(':hidden')) {
        formData.slaveid = $("#slaveid").val();
        checkNumValue(formData.slaveid, "slaveid", getI18nName('SlaveIDRange') + ':[' + 0 + '~' +
          255 + ']', 0, 255);
      }
      //2020/03/04 后新增第三方传感器使用重构后统一管理配置校验
      if (sensorConfigControlUtil.checkIsOut(formData.aduType)) {
        sensorConfigControlUtil.checkOnSave(formData.aduType);
        sensorConfigControlUtil.saveSensorInfo(formData, 'modified');
        return;
      }

      if (!$("#sleepTime-g").is(':hidden')) {
        formData.sleepTime = $("#sleepTime").val();
      } else if (formData.aduType === 'TEMP_HUM_IS'
        || formData.aduType === 'SF6_IS'
        || formData.aduType === 'FLOOD_IS'
        || formData
          .aduType === 'FLOODWS_OUT') {
        formData.sleepTime = 1; //不给用户选择的情况下，默认下发1
      }
      if (!$("#uploadInterval-g").is(':hidden')) {
        formData.uploadInterval = $("#uploadInterval").val();
        var min = 6;
        var max = 1440;
        if (formData.aduType === "TEMP_HUM_IS"
          || formData.aduType === "SF6_IS"
          || formData.aduType === "FLOOD_IS") {
          min = 1;
        }
        checkNumValue(formData.uploadInterval, "uploadInterval", getI18nName('uploadInterval') + ':[' +
          min + '~' + max + ']', min, max);
      }
      if (formData.aduType != "MECH_IS"
        && formData.aduType != "TEMP_HUM_IS"
        && formData.aduType != "SF6_IS"
        && formData.aduType != "FLOOD_IS") {
        var min = 3;
        var max = 1440;
        if (formData.aduType == 'TEMPXP_OUT' ||
          formData.aduType == 'TEMPFC_OUT' ||
          formData.aduType == 'SF6YN_OUT' ||
          formData.aduType == 'TEMPKY_OUT' ||
          formData.aduType === "TEMPSB_OUT" ||
          formData.aduType === "TEMP_HUM_JDRK_OUT" ||
          formData.aduType == 'SMOKERK_OUT') {
          min = 3;
          max = 1440;
          checkNumValue(formData.sampleSpace, "sampleSpace", stringFormat.format(getI18nName(
            'instSampleSpaceTip'), min, max), min, max);
          if (formData.aduType === "TEMPSB_OUT" || formData.aduType === "TEMP_HUM_JDRK_OUT") {
            var tempContinuSampTime = $('#continuSampTime').val();
            formData.extendInfo = JSON.stringify({
              continuSampTime: tempContinuSampTime,
            });
            checkNumValue(tempContinuSampTime, "continuSampTime", stringFormat.format(getI18nName(
              'continuSampTimeTip'), 1, 10), 1, 10);
            if (parseInt(tempContinuSampTime) > parseInt(formData.sampleSpace)) {
              $.notifyUtil.notifyWarning(getI18nName('continuSampTimeCompareTip'))
              return;
            }
          }
        } else {
          checkNumValue(formData.sampleSpace, "sampleSpace", stringFormat.format(getI18nName(
            'instSampleSpaceTip'), min, max), min, max);
          checkNumValue(formData.sampleStartTime, "sampleStartTime", getI18nName(
            'instSampleStartTimeTip'), 0, 23);
        }
      }
      if (!$('#ADUMode').parent().parent().is(':hidden')) {
        // if ($('#ADUMode')[0].needADUMode == true) {
        formData.ADUMode = $("#ADUMode").val();
        formData.artificialStateStartTime = $("#artificialStateStartTime").val();
        formData.artificialStateEndTime = $("#artificialStateEndTime").val();
        formData.artificialStateWakeUpSpace = $("#artificialStateWakeUpSpace").val();
        formData.unartificialStateWakeUpSpace = $("#unartificialStateWakeUpSpace").val();
        formData.isAutoChangeMode = $("#isAutoChangeMode").val();
        if (!$('#monitorModeSampleSapce').parent().parent().is(':hidden')) {
          formData.monitorModeSampleSapce = $("#monitorModeSampleSapce").val();
        }
        if (!$('#lowPowerModeParams').is(':hidden')) {
          checkNumValue(formData.artificialStateStartTime, "artificialStateStartTime", getI18nName(
            'artificialStateStartTimeTip'), 0, 23);
          checkNumValue(formData.artificialStateEndTime, "artificialStateEndTime", getI18nName(
            'artificialStateEndTimeTip'), 0, 23);
        }
        if (!$('#monitorModeParams').is(':hidden')) {
          checkNumValue(formData.monitorModeSampleSapce, "monitorModeSampleSapce", getI18nName(
            'monitorModeSampleSapce') + ':' + monitorModeMinSampleSapce + '-1440',
            monitorModeMinSampleSapce, 1440);
        }
      }

      checkLegalNameValue(formData.aduName, 'inst_name');


      if (formData.aduType != 'SPTR_IS' &&
        formData.aduType != 'TEMPXP_OUT' &&
        formData.aduType != 'TEMPKY_OUT' &&
        formData.aduType != 'SMOKERK_OUT' &&
        formData.aduType != 'TEMPFC_OUT' &&
        formData.aduType != "TEMPSB_OUT" &&
        formData.aduType != "TEMP_HUM_JDRK_OUT" &&
        formData.aduType != 'SF6YN_OUT') {
        var max = getSession('user') == 'SystemAdmin' ? 9999 : 1000;
        checkNumValue(formData.workGroup, "inst_work_group", getI18nName('deviceWorkGroupCheckTip'), 0,
          max);
      }
      if (formData.aduType == 'TEMPXP_OUT' ||
        formData.aduType == 'TEMPFC_OUT' ||
        formData.aduType == 'SF6YN_OUT' ||
        formData.aduType == 'TEMPKY_OUT' ||
        formData.aduType === "TEMPSB_OUT" ||
        formData.aduType === "TEMP_HUM_JDRK_OUT" ||
        formData.aduType == 'SMOKERK_OUT'
      ) {
        if (formData.aduType == 'TEMPSB_OUT') {
          var codePatten =
            /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
          checkLegalNameValueByPatten(formData.aduId, 'inst_code', codePatten,
            getI18nName('TEMPSB_OUT_ID_TIPS'));
        } else {
          var codePatten =
            /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
          checkLegalNameValueByPatten(formData.aduId, 'inst_code', codePatten,
            getI18nName('OUT_NORMAL_ID_TIPS'));
        }
      } else if (formData.aduType == 'HIKVIDEO_OUT') {
        checkLegalNameValueByPatten(formData.aduId, 'inst_code', IPPatten, getI18nName(
          'instIPPattenTip'));
      } else {
        checkIllegalNameValueByPatten(formData.aduId, 'inst_code', numberPatten, getI18nName(
          'instNumberPattenTip'));
      }
      //************* 新增降噪 UHF_IS 新增UHF独立参数配置
      //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
      formData.thresholdMode = '';
      formData.threshold = '';
      if (formData.aduType == "UHF_IS") {
        // var thresholdParams={
        //     aduType:'UHF_IS',
        //     thresholdMode:$('#ThresholdMode').val(),
        //     threshold:$('#Threshold').val(),
        //     aduId:formData.aduId,
        //     aduOldId:formData.aduOldId,
        //     isActiveOnce:0,
        // }
        // saveADUthresholdMode(thresholdParams);

        //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
        formData.thresholdMode = $('#ThresholdMode').val();
        formData.threshold = parseInt($('#Threshold').val());
      }
      //2.0.0版本强制工作模式枚举值：维护模式normalMode
      formData.ADUMode = 'normalMode';
      saveInstInfo(formData, "modified");
    }
  });

  function saveModbusAdu (formData) {
    formData.aduId = $('#inst_code_bus').val();
    formData.aduType = $('#inst_type_bus').val();
    formData.aduName = $('#inst_name_bus').val();
    formData.modbusIp = $('#inst_ip_bus').val();
    formData.modbusPort = $('#inst_port_bus').val();
    formData.modbusSlaveId = $('#inst_slave_bus').val();
    formData.commType = $('#inst_connect_bus').val();

    formData.modbusSample = $('#inst_sample_bus').val();
    formData.modbusAduModelType = $('#inst_ADUModelType_bus').val();
    formData.modbusSysIO = $('#inst_moren_bus').val(); //系统默认读取
    var readInfo = $('#readInfo');
    var readCount = (readInfo.children().length - 2) / 3 - 1;
    formData.readCount = readCount;
    for (var i = 0; i < readCount; i++) {
      formData["read_readType_bus_" + (i + 1)] = $('#inst_readType_bus_' + (i + 1)).val();
      formData["read_addr_bus_" + (i + 1)] = $('#inst_addr_bus_' + (i + 1)).val();
      formData["read_key_bus_" + (i + 1)] = $('#inst_key_bus_' + (i + 1)).val();
    }
    var writeInfo = $('#writeInfo');
    var writeCount = (writeInfo.children().length - 2) / 3 - 1;
    formData.writeCount = writeCount;
    for (var i = 0; i < writeCount; i++) {
      formData["write_readType_bus_" + (i + 1)] = $('#inst_readType_w_bus_' + (i + 1)).val();
      formData["write_addr_bus_" + (i + 1)] = $('#inst_addr_w_bus_' + (i + 1)).val();
      formData["write_key_bus_" + (i + 1)] = $('#inst_key_w_bus_' + (i + 1)).val();
    }

    saveInstInfo(formData, "add");
  }

  function saveNormalAdu (formData) {
    var inst_code = $('#inst_code').val();
    var comm_link_type = $('#comm_link_type').val();
    var comm_link_port = $('#comm_link_port').val();

    formData.aduType = $('#inst_type').val();
    if (formData.aduType == 'HIKVIDEO_OUT') {
      comm_link_type = '';
      formData.extendInfo = JSON.stringify({
        cameraType: $("#cameraType").val(),
        enterKey: $("#enterKey").val()
      })
    }
    if (validateStrLen(inst_code) == 0) {
      $.showMsgText(getI18nName('instCodeTip'));
      return;
    }
    if (comm_link_type == 'Invalid') {
      $.showMsgText(getI18nName('commLinkTypeTip'));
      return;
    }
    if (comm_link_port == 'Invalid') {
      $.showMsgText(getI18nName('commLinkPortTip'));
      return;
    }
    if (!$("#slaveid-g").is(':hidden')) {
      formData.slaveid = $("#slaveid").val();
      checkNumValue(formData.slaveid, "slaveid", getI18nName('SlaveIDRange') + ':[' + 0 + '~' +
        255 + ']', 0, 255);
    }
    formData.aduId = $('#inst_code').val();
    formData.aduName = $('#inst_name').val();
    formData.workMode = $('#inst_work_mode').val();
    formData.workGroup = $('#inst_work_group').val();
    formData.warnTemp = $('#warnTemp').val();
    formData.taskGroup = $('#inst_task_group').val();
    formData.commType = $('#comm_link_type').val();
    formData.commPort = $('#comm_link_port').val();
    formData.sampleSpace = $("#sampleSpace").val();
    formData.sampleStartTime = $("#sampleStartTime").val();
    formData.frequency = $("#frequency").val();
    formData.commSpeed = $("#commSpeed").val();
    formData.commLoad = $("#commLoad").val();

    //2020/03/04 后新增第三方传感器使用重构后统一管理配置校验
    if (sensorConfigControlUtil.checkIsOut(formData.aduType)) {
      sensorConfigControlUtil.checkOnSave(formData.aduType);
      sensorConfigControlUtil.saveSensorInfo(formData, 'add');
      return;
    }

    if (!$("#sleepTime-g").is(':hidden')) {
      formData.sleepTime = $("#sleepTime").val();
    } else {
      formData.sleepTime = 1; //不给用户选择的情况下，默认下发1
    }
    if (!$("#uploadInterval-g").is(':hidden')) {
      formData.uploadInterval = $("#uploadInterval").val();
      var min = 6;
      var max = 1440;
      if (formData.aduType === "TEMP_HUM_IS"
        || formData.aduType === "SF6_IS"
        || formData.aduType === "FLOOD_IS") {
        min = 1;
      }
      checkNumValue(formData.uploadInterval, "uploadInterval", getI18nName('uploadInterval') + ':[' + min + '~' +
        max + ']', min, max);
    }
    if (!$("#slaveid-g").is(':hidden')) {
      formData.slaveid = $("#slaveid").val();
      checkNumValue(formData.slaveid, "slaveid", getI18nName('SlaveIDRange') + ':[' + 0 + '~' +
        255 + ']', 0, 255);
    }
    if (formData.aduType != "MECH_IS"
      && formData.aduType != "TEMP_HUM_IS"
      && formData.aduType != "SF6_IS"
      && formData.aduType != "FLOOD_IS") {
      var min = 3;
      var max = 1440;
      if (formData.aduType == 'TEMPXP_OUT' ||
        formData.aduType == 'TEMPFC_OUT' ||
        formData.aduType == 'SF6YN_OUT' ||
        formData.aduType == 'TEMPKY_OUT' ||
        formData.aduType === "TEMPSB_OUT" ||
        formData.aduType === "TEMP_HUM_JDRK_OUT" ||
        formData.aduType == 'SMOKERK_OUT') {
        min = 3;
        max = 1440;
        checkNumValue(formData.sampleSpace, "sampleSpace", stringFormat.format(getI18nName(
          'instSampleSpaceTip'), min, max), min, max);
        if (formData.aduType === "TEMPSB_OUT" || formData.aduType === "TEMP_HUM_JDRK_OUT") {
          var tempContinuSampTime = $('#continuSampTime').val();
          formData.extendInfo = JSON.stringify({
            continuSampTime: tempContinuSampTime,
          });
          checkNumValue(tempContinuSampTime, "continuSampTime", stringFormat.format(getI18nName(
            'continuSampTimeTip'), 1, 10), 1, 10);
          if (parseInt(tempContinuSampTime) > parseInt(formData.sampleSpace)) {
            $.notifyUtil.notifyWarning(getI18nName('continuSampTimeCompareTip'))
            return;
          }
        }
      } else {
        checkNumValue(formData.sampleSpace, "sampleSpace", stringFormat.format(getI18nName(
          'instSampleSpaceTip'), min, max), min, max);
        checkNumValue(formData.sampleStartTime, "sampleStartTime", getI18nName('instSampleStartTimeTip'), 0,
          23);
      }
    }
    // if (!$('#ADUMode').parent().parent().is(':hidden')) {
    if ($('#ADUMode')[0].needADUMode == true) {
      formData.ADUMode = $("#ADUMode").val();
      formData.artificialStateStartTime = $("#artificialStateStartTime").val();
      formData.artificialStateEndTime = $("#artificialStateEndTime").val();
      formData.artificialStateWakeUpSpace = $("#artificialStateWakeUpSpace").val();
      formData.unartificialStateWakeUpSpace = $("#unartificialStateWakeUpSpace").val();
      formData.isAutoChangeMode = $("#isAutoChangeMode").val();
      if (!$('#lowPowerModeParams').is(':hidden')) {
        checkNumValue(formData.artificialStateStartTime, "artificialStateStartTime", getI18nName(
          'artificialStateStartTimeTip'), 0, 23);
        checkNumValue(formData.artificialStateEndTime, "artificialStateEndTime", getI18nName(
          'artificialStateEndTimeTip'), 0, 23);
      }
      if (!$('#monitorModeSampleSapce').parent().parent().is(':hidden')) {
        formData.monitorModeSampleSapce = $("#monitorModeSampleSapce").val();
      }
      if (!$('#monitorModeParams').is(':hidden')) {
        checkNumValue(formData.monitorModeSampleSapce, "monitorModeSampleSapce", getI18nName(
          'monitorModeSampleSapce') + ':' + monitorModeMinSampleSapce + '-1440',
          monitorModeMinSampleSapce, 1440);
      }
    }
    checkLegalNameValue(formData.aduName, 'inst_name');
    if (formData.aduType != 'SPTR_IS' &&
      formData.aduType != 'TEMPXP_OUT' &&
      formData.aduType != 'TEMPFC_OUT' &&
      formData.aduType != 'SF6YN_OUT' &&
      formData.aduType != 'TEMPKY_OUT' &&
      formData.aduType != "TEMPSB_OUT" &&
      formData.aduType != "TEMP_HUM_JDRK_OUT" &&
      formData.aduType != 'SMOKERK_OUT') {
      var max = getSession('user') == 'SystemAdmin' ? 9999 : 1000; //测试专用账户增加到9999限制
      checkNumValue(formData.workGroup, "inst_work_group", getI18nName('deviceWorkGroupCheckTip'), 0, max);
    }
    if (formData.aduType == 'TEMPXP_OUT' ||
      formData.aduType == 'TEMPFC_OUT' ||
      formData.aduType == 'SF6YN_OUT' ||
      formData.aduType == 'TEMPKY_OUT' ||
      formData.aduType === "TEMPSB_OUT" ||
      formData.aduType === "TEMP_HUM_JDRK_OUT" ||
      formData.aduType == 'SMOKERK_OUT'
    ) {
      if (formData.aduType == 'TEMPSB_OUT') {
        var codePatten =
          /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):(([0-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
        checkLegalNameValueByPatten(formData.aduId, 'inst_code', codePatten,
          getI18nName('TEMPSB_OUT_ID_TIPS'));
      } else {
        var codePatten =
          /^((([1-9])|([1-9][0-9])|(1[0-9][0-9])|(2[0-4][0-9])|(25[0-5])):([0-9]|[1-9]\d|[1-9]\d{2}|[1-9]\d{3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5]))$/
        checkLegalNameValueByPatten(formData.aduId, 'inst_code', codePatten,
          getI18nName('OUT_NORMAL_ID_TIPS'));
      }
    } else if (formData.aduType == 'HIKVIDEO_OUT') {
      checkLegalNameValueByPatten(formData.aduId, 'inst_code', IPPatten, getI18nName('instIPPattenTip'));
    } else {
      checkIllegalNameValueByPatten(formData.aduId, 'inst_code', numberPatten, getI18nName(
        'instNumberPattenTip'));
    }
    //************* 新增降噪 UHF_IS 新增UHF独立参数配置
    //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
    formData.thresholdMode = '';
    formData.threshold = '';
    if (formData.aduType == "UHF_IS") {
      // var thresholdParams={
      //     aduType:'UHF_IS',
      //     thresholdMode:$('#ThresholdMode').val(),
      //     threshold:$('#Threshold').val(),
      //     aduId:formData.aduId,
      //     aduOldId:formData.aduOldId,
      //     isActiveOnce:0,
      // }
      // saveADUthresholdMode(thresholdParams);
      //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
      formData.thresholdMode = $('#ThresholdMode').val();
      formData.threshold = parseInt($('#Threshold').val());
    }
    //2.0.0版本强制工作模式枚举值：维护模式normalMode
    formData.ADUMode = 'normalMode';
    saveInstInfo(formData, "add");
  }

  //增加前端
  $("#add_inst").click(function () {
    //        var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    //        var selectNodes = treeObj.getSelectedNodes();
    //        if(selectNodes[0].level !== 1){
    //            $.showMsgText('请选择需要修改的前端！');
    //            return;
    //        }

    var type = $('#inst_type').val();
    if (type == "" || type == null || type == undefined) {
      type = $('#inst_type_bus').val();
    }
    var formData = {};
    if (isModbusADU(type)) {
      saveModbusAdu(formData);
    } else {
      saveNormalAdu(formData);
    }

  });

  //删除前端
  $("#del_inst").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();
    if (selectNodes[0].level !== 1) {
      $.showMsgText(getI18nName('deleteInstTip'));
      return;
    }
    //        var text = "确定删除</br>" + treeObj.getSelectedNodes()[0].getParentNode().name + "</br>前端编码:" + selectNodes[0].id + "？";
    var text = stringFormat.format(getI18nName('deleteInstConfirmTip'), treeObj.getSelectedNodes()[0]
      .getParentNode().name, selectNodes[0].id);
    $.fn.alertMsg(
      'warning',
      text, [{
        id: 'yes',
        text: getI18nName('delete'),
        callback: function () {
          var formData = {};
          formData.aduId = selectNodes[0].id;
          delInstInfo(formData);
        }
      }, {
        id: 'no',
        text: getI18nName('cancel')
      }]
    );
  });
  //保存当前（通道参数）
  $("#save_curr_inst_set").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();
    if (selectNodes[0].level !== 2) {
      $.showMsgText(getI18nName('selectInstTip'));
      return;
    }
    var selectNode = selectNodes[0];
    var formData = {};
    formData.aduId = selectNode.pId;
    formData.channelIndex = $("#channel_index").val();
    formData.channelType = selectNode.channelType;
    formData.channelName = $("#channel_name").val();
    formData.backGroundData = $("#back_ground_data").val();
    if (formData.channelType == "TEV") {
      checkNumValue(formData.backGroundData, "back_ground_data", getI18nName('bias_data'), -100, 100);
      //            checkNumValue(formData.backGroundData, "back_ground_data", getI18nName('back_ground_data'), 0, 100);
    }

    if (formData.channelType == "SF6") {
      formData.PhysicalChannelType = $('#PhysicalChannelType').val();
      formData.OverPressureThreshold = $('#OverPressureThreshold').val();
      formData.LowPressureThreshold = $('#LowPressureThreshold').val();
      formData.ShutThreshold = $('#ShutThreshold').val();
      formData.VolumeRatio = $('#VolumeRatio').val();

      if (formData.PhysicalChannelType == -999) {
        var pcTypeMsg = getI18nName('PhysicalChannelType') + ":" + getI18nName('unknown');
        $.notifyUtil.notifyWarning(pcTypeMsg)
        return;
      }

      var min = 0;
      var max = 1;
      if (formData.PhysicalChannelType == 1) {
        min = -0.1;
        max = 0.9;
      }

      checkNumValue(formData.OverPressureThreshold, "OverPressureThreshold", getI18nName('OverPressureThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.LowPressureThreshold, "LowPressureThreshold", getI18nName('LowPressureThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.ShutThreshold, "ShutThreshold", getI18nName('ShutThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.VolumeRatio, "VolumeRatio", getI18nName('VolumeRatio') + ":[" + 0 + "~" + 100 + "]", 0, 100);
    }

    formData.gain = $('#gain').val();
    formData.triggerThreshold = $("#triggerThreshold").val()
    formData.openTime = $("#openTime").val()
    formData.closeTime = $("#closeTime").val()

    formData.gainModel = $('#gain_mode').val();
    formData.samplePeriod = $('#sam_cycle').val();
    formData.samplePoint = $('#sam_point').val();
    formData.sampleRate = $('#sam_rate').val();
    formData.ratio = $('#u_ratio').val();
    formData.channelPhase = $('#u_phase').val();
    formData.filter = $('#wave_filter').val();

    if (!$('#triggerThreshold').parent().parent().is(':hidden')) {
      checkNumValue(formData.triggerThreshold, "triggerThreshold", getI18nName('triggerUnitTips'), 0.35, 35000);
    }
    if (!$('#sam_point').parent().parent().is(':hidden')) {
      checkNumValue(formData.samplePoint, "sam_point", getI18nName('sam_point'), 20, 2000);
    }
    if (!$('#sam_rate').parent().parent().is(':hidden')) {
      checkNumValue(formData.sampleRate, "sam_rate", getI18nName('sam_rate'), 20, 2000);
    }
    if (!$('#sample_point').parent().parent().is(':hidden')) { //HFCT/TEV PRPS通道的相位数
      formData.samplePoint = $('#sample_point').val();
    }
    if (!$('#samplePeriod').parent().parent().is(':hidden')) { //HFCT/TEV PRPS通道的采样周波数
      formData.samplePeriod = $('#samplePeriod').val();
    }
    if (!$('.temp-hum-sensor-g').is(':hidden')) {
      formData.upThreshold = $('#upThreshold').val();
      formData.lowerThreshold = $('#lowerThreshold').val();
      formData.changeThreshold = $('#changeThreshold').val();
      if (!$("#devInfo").hasClass('hide')) {
        formData.uploadInterval = $("#uploadInterval").val();
        checkNumValue(formData.uploadInterval, "uploadInterval", getI18nName('uploadInterval'), 1,
          1440);
      }
      if (selectNode.channelType == "TEMP") {
        var msg = getI18nName('upThresholdTip') + ":[-40~85]";
        checkNumValue(formData.upThreshold, "upThreshold", msg, -40, 85);
        msg = getI18nName('lowerThresholdTip') + ":[-40~85]";
        checkNumValue(formData.lowerThreshold, "lowerThreshold", msg, -40, 85);
        msg = getI18nName('changeThresholdTip') + ":[0~125]";
        checkNumValue(formData.changeThreshold, "changeThreshold", msg, 0, 125);
      } else {
        var msg = getI18nName('upThresholdTip') + ":[0~100]";
        checkNumValue(formData.upThreshold, "upThreshold", msg, 0, 100);
        msg = getI18nName('lowerThresholdTip') + ":[0~100]";
        checkNumValue(formData.lowerThreshold, "lowerThreshold", msg, 0, 100);
        msg = getI18nName('changeThresholdTip') + ":[0~100]";
        checkNumValue(formData.changeThreshold, "changeThreshold", msg, 0, 100);
      }
      if (parseInt(formData.lowerThreshold) > parseInt(formData.upThreshold)) {
        $.notifyUtil.notifyWarning(getI18nName('upLowerThresholderrTip'))
        return;
      }
    }
    if (!checkDecimalPlaces(formData.upThreshold)) return;
    if (!checkDecimalPlaces(formData.lowerThreshold)) return;
    if (!checkDecimalPlaces(formData.changeThreshold)) return;
    formData.loopCurrentThred = $('#mec_loop_current_thred').val();
    formData.motorCurrentThred = $('#mec_motor_current_thred').val();
    formData.switchState = $('#mec_switch_state').val();
    formData.breakerType = $('#mec_breaker_type').val();
    formData.motorFunctionType = $('#mec_motor_function_type').val();
    if (!$('#sptr_g').is(':hidden')) {
      channelValidater.reset();
      formData.fWrnThreshold = $('#fWrnThreshold').val();
      formData.fAlmThreshold = $('#fAlmThreshold').val();
      if (!channelValidater.validateSubmitForm()) {
        return;
      }
      channelValidater.reset();
    }
    saveChannelInfo(formData);
  });

  function checkDecimalPlaces (number, places) {
    places = places >= 0 ? places : 1;
    if (number >= 0 && number.indexOf('.') !== -1 && number.toString().split('.')[1].length > places) {
      $.notifyUtil.notifyWarning(getI18nName('changeThresholdLimit'))
      return false;
    } else {
      return true
    }
  }
  //保存同类所有传感器(通道参数)
  $("#save_all_inst_set").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();
    if (selectNodes[0].level !== 2) {
      $.showMsgText(getI18nName('selectChannelTip'));
      return;
    }
    var selectNode = selectNodes[0];
    var formData = {};
    //        formData.aduId = $('#inst_code').val();
    formData.channelType = selectNode.channelType;
    //        formData.channelName = selectNodes[0].channelType;
    formData.backGroundData = $("#back_ground_data").val();
    if (formData.channelType == "TEV") {
      checkNumValue(formData.backGroundData, "back_ground_data", getI18nName('bias_data'), -100, 100);
      //            checkNumValue(formData.backGroundData, "back_ground_data", getI18nName('back_ground_data'), 0, 100);
    }

    if (formData.channelType == "SF6") {
      formData.PhysicalChannelType = $('#PhysicalChannelType').val();
      formData.OverPressureThreshold = $('#OverPressureThreshold').val();
      formData.LowPressureThreshold = $('#LowPressureThreshold').val();
      formData.ShutThreshold = $('#ShutThreshold').val();
      formData.VolumeRatio = $('#VolumeRatio').val();

      if (formData.PhysicalChannelType == -999) {
        var pcTypeMsg = getI18nName('PhysicalChannelType') + ":" + getI18nName('unknown');
        $.notifyUtil.notifyWarning(pcTypeMsg)
        return;
      }

      var min = 0;
      var max = 1;
      if (formData.PhysicalChannelType == 1) {
        min = -0.1;
        max = 0.9;
      }

      checkNumValue(formData.OverPressureThreshold, "OverPressureThreshold", getI18nName('OverPressureThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.LowPressureThreshold, "LowPressureThreshold", getI18nName('LowPressureThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.ShutThreshold, "ShutThreshold", getI18nName('ShutThreshold') + ":[" + min + "~" + max + "]", min, max);
      checkNumValue(formData.VolumeRatio, "VolumeRatio", getI18nName('VolumeRatio') + ":[" + 0 + "~" + 100 + "]", 0, 100);
    }

    formData.gain = $('#gain').val();
    formData.triggerThreshold = $("#triggerThreshold").val()
    formData.openTime = $("#openTime").val()
    formData.closeTime = $("#closeTime").val()

    var aduType = selectNode.getParentNode().pId;
    formData.aduType = aduType;
    formData.gainModel = $('#gain_mode').val();
    formData.samplePeriod = $('#sam_cycle').val();
    formData.samplePoint = $('#sam_point').val();
    formData.sampleRate = $('#sam_rate').val();
    formData.ratio = $('#u_ratio').val();
    formData.filter = $('#wave_filter').val();
    if (!$('#triggerThreshold').parent().parent().is(':hidden')) {
      checkNumValue(formData.triggerThreshold, "triggerThreshold", getI18nName('triggerUnitTips'), 0.35, 35000);
    }
    if (!$('#sam_point').parent().parent().is(':hidden')) {
      checkNumValue(formData.samplePoint, "sam_point", getI18nName('sam_point'), 20, 2000);
    }
    if (!$('#sam_rate').parent().parent().is(':hidden')) {
      checkNumValue(formData.sampleRate, "sam_rate", getI18nName('sam_rate'), 20, 2000);
    }
    if (!$('#sample_point').parent().parent().is(':hidden')) { //HFCT/TEV PRPS通道的相位数
      formData.samplePoint = $('#sample_point').val();
    }
    if (!$('#samplePeriod').parent().parent().is(':hidden')) { //HFCT/TEV PRPS通道的周波数
      formData.samplePeriod = $('#samplePeriod').val();
    }
    if (!$('.temp-hum-sensor-g').is(':hidden')) {
      formData.upThreshold = $('#upThreshold').val();
      formData.lowerThreshold = $('#lowerThreshold').val();
      formData.changeThreshold = $('#changeThreshold').val();
      if (!$("#devInfo").hasClass('hide')) {
        formData.uploadInterval = $("#uploadInterval").val();
        checkNumValue(formData.uploadInterval, "uploadInterval", getI18nName('uploadInterval'), 1,
          1440);
      }
      if (selectNode.channelType == "TEMP") {
        var msg = getI18nName('upThresholdTip') + ":[-40~85]";
        checkNumValue(formData.upThreshold, "upThreshold", msg, -40, 85);
        msg = getI18nName('lowerThresholdTip') + ":[-40~85]";
        checkNumValue(formData.lowerThreshold, "lowerThreshold", msg, -40, 85);
        msg = getI18nName('changeThresholdTip') + ":[0~125]";
        checkNumValue(formData.changeThreshold, "changeThreshold", msg, 0, 125);
      } else {
        var msg = getI18nName('upThresholdTip') + ":[0~100]";
        checkNumValue(formData.upThreshold, "upThreshold", msg, 0, 100);
        msg = getI18nName('lowerThresholdTip') + ":[0~100]";
        checkNumValue(formData.lowerThreshold, "lowerThreshold", msg, 0, 100);
        msg = getI18nName('changeThresholdTip') + ":[0~100]";
        checkNumValue(formData.changeThreshold, "changeThreshold", msg, 0, 100);
      }
      if (formData.lowerThreshold - 0 > formData.upThreshold - 0) {
        $.notifyUtil.notifyWarning(getI18nName('upLowerThresholderrTip'))
        return;
      }
    }

    formData.loopCurrentThred = $('#mec_loop_current_thred').val();
    formData.motorCurrentThred = $('#mec_motor_current_thred').val();
    formData.switchState = $('#mec_switch_state').val();
    formData.breakerType = $('#mec_breaker_type').val();
    formData.motorFunctionType = $('#mec_motor_function_type').val();
    if (validateStrLen(formData.sampleSpace) != 0
      && formData.aduType != "TEMP_HUM_IS"
      && formData.aduType != "SF6_IS"
      && formData.aduType != "FLOOD_IS"
      && formData.aduType != "FLOODWS_OUT") {
      checkNumValue(formData.sampleSpace, "sampleSpace", getI18nName('instSampleSpaceTip'), 3, 1440);
    }
    applySensorConfig(formData);
  });

  //应用到同种前端
  $("#applyAllSensor").click(function () {
    var formData = {};
    var comm_link_type = $('#comm_link_type').val();
    var comm_link_port = $('#comm_link_port').val();

    formData.aduType = $('#inst_type').val();

    if (formData.aduType == 'HIKVIDEO_OUT') {
      comm_link_type = '';
      formData.extendInfo = JSON.stringify({
        cameraType: $("#cameraType").val(),
        enterKey: $("#enterKey").val()
      })
    }
    if (comm_link_type == 'Invalid') {
      $.showMsgText(getI18nName('commLinkTypeTip'));
      return;
    }
    if (comm_link_port == 'Invalid') {
      $.showMsgText(getI18nName('commLinkPortTip'));
      return;
    }
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    //        var text = "确定将此配置应用到</br>" + treeObj.getSelectedNodes()[0].name + " 的同类前端？";
    var text = stringFormat.format(getI18nName('activeInstConfigTip'), treeObj.getSelectedNodes()[0].name);


    formData.workMode = $('#inst_work_mode').val();
    formData.workGroup = $('#inst_work_group').val();
    formData.warnTemp = $('#warnTemp').val();
    formData.taskGroup = $('#inst_task_group').val();
    formData.commType = comm_link_type;
    formData.commPort = comm_link_port;
    formData.frequency = $("#frequency").val();
    formData.sleepTime = $("#sleepTime").val();
    if (formData.aduType === 'TEMP_HUM_IS'
      || formData.aduType === 'SF6_IS'
      || formData.aduType === 'FLOOD_IS') {
      formData.sleepTime = 1; //不给用户选择的情况下，默认下发1
    }
    formData.sampleSpace = $("#sampleSpace").val();
    formData.sampleStartTime = $("#sampleStartTime").val();
    if (instrumentTypeGroup[formData.aduType] == 3 && !$('#ADUMode').parent().parent().is(':hidden')) {
      // if (instrumentTypeGroup[formData.aduType] == 3 && $('#ADUMode')[0].needADUMode == true) {
      formData.ADUMode = $("#ADUMode").val();
      formData.artificialStateStartTime = $("#artificialStateStartTime").val();
      formData.artificialStateEndTime = $("#artificialStateEndTime").val();
      formData.artificialStateWakeUpSpace = $("#artificialStateWakeUpSpace").val();
      formData.unartificialStateWakeUpSpace = $("#unartificialStateWakeUpSpace").val();
      formData.isAutoChangeMode = $("#isAutoChangeMode").val();
      if (!$('#monitorModeSampleSapce').parent().parent().is(':hidden')) {
        formData.monitorModeSampleSapce = $("#monitorModeSampleSapce").val();
      }
      if (!$('#lowPowerModeParams').is(':hidden')) {
        checkNumValue(formData.artificialStateStartTime, "artificialStateStartTime", getI18nName(
          'artificialStateStartTimeTip'), 0, 23);
        checkNumValue(formData.artificialStateEndTime, "artificialStateEndTime", getI18nName(
          'artificialStateEndTimeTip'), 0, 23);
      }
      if (!$('#monitorModeParams').is(':hidden')) {
        checkNumValue(formData.monitorModeSampleSapce, "monitorModeSampleSapce", getI18nName(
          'monitorModeSampleSapce') + ':' + monitorModeMinSampleSapce + '-1440',
          monitorModeMinSampleSapce, 1440);
      }
    }
    if (formData.aduType != "MECH_IS"
      && formData.aduType != "TEMP_HUM_IS"
      && formData.aduType != "SF6_IS"
      && formData.aduType !=
      "FLOOD_IS") {
      checkNumValue(formData.sampleSpace, "sampleSpace", getI18nName('instSampleSpaceTip'), 3, 1440);
      checkNumValue(formData.sampleStartTime, "sampleStartTime", getI18nName('instSampleStartTimeTip'), 0,
        23);
    }
    if (!$("#devInfo").hasClass('hide') && !$('#uploadInterval-g').hasClass('hide')) {
      formData.uploadInterval = $("#uploadInterval").val();
      checkNumValue(formData.uploadInterval, "uploadInterval", getI18nName('uploadInterval'), 1, 1440);
    }
    if (!$('#sptr_g').is(':hidden')) {
      formData.fWrnThreshold = $('#fWrnThreshold').val();
      formData.fAlmThreshold = $('#fAlmThreshold').val();
      if (!channelValidater.validateSubmitForm()) {
        return;
      }
    }
    var max = getSession('user') == 'SystemAdmin' ? 9999 : 1000;
    checkNumValue(formData.workGroup, "inst_work_group", getI18nName('deviceWorkGroupCheckTip'), 0, max);
    layer.confirm(text, {
      title: getI18nName('tips'),
      btn: [getI18nName('activeOnce'), getI18nName('cancel')]
    }, function (index) {
      //************* 新增降噪 UHF_IS 新增UHF独立参数配置
      //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
      formData.thresholdMode = '';
      formData.threshold = '';
      if (formData.aduType == "UHF_IS") {
        // var thresholdParams={
        //     aduType:'UHF_IS',
        //     thresholdMode:$('#ThresholdMode').val(),
        //     threshold:$('#Threshold').val(),
        //     aduId:formData.aduId,
        //     aduOldId:"",
        //     isActiveOnce:1,
        // }
        // saveADUthresholdMode(thresholdParams);

        //20230808 后端修改业务流程，暂定不使用分接口形式,更改为内置formData
        formData.thresholdMode = $('#ThresholdMode').val();
        formData.threshold = parseInt($('#Threshold').val());
      }
      //2.0.0版本强制工作模式枚举值：维护模式normalMode
      formData.ADUMode = 'normalMode';
      poll('POST', APPLY_ADU_CONFIG, formData, function (text) {
        var textshow = getI18nName('activeBatchConfigsBegin');
        $('.notifications').notify({
          message: {
            text: textshow
          },
          type: "success"
        }).show();
      }, function (errorCode) {
        $('.notifications').notify({
          fadeOut: {
            enabled: false
          },
          message: {
            text: getI18nName('activeBatchConfigsFailed') +
              convertErrorCode(errorCode)
          },
          type: "warning"
        }).show();
      });
      layer.close(index);
    }, function () {

    });
  });

  //批量采集
  $("#collectOnce").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();

    var aduId = selectNodes[0].id;
    var aduType = $('#inst_type').val();
    var aduOperationType;
    var text;
    if (selectNodes[0].level === 0) {
      aduOperationType = 2;
      //            text = "确定开始采集</br>" + treeObj.getSelectedNodes()[0].name + " 同类前端的数据？";
      text = stringFormat.format(getI18nName('collectStartOnceTip'), treeObj.getSelectedNodes()[0].name);
    } else {
      aduOperationType = 3;
      //            text = "确定开始采集</br>" + treeObj.getSelectedNodes()[0].getParentNode().name + "</br>前端编码:" + aduId + " 的数据？";
      text = stringFormat.format(getI18nName('collectStartTip'), treeObj.getSelectedNodes()[0]
        .getParentNode().name, aduId);
    }
    layer.confirm(text, {
      title: getI18nName('tips'),
      btn: [getI18nName('collect'), getI18nName('cancel')]
    }, function (index) {
      var message = {
        "methodName": "PushSampleProgress",
        "enablePush": true,
        "parameters": {
          "aduOperationType": aduOperationType,
          "aduType": aduType,
          "aduId": aduId,
        }
      };
      webSocketSend(message);
      $('.notifications').notify({
        message: {
          text: getI18nName('orderSend')
        },
        type: "success"
      }).show();
      layer.close(index);
    }, function () {

    });
  });

  //批量唤醒前端
  $("#wakeUpInstrument").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();

    var aduId = selectNodes[0].id;
    var aduType = $('#inst_type').val();
    var aduOperationType;
    var text;
    if (selectNodes[0].level === 0) {
      aduOperationType = 2;
      text = stringFormat.format(getI18nName('wakeUpInstrumentOnceTip'), treeObj.getSelectedNodes()[0]
        .name);
    } else {
      aduOperationType = 3;
      //            text = "确定开始采集</br>" + treeObj.getSelectedNodes()[0].getParentNode().name + "</br>前端编码:" + aduId + " 的数据？";
      text = stringFormat.format(getI18nName('wakeUpInstrumentTip'), treeObj.getSelectedNodes()[0]
        .getParentNode().name, aduId);
    }
    layer.confirm(text, {
      title: getI18nName('tips'),
      btn: [getI18nName('wakeUp'), getI18nName('cancel')]
    }, function (index) {
      var data = {
        "aduOperationType": aduOperationType,
        "aduType": aduType,
        "aduId": aduId
      };
      pollGet(WAKE_UP_ADU, data, function () {
        $('.notifications').notify({
          message: {
            text: getI18nName('orderSend')
          },
          type: "success"
        }).show();
      });
      layer.close(index);
    }, function () {

    });
  });

  function aduType_onchange_bus () {
    var instType = $('#inst_type_bus').val();
    if (!isModbusADU(instType)) {
      $('#inst_type').val(instType)
      $('#select2-inst_type-container')[0].innerHTML = $('#inst_type_bus').find("option:selected").text();
      $('#select2-inst_type-container').attr('title', $('#inst_type_bus').find("option:selected").text());
    }
    console.info(instType);
    checkInstrumentType(instType);
  }

  function isModbusADU (aduType) {
    //|| aduType == "SF6_IS"->旧版SF6使用了自研智能传感器命名，新增自研传感器后暂时替代该枚举，去除判定
    if (aduType == "LOWTENSVG_IS"
      // || aduType == "SF6_IS" 
      || aduType == "AIR_CONDITIONER_IS"
      || aduType == "LIQUID_LEVEL_IS"
      || aduType == "FIREWORKS_ALARM_IS"
      || aduType == "NOISE_IS"
      || aduType == "ENVI_HUMID_IS"
      || aduType == "LIGHT_IS"
      || aduType == "OPTICAL_TEMP_IS"
      || aduType == "GIS_LOWTEN"
      || aduType == "HIKVIDEO_IS"
      || aduType == "WATER_IS"
      || aduType == "DRYTEMP_IS"
    )
      return true;
    else
      return false;
  }

  setInterval(addBusAddr, 1000);
  var currentAddCount = 1;
  var writeAddCount = 1;

  function addBusAddr () {
    var input = $('#inst_key_bus_' + currentAddCount)
    // console.info(input.val());
    if (input.val() != '' && input.val() != undefined) {
      currentAddCount++;
      var devInfo = $('#readInfo');
      var script = "<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label for=\"inst_readType_bus\" class=\"col-sm-4 control-label\"\
 >读取方式</label><div class=\"col-sm-7\"><select id=\"inst_readType_bus\" onchange=\"aduType_onchange();\"\
class=\"form-control select2\"data-placeholder=\"" + getI18nName('selectPlease') + "\" style=\"width: 100%;\"><option value=\"2\"data-i18n=\"DISCRETE_INPUTS(2)\"selected=\"selected\"> DISCRETE_INPUTS(2)\
</option><option value=\"4\"data-i18n=\"INPUT_REGISTERS(4)\"selected=\"selected\">INPUT_REGISTERS(4)</option><option value=\"3\"data-i18n=\"HOLD_REGISTERS(3)\"selected=\"selected\">\
HOLD_REGISTERS(3)</option></select></div></div>\
<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label class=\"col-sm-4 control-label\" \
>地址</label><div class=\"col-sm-7\"><input id=\"inst_addr_bus\" vk=\"true\" type=\"text\"\
class=\"col-sm-4 form-control\"placeholder=\"请输入...\" value=\"\"></div></div>\
<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label class=\"col-sm-4 control-label\" \
>字段</label><div class=\"col-sm-7\"><input id=\"inst_key_bus\" vk=\"true\" type=\"text\"\
class=\"col-sm-4 form-control\"placeholder=\"请输入...\" value=\"\"></div></div>"
      script = script.replace(new RegExp("inst_readType_bus", "gm"), "inst_readType_bus_" + currentAddCount);
      script = script.replace(new RegExp("inst_addr_bus", "gm"), "inst_addr_bus_" + currentAddCount);
      script = script.replace(new RegExp("inst_key_bus", "gm"), "inst_key_bus_" + currentAddCount);
      //  console.info(script)
      devInfo.append(script);
      // console.info(script)

    }


    var writeInput = $('#inst_key_w_bus_' + writeAddCount)
    //  console.info(input.val());
    if (writeInput.val() != '' && writeInput.val() != undefined) {
      writeAddCount++;
      var devInfo = $('#writeInfo');
      var script = "<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label for=\"inst_readType_bus\" class=\"col-sm-4 control-label\"\
 >读取方式</label><div class=\"col-sm-7\"><select id=\"inst_readType_w_bus\" onchange=\"aduType_onchange();\"\
class=\"form-control select2\"data-placeholder=\"" + getI18nName('selectPlease') + "\" style=\"width: 100%;\"><option value=\"6\"data-i18n=\"SINGLE_REGISTER(6)\"\selected=\"selected\">\
SINGLE_REGISTER(6)</option><option value=\"5\"data-i18n=\"SINGLE_COIL(5)\"selected=\"selected\">SINGLE_COIL(5)</option>\
</select></div></div>\
<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label class=\"col-sm-4 control-label\" \
>地址</label><div class=\"col-sm-7\"><input id=\"inst_addr_w_bus\" vk=\"true\" type=\"text\"\
class=\"col-sm-4 form-control\"placeholder=\"请输入...\" value=\"\"></div></div>\
<div class=\"form-group g10\" style=\"padding:1px;margin:0px\"><label class=\"col-sm-4 control-label\" \
>字段</label><div class=\"col-sm-7\"><input id=\"inst_key_w_bus\" vk=\"true\" type=\"text\"\
class=\"col-sm-4 form-control\"placeholder=\"请输入...\" value=\"\"></div></div>"
      script = script.replace(new RegExp("inst_readType_w_bus", "gm"), "inst_readType_w_bus_" + writeAddCount);
      script = script.replace(new RegExp("inst_addr_w_bus", "gm"), "inst_addr_w_bus_" + writeAddCount);
      script = script.replace(new RegExp("inst_key_w_bus", "gm"), "inst_key_w_bus_" + writeAddCount);
      // console.info(script)
      devInfo.append(script);
      // console.info(script)

    }

  }

  //清空同类所有
  $("#clearData").click(function () {
    var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
    var selectNodes = treeObj.getSelectedNodes();
    if (selectNodes.length <= 0 || selectNodes[0].level < 0) {
      $.showMsgText(getI18nName('selectSensorTip'));
      return;
    }

    var aduId = selectNodes[0].id;
    var aduType = $('#inst_type').val();
    var aduOperationType;
    var text;
    var data;
    if (selectNodes[0].level === 0) {
      aduOperationType = 2;
      data = "aduOperationType=" + aduOperationType + "&aduType=" + aduType;
      //            text = "确定清空</br>" + treeObj.getSelectedNodes()[0].name + " 同类前端的所有数据？";
      text = stringFormat.format(getI18nName('emptySensorDataOnceTip'), treeObj.getSelectedNodes()[0]
        .name);
    } else {
      aduOperationType = 3;
      data = "aduOperationType=" + aduOperationType + "&aduType=" + aduType + "&aduId=" + aduId;
      //            text = "确定清空</br>" + treeObj.getSelectedNodes()[0].getParentNode().name + "</br>前端编码:" + aduId + " 的所有数据？";
      text = stringFormat.format(getI18nName('emptySensorDataTip'), treeObj.getSelectedNodes()[0]
        .getParentNode().name, aduId);

    }
    var index = layer.confirm(text, {
      title: getI18nName('tips'),
      btn: [getI18nName('confirm'), getI18nName('cancel')]
    }, function () {
      poll('GET', 'ClearADUData', data, function (text) {
        var textshow = getI18nName('emptySensorDataSuccess');
        $('.notifications').notify({
          message: {
            text: textshow
          },
          type: "success"
        }).show();
      }, function (errorCode) {
        $('.notifications').notify({
          fadeOut: {
            enabled: false
          },
          message: {
            text: getI18nName('emptySensorDataFailed') + convertErrorCode(
              errorCode)
          },
          type: "warning"
        }).show();
      });
      layer.close(index);
    }, function () {

    });
  });


  /**
   * 校验规则
   */
  var channelValidater = new BSValidater({
    id: 'chnInfoForm',
    message: '',
    feedbackIcons: {
      validating: 'glyphicon glyphicon-refresh'
    },
    fields: {
      fWrnThreshold: {
        validators: {
          regexp: {
            regexp: /(.*)/,
          },
          callback: {
            message: ' ',
            callback: function (value, validator) {
              if (value.trim() === '' || typeof parseInt(value) !== 'number' || parseInt(
                value) < 10 || parseInt(value) > 1000) {
                $.notifyUtil.notifyWarning(getI18nName('fWrnThreshold_CHECK_Tips'));
                return false;
              } else {
                return true;
              }
            }
          }
        }
      },
      fAlmThreshold: {
        validators: {
          regexp: {
            regexp: /(.*)/,
          },
          callback: {
            message: ' ',
            callback: function (value, validator) {
              //                            "fWrnThreshold": "注意阈值",
              //                                    "fAlmThreshold": "告警阈值"
              if (value.trim() === '' || typeof parseInt(value) !== 'number' || parseInt(
                value) < 50 || parseInt(value) > 5000) {
                $.notifyUtil.notifyWarning(getI18nName('fAlmThreshold_CHECK_1_Tips'));
                return false;
              } else if (parseInt(value) <= parseInt($('#fWrnThreshold').val())) {
                $.notifyUtil.notifyWarning(getI18nName('fAlmThreshold_CHECK_2_Tips'));
                return false;
              } else {
                return true;
              }
            }
          }
        }
      },
    }
  });
  $('#chnInfoForm').bootstrapValidator('removeField', 'OverPressureThreshold');
  $('#chnInfoForm').bootstrapValidator('removeField', 'LowPressureThreshold');
  $('#chnInfoForm').bootstrapValidator('removeField', 'ShutThreshold');
</script>