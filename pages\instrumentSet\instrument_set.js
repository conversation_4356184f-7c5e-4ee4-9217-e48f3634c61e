/*
 * 前端*/
var currentAduNode;

function GetAduTypeList() {
    var data = "type=3"; //前端类型
    checkInstrumentType(1);
    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        $("#inst_type").empty();
        var data = respData;
        if (data.length > 0) {
            // data.push('TEMPKY_OUT');
            // data.push('TEMPSB_OUT');
            // data.push('TEMP_HUM_JDRK_OUT');
            // data.push('TEMP_HUM_JDRK_OUT');
            // data.push('SF6_OUT');
            // data.push('FAN_OUT');
            // data.push('LIGHT_OUT');
            // data.push('IR_DETECTION_OUT');
            // data.push('TEVPRPS_IS');
            // data.push('SF6_IS');
            data.forEach(function (value, index) {
                var option = $("<option />", {
                    "value": "" + value + ""
                }).append(getI18nName(value));
                $("#inst_type").append(option);
                var optionBus = $("<option />", {
                    "value": "" + value + ""
                }).append(getI18nName(value));
                $("#inst_type_bus").append(optionBus);

                if (index == data.length - 1) {
                    checkInstrumentType(data[0]);
                }
            });
        }
    });

    data = "type=4"; //通讯链路类型
    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        $("#comm_link_type").empty();
        var data = respData;
        if (data.length > 0) {
            data.forEach(function (value, index) {
                var option = $("<option />", {
                    "value": "" + value + ""
                }).append(value);
                $("#comm_link_type").append(option);
            });
        }
        $('#comm_link_type').trigger('change');
        checkDefValue($("#inst_type").val());
    });
    data = "type=6"; //通讯端口
    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        $("#comm_link_port").empty();
        var data = respData;
        if (data.length > 0) {
            data.forEach(function (value, index) {
                var option = $("<option />", {
                    "value": "" + value + ""
                }).append(value);
                $("#comm_link_port").append(option);
            });
        }
    });
    data = "type=8"; //干预状态苏醒间隔
    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        $("#artificialStateWakeUpSpace").empty();
        $("#unartificialStateWakeUpSpace").empty();
        var data = respData;
        if (data.length > 0) {
            data.forEach(function (value, index) {
                var option1 = $("<option />", {
                    "value": "" + value + ""
                }).append(value);
                var option2 = $("<option />", {
                    "value": "" + value + ""
                }).append(value);
                $("#artificialStateWakeUpSpace").append(option1);
                $("#unartificialStateWakeUpSpace").append(option2);
            });
            $("#artificialStateWakeUpSpace").val(3).select2();
            $("#unartificialStateWakeUpSpace").val(30).select2();
        }
    });
}

function GetAduCodeList() {
    var data = null;

    // poll('GET', 'GetADUConfig', data, function (text) {
    //     //TODO:Nothing
    // });

    poll('GET', 'GetADUList', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        var zNodes = [];
        var setting = {
            data: {
                simpleData: {
                    idKey: "id",
                    pIdKey: "pId",
                    name: "name",
                    enable: true
                }
            },
            async: {
                enable: true,
                url: serverUrl + "GetChannelList",
                type: "GET",
                dataFilter: ajaxDataFilter,
                autoParam: ["id=aduId"]
            },
            callback: {
                // onExpand: getChannels,
                // beforeExpand: beforeExpandAdu,
                onClick: getDevInfo
                // onAsyncSuccess: onAsyncSuccessret,
                // onExpand:zTreeOnExpand
            }
        };

        for (var i = 0; i < respData.length; i++) {
            var tempdata = {
                id: respData[i].aduType,
                pId: 0,
                name: globalParms[respData[i].aduType],
                level: 0,
                open: true,
                icon: "./plugins/zTree/css/zTreeStyle/img/diy/3.png",
                isParent: respData[i].aduItems.length > 0
            };
            zNodes.push(tempdata);
            if (tempdata.isParent) {
                for (var j = 0; j < respData[i].aduItems.length; j++) {
                    var childdev = {
                        id: respData[i].aduItems[j].aduId,
                        pId: respData[i].aduType,
                        level: 1,
                        name: respData[i].aduItems[j].aduId,
                        isParent: true
                    };
                    zNodes.push(childdev);
                }
            }
        }
        $.fn.zTree.init($("#dev_tree"), setting, zNodes);
    });
}

function ajaxDataFilter(treeId, parentNode, responseData) {
    if (responseData) {
        for (var i = 0; i < responseData.result.length; i++) {
            // responseData.result[i].name = getI18nName(responseData.result[i].channelType);
            responseData.result[i].name = responseData.result[i].channelName;
        }
    }
    return responseData.result;
}

function getChannels(event, treeId, treeNode) {
    var data = "aduId=" + treeNode.id;
    if (treeNode.level === 1) {
        poll('GET', 'GetChannelList', data, function (text) {
            var respData = text.result;
            if (respData.length <= 0)
                return;

            var zNodes = [];
            for (var i = 0; i < respData.length; i++) {
                var tempdata = {
                    id: respData[i].channelIndex,
                    pId: treeNode.id,
                    level: 2,
                    name: respData[i].channelType
                };
                zNodes.push(tempdata);
            }

            var treeObj = $.fn.zTree.getZTreeObj("dev_tree"); //获取ztree对象
            var parentZNode = treeObj.getNodeByParam("id", treeNode.id, null); //获取父节点
            treeObj.addNodes(parentZNode, zNodes, true);
        });
    }
}


function checkDMWChannelType(type) {
    var flag = false;
    switch (type) {
        case "FrostPointRaw":
        case "FrostPointATM":
        case "DewPointRaw":
        case "DewPointATM":
        case "Moisture":
        case "AbsolutePressure":
        case "NormalPressure":
        case "Density":
        case "Oxygen":
        case "SF6":
        case "TEMP":
            flag = true;
            break;
        default:
            break;
    }
    return flag;
}

function checkDMWsenorType(type) {
    var flag = false;
    switch (type) {
        case "VaisalaDTP145":
        case "Wika_GDT20":
        case "Wika_GDHT20":
        case "SHQiuqi_SC75D_SF6":
            flag = true;
            break;
        default:
            break;
    }
    return flag;
}

var DevInfoController = {
    treeLevel: {
        root: 0,
        devInfo: 1,
        channelInfor: 2,
    },
    currentADUinfo: undefined,
    getADUConfig: function (treeNode) {
        var data = "aduType=" + treeNode.id;
        var _this = this;
        poll('GET', GET_ADU_CONFIG, data, function (text) {
            var respData = text.result;
            if (respData.length <= 0)
                return;
            $("#inst_type").val(respData.aduType).select2();
            checkInstrumentType(respData.aduType);
            if (isModbusADU(respData.aduType)) {
                $('#inst_code_bus').val(respData.aduId);
                $('#inst_name_bus').val(respData.aduName);
                $('#inst_type_bus').val(respData.aduType).select2();
                $('#inst_ip_bus').val(respData.modbusIp);
                $('#inst_port_bus').val(respData.modbusPort);
                $('#inst_slave_bus').val(respData.modbusSlaveId);
                $('#inst_connect_bus').val(respData.commType).select2();
                $('#inst_sample_bus').val(respData.modbusSample);
                $('#inst_ADUModelType_bus').val(respData.modbusAduModelType).select2();
                $('#inst_moren_bus').val(respData.modbusSysIO).select2();
                $("#uploadInterval").val(respData.uploadInterval);

                var readCount = parseInt(respData.readCount);
                for (var i = 0; i < readCount; i++) {
                    $('#inst_readType_bus_' + (i + 1)).val(respData['read_readType_bus_' + (i + 1)]).select2();
                    $('#inst_addr_bus_' + (i + 1)).val(respData['read_addr_bus_' + (i + 1)]);
                    $('#inst_key_bus_' + (i + 1)).val(respData['read_key_bus_' + (i + 1)]);

                    addBusAddr();
                }

                var writeCount = parseInt(respData.writeCount);
                for (var i = 0; i < writeCount; i++) {
                    $('#inst_readType_w_bus_' + (i + 1)).val(respData['write_readType_bus_' + (i + 1)]).select2();
                    $('#inst_addr_w_bus_' + (i + 1)).val(respData['write_addr_bus_' + (i + 1)]);
                    $('#inst_key_w_bus_' + (i + 1)).val(respData['write_key_bus_' + (i + 1)]);
                }
                return;
            }
            _this.currentADUinfo = respData;
            //20230808 1840 新增降噪 - 传感器层级编码和名称类型置空
            $('#inst_code').val('');
            $('#inst_name').val('');
            $("#inst_type").val(respData.aduType).select2();
            $('#inst_work_mode').val(respData.workMode).select2();
            $('#inst_work_group').val(respData.workGroup);
            $('#warnTemp').val(respData.warnTemp);
            $('#inst_task_group').val(respData.taskGroup);
            $("#comm_link_type").val(respData.commType).select2();
            $("#comm_link_port").val(respData.commPort).select2();
            $("#uploadInterval").val(respData.uploadInterval);
            $("#sleepTime").val(respData.sleepTime).select2();

            if (respData.aduType == 'HIKVIDEO_OUT') {
                $("#cameraType").val(JSON.parse(respData.extendInfo).cameraType).select2();
                $("#enterKey").val(JSON.parse(respData.extendInfo).enterKey);
            }

            if (respData.aduType === "TEMPSB_OUT" ||
                respData.aduType === "TEMP_HUM_JDRK_OUT") {
                $("#continuSampTime").val(JSON.parse(respData.extendInfo).continuSampTime);
            }

            //ADUMode为新开发功能，旧版无此配置
            if (respData.ADUMode != undefined) {
                var $ADUMode = $("#ADUMode");
                $ADUMode.parent().parent().show();
                $(".ADUModeContent").show();
                // $ADUMode.val(respData.ADUMode).select2();
                $ADUMode.val('normalMode').select2();
                $ADUMode.trigger('change');
                $ADUMode[0].needADUMode = true;
                // $ADUMode.parent().parent().hide();
                $("#sleepTime").trigger('change');
            } else {
                $ADUMode[0].needADUMode = false;
                $("#ADUMode").parent().parent().hide();
                $(".ADUModeContent").hide();
                $("#sleepTime-g").hide();
                // $('#wakeUpInstrument').parent().remove();
            }

            $("#artificialStateStartTime").val(respData.artificialStateStartTime);
            $("#artificialStateEndTime").val(respData.artificialStateEndTime);
            $("#artificialStateWakeUpSpace").val(respData.artificialStateWakeUpSpace).select2();
            $("#unartificialStateWakeUpSpace").val(respData.unartificialStateWakeUpSpace).select2();
            $("#isAutoChangeMode").val(respData.isAutoChangeMode).select2();
            if (respData.monitorModeSampleSapce != undefined) {
                if (respData.monitorModeMinSampleSapce) {
                    monitorModeMinSampleSapce = respData.monitorModeMinSampleSapce;
                }
                var $monitorModeSampleSapce = $("#monitorModeSampleSapce");
                $monitorModeSampleSapce.parent().parent().show();
                $monitorModeSampleSapce.val(respData.monitorModeSampleSapce);
            } else {
                $("#monitorModeSampleSapce").parent().parent().hide();
            }
            $("#frequency").val(respData.frequency).select2();
            $("#sampleSpace").val(respData.sampleSpace);
            $("#commSpeed").val(respData.commSpeed);
            $("#commLoad").val(respData.commLoad);
            $("#sleepTime").val(respData.sleepTime).select2();
            $("#sampleStartTime").val(respData.sampleStartTime);

            var controlType;
            if (respData.extendInfo) {
                controlType = JSON.parse(respData.extendInfo).controlType;
            } else {
                controlType = 1;
            }
            $("#controlType").val(controlType ? controlType : 1).select2();

            //20230808 1840 新增降噪 UHF_IS 新增UHF独立参数配置
            //特殊处理传感器参数
            if (respData.aduType == "UHF_IS"){
                if(respData.thresholdMode == undefined){
                    respData.thresholdMode = 1;
                }
                if(respData.threshold == undefined){
                    respData.threshold = 0;
                }

                $(".ThresholdModeContent").removeClass("hide");
                $('#ThresholdMode').val(respData.thresholdMode).select2({minimumResultsForSearch: 60,language: "zh-CN"}).trigger('change');
                $('#Threshold').val(respData.threshold)
            }else{
                $(".ThresholdModeContent").removeClass("hide").addClass("hide");
            }

            //20250408 2.0.0版本强制隐藏ADUMode
            // $("#ADUMode").parent().parent().hide();

            //临时补丁-机械特性强制通讯链路类型为LoRa，待问题解决后开放
            /* if (respData.aduType === "MECH_IS") {
                $('#comm_link_type').val('LoRa').select2()
                $('#comm_link_type').attr('disabled', 'disabled')
            } else {
                $('#comm_link_type').removeAttr('disabled')
            } */
        });
    },
    getDevInfo: function (treeNode) {
        var data = "aduId=" + treeNode.id;
        var _this = this;
        poll('GET', 'GetADUInfo', data, function (text) {
            var respData = text.result;
            if (respData.length <= 0)
                return;

            _this.currentADUinfo = respData;
            $("#inst_type").val(respData.aduType).select2();
            $("#artificialStateStartTime").val(respData.artificialStateStartTime);
            $("#artificialStateEndTime").val(respData.artificialStateEndTime);
            $("#artificialStateWakeUpSpace").val(respData.artificialStateWakeUpSpace).select2();
            $("#unartificialStateWakeUpSpace").val(respData.unartificialStateWakeUpSpace).select2();
            $("#isAutoChangeMode").val(respData.isAutoChangeMode).select2();
            if (respData.monitorModeSampleSapce != undefined) {
                var $monitorModeSampleSapce = $("#monitorModeSampleSapce");
                $monitorModeSampleSapce.parent().parent().show();
                $monitorModeSampleSapce.val(respData.monitorModeSampleSapce);
            } else {
                $("#monitorModeSampleSapce").parent().parent().hide();
            }

            //20230808 1840 新增降噪 UHF_IS 新增UHF独立参数配置-初始化处理
            $(".ThresholdModeContent").removeClass("hide").addClass("hide");

            if (respData.aduType === "PD" ||
                respData.aduType === "PD_THREE" ||
                respData.aduType === "PD_FIVE" ||
                respData.aduType === "MEU" ||
                respData.aduType === "OLD_IS") {
                $("#numInGroup-g").addClass("hide");
                $("div.form-group.mec-set-non").addClass("hide");
                $("div.form-group.mec-set").addClass("hide");
            } else {
                $("#numInGroup-g").removeClass("hide");
                $("div.form-group.mec-set-non").removeClass("hide");
                $("div.form-group.mec-set").removeClass("hide");
                $("#numInGroup").val(respData.numInGroup);
            }
            $("#clearData_g").removeClass("hide");
            $("#clearData_g").addClass("hide");
            if (!respData.aduType != "PD" ||
                !respData.aduType != "HIKVIDEO_OUT" ||
                !respData.aduType != "PD_FIVE" ||
                !respData.aduType != "PD_THREE" ||
                !respData.aduType != "TEMPFC_OUT" ||
                !respData.aduType != "TEMPSB_OUT" ||
                !respData.aduType != "TEMP_HUM_JDRK_OUT" ||
                !respData.aduType != "SF6YN_OUT" ||
                !respData.aduType != "TEMPXP_OUT") {
                $("#clearData_g").removeClass("hide");
            }
            if (respData.aduType === "UHF_IS" ||
                respData.aduType === "HFCT_IS" ||
                respData.aduType === "TEVPRPS_IS" ||
                respData.aduType === "SF6_IS" ||
                respData.aduType === "PD_IS" ||
                respData.aduType === "TRANSFORMER_AE_IS" ||
                respData.aduType === "GIS_AE_IS" ||
                respData.aduType === "Arrester_I_IS" ||
                respData.aduType === "Arrester_U_IS" ||
                respData.aduType === "LeakageCurrent_IS" ||
                respData.aduType === "GrounddingCurrent_IS" ||
                respData.aduType === "Vibration_IS" ||
                respData.aduType === "ENV_IS" ||
                respData.aduType === "MECH_IS" ||
                respData.aduType === "CHANNEL_OPTICAL_TEMP" ||
                respData.aduType === "TEMP_HUM_IS" ||
                respData.aduType === "FLOOD_IS" ||
                respData.aduType === "FLOODWS_OUT" ||
                respData.aduType === "TEMPFC_OUT" ||
                respData.aduType === "TEMPXP_OUT" ||
                respData.aduType === "SF6YN_OUT" ||
                respData.aduType === "TEMPKY_OUT" ||
                respData.aduType === "TEMPSB_OUT" ||
                respData.aduType === "TEMP_HUM_JDRK_OUT" ||
                respData.aduType === "SMOKERK_OUT" ||
                respData.aduType === "GIS_LOWTEN"
            ) {
                $("div.form-group.mec-set").removeClass("hide");
                $("#frequency").val(respData.frequency).select2();
                $("#sampleSpace").val(respData.sampleSpace);
                $("#numInGroup").val(respData.numInGroup);
                $("#commSpeed").val(respData.commSpeed);
                $("#commLoad").val(respData.commLoad);
                $("#sleepTime").val(respData.sleepTime).select2();

                $("#sampleStartTime").val(respData.sampleStartTime);
                if (respData.aduType != "MECH_IS") {
                    $("div.form-group.mec-set-non").removeClass("hide");
                } else {
                    $("div.form-group.mec-set-non").removeClass("hide").addClass("hide");
                }
            } else {
                $("div.form-group.mec-set").addClass("hide");
                $("#numInGroup-g").removeClass("hide");
            }

            //20230808 1840 新增降噪 UHF_IS 新增UHF独立参数配置-参数展示
            checkInstrumentType(respData.aduType);

            //20230808 1840 新增降噪 UHF_IS 新增UHF独立参数配置-参数展示
            if (respData.aduType == "UHF_IS") {
                if(respData.thresholdMode == undefined){
                    respData.thresholdMode = 1;
                }
                if(respData.threshold == undefined){
                    respData.threshold = 0;
                }
                $(".ThresholdModeContent").removeClass("hide");
                $('#ThresholdMode').val(respData.thresholdMode).select2({minimumResultsForSearch: 60,language: "zh-CN"}).trigger('change');
                $('#Threshold').val(respData.threshold)
            } else {
                $(".ThresholdModeContent").removeClass("hide").addClass("hide");
            }

            var controlType;
            if (respData.extendInfo) {

                controlType = JSON.parse(respData.extendInfo).controlType;
            } else {
                controlType = 1;
            }
            $("#controlType").val(controlType ? controlType : 1).select2();

            //ADUMode为新开发功能，旧版无此配置,需在检查显隐关系之后再去触发工作模式以正确显示工作模式相关参数
            if (respData.ADUMode != undefined) {
                var $ADUMode = $("#ADUMode");
                $ADUMode.parent().parent().show();
                $(".ADUModeContent").show();
                $ADUMode.val(respData.ADUMode).select2();
                // $ADUMode.val('normalMode').select2();//暂定所有传感器均为维护模式并隐藏显示
                $ADUMode.trigger('change');

                // $ADUMode.parent().parent().hide();
                if ((respData.aduType === "TEMP_HUM_IS"
                || respData.aduType === "SF6_IS" 
                || respData.aduType === "FLOOD_IS" 
                || respData.aduType === "FLOODWS_OUT") 
                && respData.ADUMode === "lowPowerMode") {
                    $('#lowPowerModeParams').hide();
                    $("#sleepTime-g").hide();
                }
            } else {
                $("#ADUMode").parent().parent().hide();
                $(".ADUModeContent").hide();
                $("#sleepTime-g").hide();
                // $('#wakeUpInstrument').parent().remove();
            }
            if (treeNode.getParentNode().id === 'TEMP_HUM_IS'
            || treeNode.getParentNode().id === "SF6_IS" 
            || treeNode.getParentNode().id === 'FLOOD_IS' 
            || treeNode.getParentNode().id === 'FLOODWS_OUT') {
                $("#forceChange").parent().show();
                $("#wakeUpInstrument").parent().hide();
                $("#sleepTime-g").hide();
            }
            $("#inst_code_old").val(treeNode.id);
            $("#inst_code").val(treeNode.id);
            $("#inst_type").val(respData.aduType).select2();
            $('#inst_name').val(respData.aduName);
            $('#inst_work_mode').val(respData.workMode).select2();
            $('#inst_work_group').val(respData.workGroup);
            $('#warnTemp').val(respData.warnTemp);
            $('#inst_task_group').val(respData.taskGroup);
            $("#sampleSpace").val(respData.sampleSpace);
            $("#comm_link_type").val(respData.commType).select2();
            $("#comm_link_port").val(respData.commPort).select2();
            $("#uploadInterval").val(respData.uploadInterval);
            if (treeNode.getParentNode().id == 'HIKVIDEO_OUT') {
                $("#cameraType").val(JSON.parse(respData.extendInfo).cameraType).select2();
                $("#enterKey").val(JSON.parse(respData.extendInfo).enterKey);
            }
            if (treeNode.getParentNode().id === "TEMPSB_OUT" ||
                treeNode.getParentNode().id === "TEMP_HUM_JDRK_OUT") {
                $("#continuSampTime").val(JSON.parse(respData.extendInfo).continuSampTime);
            }

            //20250408 2.0.0版本强制隐藏ADUMode
            // $("#ADUMode").parent().parent().hide();

            //临时补丁-机械特性强制通讯链路类型为LoRa，待问题解决后开放
            /*  if (respData.aduType === "MECH_IS") {
                 $('#comm_link_type').val('LoRa').select2()
                 $('#comm_link_type').attr('disabled', 'disabled')
             } else {
                 $('#comm_link_type').removeAttr('disabled')
             } */
        });
    },
    getChannelInfo: function (treeNode) {
        var data = "aduId=" + treeNode.pId + "&channelIndex=" + treeNode.channelIndex;
        poll('GET', 'GetChannelInfo', data, function (text) {
            var respData = text.result;
            if (respData.length <= 0)
                return;
            $("#sample_point_g").addClass("hide");
            $("#samplePeriod_g").addClass("hide");
            $("#sf6_g").addClass("hide");
            $("div.tev").addClass("hide");
            $("#triggerThreshold_g").addClass("hide");
            $("#openTime_g").addClass("hide");
            $("#closeTime_g").addClass("hide");

            if (respData.channelType === "UHF") {
                $("#wave_filter_g").removeClass("hide");
                $("#gain_g").removeClass("hide");
                $("#gain_mode_g").removeClass("hide");
                $("#sample_point_g").removeClass("hide");
                $("#samplePeriod_g").removeClass("hide");
                $("#samplePeriod").removeAttr("disabled");

                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
            } else if (respData.channelType === "HFCT") {
                $("#gain_g").removeClass("hide");
                $("#gain_mode_g").removeClass("hide");
                $("#sample_point_g").removeClass("hide");
                $("#samplePeriod_g").removeClass("hide");
                $("#samplePeriod").removeAttr("disabled");

                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#sptr_g").addClass("hide");
            } else if (respData.channelType === "TEVPRPS") {
                $("#gain_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#sample_point_g").removeClass("hide");
                $("#samplePeriod_g").removeClass("hide");
                $("#samplePeriod").removeAttr("disabled");
            } else if (respData.channelType === "AE") {
                $("#gain_g").removeClass("hide");
                $("#gain_mode_g").removeClass("hide");
                if (
                  treeNode.getParentNode().pId === 'PD_IS' ||
                  treeNode.getParentNode().pId === 'TRANSFORMER_AE_IS' ||
                  treeNode.getParentNode().pId === 'GIS_AE_IS'
                ) {
                  $('#triggerThreshold_g').removeClass('hide');
                  $('#openTime_g').removeClass('hide');
                  $('#closeTime_g').removeClass('hide');
                }
                // $("#sam_cycle_g").removeClass("hide");
                // $("#sam_point_g").removeClass("hide");

                $("#wave_filter_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#sptr_g").addClass("hide");
            } else if (respData.channelType === "Vibration") {
                $("#sam_cycle_g").removeClass("hide");
                $("#sam_rate_g").removeClass("hide");

                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sptr_g").addClass("hide");
            } else if (respData.channelType === "ArresterU") {
                $("#u_ratio_g").removeClass("hide");
                $("#u_phase_g").removeClass("hide");

                $("#wave_filter_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
            } else if (respData.channelType === "ArresterI") {
                $("#u_phase_g").removeClass("hide");

                $("#u_ratio_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
            } else if (respData.channelType === "GrounddingCurrent" || respData.channelType === "LeakageCurrent") {
                $("#u_phase_g").removeClass("hide");

                $("#u_ratio_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
            } else if (respData.channelType === "SPTR") {
                $("#sptr_g").removeClass("hide");

                $("#u_ratio_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
            } else {
                $("div.tev").removeClass("hide");
                $("#wave_filter_g").addClass("hide");
                $("#gain_g").addClass("hide");
                $("#gain_mode_g").addClass("hide");
                $("#sam_cycle_g").addClass("hide");
                $("#sam_point_g").addClass("hide");
                $("#sam_rate_g").addClass("hide");
                $("#u_phase_g").addClass("hide");
                $("#u_ratio_g").addClass("hide");
                $("#sptr_g").addClass("hide");
                $("#triggerThreshold_g").addClass("hide");
                $("#openTime_g").addClass("hide");
                $("#closeTime_g").addClass("hide");

                if (treeNode.getParentNode().pId === "TEMP_HUM_IS" &&
                    (respData.channelType === "TEMP" || respData.channelType === "Humidity")) {
                    function addUnit(type) {
                        var unit = type === "TEMP" ? "[℃]" : "[%]";
                        var lowerThresholdDom = $("[data-i18n='lowerThreshold']");
                        var upThresholdDom = $("[data-i18n='upThreshold']");
                        var changeThresholdDom = $("[data-i18n='changeThreshold']");
                        lowerThresholdDom.text(getI18nName('lowerThreshold') + unit);
                        upThresholdDom.text(getI18nName('upThreshold') + unit);
                        changeThresholdDom.text(getI18nName('changeThreshold') + unit);
                    }

                    addUnit(respData.channelType);
                    $(".temp-hum-sensor-g").show();
                    $('#upThreshold').val(respData.upThreshold);
                    $('#lowerThreshold').val(respData.lowerThreshold);
                    $('#changeThreshold').val(respData.changeThreshold);
                }

                if (respData.channelType === "SF6") {
                    $("#sf6_g").removeClass("hide");
                    var pcType=respData.PhysicalChannelType;
                    if(pcType!=0&&pcType!=1){
                        pcType=-999;
                    }
                    $("#PhysicalChannelType").val(pcType).select2();
                    $("#OverPressureThreshold").val(respData.OverPressureThreshold);
                    $("#LowPressureThreshold").val(respData.LowPressureThreshold);
                    $("#ShutThreshold").val(respData.ShutThreshold);
                    $("#VolumeRatio").val(respData.VolumeRatio);

                    // $("#PhysicalChannelType").val(1).select2();
                    // $("#OverPressureThreshold").val(0.5);
                    // $("#LowPressureThreshold").val(0.8);
                    // $("#ShutThreshold").val(0);
                }

                if (respData.channelType === "TEV") {
                    $("div.tev").removeClass("hide");
                    $("#back_ground_data").val(respData.backGroundData);
                } else {
                    $("div.tev").addClass("hide");
                }
                if (respData.channelType === "MEU") {
                    $('#mec_switch_state').val(0).select2();
                    $('#mec_breaker_type').val(1).select2();
                    $('#mec_motor_function_type').val(1).select2();
                    $("div.form-group.mec-sensor").removeClass("hide");
                } else {
                    $("div.form-group.mec-sensor").addClass("hide");
                }
            }

            $("#channel_type").val(getI18nName(respData.channelType));
            $("#channel_name").val(respData.channelName);
            $("#sam_rate").val(respData.sampleRate);
            // addSelectList("wave_filter", getFilter(respData.filterList));
            if (!$("#wave_filter_g").hasClass("hide")) {
                addSelectList("wave_filter", respData.filterList);
                $("#wave_filter").val(respData.filter).select2();
            }

            if (!$("#gain_g").hasClass("hide")) {
                addSelectList("gain", respData.gainList);
                addSelectList("gain_mode", respData.gainModelList);
                $("#gain").removeAttr("disabled");
                $("#gain").val(respData.gain).select2();
                $("#gain_mode").val(respData.gainModel).select2();
                if(respData.channelType === "HFCT"){
                  /* 
                  * 新HFCT传感器通道gain保存需求： by 江峰 20240625
                  * 1. 这个HFCT通道参数设置有一个需求：有一个新的HFCT传感器，没有增益档位，增益是不支持用户配置的
                  * 2. 现在我想返回gainListl时只有0dB，gain也是0dB时，你前端增益这个下拉框给disable
                  * 3. 保存通道参数时，如果gainList只有0dB，gain值这个下发给我-1
                  */
                  if(respData.gainList.length == 1 && respData.gainList[0] === 0 && respData.gain == 0){
                    $("#gain").attr("disabled", 'disabled');
                  }
                }
                $("#gain_g").removeClass("hide");
                if (respData.gainModel == 1) {
                    $("#gain_g").addClass("hide");
                }

                if(respData.channelType === "AE"){
                  $("#gain_g").addClass("hide");
                }
            }

            if (!$("#sam_cycle_g").hasClass("hide")) {
                addSelectList("sam_cycle", respData.samplePeriodList);
                $("#sam_cycle").val(respData.samplePeriod).select2();
                $("#sam_point").val(respData.samplePoint);
            }

            if (!$("#u_phase_g").hasClass("hide")) {
                $("#u_phase").val(respData.channelPhase).select2();
            }
            if (!$("#u_ratio_g").hasClass("hide")) {
                $("#u_ratio").val(respData.ratio);
            }
            if (!$("#sptr_g").hasClass("hide")) {
                $("#fWrnThreshold").val(respData.fWrnThreshold);
                $("#fAlmThreshold").val(respData.fAlmThreshold);
            }
            if (!$("#sample_point_g").hasClass("hide")) {
                if (respData.samplePointsList && respData.samplePointsList.length > 0) {
                    addSelectList("sample_point", respData.samplePointsList);
                }
                $("#sample_point").val(respData.samplePoint).select2();
            }
            if (!$("#samplePeriod_g").hasClass("hide")) {
                if (respData.samplePeriodList && respData.samplePeriodList.length > 0) {
                    addSelectList("samplePeriod", respData.samplePeriodList);
                }
                $("#samplePeriod").val(respData.samplePeriod).select2();
            }
            if (!$("div.form-group.mec-sensor").hasClass("hide")) {
                $('#mec_loop_current_thred').val(respData.loopCurrentThred);
                $('#mec_motor_current_thred').val(respData.motorCurrentThred);
                $('#mec_switch_state').val(respData.switchState).select2();
                $('#mec_breaker_type').val(respData.breakerType).select2();
                $('#mec_motor_function_type').val(respData.motorFunctionType).select2();
            }
            //2.0.0 三合一传感器 AE新增触发幅值、开门时间、关门时间 模拟数据
            // respData.triggerThresholdList = [0,1,10];
            // respData.openTimeList = [100,200,300];
            // respData.closeTimeList = [2000,3000,4000];
            // respData.triggerThreshold = 1;
            // respData.openTime = 200;
            // respData.closeTime = 3000;
            if (!$("#triggerThreshold_g").hasClass("hide")) {
              addSelectList("triggerThreshold", respData.triggerThresholdList);
              // $("#triggerThreshold").val(respData.triggerThreshold).select2();
              $("#triggerThreshold").val(respData.triggerThreshold);
            }
            if (!$("#openTime_g").hasClass("hide")) {
              addSelectList("openTime", respData.openTimeList);
              $("#openTime").val(respData.openTime).select2();
            }
            if (!$("#closeTime_g").hasClass("hide")) {
              addSelectList("closeTime", respData.closeTimeList);
              $("#closeTime").val(respData.closeTime).select2();
            }
        });
    }
}

function getDevInfo(event, treeId, treeNode) {
    $(".temp-hum-sensor-g").hide();
    $("#forceChange").parent().hide();
    switch (treeNode.level) {
        case DevInfoController.treeLevel.root: //获取前端配置基础参数
            $(".content-footer").hide();
            $('#save_inst').hide();
            $("#inst_type").attr("disabled", true);
            $("#devInfo").removeClass("hide");
            $("#chnInfo").addClass("hide");
            $("#applyAllSensor").parent().show();
            $("#collectOnce").html(getI18nName('collectOnce'));
            $("#ADUMode").parent().parent().hide();
            $(".ADUModeContent").hide();
            $("#wakeUpInstrument").html(getI18nName('wakeUpInstrumentOnce'));
            $("div.form-group.mec-sensor").removeClass("hide").addClass("hide");
            $("#inst_code").parent().parent().removeClass("hide").addClass("hide");
            $("#inst_name").parent().parent().removeClass("hide").addClass("hide");
            $("#del_inst").parent().removeClass("hide").addClass("hide");
            DevInfoController.getADUConfig(treeNode);
            break;
        case DevInfoController.treeLevel.devInfo: //获取当前dev信息;
            $(".content-footer").show();
            $('#save_inst').show();
            $("#applyAllSensor").parent().hide();
            $("#collectOnce").html(getI18nName('collect'));
            $("#ADUMode").parent().parent().hide();
            $("#wakeUpInstrument").html(getI18nName('wakeUpInstrument'));
            $("#inst_type").attr("disabled", false);
            $("#devInfo").removeClass("hide");
            $("#chnInfo").addClass("hide");
            $("div.form-group.mec-sensor").removeClass("hide").addClass("hide");
            $("#del_inst").parent().removeClass("hide");
            DevInfoController.getDevInfo(treeNode);
            break;
        case DevInfoController.treeLevel.channelInfor: //获取通道信息
            $(".content-footer").show();
            $("#chnInfo").removeClass("hide");
            $("#devInfo").addClass("hide");
            $("#channel_index").val(treeNode.channelIndex);
            $("div.form-group.mec-sensor").removeClass("hide").addClass("hide");
            DevInfoController.getChannelInfo(treeNode);
            break;
    }
}


//前端类型分组
var instrumentTypeGroup = {
    "PD": 1,
    "PD_THREE": 1,
    "PD_FIVE": 1,
    "OLD_IS": 2,
    "MEU": 2,
    "UHF_IS": 3,
    "HFCT_IS": 3,
    "TEVPRPS_IS": 3,
    "SF6_IS": 3,
    "PD_IS": 3,
    "TRANSFORMER_AE_IS": 3,
    "GIS_AE_IS": 3,
    "ENV_IS": 3,
    "Arrester_U_IS": 3,
    "Arrester_I_IS": 3,
    "LeakageCurrent_IS": 3,
    "GrounddingCurrent_IS": 3,
    "Vibration_IS": 3,
    "MECH_PD_TEMP": 3,
    "TEMP_HUM_IS": 3,
    "FLOOD_IS": 3,
    "FLOODWS_OUT": 3,
    "CHANNEL_OPTICAL_TEMP": 3,
    "SPTR_IS": 4,
    "MECH_IS": 4,
    "VaisalaDTP145": 5,
    "Wika_GDT20": 5,
    "Wika_GDHT20": 5,
    "SHQiuqi_SC75D_SF6": 5,
    "TEMPFC_OUT": 6,
    "TEMPXP_OUT": 6,
    "SF6YN_OUT": 6,
    "TEMPKY_OUT": 6,
    "TEMPSB_OUT": 6,
    "TEMP_HUM_JDRK_OUT": 6,
    "SMOKERK_OUT": 6,
    "HIKVIDEO_OUT": 6,
    "VibrationSY_OUT": 6,
    "GIS_LOWTEN": 10,
};

function checkInstrumentType(type) {
    $("div.form-group.g1").removeClass("hide").addClass("hide");
    $("div.form-group.g2").removeClass("hide").addClass("hide");
    $("div.form-group.g3").removeClass("hide").addClass("hide");
    $("div.form-group.g4").removeClass("hide").addClass("hide");
    $("div.form-group.g5").removeClass("hide").addClass("hide");
    $("div.form-group.g6").removeClass("hide").addClass("hide");
    $("div.form-group.g10").removeClass("hide").addClass("hide");
    $("p.form-group.g10").removeClass("hide").addClass("hide");

    $('#slaveid-g').hide();
    $('#expandParams').hide();
    $('#expandParams').children().map(function (child) {
        $(child).hide();
    });

    $('#inst_code').attr('placeholder', '');

    if (sensorConfigControlUtil.checkIsOut(type)) {
        $('#expandParams').show()
        sensorConfigControlUtil.initSensorParamView(type);
        checkDefValue(type);
        return;
    }
    switch (instrumentTypeGroup[type]) {
        case 1:
            $("div.form-group.g1").removeClass("hide");
            break;
        case 2:
            $("div.form-group.g2").removeClass("hide");
            break;
        case 3:
            $("div.form-group.g3").removeClass("hide");
            $("#sleepTime-g").hide();
            $(".ADUModeContent").show();
            $("#ADUMode").parent().parent().show();
            if (type === "TEMP_HUM_IS") {
                $("#sleepTime-g").hide()
            } else {
                $("#sleepTime-g").show()
            }
            $('#ADUMode').trigger('change');
            break;
        case 4:
            $("div.form-group.g4").removeClass("hide");
            break;
        case 5:
            $("div.form-group.g5").removeClass("hide");
            break;
        case 6:
            $("div.form-group.g6").removeClass("hide");
            if (type === "HIKVIDEO_OUT") {
                $('#expandParams').show()
                $('#comm_link_type').parent().parent().removeClass("hide").addClass("hide");
            } else {
                $('#expandParams').hide()
                $('#comm_link_type').parent().parent().removeClass("hide");
            }
            break;
        case 10:
            $("div.form-group.g10").removeClass("hide");
            $("p.form-group.g10").removeClass("hide");
            break;
    }
    checkSpecial(type);
    checkDefValue(type);
    if (checkDMWsenorType(type)) {
        $("#clearData").parent().removeClass("hide").addClass("hide");
    } else {
        $("#clearData").parent().removeClass("hide");
    }
    // $("#sleepTime-g").hide();
    //20250408 2.0.0版本强制隐藏ADUMode
    // $("#ADUMode").parent().parent().hide();
}

function checkDefValue(type) {
    if (sensorConfigControlUtil.checkIsOut(type)) {
        sensorConfigControlUtil.setDefaultValue(type);
        return;
    }
    switch (type) {
        case 'TEMPFC_OUT':
        case 'TEMPXP_OUT':
        case 'TEMPKY_OUT':
        case 'SMOKERK_OUT':
        case 'SF6YN_OUT':
        case 'VibrationSY_OUT':
            $('#comm_link_type').val('Modbus485').select2();
            $('#sampleSpace').val('6');
            break;
        case 'TEMPSB_OUT':
        case 'TEMP_HUM_JDRK_OUT':
            $('#sampleSpace').val('30');
            $('#continuSampTime').val('2');
            break;
        default:
            if (DevInfoController.currentADUinfo && type == DevInfoController.currentADUinfo.aduType) {
                $('#sampleSpace').val(DevInfoController.currentADUinfo.sampleSpace);
            } else {
                $('#sampleSpace').val('1440');
            }
            break;
    }
}

function checkSpecial(type) {
    $('#intervalMinusUnit').html(getI18nName('min'));
    $("#clearData").parent().show();
    $("#continuSampTime-g").hide();
    $('#ADUMode').removeAttr('disabled');
    $("#collectOnce").parent().removeClass("hide")

    switch (type) {
        case 'warnTemp':
            $("#warnTemp").parent().parent().removeClass("hide");
            break;
        case 'Arrester_I_IS':
        case 'LeakageCurrent_IS':
        case 'GrounddingCurrent_IS':
            $('#ADUMode').val('normalMode').select2();
            $('#ADUMode').trigger('change');
            $('#ADUMode').attr('disabled', 'disabled');
            $("#warnTemp").parent().parent().removeClass("hide").addClass("hide");
            $("#uploadInterval-g").removeClass("hide").addClass("hide");
            $("#forceChange").parent().hide();
            $("#lowPowerModeParams").show();
            if (type != 'GrounddingCurrent_IS') {
                $("#wakeUpInstrument").parent().show();
            }
            break;
        case 'HIKVIDEO_OUT':
            $("#clearData").parent().hide();
            break;
        case 'TEMPSB_OUT':
        case 'TEMP_HUM_JDRK_OUT':
            $("#continuSampTime-g").show();
            break;
        case 'TRANSFORMER_AE_IS':
        case 'GIS_AE_IS':
            $("#warnTemp").parent().parent().removeClass("hide").addClass("hide");
            $("#uploadInterval-g").removeClass("hide").addClass("hide");
            $("#forceChange").parent().hide();
            $("#lowPowerModeParams").show();
            $("#wakeUpInstrument").parent().hide();
            break;
        case 'SPTR_IS':
            $("#warnTemp").parent().parent().removeClass("hide").addClass("hide");
            $("#inst_work_group").parent().parent().addClass("hide");
            $("#sampleSpace-g").removeClass("hide");
            $("#forceChange").parent().hide();
            $('#intervalMinusUnit').html(getI18nName('second'));
            $('#sampleSpace').val(30);
            break;
        // case 'PD_IS':
        //     break;
        case 'TEMP_HUM_IS':
        case 'SF6_IS':
        case 'FLOOD_IS':
        case 'FLOODWS_OUT':
            $("#warnTemp").parent().parent().removeClass("hide").addClass("hide");
            $("#uploadInterval-g").removeClass("hide");
            $("#inst_work_mode").parent().parent().removeClass("hide").addClass("hide");
            $("#frequency-g").removeClass("hide").addClass("hide");
            $("#sampleSpace-g").removeClass("hide").addClass("hide");
            $("#sampleStartTime-g").removeClass("hide").addClass("hide");
            $("#collectOnce").parent().removeClass("hide").addClass("hide");
            var treeObj = $.fn.zTree.getZTreeObj("dev_tree");
            if (treeObj) {
                var selectNodes = treeObj.getSelectedNodes();
                if (selectNodes[0] && selectNodes[0].level == 2) {
                    $("#forceChange").parent().show();
                } else {
                    $("#forceChange").parent().hide();
                }
            }
            $("#lowPowerModeParams").hide();
            $("#wakeUpInstrument").parent().hide();
            break;
        default:
            $("#warnTemp").parent().parent().removeClass("hide").addClass("hide");
            $("#uploadInterval-g").removeClass("hide").addClass("hide");
            $("#forceChange").parent().hide();
            $("#lowPowerModeParams").show();
            $("#wakeUpInstrument").parent().show();
            break;
    }
    //20230808 1840 新增降噪 UHF_IS 新增UHF独立参数配置-参数展示
    if ($('#inst_type').val() == "UHF_IS") {
        $(".ThresholdModeContent").removeClass("hide");
    } else {
        $(".ThresholdModeContent").removeClass("hide").addClass("hide");
    }
}

//添加列表
function addSelectList(item, arrayList) {
    // if(checkIsEmpty(arrayList)) return;
    $("#" + item).empty();
    var data = arrayList;
    if (data.length > 0) {
        data.forEach(function (value, index) {
            var valtemp = value;
            if (item == 'wave_filter') {
                valtemp = getFilter(value);
            }
            var option = $("<option />", {
                "value": "" + value + ""
            }).append(valtemp);
            $("#" + item).append(option);
        });
    }
}


function saveInstInfo(formData, typeCont) {
    poll('POST', 'SaveADUInfo', formData, function (text) {
        var textshow = getI18nName('operationSuccess');
        if (typeCont === "add") {
            textshow = getI18nName('sensorAddSuccess');
        } else {
            textshow = getI18nName('sensorEditBegin');
        }
        GetAduCodeList();

        $('.notifications').notify({
            message: {
                text: textshow
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))

            },
            type: "danger",
        }).show();
    });
}

function delInstInfo(formData) {
    poll('POST', 'DeleteADUInfo', formData, function (text) {
        var textshow = getI18nName('sensorDelSuccess');
        GetAduCodeList();

        $('.notifications').notify({
            message: {
                text: textshow
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))
            },
            type: "danger",
        }).show();
    });
}

function saveChannelInfo(formData) {
    poll('POST', 'SaveChannelInfo', formData, function (text) {
        var textshow = getI18nName('channelInfoSaveSuccess');

        // GetAduCodeList();

        $('.notifications').notify({
            message: {
                text: textshow
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))
            },
            type: "danger",
        }).show();
    });
}

function applySensorConfig(formData) {
    poll('POST', 'ApplySensorConfig', formData, function (text) {
        var textshow = getI18nName('channelInfoSaveBegin');

        // GetAduCodeList();

        $('.notifications').notify({
            message: {
                text: textshow
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))
            },
            type: "danger"
        }).show();
    });
}

// 20230808 1840 新增降噪 UHF_IS 特高频智能传感器配置 保存降噪配置
function saveADUthresholdMode(params){
    poll('POST', 'SaveADUthresholdMode', params, function (text) {
        $('.notifications').notify({
            message: {
                text: "降噪模式配置完成"
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: "降噪模式配置失败"
            },
            type: "danger"
        }).show();
    });
}