<style>
    .content {
        padding: 0;
    }

    .nav-tabs-custom {
        background: transparent;
    }

    .modbus-content-card {
        padding: 1px 0 10px 0;
        background: #fff;
        border-radius: 3px;
    }

    .modbus-title {
        margin: 15px 0px;
        border-bottom: 1px solid #ddd;
        padding: 0 15px 15px 15px;
    }

    .form-group.has-success .form-control,
    .form-group.has-success .input-group-addon {
        border-color: #d2d6de;
    }

    .form-group.has-success label {
        color: #333;
    }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height" id="modbus" style="box-sizing:border-box">
        <br>
        <form id="submitForm" class="form-horizontal" style="height: 100%">
            <div id="modbusConfig1" class="col-md-12 col-lg-6">
                <div class="modbus-content-card">
                    <div>
                        <h3 class="modbus-title" data-i18n="config1">配置 1</h3>
                    </div>
                    <div class="form-group">
                        <label for="serialPort" class="col-sm-3 control-label" data-i18n="serialPort">串口端口</label>
                        <div class="col-sm-8">
                            <input name="serialPort" class="form-control select2" data-i18n-placeholder="selectPlease"
                                style="width: 100%;" value="/dev/ttyO2" disabled="disabled" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="stand_mobus" class="col-sm-3 control-label" data-i18n="standMobus">传感器厂家</label>
                        <div class="col-sm-8">
                            <select name="stand_modbus" class="form-control" style="width: 100%;"
                                onchange="onStandModbusChange(this)">
                                <option value="0" selected="selected" data-i18n="modbusCompanySelf">华乘</option>
                                <option value="1" data-i18n="Other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="baudRate" class="col-sm-3 control-label" data-i18n="baudRate">波特率</label>
                        <div class="col-sm-8">
                            <select name="baudRate" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="dataBit" class="col-sm-3 control-label" data-i18n="dataBit">数据位</label>
                        <div class="col-sm-8">
                            <select name="dataBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="stopBit" class="col-sm-3 control-label" data-i18n="stopBit">停止位</label>
                        <div class="col-sm-8">
                            <select name="stopBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="checkBit" class="col-sm-3 control-label" data-i18n="checkBit">校验位</label>
                        <div class="col-sm-8">
                            <select name="checkBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="RTS" class="col-sm-3 control-label">RTS</label>
                        <div class="col-sm-8">
                            <select name="RTS" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="modbusAddress" class="col-sm-3 control-label"
                            data-i18n="modbusAddress">ModBus地址</label>
                        <div class="col-sm-8">
                            <input type="number" name="modbusAddress" class="form-control" style="width: 100%;" />
                        </div>
                    </div>
                </div>
            </div>
            <div id="modbusConfig2" class="col-md-12 col-lg-6">
                <div class="modbus-content-card">
                    <div>
                        <h3 class="modbus-title" data-i18n="config2">配置 2</h3>
                    </div>
                    <div class="form-group">
                        <label for="serialPort" class="col-sm-3 control-label" data-i18n="serialPort">串口端口</label>
                        <div class="col-sm-8">
                            <input name="serialPort" class="form-control select2" data-i18n-placeholder="selectPlease"
                                style="width: 100%;" value="/dev/ttyO4" disabled="disabled" /></div>
                    </div>
                    <div class="form-group">
                        <label for="stand_mobus" class="col-sm-3 control-label" data-i18n="standMobus">传感器厂家</label>
                        <div class="col-sm-8">
                            <select name="stand_modbus" class="form-control" style="width: 100%;"
                                onchange="onStandModbusChange(this)">
                                <option value="0" data-i18n="modbusCompanySelf">华乘</option>
                                <option value="1" selected="selected" data-i18n="Other">其他</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="baudRate" class="col-sm-3 control-label" data-i18n="baudRate">波特率</label>
                        <div class="col-sm-8">
                            <select name="baudRate" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="dataBit" class="col-sm-3 control-label" data-i18n="dataBit">数据位</label>
                        <div class="col-sm-8">
                            <select name="dataBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="stopBit" class="col-sm-3 control-label" data-i18n="stopBit">停止位</label>
                        <div class="col-sm-8">
                            <select name="stopBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="checkBit" class="col-sm-3 control-label" data-i18n="checkBit">校验位</label>
                        <div class="col-sm-8">
                            <select name="checkBit" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="RTS" class="col-sm-3 control-label">RTS</label>
                        <div class="col-sm-8">
                            <select name="RTS" class="form-control select2" style="width: 100%;"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="modbusAddress" class="col-sm-3 control-label"
                            data-i18n="modbusAddress">ModBus地址</label>
                        <div class="col-sm-8 ">
                            <input type="number" name="modbusAddress" class="form-control" style="width: 100%;" />
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="content-footer">
            <button type="button" id="modbus-save" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 60px 10px auto" data-i18n="save">保存
            </button>
        </div>
        <br />
    </div>
    <!-- /.box -->
</div>

<!-- /.row -->
<script>
    //@ sourceURL=modbus_set.js
    //获取ModBus参数信息
    initPageI18n();
    getModbusConfig();
    checkNeedKeyboard();


    //保存ModBus信息
    $("#modbus-save").click(function () {
        if (validateSubmitForm()) {
            saveModbusInfo('modbusConfig1');
            saveModbusInfo('modbusConfig2');
        }
    });
    var $submitForm = $('#submitForm')

    function validateSubmitForm() {
        var validater = $submitForm.data('bootstrapValidator');
        validater.validate();
        return validater.isValid();
    }
    $submitForm.bootstrapValidator({
        message: '',
        feedbackIcons: {
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            modbusAddress: {
                validators: {
                    regexp: {
                        regexp: /(.*)/,
                    },
                    callback: {
                        message: getI18nName('modbusAddressCheckTip'),
                        callback: function (value, validator) {
                            if (value.trim() === '' || typeof parseInt(value) !== 'number' || parseInt(
                                    value) < 0 || parseInt(value) > 254) {
                                return false;
                            } else {
                                return true;
                            }
                        }
                    }
                }
            },
        }
    });
</script>