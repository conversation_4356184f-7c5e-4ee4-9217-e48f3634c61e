<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <br>
        <form class="form-horizontal" id="MonitorType-d">
            <div class="form-group">
                <label for="MonitorType" class="col-sm-3 control-label" data-i18n="monitorType">主机类型</label>
                <div class="col-sm-8">
                    <!--<input id="MonitorType" vk="true" type="text" class="col-sm-4 form-control"-->
                    <!--data-i18n-placeholder="pleaseInput"-->
                    <!--disabled="disabled"-->
                    <!--value="">-->
                    <select id="MonitorType" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;">
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="MonitorName" class="col-sm-3 control-label" data-i18n="monitorName">主机名称</label>
                <div class="col-sm-8">
                    <input id="MonitorName" vk="true" type="text" class="col-sm-4 form-control"
                        data-i18n-placeholder="pleaseInput" value="">
                </div>
            </div>
            <!-- <div class="form-group hide" id="LoraFrequency-d"> -->
            <div class="form-group" id="LoraFrequency-d" style="display: none !important;">
                <label for="LoraFrequency" class="col-sm-3 control-label" data-i18n="LoraFrequency">主机区域</label>
                <div class="col-sm-8">
                    <select id="LoraFrequency" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;">
                    </select>
                </div>
            </div>
            <div class="form-group hide" id="monitor_work_group-d">
                <label for="monitor_work_group" class="col-sm-3 control-label"
                    data-i18n="deviceWorkGroup">主机工作组号</label>
                <div class="col-sm-8">
                    <input id="monitor_work_group" vk="true" type="number" class="col-sm-4 form-control"
                        data-i18n-placeholder="pleaseInput" value="1">
                </div>
            </div>
            <div class="form-group">
                <label for="frequency" class="col-sm-3 control-label" data-i18n="frequency">电网频率</label>
                <div class="col-sm-8">
                    <select id="frequency" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;"> </select>
                </div>
            </div>
            <div class="form-group" style="display:block;" id="pdSampleInterval-d">
                <label for="pdSampleInterval" class="col-sm-3 control-label"
                    data-i18n="pdSampleInterval">PD-采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="pdSampleInterval-minus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="pdSampleInterval" vk="true" type="number" class="col-sm-4 form-control" value="600">
                        <span class="input-group-btn">
                            <button id="pdSampleInterval-plus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="spaceSecond">&nbsp秒</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group" id="aduOfflineThreshold-d">
              <label for="aduOfflineThreshold" class="col-sm-3 control-label" data-i18n="SENSOR_TIMEOUT_COUNT">传感器离线判定次数</label>
              <div class="col-sm-8">
                  <select id="aduOfflineThreshold" class="form-control select2" data-i18n-placeholder="selectPlease"
                      style="width: 100%;">
                      <option value='1'>1</option>
                      <option value='2'>2</option>
                      <option value='3'>3</option>
                      <option value='4'>4</option>
                      <option value='5'>5</option>
                      <option value='6'>6</option>
                      <option value='7'>7</option>
                      <option value='8'>8</option>
                      <option value='9'>9</option>
                      <option value='10'>10</option>
                  </select>
              </div>
            </div>
            <div class="form-group" id="auxRtuID-d">
              <label class="col-sm-3 control-label" data-i18n="MainStationAuxRtuID">RtuID</label>
              <div class="col-sm-8">
                <input id="auxRtuID" class="col-sm-4 form-control" type="number" autocomplete="off"
                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                    style="ime-mode:Disabled" value="999"
                    onkeyup="this.value=this.value === '' ? 0 :parseInt(this.value)>100000 ? 100000: this.value.match(/\d+(\.\d{0,0})?/) ? this.value.match(/\d+(\.\d{0,0})?/)[0] : ''">
              </div>
            </div>
            <div class="form-group" id="iedRtuID-d">
              <label class="col-sm-3 control-label" data-i18n="MainStationIedRtuID">汇集RtuID</label>
              <div class="col-sm-8">
                <input id="iedRtuID" class="col-sm-4 form-control" type="number" autocomplete="off"
                    onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                    style="ime-mode:Disabled" value="1000"
                    onkeyup="this.value=this.value === '' ? 0 :parseInt(this.value)>100000 ? 100000:  this.value.match(/\d+(\.\d{0,0})?/) ? this.value.match(/\d+(\.\d{0,0})?/)[0] : ''">
              </div>
            </div>
            <div class="form-group hide" style="display:block " id="meuSampleInterval-d">
                <label for="meuSampleInterval" class="col-sm-3 control-label"
                    data-i18n="meuSampleInterval">MEU-采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="meuSampleInterval-minus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="meuSampleInterval" vk="true" type="text" class="col-sm-4 form-control"
                            placeholder="Please input" value="20">
                        <span class="input-group-btn">
                            <button id="meuSampleInterval-plus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="spaceSecond">&nbsp秒</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide" style="display:block " id="monitorAwakeTime-d">
                <label for="monitorAwakeTime" class="col-sm-3 control-label" data-i18n="monitorAwakeTime">休眠间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="monitorAwakeTime-minus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="monitorAwakeTime" vk="true" type="text" class="col-sm-4 form-control"
                            placeholder="Please input" value="20">
                        <span class="input-group-btn">
                            <button id="monitorAwakeTime-plus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="spaceSecond">&nbsp秒</label></span>
                    </div>
                </div>
            </div>

            <div class="form-group hide" style="display:block " id="monitorSleepSpace-d">
                <label for="monitorSleepSpace" class="col-sm-3 control-label" data-i18n="monitorSleepSpace">苏醒时间</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="monitorSleepSpace-minus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="monitorSleepSpace" vk="true" type="text" class="col-sm-4 form-control"
                            placeholder="Please input" value="720">
                        <span class="input-group-btn">
                            <button id="monitorSleepSpace-plus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">&nbsp分</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide" style="display:block " id="wakeStartTime-d">
                <label for="wakeStartTime" class="col-sm-3 control-label" data-i18n="wakeStartTime">起始苏醒时间（整点）</label>
                <div class="col-sm-8">
                    <select id="wakeStartTime" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;">
                    </select>
                </div>
            </div>
            <!--<div class="form-group hide" id="uploadInterval-d">-->
            <!--<label for="uploadInterval" class="col-sm-3 control-label" data-i18n="intervalServer">上传间隔（服务器）</label>-->
            <!--<div class="col-sm-8">-->
            <!--<div class="input-group">-->
            <!--<span class="input-group-btn">-->
            <!--<button id="interval-minus" type="button"-->
            <!--class="btn btn-default btn-flat">-->
            <!--<i class="fa fa-minus"></i>-->
            <!--</button>-->
            <!--</span>-->
            <!--<input id="uploadInterval" vk="true" type="text"-->
            <!--class="col-sm-4 form-control"-->
            <!--placeholder="Please input" value="60">-->
            <!--<span class="input-group-btn">-->
            <!--<button id="interval-plus" type="button"-->
            <!--class="btn btn-default btn-flat">-->
            <!--<i class="fa fa-plus"></i>-->
            <!--</button>-->
            <!--</span>-->
            <!--<span class="input-group-btn"><label-->
            <!--class="btn btn-default btn-flat">&nbsp秒</label></span>-->
            <!--</div>-->
            <!--</div>-->
            <!--</div>-->
            <div class="form-group hide" id="sampleInterval-d">
                <label for="sampleInterval" class="col-sm-3 control-label" data-i18n="intervalMinus">采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="interval-minus-s" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="sampleInterval" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput" value="12">
                        <span class="input-group-btn">
                            <button id="interval-plus-s" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">小时</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide" id="monitorSampleTime-d">
                <label for="sampleInterval" class="col-sm-3 control-label" data-i18n="startSampleTime">主机起始采样时刻</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="interval-minus-m" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="monitorSampleTime" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput" value="2">
                        <span class="input-group-btn">
                            <button id="interval-plus-m" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="spacePoint">&nbsp点</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide" id="monitorSampleInterval-d">
                <label for="sampleInterval" class="col-sm-3 control-label"
                    data-i18n="startSampleInterval">主机起始采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="interval-minus-mi" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="monitorSampleInterval" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput" value="4">
                        <span class="input-group-btn">
                            <button id="interval-plus-mi" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="hour">小时</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide">
                <label for="poweroff" class="col-sm-3 control-label" data-i18n="poerOffSpace">关机间隔</label>
                <!--<div class="col-sm-8">-->
                <!--<input id="poweroff" vk="true" type="text" class="form-control timepicker col-sm-10">-->
                <!--<label class="col-sm-2">小时</label>-->
                <!--</div>-->
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="poweroff" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput">
                        <span class="input-group-btn">
                            <label class="btn btn-default btn-flat" data-i18n="hour">小时
                            </label>
                        </span>
                    </div>
                </div>
            </div>
            <div class="form-group hide">
                <label for="poweron" class="col-sm-3 control-label" data-i18n="powerOnSpace">开机间隔</label>
                <!--<div class="col-sm-8">-->
                <!--<input id="poweron" vk="true" type="text" class="form-control timepicker">-->
                <!--<label>小时</label>-->
                <!--</div>-->
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="poweron" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput">
                        <span class="input-group-btn"><label class="btn btn-default btn-flat"
                                data-i18n="hour">小时</label></span>
                    </div>
                </div>
            </div>
        </form>
        <div class="content-footer">
            <div style="
                display: flex;
                width: 60%;
                align-items: center;
                float: right;">
                <button type="button" id="emptyMonitorDatas" class="btn btn-block btn-primary"
                    style="width: 200px; margin: 10px 20px 10px auto" data-i18n="emptyMonitorDatas">
                    清空主机数据
                </button>
                <button type="button" id="resetSoftSet" class="btn btn-block btn-primary"
                    style="width: 200px; margin: 10px 20px 10px auto" data-i18n="resetSoftSet">
                    恢复出厂设置
                </button>
                <button type="button" id="save_soft_set" class="btn btn-block btn-primary"
                    style="width: 200px; margin: 10px 20px 10px auto" data-i18n="save">保存
                </button>
            </div>
        </div>
        <br>
    </div>

</div>


<!-- /.row -->
<script>
    initLoraFrequencySelector();
    initPageI18n();
    //@ sourceURL=soft_set.js
    //select2
    $('.select2').select2({
        minimumResultsForSearch: Infinity
    });
    //获取网络参数信息
    getSysConfig();

    checkNeedKeyboard();

    //增减PD采样间隔
    $("#pdSampleInterval-minus").click(function () {
        var value = $("#pdSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('pdSampleIntervalTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 20) {
                $("#pdSampleInterval-plus").removeClass("disabled");
                $("#pdSampleInterval").val(number - 1);
            } else {
                $("#pdSampleInterval").val(20);
                $("#pdSampleInterval-minus").addClass("disabled");
            }
        }
    });
    $("#pdSampleInterval-plus").click(function () {
        var value = $("#pdSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('pdSampleIntervalTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 594099) {
                $("#pdSampleInterval-minus").removeClass("disabled");
                $("#pdSampleInterval").val(number + 1);
            } else {
                $("#pdSampleInterval").val(594099);
                $("#pdSampleInterval-plus").addClass("disabled");
            }
        }
    });

    //增减MEU采样间隔
    $("#meuSampleInterval-minus").click(function () {
        var value = $("#meuSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('meuSampleIntervalTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 10) {
                $("#meuSampleInterval-plus").removeClass("disabled");
                $("#meuSampleInterval").val(number - 1);
            } else {
                $("#meuSampleInterval").val(10);
                $("#meuSampleInterval-minus").addClass("disabled");
            }
        }
    });
    $("#meuSampleInterval-plus").click(function () {
        var value = $("#meuSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('meuSampleIntervalTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 86400) {
                $("#meuSampleInterval-minus").removeClass("disabled");
                $("#meuSampleInterval").val(number + 1);
            } else {
                $("#meuSampleInterval").val(86400);
                $("#meuSampleInterval-plus").addClass("disabled");
            }
        }
    });


    //增减莆田-休眠间隔
    $("#monitorAwakeTime-minus").click(function () {
        var value = $("#monitorAwakeTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('monitorAwakeTimeTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#monitorAwakeTime-plus").removeClass("disabled");
                $("#monitorAwakeTime").val(number - 1);
            } else {
                $("#monitorAwakeTime").val(1);
                $("#monitorAwakeTime-minus").addClass("disabled");
            }
        }
    });
    $("#monitorAwakeTime-plus").click(function () {
        var value = $("#monitorAwakeTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('monitorAwakeTimeTip')
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 86400) {
                $("#monitorAwakeTime-minus").removeClass("disabled");
                $("#monitorAwakeTime").val(number + 1);
            } else {
                $("#monitorAwakeTime").val(86400);
                $("#monitorAwakeTime-plus").addClass("disabled");
            }
        }
    });

    //增减莆田-苏醒时间
    $("#monitorSleepSpace-minus").click(function () {
        var value = $("#monitorSleepSpace").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: stringFormat.format(getI18nName('monitorSleepSpaceTip'), 5, 1440)
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#monitorSleepSpace-plus").removeClass("disabled");
                $("#monitorSleepSpace").val(number - 1);
            } else {
                $("#monitorSleepSpace").val(1);
                $("#monitorSleepSpace-minus").addClass("disabled");
            }
        }
    });
    $("#monitorSleepSpace-plus").click(function () {
        var value = $("#monitorSleepSpace").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: stringFormat.format(getI18nName('monitorSleepSpaceTip'), 5, 1440)
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 86400) {
                $("#monitorSleepSpace-minus").removeClass("disabled");
                $("#monitorSleepSpace").val(number + 1);
            } else {
                $("#monitorSleepSpace").val(86400);
                $("#monitorSleepSpace-plus").addClass("disabled");
            }
        }
    });

    //增减上传间隔
    $("#interval-minus").click(function () {
        var value = $("#uploadInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'uploadIntervalTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 60) {
                $("#interval-plus").removeClass("disabled");
                $("#uploadInterval").val(number - 1);
            } else {
                $("#uploadInterval").val(60);
                $("#interval-minus").addClass("disabled");
            }
        }
    });
    $("#interval-plus").click(function () {
        var value = $("#uploadInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'uploadIntervalTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 86400) {
                $("#interval-minus").removeClass("disabled");
                $("#uploadInterval").val(number + 1);
            } else {
                $("#uploadInterval").val(86400);
                $("#interval-plus").addClass("disabled");
            }
            if (number < 60) {
                $("#uploadInterval").val(60);
                $("#interval-minus").addClass("disabled");
            }
        }
    });

    //增减采样间隔
    $("#interval-minus-s").click(function () {
        var value = $("#sampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'sampleInterval'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#interval-plus-s").removeClass("disabled");
                $("#sampleInterval").val(number - 1);
            } else {
                $("#sampleInterval").val(1);
                $("#interval-minus-s").addClass("disabled");
            }
        }
    });
    $("#interval-plus-s").click(function () {
        var value = $("#sampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'sampleInterval'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 72) {
                $("#interval-minus-s").removeClass("disabled");
                $("#sampleInterval").val(number + 1);
            } else {
                $("#sampleInterval").val(72);
                $("#interval-plus-s").addClass("disabled");
            }
        }
    });

    //增减主机起始采样时刻
    $("#interval-minus-m").click(function () {
        var value = $("#monitorSampleTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'monitorSampleTimeTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 0) {
                $("#interval-plus-m").removeClass("disabled");
                $("#monitorSampleTime").val(number - 1);
            } else {
                $("#monitorSampleTime").val(0);
                $("#interval-minus-m").addClass("disabled");
            }
        }
    });
    $("#interval-plus-m").click(function () {
        var value = $("#monitorSampleTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'monitorSampleTimeTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 23) {
                $("#interval-minus-m").removeClass("disabled");
                $("#monitorSampleTime").val(number + 1);
            } else {
                $("#monitorSampleTime").val(23);
                $("#interval-plus-m").addClass("disabled");
            }
        }
    });

    //增减采样间隔
    $("#interval-minus-mi").click(function () {
        var value = $("#monitorSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'monitorSampleIntervalTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#interval-plus-mi").removeClass("disabled");
                $("#monitorSampleInterval").val(number - 1);
            } else {
                $("#monitorSampleInterval").val(1);
                $("#interval-minus-mi").addClass("disabled");
            }
        }
    });
    $("#interval-plus-mi").click(function () {
        var value = $("#monitorSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: getI18nName(
                        'monitorSampleIntervalTip'
                    ) //'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 168) {
                $("#interval-minus-mi").removeClass("disabled");
                $("#monitorSampleInterval").val(number + 1);
            } else {
                $("#monitorSampleInterval").val(168);
                $("#interval-plus-mi").addClass("disabled");
            }
        }
    });

    $('#emptyMonitorDatas').click(function () {
        var emptyTipsContent = getSoftSetWarningContent('emptyMonitorDatasTip');
        layer.confirm(emptyTipsContent, {
            title: '',
            resize: false,
            maxWidth: 800,
            closeBtn: 0,
            btn: [getI18nName('continue'), getI18nName('cancel')]
        }, function (index, layeror) {
            pollGet(GET_CLEAR_ALL_DATA, {}, function (res) {
                $.notifyUtil.notifySuccess(getI18nName('emptyMonitorDatasApply'));
                // layer.msg(GET_CLEAR_ALL_DATA + " -> " + res.errormsg);
            });
            layer.close(index);
        });
    });

    $('#resetSoftSet').click(function () {
        var emptyTipsContent = getSoftSetWarningContent('resetSoftSetTip');
        layer.confirm(emptyTipsContent, {
            title: '',
            resize: false,
            maxWidth: 800,
            closeBtn: 0,
            btn: [getI18nName('continue'), getI18nName('cancel')],
            success: function (content, index) {
                content.children()[0].style.overflow = "initial";
            }
        }, function (index, layeror) {
            var resetNetConfig = $('#checkResetNet').is(":checked") ? 1 : 0;
            pollGet(GET_FACTORY_SETTINGS, {
                resetNetConfig: resetNetConfig
            }, function (res) {
                $.notifyUtil.notifySuccess(getI18nName('resetSoftSetSuccess'));
                getSysConfig();
                // layer.msg(GET_FACTORY_SETTINGS + " -> " + res.errormsg);
            });
            layer.close(index);
        });
    });

    function getSoftSetWarningContent(key) {
        return '<div style="display: flex;justify-content: space-between;align-items: center;">' +
            '<i style="margin-right:15px;color: #faad14;font-size: 20px;justify-content: space-between;display: flex;">' +
            '  <svg viewBox="64 64 896 896" class=""' +
            '						data-icon="exclamation-circle" width="1em" height="1em" fill="currentColor" aria-hidden="true"' +
            '						focusable="false">' +
            '						<path' +
            '							d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 0 1 0-96 48.01 48.01 0 0 1 0 96z">' +
            '						</path>' +
            '  </svg>' +
            '</i><span style="font-size: 16px;margin-right: 50px;">' + getI18nName(key) + '</span>' +
            '</div>' +
            '<span style="font-size: 14px;margin-left: 35px;color: #f04134;">' + getI18nName('continueConfirmTip') +
            '</span>' +
            '<div style="position:absolute;bottom:-32px;left:33px;display:flex;align-items:center">' +
            '   <input id="checkResetNet" type="checkbox" style="margin: 0px 5px;"></input>' +
            '   <label for="checkResetNet" style="margin: 0;color: slategray;">恢复本机默认网络设置</label>' +
            '</div>';
    }

    /**
     *  MonitorType主机类型：
     *  MonitorTypeHanging  壁挂
     *  MonitorType2U 2u,
     *  MonitorTypeCollectionNode 汇集节点
     *  MonitorTypeLowPower  低功耗太阳能主机
     */

    //保存全局信息
    $("#save_soft_set").click(function () {
        var check = true;
        var formData = {};
        //        var systemDate = $("#systemDate").val();
        //        formData.sysTime = $("#systemDate").val() + " " + $("#systemTime").val();
        //        formData.language = $("#language").val();
        //        formData.monitorAwakeTime = $("#poweroff").val();
        //        formData.monitorSleepSpace = $("#poweron").val();

        formData.MonitorName = $('#MonitorName').val();
        if (validateStrLen(formData.MonitorName) == 0) {
            var label = "label[for=serverIp]";
            var text = $("#network").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: getI18nName('enterHost')
                },
                type: "warning",
            }).show();
            check = false;
        }
        formData.MonitorType = $('#MonitorType').val();
        formData.frequency = $("#frequency").val();
        if (!$("#monitor_work_group").is(":hidden")) {
            formData.monitorWorkGroup = $("#monitor_work_group").val();
        }
        formData.monitorAwakeTime = $("#monitorAwakeTime").val();
        formData.monitorSleepSpace = $("#monitorSleepSpace").val();
        formData.wakeStartTime = $("#wakeStartTime").val();
        formData.pdSampleInterval = $("#pdSampleInterval").val();
        formData.meuSampleInterval = $("#meuSampleInterval").val();
        formData.uploadInterval = $("#uploadInterval").val();
        formData.sampleInterval = $("#sampleInterval").val();
        formData.monitorSampleTime = $("#monitorSampleTime").val();
        formData.monitorSampleInterval = $("#monitorSampleInterval").val();
        formData.aduOfflineThreshold = $("#aduOfflineThreshold").val();

        formData.lora_frequency = $("#LoraFrequency").val();


        var auxRtuID = $("#auxRtuID").val();
        var iedRtuID = $("#iedRtuID").val();
        if (auxRtuID === iedRtuID) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('RtuidRepeatTips')
                },
                type: "warning",
            }).show();
            return;
        }

        formData.auxRtuID = auxRtuID;
        formData.iedRtuID = iedRtuID;

        if (validateStrLen(formData.frequency) == 0) {
            var label = "label[for=frequency]";
            var text = $("#global").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: stringFormat.format(getI18nName('pleaseInputFormat', text))
                },
                type: "warning",
            }).show();
            check = false;
        }
        var workgroupmax = 0;
        if (!$("#LoraFrequency").is(":hidden")) {
            var LFenum = _loraFrequencyEnum[getLoraFrequencyName(formData.lora_frequency)];
            workgroupmax = getSession('user') == 'SystemAdmin' ? 9999 : LFenum.range[1];
        } else {
            workgroupmax = getSession('user') == 'SystemAdmin' ? 9999 : 6;
        }

        checkNumValue(formData.monitorWorkGroup, "monitorWorkGroup", getI18nName('deviceWorkGroupCheckTip'), 0,
            workgroupmax);
        checkNumValue(formData.pdSampleInterval, "pdSampleInterval", getI18nName('pdSampleIntervalTip'), 20,
            594099);
        checkNumValue(formData.meuSampleInterval, "meuSampleInterval", getI18nName('meuSampleIntervalTip'), 10,
            86400);
        checkNumValue(formData.monitorAwakeTime, "monitorAwakeTime", getI18nName('monitorAwakeTimeTip'), 1,
            86400);
        checkNumValue(formData.monitorSleepSpace, "monitorSleepSpace", stringFormat.format(getI18nName(
            'monitorSleepSpaceTip'), 5, 1440), 5, 1440);
        checkNumValue(formData.monitorSampleTime, "monitorSampleTime", getI18nName('monitorSampleTimeTip'), 0,
            23);
        checkNumValue(formData.monitorSampleInterval, "monitorSampleInterval", getI18nName(
            'monitorSampleIntervalTip'), 1, 168);
        checkNumValue(formData.sampleInterval, "sampleInterval", getI18nName('sampleIntervalTip'), 1, 72);
        //        checkNumValue(formData.uploadInterval, "uploadInterval", '上传数据间隔在60s至86400之间。', 60, 86400);

        formData.isDaq = $("#isDaq").val();
        if (check) {
            saveSysInfo(formData);
        }
    });
    var currentMonitorType;
    $('#MonitorType').on('change', function (obj) {
        var mtype = obj.target.value;
        if (mtype != currentMonitorType) {
            var msgContent = '<div><div>'+getI18nName('deviceType')+':</div><div style="color:red">[{1}]</div><div>'+getI18nName('ConfirmChange')+'</div></div>'
            switch (mtype) {
                case 'MonitorTypeHanging':
                    layer.confirm(stringFormat.format(msgContent, getI18nName('MonitorTypeHanging')), {
                        title: '',
                        resize: false,
                        maxWidth: 800,
                        closeBtn: 0,
                        btn: [getI18nName('continue'), getI18nName('cancel')],
                    }, function (index, layeror) {
                        currentMonitorType = obj.target.value;
                        $('#monitorSleepSpace-d').removeClass('hide').addClass('hide');
                        var formData = {
                            MonitorType: mtype
                        };
                        saveSysInfo(formData);
                        layer.close(index);
                    }, function (index, layeror) {
                        obj.target.value = 'MonitorTypeLowPower';
                        $('#MonitorType').val('MonitorTypeLowPower').select2();
                        currentMonitorType = obj.target.value;
                        $('#monitorSleepSpace-d').removeClass('hide');
                        layer.close(index);
                    });
                    break;
                case 'MonitorTypeLowPower':
                    layer.confirm(stringFormat.format(msgContent, getI18nName('MonitorTypeLowPower')), {
                        title: '',
                        resize: false,
                        maxWidth: 800,
                        closeBtn: 0,
                        btn: [getI18nName('continue'), getI18nName('cancel')],
                    }, function (index, layeror) {
                        currentMonitorType = obj.target.value;
                        $('#monitorSleepSpace-d').removeClass('hide');
                        var formData = {
                            MonitorType: mtype
                        };
                        saveSysInfo(formData);
                        layer.close(index);
                    }, function (index, layeror) {
                        obj.target.value = 'MonitorTypeHanging';
                        $('#MonitorType').val('MonitorTypeHanging').select2();
                        currentMonitorType = obj.target.value;
                        $('#monitorSleepSpace-d').removeClass('hide').addClass('hide');
                        layer.close(index);
                    });
                    break;
                default:
                    currentMonitorType = obj.target.value;
                    $('#monitorSleepSpace-d').removeClass('hide').addClass('hide');
                    break;
            }
        }
    });

    function initLoraFrequencySelector() {
        var $loraFrequency = $("#LoraFrequency");
        for (var key in _loraFrequencyEnum) {
            var tempName = _loraFrequencyEnum[key].name;
            $loraFrequency.append('<option value=' + _loraFrequencyEnum[key].code + ' data-i18n="' + tempName + '">' +
                getI18nName(tempName) + '</option>');
        }
    }
</script>