{"loginTime": "Anmeldezeit", "changerUser": "Benutzerwechsel", "change": "<PERSON><PERSON><PERSON>", "login": "<PERSON><PERSON>", "systemShutDownConfirmTip": "Das System wird innerhalb einer Minute heruntergefahren. Möchten Sie den Vorgang abbrechen?", "systemShutDownTip": "Eine Minute ist vergangen, automatische Abschaltung!", "systemShutDownCancel": "Herunterfahren -a", "loadingData": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> zu <PERSON>, bitte warten", "pageSize": "{1} Elemente pro Seite", "pageSelecter": "Anzeige: {1}-{2}, insgesamt {3} Elemente", "search": "<PERSON><PERSON>", "noMatch": "<PERSON>ine übereinstimmenden Datensätze gefunden", "index": "Seriennummer", "type": "<PERSON><PERSON>", "aduId": "Codierung", "connectedState": "Netzwerkstatus", "commType": "Kommunikationsmethode", "loraSignalLevel": "LoRa-Signalstärke", "loraSNR": "Signal-Rausch-Verhältnis", "powerSupplyMode": "Stromversorgungsverfahren", "byBattery": "Eingebaute Batterie", "byCable": "Externe Stromversorgung", "battaryPercent": "Stromniveau", "battaryLife": "Ausdauerzeit (Tage)", "deviceNameTable": "G<PERSON><PERSON><PERSON><PERSON>", "pointType": "Sensor", "isconnected": "Zustand", "onlineState": "on line", "offlineState": "Verbindungsverlust", "aduNeverConnectedState": "Nicht verbunden", "updateTime": "Aktualisierungszeit", "data": "Daten", "chart": "Atlas", "operate": "<PERSON><PERSON><PERSON>", "lookUp": "Prüfung", "tbSampleDate": "Abholzeit", "collect": "<PERSON><PERSON><PERSON>", "errorTip": "<PERSON><PERSON>: {1}", "AEamp": "AE-Amplitude", "aeChartTitle": "AE Amplitudenspektrum", "humidityChartTitle": "Feuchtigkeitskurve", "temperatureChartTitle": "Temperaturkurve", "noiseChartTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coreGroundingCurrentChartTitle": "Kernerdungsstromkurve", "TEVamp": "TEV-Amplitude", "TEVYunit": "Einheit [dB]", "UnitFomat": "Einheit [{1}]", "unitParse": "Einheit [{1}]", "maxValue": "Höchstwert", "minValue": "Mindestwert", "average": "Durchschnittswert", "AT&T": "US AT&T", "China Unicom": "China Unicom", "China Mobile": "China Mobile", "Transformer": "Transformator", "Breaker": "Leistungsschalter", "Disconnector": "Isolationsschalter", "Isolator": "Messerschalter", "Arrester": "Blitzableiter", "PT": "Spannungswandler", "CT": "<PERSON><PERSON><PERSON><PERSON>", "Busbar": "Generatorix", "Circuit": "Mutter Union", "Switchgear": "Schaltschrank", "Power Cable": "Netzkabel", "Lightning Rod": "Blitzstange", "Wall Bushing": "<PERSON><PERSON><PERSON><PERSON>", "Reactor": "<PERSON><PERSON><PERSON>", "Electric Conductor": "Leistungsleiter (Führungsschiene)", "Power Capacitor": "Leistungskondensator", "Discharge Coil": "Entladespule", "Load Switch": "Lastschalter", "Grounding Transformer": "Erdungstransformator", "Grounding Resistance": "Erdungswiderstand", "Grounding Grid": "Erdungsgitter", "Combined Filter": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Insulator": "Isolator", "Coupling Capacitor": "Kopplungskondensator", "Cabinet": "<PERSON><PERSON><PERSON><PERSON>", "Other": "andere", "Fuse": "Sicherungen", "Using Transformer": "Verwendete Variable", "Arc Suppression Device": "Lichtbogenlöschanlage", "Main Transformer": "Haupttransformator", "Wave Trap": "Wellenfalle", "Combined Electric Appliance": "Kombinierte Elektrogeräte", "Combined Transformer": "Kombinierter Transformator", "monitor": "Überwachung in Echtzeit", "dataSelect": "Datenabfrage", "themeSkin": "<PERSON><PERSON>", "themeBlue": "blau", "settings": "Systemkonfiguration", "datetime_set": "Zeiteinstellung", "language_set": "Spracheinstellungen", "soft_setting": "Systemeinstellungen", "file_manage": "Dateiverwaltung", "instrument_set": "Sensorkonfiguration", "file_point_set": "Messpunktkonfiguration", "alarm_param_set": "Alarmkonfiguration", "alarm_manager": "Alarmverwaltung", "audit_view": "Audit-Ansicht", "modbus_setting": "Modbus-Einstellungen", "hardware_update": "Firmware Update", "collect_mode": "<PERSON><PERSON><PERSON><PERSON>", "net_set": "Netzwerkeinstellungen", "main_station_params": "Master Station Konfiguration", "sync_data": "Daten synchronisieren", "syncingData": "Daten synchronisieren", "syncingDataCancel": "Synchronisation abbrechen", "syncingDataFailed": "Synchronisierung fehlgeschlagen", "syncingDataSuccess": "Daten synchronisieren abgeschlossen", "syncingDataProgress": "Datenfortschritt synchronisieren: {1}", "syncingDataProgress2": "Datenfortschritt synchronisieren [{1}]", "export_data": "Daten exportieren", "system_info": "Systeminformationen", "monitoringTable": "Datenblatt", "PD": "Eingebauter Teilentladungssensor", "PD_THREE": "Integrierter Teilentladungssensor (drei in einem)", "PD_FIVE": "Integrierter Teilentladungssensor (fünf in einem)", "OLD_IS": "Ultrahochfrequenter Smart Sensor", "MEU": "Mechanischer Kennwertsensor", "UHF_IS": "Ultrahochfrequenter Smart Sensor", "HFCT_IS": "Intelligent<PERSON><PERSON>", "PD_IS": "Externer intelligenter <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TRANSFORMER_AE_IS": "Ultraschallsensor des Transformators", "GIS_AE_IS": "GIS Ultraschallsensor", "ENV_IS": "Umgebungsintelligenz-Sensor", "Arrester_U_IS": "Intelligenter Sensor für Blitzableiter", "Arrester_I_IS": "Intelligenter <PERSON>-Stromsensor", "LeakageCurrent_IS": "Intelligent<PERSON>", "Vibration_IS": "Intelligent<PERSON>", "MECH_IS": "Intelligentes Überwachungsinstrument für mechanische Eigenschaften", "TEMP_HUM_IS": "Temperatur- und Feuchtigkeitssensor", "GrounddingCurrent_IS": "Intelligenter Sensor für Erdungsstrom", "MECH_PD_TEMP": "Integrierter 3-in-1 Sensor für mechanische Kennwerte der Teilentladung", "GIS_LOWTEN": "Niederdrucksensor", "CHANNEL_OPTICAL_TEMP": "Lichtwellentemperatursensor", "VaisalaDTP145": "Vaisala DTP145", "Wika_GDT20": "WIKA GDT20", "Wika_GDHT20": "WIKA GDHT20", "SHQiuqi_SC75D_SF6": "Shanghai Qiuqi SC75D", "SPTR_IS": "Erdungsstromsensor aus Eisenkern", "TEMPFC_OUT": "Temperatursensor (CLMD)", "TEMPXP_OUT": "Temperatursensor (SPS061)", "SF6YN_OUT": "SF6 Gasdrucksensor (FTP-18)", "FLOOD_IS": "Intelligenter <PERSON><PERSON> zum Eintauchen in Wasser", "TEMPKY_OUT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (RFC-01)", "SMOKERK_OUT": "Rauchmelder (RS-YG-N01)", "HIKVIDEO_OUT": "Videosensor (HIK)", "TEMPSB_OUT": "Temperatursensor (DS18B20)", "TEMP_HUM_JDRK_OUT": "Temperatur- und Feuchtigkeitssensor (RS-WS-N01-2)", "SF6_OUT": "SF6&O2 Sensor (WFS-S1P-SO)", "FAN_OUT": "Lüftersteuerung", "LIGHT_OUT": "Beleuchtungssteuerung", "NOISE_JDRK_OUT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (RS-WS-N01)", "IR_DETECTION_OUT": "Infrarot-Doppeldetektor", "TEMPWS_OUT": "Temperatur- und Feuchtigkeitssensor (WS-DMC100)", "FLOODWS_OUT": "<PERSON><PERSON><PERSON><PERSON><PERSON>sor (WS-DMC100)", "NOISEWS_OUT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (WS-DMC100)", "VibrationSY_OUT": "Schwingungssensor der aufgehenden Sonne", "TEVPRPS_IS": "Intelligenter Sensor der transienten Erdspannung", "SF6_IS": "Intelligenter Sensor SF6", "phase": "Phase", "period": "Z<PERSON><PERSON>", "AMP": "Amplitude", "RMS": "Effektiver Wert", "max": "Maximaler <PERSON>", "fre1Value": "Frequenzkomponente 1", "fre2Value": "Frequenzkomponente 2", "All-pass": "Omnidirektional", "Low-pass": "Tiefpass", "High-pass": "Hochpass", "No-Record": "Nicht aufgezeichnet", "TEV": "Transiente E<PERSON>pannung", "AE": "Ultraschall", "TEMP": "Temperatur", "HFCT": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "UHF": "Ultrahochfrequenz", "Humidity": "Feuchtigkeit", "Decibels": "Decibel", "Noisy": "Schalldruck", "Noise": "L<PERSON>rm", "ENVGas": "Atmosphäre", "envgas": "Atmosphärische Trends", "MP": "mechanische Eigenschaften", "FLOOD": "Eintauchen in Wasser", "SMOKE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SF6": "Schwefelhexafluorid", "FAN": "<PERSON><PERSON><PERSON>", "LIGHT": "Beleuchtung", "NOISE": "L<PERSON>rm", "IRDETECTION": "Infrarot", "SF6YN": "SF6 Gasdruck", "ArresterU": "Blitzableiter Spannung", "ArresterI": "Blitzableiter-Strom", "LeakageCurrent": "<PERSON><PERSON><PERSON>", "Vibration": "Vibration", "FrostPointRaw": "Frostfleck", "FrostPointATM": "Frostpunkt (Standardatmosphärendruck)", "DewPointRaw": "Taupunkt", "DewPointATM": "Taupunkt (Standardatmosphärendruck)", "Moisture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AbsolutePressure": "Absoluter Druck", "NormalPressure": "Standarddruck", "Density": "<PERSON><PERSON><PERSON>", "Oxygen": "Sauerstoffgehalt", "SPTR": "Kern-Erdungsstrom", "GrounddingCurrent": "Erdungsstrom", "VIDEO": "Videobilder", "TEVPRPS": "Transiente Erdspannung PRPS", "confirm": "bestätigen", "close": "schließen", "yes": "ja", "no": "nein", "continue": "weiter", "none": "nichts", "DISK": "Lagerung", "MEMORY": "<PERSON><PERSON><PERSON><PERSON>", "noTestData": "<PERSON><PERSON>", "connected": "verbinden", "disconnected": "<PERSON><PERSON><PERSON>", "virtualKeyBord": "<PERSON>irt<PERSON><PERSON> Ta<PERSON>", "pleaseInput": "<PERSON>te geben <PERSON> ein", "pleaseInputFormat": "<PERSON><PERSON><PERSON> {1}ein", "tips": "prompt", "confirmTips": "Eingabeaufforderung zur Bestätigung", "getDirFailed": "Verzeichnis konnte nicht abgerufen werden:", "backupSuccessTip": "Die Daten wurden erfolgreich gesichert. Bitte sehen <PERSON> sie im Verzeichnis {1} an.", "backupFailedTip": "Datensicherung fehlgeschlagen", "exportFailedTip": "Datenexport fehlgeschlagen: {1}", "historyDataTypeNotSuport": "Die Abfrage historischer Daten dieses Sensortyps wird derzeit nicht unterstützt", "stopExportTips": "<PERSON><PERSON><PERSON> Si<PERSON> aufhören, Daten zu exportieren?", "stationNameTips": "Bitte geben Sie den Namen der Website ein", "powerUnitNameTips": "Bitte füllen Sie das Stromaggregat aus", "selectDeviceTips": "Bitte wählen Si<PERSON> ein Gerät einmal", "selectPointTips": "Bitte wählen Si<PERSON> einen Messpunkt", "selectDeviceOfPointTips": "Bitte wählen Sie das primäre Gerät aus, das Messpunkte hinzufügen soll", "saveSuccess": "Erfolgreich gespeichert", "saveFailed": "Speichern fehlgeschlagen:", "operatFailed": "Operation fehlgeschlagen: {1}", "chosseSensorUpdate": "Bitte wählen Sie den Sensor aus, der aktualisiert werden soll", "chooseFileUpdate": "Bitte wählen Sie eine Update-Datei aus", "cancelUpdateSensorConfirm": "Sind <PERSON> sic<PERSON>, dass Sie die Aktualisierung des Sensorprogramms abbrechen?", "enterServerUrlTips": "<PERSON>te geben Si<PERSON> die Serveradresse ein", "enterServerUrlError": "Fehler bei der Eingabe der Serveradresse, bitte erneut eingeben", "enterServerPortTips": "<PERSON>te geben Si<PERSON> den Server-Port ein", "enterServerPortError": "Server Port Eingabefehler, Port Eingabebereich: [165535]", "NTPserverIpError": "Die Adresse des Timing-Servers wurde falsch eingegeben. Bitte geben Sie sie erneut ein", "NTPserverPortError": "Timing Server Port Eingabe Fehler, Port Eingabe Bereich: [165535]", "enterNetName": "Bitte geben Si<PERSON> einen Netzwerknamen ein", "enterIP": "Bitte geben Sie IP ein", "enterIPError": "IP-<PERSON><PERSON><PERSON>, wie ***********", "enterNetMask": "<PERSON>te geben Si<PERSON> die Subnetzmaske ein", "enterNetError": "Eingabefehler für Subnetzmasken, wie *************", "enterGateWay": "<PERSON>te geben Si<PERSON> ein Gateway ein", "enterGateWayError": "Konfigurations-<PERSON><PERSON><PERSON><PERSON>, wie ***********", "enterAPN": "Bitte wählen Sie APN", "pdSampleIntervalTip": "PD Probenahmeintervall zwischen 20s und 594099s (99h99m99s)", "meuSampleIntervalTip": "MEU-Probenahmeintervall zwischen 10s und 86400", "monitorAwakeTimeTip": "Schlafintervall zwischen einer Sekunde und 86400", "monitorSleepSpaceTip": "Aufwachen Intervall Bereich: [{1}, {2}] min", "uploadIntervalTip": "Upload-Intervall zwischen 60er und 86400", "sampleIntervalTip": "Eine Ganzzahl mit einem Abtastintervall zwischen einer Stunde und 72 Stunden", "monitorSampleTimeTip": "Die Probenahmezeit des Wirts liegt zwischen 0:00 und 23:00", "monitorSampleIntervalTip": "Eine Ganzzahl mit einem Host-Abtastintervall zwischen 1 Stunden und 168 Stunden", "updatingSensor": "Sensor wird aktualisiert", "updatingSensorProgress": "Fortschritt der Sensoraktualisierung: {1}", "updatingSensorProgress2": "Fortschritt der Sensoraktualisierung [{1}]", "selectInstTip": "Bitte wählen Sie den Sensor aus, der geändert werden soll", "selectChannelTip": "Bitte wählen Sie den Kanal aus, der geändert werden soll", "instCodeTip": "Sensorcode kann nicht leer sein", "commLinkTypeTip": "Bitte wählen Sie den Typ der Kommunikationsverbindung", "commLinkPortTip": "Bitte wählen Sie einen Kommunikationsport", "continuSampTimeTip": "Die kontinuierliche Sammelzeit liegt zwischen {1} min und {2} min.", "continuSampTimeCompareTip": "Die kontinuierliche Sammelzeit sollte kürzer als das Probenahmeintervall sein.", "instSampleSpaceTip": "Das Probenahmeintervall liegt zwischen {1} min und {2} min.", "instSampleStartTimeTip": "Die Probenahmezeit beträgt zwischen 0:00 und 23:00.", "instNumberPattenTip": "Kann nur 0-9 <PERSON><PERSON><PERSON>, a-zA-Z Buchstaben enthalten und:", "instIPPattenTip": "Die IP- und Port-Formate sind wie folgt: 0.0.0.0:1", "deleteInstTip": "Bitte wählen Sie den Sensor aus, der gelöscht werden soll", "selectSensorTip": "Bitte wählen Sie einen Sensor", "deleteInstConfirmTip": "<PERSON><PERSON> <PERSON>, dass Sie den Sensorcode </br>{1}</br>löschen: {2}?", "activeInstConfigTip": "Sind <PERSON><PERSON> sicher, diese Konfiguration auf ähnliche Sensoren von</br>{1} anzuwenden?", "activeBatchConfigsSuccess": "Batch-Konfiguration der Sensoren erfolgreich", "activeBatchConfigsBegin": "Batch Konfiguration der Sensoren beginnt", "activeBatchConfigsFailed": "Batch-Konfiguration der Sensoren fehlgeschlagen", "collectStartOnceTip": "Sind <PERSON> sicher, dass Sie Daten von ähnlichen Sensoren sammeln werden?", "collectStartTip": "<PERSON><PERSON> <PERSON>, dass <PERSON> beginnen, <PERSON>n für</br>{1}</br>Sensorcode zu sammeln: {2}?", "wakeUpInstrumentOnceTip": "<PERSON><PERSON> <PERSON>, dass Si<PERSON> damit beginnen, Sensoren des gleichen Typs zu wecken</br>{1}?", "wakeUpInstrumentTip": "<PERSON><PERSON> <PERSON><PERSON>, dass <PERSON>, den</br>{1}</br><PERSON><PERSON> zu wecken: {2}?", "emptySensorDataOnceTip": "Sind <PERSON> sicher, alle Daten von ähnlichen Sensoren in</br>{1} zu löschen?", "emptySensorDataTip": "<PERSON>d <PERSON> sic<PERSON>, dass Sie alle Daten für</br>{1}</br>Sensorcode löschen: {2}?", "emptySensorDataSuccess": "Sensordaten erfolgreich gel<PERSON>scht", "emptySensorDataFailed": "Sensordatenbereinigung fehlgeschlagen:", "ae_chart": "Echtzeit-Überwachung des AE-Diagramms", "uhf_chart": "Überwachung des UHF-Spektrums in Echtzeit", "hfct_chart": "Echtzeit-Überwachung des HFCT-Diagramms", "tev_chart": "Überwachung der Amplitude in Echtzeit", "tev_prps_chart": "PRPS Graph zur Überwachung der transienten Erdspannung in Echtzeit", "temperature_chart": "Überwachung der Temperatur in Echtzeit", "humidity_chart": "Überwachung der Luftfeuchtigkeit in Echtzeit", "mechanical_chart": "Überwachung der mechanischen Eigenschaften in Echtzeit", "arrester_chart": "Überwachung des Blitzableiters in Echtzeit", "leakage_current_chart": "Überwachung des Leckstroms in Echtzeit", "grounddingcurrent_chart": "Überwachung des Erdungsstroms in Echtzeit", "vibration_pickup_chart": "Überwachung der Schwingungen in Echtzeit", "density_micro_water_history": "Echtzeitüberwachung von Mi<PERSON>rowasser der Korndichte", "core_grounding_current": "Echtzeitüberwachung des Erdungsstroms des Kerns", "noise_chart": "Überwachung des Lärms in Echtzeit", "water_immersion": "Echtzeit-Überwachung der Wassereintauchung", "smoke_sensor": "Überwachung der Raucherkennung in Echtzeit", "video_sensor": "Videoüberwachung in Echtzeit", "sf6_sensor": "Echtzeit-Überwachung SF6", "error": "<PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON>", "success": "Erfolg", "userErrorTip": "Falscher Benutzername oder Passwort", "errorNoDeviceId": "Geräte-ID existiert nicht", "errorNoSensorId": "Sensor ID existiert nicht", "errorExistSensorId": "Sensor-ID existiert bereits", "errorNoChannelId": "Kanal-ID existiert nicht", "errorNoPointId": "Der Messpunkt existiert nicht", "errorDataExportErrDir": "Datenexport, Dateipfadfehler.", "errorDataExportErrTime": "Fehler beim Datenexport", "errorDataExportNoData": "Datenexport, keine Daten verfügbar.", "errorDataExportNoSpace": "Datenexport, USB-Laufwerk ist voll", "errorDataExportNoDisk": "Datenexport ohne USB-Stick.", "errorDataExportNoInfo": "Datenexport ohne Standortinformationen.", "errorDataExportOther": "Datenexport, andere <PERSON>hler.", "errorSetModeBus": "Modbus-Einstellungsfehler", "errorSameNamePoint": "Eine Messstelle mit dem gleichen Namen existiert bereits", "errorIP": "IP-Fehler", "errorGPRSSet": "GPRS-Einstellungsfehler", "errorSenorUpdateErrFile": "Fehler beim Aktualisieren der Sensordatei", "errorSenorUpdateNotMatch": "Sensor Update, Sensor Typ stimmt nicht mit Upgrade Datei überein", "errorSenorUpdating": "Sensoraktualisierung, Aktualisierung in Bearbeitung", "errorSenorUpdateSizeCheckFailed": "Überprüfung der Firmware-Größe fehlgeschlagen", "errorSenorUpdateVersionCheckFailed": "Überprüfung der Firmware-Versionsnummer fehlgeschlagen", "errorSenorUpdateDeviceTypeCheckFailed": "Überprüfung des Gerätetyps der Firmware fehlgeschlagen", "errorSenorUpdateCRCCheckFailed": "Überprüfung der Firmware-CRC fehlgeschlagen", "errorSenorUpdateFailed": "Sensor-Upgrade fehlgeschlagen", "errorSenorUpdateConnectErr": "Abnormale Firmware-Update-Kommunikation", "errorDataCleanFailed": "Datenlöschung fehlgeschlagen!", "errorCodeWorkGroupNotExist": "Die Arbeitsgruppe existiert nicht!", "errorCodeInvalidChannel": "Illegaler Kanal!", "errorCodeInvalidWirignMode": "Illegale Verkabelung!", "errorCodeInvalidAlarmMode": "Illegale Alarmmethode!", "errorNoPermission": "Dieser <PERSON>utzer hat diese Berechtigung nicht", "illegalUser": "Illegaler Benutzer!", "legalPattenMsg": "Kann nur Zeichen enthalten (außer Leerzeichen): Chinesische Zeichen, Alphabete, <PERSON><PERSON><PERSON>, römische Ziffern I bis XII `- () [] #_", "groundCurrent": "Diagramm des Erdungsstroms ausführen", "groundCurrentA": "A-Phase Erdungsstrom", "groundCurrentB": "Erdungsstrom der B-Phase", "groundCurrentC": "C-Phase Erdungsstrom", "leakageCurrent": "Vollständiges aktuelles Ausführungsdiagramm", "leakageCurrentA": "A-Phase Vollstrom", "leakageCurrentB": "B-Phase Vollstrom", "leakageCurrentC": "C-Phase Vollstrom", "ResistiveCurrent": "Resistives Stromlaufdiagramm", "ResistiveCurrentA": "A-Phase-Widerstandsstrom", "ResistiveCurrentB": "B-Phase Widerstandsstrom", "ResistiveCurrentC": "C-Phase Widerstandsstrom", "resistiveCurrentA": "A-Phase-Widerstandsstrom", "resistiveCurrentB": "B-Phase Widerstandsstrom", "resistiveCurrentC": "C-Phase Widerstandsstrom", "referenceVoltageA": "A-Phase Bezugsspannung", "referenceVoltageB": "B-Phase Bezugsspannung", "referenceVoltageC": "C-Phase Bezugsspannung", "grounddingCurrent": "Diagramm des Erdungsstroms ausführen", "grounddingCurrentA": "A-Phase Erdungsstrom", "grounddingCurrentB": "Erdungsstrom der B-Phase", "grounddingCurrentC": "C-Phase Erdungsstrom", "timeDomain": "Zeitbereichsdiagramm", "frequencyDomain": "Diagramm des Frequenzbereichs", "characterParam": "charakteristischer Parameter", "TimeDomainDataX": "Schwingungssignal der X-Achse", "TimeDomainDataY": "Schwingungssignal der Y-Achse", "TimeDomainDataZ": "Schwingungssignal der Z-Achse", "FrequencyDomainDataX": "Schwingungssignalspektrumdiagramm der X-Achse", "FrequencyDomainDataY": "Schwingungssignalspektrumdiagramm Y-Achse", "FrequencyDomainDataZ": "Schwingungssignalspektrumdiagramm in Z-Achse", "ACCAVGX": "Durchschnittliche Beschleunigung der X-Achse", "ACCAVGY": "Durchschnittliche Beschleunigung der Y-Achse", "ACCAVGZ": "Durchschnittliche Beschleunigung der Z-Achse", "ACCMAXX": "Maximale Beschleunigung der X-Achse", "ACCMAXY": "Maximale Beschleunigung der Y-Achse", "ACCMAXZ": "Maximale Beschleunigung der Z-Achse", "AMPAVGX": "Durchschnittliche Amplitude der X-Achse", "AMPAVGY": "Amplitudendurchschnitt der Y-Achse", "AMPAVGZ": "Amplitudendurchschnitt der Z-Achse", "AMPMAXX": "Maximale Amplitude der X-Achse", "AMPMAXY": "Maximale Amplitude der Y-Achse", "AMPMAXZ": "Maximale Amplitude der Z-Achse", "MAXFreqX0": "X-Achse Extrempunktfrequenz 1", "MAXFreqY0": "Y-Achse Extrempunktfrequenz 1", "MAXFreqZ0": "Z-Achse Extrempunktfrequenz 1", "MAXFreqX1": "X-Achse Extrempunktfrequenz 2", "MAXFreqY1": "Y-Achse Extrempunktfrequenz 2", "MAXFreqZ1": "Z-Achse Extrempunktfrequenz 2", "MAXFreqX2": "X-Achse Extrempunktfrequenz 3", "MAXFreqY2": "Y-Achse Extrempunktfrequenz 3", "MAXFreqZ2": "Z-Achse Extrempunktfrequenz 3", "sensorType": "Sensortyp", "sensorList": "Sensorliste", "gain": "G<PERSON>inn", "trigger": "<PERSON>gger-Amplitude", "wave_filter": "Frequenzband", "sample_date": "Stichprobendatum", "sample_time": "Probenahmezeit", "rms": "Effektiver Wert", "cycle_max": "Maximaler <PERSON>", "frequency1": "Frequenzkomponente 1", "frequency2": "Frequenzkomponente 2", "time_interval": "Zeitintervall", "realData": "Echtzeitdaten", "historyData": "historische Daten", "trendSync": "Trendanalyse", "preData": "<PERSON><PERSON><PERSON><PERSON>", "nextData": "Nächster", "systemDate": "Systemdatum", "systemTime": "Systemzeit", "backupType": "Sicherungstyp", "plusBackup": "Inkrementelle Sicherung", "allBackup": "Vollständige Sicherung", "timeRange": "Zeitrahmen", "exportPath": "Exportpfad", "getExportDir": "Verzeichnis abrufen", "checkDir": "Verzeichnisse überprüfen", "exportingData": "Exportieren", "cancel": "Abbrechen", "export": "Export", "stationConfig": "Standortkonfiguration", "stationDelSuccess": "Die Website wurde erfolgreich gelöscht", "stationDelFailed": "Löschen der Website fehlgeschlagen:", "deviceConfig": "Konfiguration des primären Geräts", "pointConfig": "Messpunktkonfiguration", "stationName": "Name der Website", "stationPMS": "Standortcode", "stationLevel": "Spannungspegel der Station", "powerUnit": "Elektrizitätseinheit", "save": "konservieren", "operationSuccess": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "deviceAddSuccessTips": "Ein Gerät auf einmal erfolgreich hinzugefügt", "deviceEditSuccessTips": "Das Gerät einmal erfolgreich modifiziert", "deviceDelSuccessTips": "Das Gerät einmal erfolgreich gelöscht", "pointAddSuccessTips": "Erfolgreich hinzugefügte Messpunkte", "pointEditSuccessTips": "Erfolgreiche Änderung der Messstelle", "pointDelSuccessTips": "Die Messstelle wurde erfolgreich gelöscht", "deleteFailedTips": "Löschen fehlgeschlagen", "save_add": "Speichern/Hinzufügen", "delete": "löschen", "deviceType": "Gerätetyp", "deviceLevel": "Spannungspegel der Ausrüstung", "add": "<PERSON><PERSON><PERSON><PERSON>", "channelTypeAlarm": "Datentyp", "alarmThreshold": "Alarmschwelle", "alarmRecoveryThreshold": "Schwellenwert zur Wiederherstellung von Alarmen", "alarmChannel": "Auswahl des Alarmkanals", "built_in_channel_1": "Eingebauter Kanal 1", "External_IO_module": "Externes IO-Modul", "not_associated": "<PERSON><PERSON> verwandt", "alarmExternalIOSN": "Codierung externer IO-Module", "alarmExternalIOChannel": "Externer IO-Mo<PERSON>lk<PERSON>l", "wiringMode": "Verdrahtungsverfahren", "alarmMode": "Alarmmethode", "alarmTime": "Alarm-Timing", "alarmInterval": "Alarmintervall", "alarmDuration": "<PERSON><PERSON><PERSON><PERSON>", "normal_open": "Normalerweise geöffnet", "normal_close": "Normalerweise geschlossen", "continuous": "Kontinuität", "timing": "Timing", "interval": "Intervall", "alarmSaveSuccess": "Alarmkonfiguration erfolgreich gespeichert", "alarmDelSuccess": "Alarmkonfiguration erfolgreich gelöscht", "deviceName": "Name des primären Geräts", "pointName": "Name des Messpunkts", "testStation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "device": "Grundausstattung", "pointList": "Liste der Messstellen", "sensorID": "Sensor-ID", "sensorChannel": "Sensorkanal", "channelList": "<PERSON><PERSON><PERSON><PERSON>", "showPoint": "Messpunkte anzeigen", "fileSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectPlease": "Bitte wählen", "deviceList": "Sensorliste", "updating": "Aktualisierung", "updatingFailed": "Update fehlgeschlagen:", "updatingCanceled": "Update abbrechen", "updatingComplete": "Vollständige Aktualisierung", "update": "Update", "sampleDate": "Stichprobendatum", "sampleTime": "Probenahmezeit", "pdMax": "Maximale Entladungsamplitude", "pdAvg": "Durchschnittliche Entladungsamplitude", "pdNum": "<PERSON><PERSON><PERSON> der Impulse", "acquisitionTime": "Abholzeit", "humidity": "Feuchtigkeit", "startDate": "Anfangsdatum", "endDate": "Enddatum", "refresh": "Aktualisieren", "scanData": "Abfrage", "sensorCode": "Sensorkodierung", "sensorTypeSet": "Sensortyp", "senorName": "Sensorname", "aduAddress": "Sensorad<PERSON><PERSON>", "senorWorkMode": "Aktive Einreichung", "On": "offen", "Off": "schließen", "ADUMode": "Arbeitsmodus", "normalMode": "Wartungsmodus", "lowPowerMode": "Modus mit geringem Stromverbrauch", "monitorMode": "Überwachungsmodus", "artificialStateStartTime": "Beginn des manuellen Eingriffs (Stunde)", "artificialStateStartTimeTip": "Startzeitbereich des manuellen Eingriffszustands 0-23", "artificialStateEndTime": "Endzeit des manuellen Eingriffs (Stunde)", "artificialStateEndTimeTip": "Endzeitbereich des manuellen Eingriffszustands 0-23", "artificialStateWakeUpSpace": "Intervall zum Aufwachen des künstlichen Interventionszustands (Minuten)", "unartificialStateWakeUpSpace": "Aufwachintervall im nicht manuellen Interventionszustand (Minuten)", "isAutoChangeMode": "Ob die Modi automatisch gewechselt werden sollen", "monitorModeSampleSapce": "Erfassungsintervall des Überwachungsmodus", "workGroup": "Nummer der Arbeitsgruppe", "warnTemp": "Alarmtemperatur", "taskGroup": "Nummer der Arbeitsgruppe", "commLinkType": "Kommunikationsverbindungstyp", "commLinkPort": "Kommunikationsport", "frequencyUnit": "Rasterfrequenz (Hz)", "numInGroup": "Gruppen-ID", "commSpeed": "Kommunikationsrate", "commLoad": "Kommunikationskanal", "sampleSpace": "<PERSON><PERSON><PERSON>meintervall (min)", "sleepTime": "Strategie für den Energieverbrauch", "samleStartTime": "Beginn der Probenahme (Stunde)", "applyData": "<PERSON><PERSON><PERSON>", "applyAllSensor": "<PERSON><PERSON><PERSON> anwenden", "collectOnce": "<PERSON><PERSON><PERSON>", "collectOnceEnd": "End<PERSON> der Datenerhebung", "collectOnceProgress": "Datenerhebung: {1} ({2})", "collectOnceProgress2": "Datenerhebung [{1}] ({2})", "activeOnce": "Batch-Anwen<PERSON>ng", "wakeUp": "<PERSON><PERSON><PERSON>", "wakeUpInstrument": "Wecksensor", "wakeUpInstrumentOnce": "Stapelwachsensoren", "orderSend": "Befehl er<PERSON>ilt", "cleanData": "Daten löschen", "sensorAddSuccess": "Erfolgreich hinzugefügter Sensor", "sensorEditSuccess": "Sensormodifikation erfolgreich", "sensorEditBegin": "Beginn der Sensoränderung", "sensorDelSuccess": "Sensor erfolgreich <PERSON>", "cleanSensorData": "Sensordaten löschen", "getSensor": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>", "channelType": "Kanaltyp", "channelName": "Kanalname", "channelInfoSaveSuccess": "Erfolgreich gespeicherte Kanalinformationen", "channelInfoSaveBegin": "Beginn der Speicherung von Sensorkanalinformationen", "bias": "Bias [dB]", "waveFilter": "Frequenzbandeinstellungen", "gainMode": "Verstärkungsmodus", "gain_unit": "Verstärkung [dB]", "samCycle": "Anzahl der Probenahmezyklen", "samPointNum": "Anzahl der Probenahmestellen pro Zyklus", "samRate": "Anzahl der Probenahmestellen pro Zyklus", "ratio": "Umwandlungsverhältnis", "channelPhase": "Phasentrennung", "mecLoopCurrentThred": "Spulenstromschwelle [mA]", "mecMotorCurrentThred": "Motorstromschwelle [mA]", "mecSwitchState": "Anfangszustand des Schaltwerts", "awaysOpen": "Normalerweise geöffnet", "awaysClose": "Normalerweise geschlossen", "mecBreakerType": "Konfiguration des Schutzschaltermechanismus", "oneMech": "Mechanische Dreiphasenverbindung (ein Mechanismus)", "threeMech": "Dreiphasige elektrische Verbindung (drei Mechanismen)", "mecMotorFunctionType": "Motorbetriebsart", "threePhaseAsynMotor": "<PERSON><PERSON><PERSON>-Asynchronmotor", "onePhaseMotor": "Single AC/DC Motor", "setCurrent": "Aktuell festlegen", "setAll": "Wird auf den gleichen Sensor angewendet", "showCoil": "Spulenstromdiagramm", "showSwitch": "Wertdiagramm umschalten", "showMotor": "Motorstromdiagramm", "showOrig": "Ursprünglicher Stromdiagramm", "actionDate": "Aktionsdatum", "actionTime": "Aktionszeit", "phaseA": "A-Phase", "phaseB": "Phase B", "phaseC": "C-Phase", "coil_charge_time": "Einschaltzeit der Spule", "coil_cutout_time": "Ausfallzeit der Spule", "max_current": "Maximaler <PERSON>strom", "hit_time": "Auslösezeit des Abziehers", "subswitch_close_time": "Schaltzeit des Hilfsschalters", "a_close_time": "Schließzeit der Phase A", "a_close_coil_charge_time": "Einschaltzeit der Phase A Spule", "a_close_coil_cutout_time": "Stromausfallzeit der Phase A Spule", "a_close_max_current": "Maximaler Strom der A-Phase Schließspule", "a_close_hit_time": "Lösezeit der Bremse in der A-Phase", "a_close_subswitch_close_time": "Schaltzeit des A-Phasen-Hilfsschalters", "b_close_time": "Schließzeit der Phase B", "b_close_coil_charge_time": "Einschaltzeit der Phase B Spule", "b_close_coil_cutout_time": "Stromausfallzeit der Phase B Spule", "b_close_max_current": "Maximaler Strom der B-Phase Schließspule", "b_close_hit_time": "Auslösezeit des Schließschalters Phase B", "b_close_subswitch_close_time": "Schaltzeit des Hilfsschalters Phase B", "c_close_time": "Schließzeit der C-Phase", "c_close_coil_charge_time": "Einschaltzeit der Phase C Spule", "c_close_coil_cutout_time": "Stromausfallzeit der Phase C Spule", "c_close_max_current": "Maximaler Strom der C-Phase Schließspule", "c_close_hit_time": "Lösezeit der Bremse in C-Phase", "c_close_subswitch_close_time": "Schaltzeit des C-Phase Hilfsschalters", "close_sync": "Schließsynchronität", "close_time": "Schließzeit", "a_open_time": "Öffnungszeit der Phase A", "a_open_coil_charge_time": "Einschaltzeit der Phase A Spule", "a_open_coil_cutout_time": "Stromausfallzeit der Phase A Spule", "a_open_max_current": "Maximaler Strom der A-Phase Öffnungsspule", "a_open_hit_time": "Auslösezeit des A-Phase Öffnungsschalters", "a_open_subswitch_close_time": "Schaltzeit des A-Phasen-Hilfsschalters", "b_open_time": "Öffnungszeit der Phase B", "b_open_coil_charge_time": "Einschaltzeit der Phase B Spule", "b_open_coil_cutout_time": "Stromausfallzeit der Phase B Spule", "b_open_max_current": "Maximaler Strom der B-Phase Öffnungsspule", "b_open_hit_time": "Auslösezeit des Öffnungsschalters Phase B", "b_open_subswitch_close_time": "Schaltzeit des Hilfsschalters Phase B", "c_open_time": "Öffnungszeit der C-Phase", "c_open_coil_charge_time": "Einschaltzeit der Phase C Spule", "c_open_coil_cutout_time": "Stromausfallzeit der Phase C Spule", "c_open_max_current": "Maximaler Strom der C-Phase Öffnungsspule", "c_open_hit_time": "Auslösezeit des C-Phase Öffnungsschalters", "c_open_subswitch_close_time": "Schaltzeit des C-Phase Hilfsschalters", "open_sync": "Synchronisierung wird geöffnet", "open_time": "Öffnungszeiten", "a_twice_open_time": "Phase A sekundäre Öffnungszeit", "a_twice_open_coil_charge_time": "Live-Zeit der sekundären Öffnungspule Phase A", "a_twice_open_coil_cutout_time": "Stromausfallzeit der Phase A sekundäre Öffnungsspule", "a_twice_open_max_current": "Maximaler Strom der sekundären Öffnungspule Phase A", "a_twice_open_hit_time": "Auslösezeit des A-Phase Split Schalters", "a_twice_open_subswitch_close_time": "Öffnungszeit der Phase A sekundärer Öffnungs-Hilfsschalter", "b_twice_open_time": "Phase B sekundäre Öffnungszeit", "b_twice_open_coil_cutout_time": "Stromausfallzeit der sekundären Öffnungsspule Phase B", "b_twice_open_max_current": "Maximaler Strom der sekundären Öffnungspule Phase B", "b_twice_open_hit_time": "Auslösezeit des Teilschalters Phase B", "b_twice_open_subswitch_close_time": "Öffnungszeit des sekundären Öffnungsschalters Phase B", "c_twice_open_time": "Sekundäre Öffnungszeit der Phase C", "c_twice_open_coil_cutout_time": "Stromausfallzeit der sekundären Öffnungsspule Phase C", "c_twice_open_max_current": "Maximaler Strom der sekundären Öffnungspule Phase C", "c_twice_open_hit_time": "Auslösezeit des C-Phase Split Schalters", "c_twice_open_subswitch_close_time": "Öffnungszeit des sekundären Öffnungsschalters Phase C", "twice_open_sync": "Synchronizität der sekundären Öffnung", "twice_open_time_text": "Sekundäre Öffnungszeit", "a_switch_shot_time": "A-Phase Gold kurze Zeit", "b_switch_shot_time": "B-Phase Gold kurze Zeit", "c_switch_shot_time": "C-Phase Gold kurze Zeit", "a_switch_no_current_time": "A-Phase keine aktuelle Zeit", "b_switch_no_current_time": "B Phase keine aktuelle Zeit", "c_switch_no_current_time": "C-Phase keine aktuelle Zeit", "motor_start_current": "Motorstartstrom", "motor_max_current": "Maximaler Motorstrom", "storage_time": "Energiespeicherzeit des Motors", "Chan_A_motor_start_current": "Anlaufstrom des A-Phasenmotors", "Chan_A_motor_max_current": "Maximaler Strom des A-Phasenmotors", "Chan_A_storage_time": "Energiespeicherzeit des A-Phasen-Motors", "Chan_B_motor_start_current": "Anlaufstrom des Phase B Motors", "Chan_B_motor_max_current": "Maximaler Strom des B-Phasen-Motors", "Chan_B_storage_time": "Energiespeicherzeit des B-Phasen-Motors", "Chan_C_motor_start_current": "Anlaufstrom des C-Phasenmotors", "Chan_C_motor_max_current": "Maximaler Strom des C-Phasenmotors", "Chan_C_storage_time": "Energiespeicherzeit des Motors in C-Phase", "serialPort": "Serieller Port", "baudRate": "<PERSON><PERSON>", "dataBit": "Datenbits", "stopBit": "Bit stoppen", "checkBit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "singleCollect": "<PERSON><PERSON><PERSON>", "sampling": "Sammeln", "serverIP": "Server IP", "serverPort": "Server Port", "NTPserverIp": "NTP Server IP", "NTPserverPort": "NTP Server Port", "netType": "Netzwerktyp", "deviceIp": "Host-IP", "subnetMask": "Subnetzmaske", "gateway": "Standardgateway", "networkAPN": "Netzwerkzugang APN", "userName": "<PERSON><PERSON><PERSON><PERSON>", "passWord": "Passwort", "deviceWorkGroup": "Nummer der Host-Arbeitsgruppe", "frequency": "Netzfrequenz", "pdSampleInterval": "PD Probenahmeintervall", "spaceSecond": "&Nbsp; zweite", "meuSampleInterval": "MEU-Probenahmeintervall", "monitorAwakeTime": "Schlafintervall", "monitorSleepSpace": "Aufwachen-Intervall", "wakeStartTime": "Beginn der Aufwachzeit (Stunde)", "intervalServer": "Upload-Intervall (Server)", "intervalMinus": "Probenahmeintervall", "continuousAcquisitionTime": "Kontinuierliche Sammelzeit", "startSampleTime": "Beginn der Probenahme des Wirts", "spacePoint": "&Nbsp; Spot", "startSampleInterval": "Probenahmeintervall für Hoststart", "hour": "Stunde", "poerOffSpace": "A<PERSON><PERSON><PERSON>tervall", "powerOnSpace": "Startintervall", "dateRange": "Datumsbereich", "synchronizing": "Synchroni<PERSON><PERSON>", "synchronize": "Synchronisation", "amplitude": "Amplitude", "switch": "<PERSON><PERSON><PERSON>", "copyRight": "© 2020 PMDT Ltd", "S1010Name": "Host für Datenerfassung", "modbusCompanySelf": "Huacheng.", "logOut": "<PERSON>den", "logOutTitle": "Abmeldebestätigung", "logOutConfirm": "Die aktuelle Benutzeranmeldung beenden?", "logOutTips": "Ausgemeldet", "enterHost": "<PERSON>te geben Si<PERSON> den Hostnamen ein", "selectDevTip": "Bitte wählen Sie ein Gerät", "stopSyncDataTip": "<PERSON><PERSON>ssen Sie die Synchronisierung von Daten beenden?", "modbusAddress": "ModBus-<PERSON><PERSON><PERSON>", "modbusAddressCheckTip": "ModBus Adressen reichen von 1 bis 254 Ganzzahlen", "deviceWorkGroupCheckTip": "Die Zahl der Host-Arbeitsgruppe ist eine Ganzzahl, Bereich: [{1}~{2}]", "emptyMonitorDatas": "Hostdaten löschen", "emptyMonitorDatasTip": "Löschen aller historischen Daten im Sammlungshost", "emptyMonitorDatasSuccess": "Erfolgreich bereinigte Daten", "emptyMonitorDatasApply": "Der Antrag auf Clearing-Daten wurde eingereicht und steht noch aus der Prüfung", "resetSoftSet": "Werkseinstellungen", "resetSoftSetTip": "Wiederherstellung aller Konfigurationen im Sammlungshost auf den Werkszustand", "resetSoftSetSuccess": "Der Antrag auf Werksreset wurde zur Überprüfung eingereicht", "continueConfirmTip": "Diese Operation ist nicht wiederherstellbar. Möchten Si<PERSON> fortfahren?", "bias_data": "Der Bias-Wertebereich ist -100-100", "back_ground_data": "Hintergrundwerte reichen von 0 bis 100", "sam_point": "Der Abtastfrequenzbereich für jeden Zyklus beträgt 20-2000", "sam_rate": "Der Abtastfrequenzbereich für jeden Zyklus beträgt 20-2000", "smartSensorStatus": "Statustabelle", "upThreshold": "<PERSON><PERSON><PERSON><PERSON>", "lowerThreshold": "<PERSON><PERSON><PERSON>", "changeThreshold": "Schwellenwert ändern", "changeThresholdLimit": "Schwelleneinstellung unterstützt nur eine Dezimalstelle", "upThresholdTip": "<PERSON><PERSON><PERSON>", "lowerThresholdTip": "<PERSON><PERSON><PERSON> Schwellenbereich", "changeThresholdTip": "Schwellenbereich ändern", "upLowerThresholderrTip": "Die untere Schwelle darf nicht größer als die obere Schwelle sein", "monitorName": "Hostname", "monitorType": "Hosttyp", "MonitorTypeHanging": "Wandaufhängung", "MonitorType2U": "2U", "MonitorTypeCollectionNode": "Aggregationsknoten", "MonitorTypeLowPower": "Solar-Host mit geringer Leistung", "devicePMSCode": "Gerätecodierung", "forceChange": "Modiumschaltung", "forceChangeSuccess": "Modus-Umschaltung erfolgreich", "currentVersion": "aktuelle Version", "abnormalRecover": "Abnormale Selbstheilung", "fromLabel": "Startzeit", "toLabel": "Endzeit", "select": "<PERSON><PERSON>", "sunday": "Tag", "monday": "eine", "tuesday": "zwei", "wednesday": "drei", "thursday": "vier", "friday": "f<PERSON>nf", "saturday": "sechs", "January": "<PERSON><PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON><PERSON>", "April": "April", "May": "<PERSON>", "June": "<PERSON><PERSON>", "July": "<PERSON><PERSON>", "August": "August", "September": "September", "October": "Oktober", "November": "November", "December": "Dezember", "all": "ganz", "strong": "stark", "weak": "schwach", "poor": "Unterschied", "min": "<PERSON><PERSON><PERSON>", "second": "zweite", "uploadInterval": "Upload-Intervall (min)", "loginWelcome": "Willkommen zum Login", "dateStartIsAfterDateEnd": "Startdatum ist größer als Enddatum, bitte wählen Si<PERSON> erneut", "maxCurrent": "Maximaler Strom", "standMobus": "<PERSON><PERSON><PERSON>", "config1": "Konfiguration 1", "config2": "Konfiguration 2", "fWrnThreshold": "Aufmerksamkeitsschwelle", "fAlmThreshold": "Alarmschwelle", "regularDormancy": "Zeitlicher Schlaf", "noDormancy": "<PERSON><PERSON> ruhend", "soakingWater": "Eintauchen", "dry": "<PERSON><PERSON><PERSON><PERSON>", "normal": "normal", "alarm": "Alarm", "lightGunCamera": "Pistolenmaschine für sichtbares Licht", "lightBallCamera": "Maschine für sichtbares Licht", "infraredGunCamera": "Infrarot-Fernglas-Pistole Maschine", "infraredBallCamera": "Infrarot binoku<PERSON><PERSON>", "maxTemp": "Höchsttemperatur", "maxTempPosition": "Maximale Temperaturlage", "devIPError": "Der IP ist bereits belegt", "unknown": "unbekannt", "fault": "<PERSON><PERSON>", "lowPower": "<PERSON><PERSON><PERSON><PERSON>", "mediumPower": "<PERSON><PERSON><PERSON>", "highPower": "Hohe Batterie", "slaveid": "Slave-ID", "LoraFrequency": "Hostbereich", "AREA_CHINA": "China", "AREA_VIETNAM": "Vietnam (AS1)", "AREA_MALAYSIA": "Malaysia (AS1)", "AREA_EUROPE": "Europa", "AREA_US": "Amerika", "AREA_INDONESIA": "Indonesien (AS2)", "AREA_INDIA": "Indien", "AREA_KOREA": "Korea", "AREA_CHINA_RSV": "China (Backup)<2>", "getLogFileError": "Abrufen der Sensorprotokolldatei fehlgeschlagen", "exportLogError": "Die Protokolldatei existiert nicht, Export fehlgeschlagen", "alertSyncProgress": "Fortschritt bei der Synchronisierung der Alarmdaten: {1}", "alertSyncProgress2": "Fortschritt bei der Synchronisierung der Alarmdaten [{1}]", "FIRMWARE_EXCEPTION": "Firmware Ausnahme", "AD_INITIALIZATION_EXCEPTION": "Ausnahme für AD-Initialisierung", "REFERENCE_VOLTAGE_EXCEPTION": "Abnormale Referenzspannung", "ONCHIP_FLASH_EXCEPTION": "On Chip <PERSON>", "OFFCHIP_FLASH_EXCEPTION": "Off Chip Flash <PERSON>", "SYSTEM_PARAMETERS_EXCEPTION": "Abnormale Systemparameter", "SAMPLE_PARAMETERS_EXCEPTION": "Abnormale Sammelparameter", "CALIBRATION_PARAMETERS_EXCEPTION": "Abnormale Kalibrierparameter", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "Systemparameter anormale Wiederherstellung", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "Abnormale Wiederherstellung der Sammelparameter", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "Abnormale Wiederherstellung der Kalibrierparameter", "LORA_MODULE_EXCEPTION": "Anomalie des LoRa-Moduls", "PHASE_NUM": "<PERSON><PERSON><PERSON>", "DISCONNECT_TIME": "Trennzeit", "DATA_TOTAL_COUNT": "Datenvolumen insgesamt", "ONLINE_RATE": "Online-<PERSON><PERSON><PERSON>", "aduInfoSetProgress": "Sensorkonfiguration: {1} ({2})", "aduInfoSetProgress2": "Sensorkonfiguration [{1}] ({2})", "aduInfoSetting": "Sensor konfigurieren", "aduInfoSetEnd": "Sensorkonfiguration abgeschlossen", "aduDataSample": "Datenerfassung", "aduInfoSet": "Sensorkonfiguration", "errorDataSampleRunning": "Der Sensorerfassungsservice ist derzeit in Bearbeitung. Bitte versuchen Sie es nach Abschluss noch einmal.", "errorAduInfoSetRunning": "Der Service zur Einstellung der Sensorparameter ist derzeit im Gange. Bitte versuchen Sie es nach Abschluss erneut.", "SAMPLE_PERIOD": "Probenahmezyklusnummer", "SF6_Density": "SF6 Gasdichte (P20)", "SF6_Temp": "SF6-Gastemperatur", "Device_env_Temp": "Umgebungstemperatur der Ausrüstung", "Alarm_Status": "Identifizierung des Alarmstatus", "SF6_Alarm_Low_Pressure": "Niederdruckalarm", "SF6_Alarm_Low_Voltage_Block": "Niederspannungsabsperrung", "SF6_Alarm_Over_Voltage": "Überspannungs-Alarm", "AlarmType": "Alarmtyp", "AlarmData": "Alarminhalt", "AlarmTime": "Alarmzeit", "AlarmLevel": "Alarmstufe", "AlarmStateWarning": "Frühwarnung", "WarningValue": "Warnwert", "AlarmValue": "Alarmwert", "SetSuccess": "Erfolgreich gesetzt", "AlarmDate": "Alarmzeit", "AlarmConfirm": "Alarmbestätigung", "Confirm": "bestätigen", "Confirmed": "Bestätigt", "AllData": "<PERSON><PERSON> Daten", "CheckManagerSponsor": "Initiator", "CheckManagerCheckType": "Prüfungstyp", "CheckManagerDate": "Ursprungsdatum", "CheckManagerTarget": "<PERSON><PERSON><PERSON><PERSON> von", "CheckManagerExtraInfo": "Zusätzliche Informationen", "CheckManagerResult": "Zustimmen oder nicht", "Refuse": "<PERSON><PERSON><PERSON>", "Agree": "verein<PERSON>en", "DataExport": "Datenexport", "DataExportStep1": "<PERSON><PERSON><PERSON>en Sie die zu exportierenden Daten aus", "DataExportStep1Title": "Dateiauswahl exportieren", "DataExportStep2": "Berechnung der Datengröße", "DataExportStep2Title": "Größe der Rohdaten berechnen", "DataExportStep3": "Verpackung komprimierter Daten", "DataExportStep3Title": "Verpackung komprimierter Daten", "DataExportStep4": "Datenkomprimierung abgeschlossen", "DataExportStep4Title": "Komprimierte Daten abgeschlossen", "DataExportCheckData": "Datenbank [/media/data/database]", "DataExportCheckDataFile": "Datendatei [/media/data/datafile]", "DataExportCheckLog": "Protokolldatei [/media/data/log]", "DataExportCheckConfig": "Konfigurationsdatei [/home/<USER>/config.xml]", "DataExportBegin": "Export wird gestartet", "SelectAtLeastOneTips": "Mindestens ein Element auswählen", "DataExportFileSizeError": "Die Dateigröße übersteigt 2000MB. Bitte verwenden Sie Tools wie Winscp, um die Daten zu exportieren", "DataExportFileSizeZeroError": "Die ausgewählte Dateigröße beträgt 0", "DataExportCancelTips": "<PERSON><PERSON> von Daten abgebrochen", "DataExportDataCompressTips": "Die ursprüngliche Datengröße beträgt {1} MB, und die geschätzte Komprimierungszeit beträgt [{2} min {3} s]", "LogExportStep1": "Sensor auswählen", "LogExportStep1Title": "Sensor auswählen", "LogExportStep2": "Protokolldatei abrufen", "LogExportStep2Title": "Protokolldateien abrufen", "LogExportStep3": "Protokolldatei abschließen", "LogExportStep3Title": "Protokolldatei abschließen", "LogExportStep4": "Protokolldateien packen und komprimieren", "LogExportStep4Title": "Komprimierte Protokolldateien verpacken", "LogExportStep5": "Kompression abgeschlossen", "LogExportStep5Title": "Kompression abgeschlossen", "LogExportCancelTips": "Exportprotokoll abgebrochen", "batteryVoltage": "<PERSON><PERSON><PERSON><PERSON><PERSON>ng", "superCapvoltage": "Superkondensatorspannung", "OverPressureThreshold": "Überspannungsschwelle", "LowPressureThreshold": "Niederspannungsschwelle", "ShutThreshold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PhysicalChannelType": "Physischer Kanaltyp", "GasAbsPressure": "Absolutdruck des Gases", "GasGaugePressure": "Gasmessdruck", "CameraType": "Kameratyp", "DEFAULT_CHECK_Tips": "Das Nummerierungsformat ist m: n, wie z.B. 1:100, wobei m von [1 bis 255] und n von [0 bis 65535] reicht.", "VibrationSY_OUT_CHECK_Tips": "Das Nummerierungsformat lautet Adresse: modbus: id, wie z.B. 1:0:1917, wobe<PERSON> der Adressbereich [1-255] und modbus/id [0-65535] ist.", "NOISEWS_OUT_CHECK_Tips": "Das Nummerierungsformat ist ip: x: y, wie *********:1321:4512, wobei das ip-Format [xxx. xxx. xxx. xxx] und x/y Zeichenfolgen der Länge 4 sind", "IR_DETECTION_OUT_IO_CHECK_Tips": "Das Nummerierungsformat ist ip: port: ss: se: cs: ce, wie 1:100, wobei ip-format [xxx. xxx. xxx. xxx] port<PERSON><PERSON><PERSON> [0-65535] ss-<PERSON><PERSON><PERSON> [0-255], se-<PERSON><PERSON>ich 0-255], cs-<PERSON><PERSON><PERSON> 0-255], ce-<PERSON><PERSON><PERSON> 0-255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "Das Nummerierungsformat ist ip: port: x: y, z. B. 1:100, wobei ip-format [xxx. xxx. xxx. xxx] port<PERSON><PERSON><PERSON> [0-65535] x bereich [1-255], y bereich [0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "Das Nummerierungsformat ist m: n, wie z.B. 1:100, wobei m von [1 bis 255] und n von [0 bis 65535] reicht.", "fWrnThreshold_CHECK_Tips": "<PERSON><PERSON>, dass der Schwellenbereich [10-1000] mA beträgt", "fAlmThreshold_CHECK_1_Tips": "Der Alarmschwellenbereich beträgt [50-5000] mA", "fAlmThreshold_CHECK_2_Tips": "Die Alarmschwelle sollte größer als die Aufmerksamkeitsschwelle sein", "AuditContent": "Prüfungsinhalte", "OPTime": "Betriebszeit", "Executed": "implementieren", "UserManager": "Benutzerverwaltung", "SystemManagement": "Systemverwaltung", "BusinessManagement": "Unternehmensführung", "LanguageChange": "Sprachwechsel", "AlarmManagement": "Alarmverwaltung", "DataOperation": "Datenbetrieb", "BackupRecovery": "<PERSON><PERSON>", "AddUser": "Benutzer hinzufügen", "AddUserCheck": "Benutzeraudit hinzufügen", "UserRightSet": "Benutzerberechtigungseinstellungen", "UserRightSetCheck": "Überprüfung der Benutzerberechtigungseinstellungen", "DeleteUser": "Benutzer löschen", "DeleteUserConfirm": "Benutzerbestätigung löschen", "FreezeUser": "Benutzer einfrieren", "FreezeUserConfirm": "Benutzerbestätigung einfrieren", "UnlockUser": "Benutzer entsperren", "UnlockUserConfirm": "Benutzerbestätigung entsperren", "FileStationSet": "Profil (Site) Einstellungen", "FileDeviceSet": "Archiv (Equipment) Einstellungen", "SenserSet": "Sensoreinstellungen", "AlarmSet": "Alarmeinstellungen", "SavePointSet": "Messpunkteinstellungen speichern", "DelPointSet": "Messpunkteinstellungen löschen", "SingleSample": "Einzelerwerb", "DataBackup": "Datensicherung", "DataRecovery": "Datenwiederherstellung", "ClearDataCheck": "Datenaudit löschen", "RestoreFactorySettingsReview": "Überprüfung der Werkseinstellungen", "SaveMainStation": "Master Station speichern", "DataView": "Datenansicht", "DataSync": "Datensynchronisation", "RightSet": "Berechtigungskonfiguration", "GetUserList": "Benutzerliste abrufen", "AuditData": "Auditdaten", "AuditPermissions": "Prüfberechtigungen", "MainStationSet": "Master Station Einstellungen", "ChangePasswordSelfOnly": "Passwort ändern (nur für diesen Benutzer)", "ChangePasswordForNormal": "Ändern eines normalen Benutzerkennworts", "ChangeUserRight": "Benutzerberechtigungen ändern/festlegen", "ChangePassword": "Passwort ändern", "ChangeLoginInfo": "Anmeldeinformationen ändern", "UserStatus": "Benutzerstatus", "UserLanguage": "Benutzersprache", "HasLogin": "Haben <PERSON> sich schon einmal e<PERSON>loggt?", "RoleType": "Rollentyp", "IsPassTimeout": "Ist das Passwort Timeout", "AuditsManagement": "Audit Management", "SensorOperation": "Sensorbetrieb", "SensorLogExport": "Sensor Log Export", "SensorAlarmDataSync": "Synchronisierung von Sensoralarmdaten", "ViewSensorAlarmData": "Alarmdaten des Sensors anzeigen", "Block": "Einfrieren", "BlockPendingReview": "Einfrieren bei ausstehender Überprüfung", "UnlockPendingReview": "Aufheben des Einfrierens von ausstehender Überprüfung", "DeletePendingReview": "Die ausstehende Überprüfung löschen", "AddUserPendingReview": "Neu bei ausstehender Überprüfung", "ChangePassPendingReview": "Passwort bei ausstehender Überprüfung ändern", "ChangeRightPendingReview": "Berechtigungen für ausstehende Überprüfung ändern", "abnormal": "anormal", "DataQueryNotSupported": "Derzeit wird die historische Datenabfrage dieses Front-End-Typs nicht unterstützt", "DeviceCodeExists": "Der Gerätename oder der Gerätecode existiert bereits", "OutputSwitchConfig": "Konfiguration des ausgehenden Schalters", "OutputSwitch": "Ausgangsschalter", "MainStationAuxRtuID": "Hilfssteuerung RtuID", "MainStationIedRtuID": "RtuID sammeln", "MainstationParams1": "Hauptbahnhof Parameter 1", "MainstationParams2": "Hauptbahnhof Parameter 2", "MainstationInterface1": "Master Station 1 Schnittstelle", "MainstationInterface2": "Master Station 2 Schnittstelle", "Port": "Hafen", "IPAddress": "IP-Adresse", "EnterUriTips": "<PERSON>te geben Sie die Pfad-URI ein", "ConnectUserName": "Kommunikations-Benutzername", "EnterConnectUserNameTips": "Bitte geben Sie den Kommunikationsnamen ein", "ConnectPass": "Kommunikationspasswort", "EnterConnectPassTips": "Bitte geben Sie das Kommunikationspasswort ein", "DataSubmissionInterval": "Intervall zum Hochladen von <PERSON>", "EnderDataSBIntervalTips": "Bitte geben Sie das Eingabeintervall ein", "HeartbeatInterval": "Herzschlagintervall", "EnterHertbeatIntervalTips": "Bitte geben Sie das Herzschlagintervall ein", "ConnectStatus": "Verbindungss<PERSON>us", "Reset": "Z<PERSON>ücksetzen", "Submit": "<PERSON><PERSON><PERSON><PERSON>", "RtuidRepeatTips": "Hilfssteuerung RtuID und Sammlung RtuID können nicht identisch sein", "DataSubmissionIntervalTips": "Das Datenübermittlungsintervall darf nicht kleiner als 60 sein", "HeartbeatIntervalTips": "Herzschlagintervall darf nicht kleiner als 60 sein", "MainstationParamsSaveSuccess": "Master Station Parameter erfolgreich gespeichert", "MainstationParamsSaveFailed": "Speichern der Master Station Parameter fehlgeschlagen", "OutSwitchSaveSuccess": "Die Konfiguration des ausgehenden Switches wurde erfolgreich gespeichert", "OutSwitchSaveFailed": "Speichern der Konfiguration des ausgehenden Switches fehlgeschlagen", "ChartConfig": "Diagrammkonfiguration", "Measurement": "Messwert", "SENSOR_TIMEOUT_COUNT": "An<PERSON>hl der Sensor-Offline-Bestimmungen", "OUTWARD_CONFIG_ENABLE_STATE": "Konfigurations<PERSON><PERSON>", "OUTWARD_CONFIG_ENABLED": "Aktiviert", "OUTWARD_CONFIG_DISABLED": "Deaktiviert", "OUTWARD_CONFIG_ENABLING": "Wird aktiviert", "SENSOR_TIMEOUT_DISABLING": "<PERSON>ir<PERSON>", "ERROR_NO_CONFIG": "Konfiguration existiert nicht", "ERROR_INPUT_PARAMS": "Eingabeparameterfehler", "ERROR_INTEGER": "<PERSON>te geben Si<PERSON> eine ganze <PERSON> ein", "ERROR_TIP_RANGE": "Wertebereich [{1}~{2}]", "ERROR_IP_FORMAT": "Das IP-Format ist falsch", "ERROR_FILE_NAME": "Der Dateiname muss {1} sein", "ERROR_FILE_SUFFIX": "<PERSON> Dateiendung muss {1} sein", "ERROR_FILE_SIZE_LESS_THAN_MB": "Die Datei muss kleiner als {1} MB sein", "ERROR_T2_T1": "T2 muss kleiner als T1 sein", "LENGTH_COMM_LIMIT": "<PERSON>te <PERSON>ten <PERSON>, dass dieses Feld {1} Zeichen nicht überschreiten darf.", "MANU_FAST_SYNC_DATA": "Schnellsynchronisation", "SYNC_DATA_STATE_LIST": "Liste der Synchronisationsstatus der Sensoren", "LAST_SYNC_DATE_RANGE": "Letzter Synchronisationszeitraum", "NoError": "<PERSON><PERSON>", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "Synchronisationstimeout", "SYNC_DATA_ERROR_DISCONNECT": "Verbindung getrennt", "SYNC_DATA_STATUS_WAITING": "<PERSON><PERSON> auf Verbindung", "SYNC_DATA_STATUS_CONNECTED": "Verbunden", "SYNC_DATA_STATUS_SYNCING": "Wird synchronisiert", "SYNC_DATA_STATUS_SUCCESS": "Synchronisation erfolgreich", "SYNC_DATA_STATUS_FAILED": "Synchronisation fehlgeschlagen", "SYNC_DATA_STATUS_CANCELED": "Synchronisation abgebrochen", "Today": "<PERSON><PERSON>", "Yesterday": "Gestern", "LAST_7_DAYS": "Letzte 7 Tage", "LAST_30_DAYS": "Letzte 30 Tage", "THIS_MONTH": "<PERSON><PERSON>", "LAST_MONTH": "Letzter Monat", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Starten des Kommunikationsdienstes fehlgeschlagen", "FastSyncDataCancelTips": "Möchten Sie die Datensynchronisation wirklich abbrechen?", "FastSyncDataSelectTips": "Bitte wählen Sie die Sensoren für die Datensynchronisation aus", "Band-pass": "Band-pass", "aeWaveChartTitle": "AE Wellenform-Spektrum", "aePhaseChartTitle": "AE Phasenspektrum", "aeFlyChartTitle": "AE Flugspektrum", "LOCAL_PARAMS_TIME": "Zeit", "LOCAL_CHART_COLORDENSITY": "Far<PERSON>dic<PERSON><PERSON>", "openTime": "Öffnungszeit", "closeTime": "Schließzeit", "triggerUnit": "Auslöseamplitude[μV]", "openTimeUnit": "Öffnungszeit[μs]", "closeTimeUnit": "Schließzeit[μs]", "triggerUnitTips": "Trigger Amplitudenbereich: [{1},{2}]μV", "TOP_DISCHARGE_AMPLITUDE": "TOP3 Entladungsamplitude", "WS_901_ERROR_TIPS": "Der Befehlspush-Dienst wird registriert, bitte versuchen Sie es nach 2 Sekunden erneut"}