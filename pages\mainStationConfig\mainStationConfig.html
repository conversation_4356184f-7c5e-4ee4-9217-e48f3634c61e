<!--
 * @Author: liyaoxu <EMAIL>
 * @Date: 2023-09-21 16:10:28
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-12-07 09:31:41
 * @FilePath: \pds_z058_web\pages\mainStationConfig\mainStationConfig.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style>
    .main-station-label-content {
        display: flex;
        align-items: center;
    }

    .ms-common-param-content {
        display: flex;
        margin: 10px -18px;
        border-bottom: 1px solid #f4f4f4;
        padding: 8px;
    }

    .ms-common-param-item {
        display: flex;
        align-items: center;
    }

    #tableMainStationConfig .option {
        display: flex;
        width: 100%;
        justify-content: center;
    }

    #tableMainStationConfig .option-icon {
        font-size: 22px;
        width: 30px;
        margin: 5px;
        cursor: pointer;
    }
    #tableMainStationConfig .option-icon:hover {
        color:cadetblue;
    }
    #tableMainStationConfig .option-icon:active {
        color:blue;
    }

    .station-config-unit{
        position: absolute;
        right: 15px;
        border: 1px solid #dddddd;
        padding: 6px;
        text-align: center;
        min-width: 36px;
    }

    .station-config-param-content{
        /* display: flex;
        align-items: center; */
    }
</style>
<section class="content-header" style="padding: 25px">
  <h1 style="display: flex;">
    <i class="fa fa-dashboard"></i> <div style="margin-left:8px;" data-i18n="settings">系统设置</div>
    <small style="display: flex;margin:8px"> > <div style="margin-left:8px;" data-i18n="ConnectionProtocolConfiguration">主站设置</div></small>
</h1>
</section>
<section class="content">
    <div class="row" style="margin: auto;">
        <div class="col-md-12">
            <div class="box no-border no-margin">
                <div class="box-body">
                    <div class="tab-content " style="padding:0px;height: 90%">
                        <div class="tab-pane full-height active" id="main_station_tab">
                            <div id="mainStationToolbar" class="tableTitleBoxChild">
                                <div class="btn-group" style="margin-left: 0px;">
                                    <button type="button" class="btn btn-default btn-sm" style="outline: none;" onclick="addMainStationConfig()">
                                        <i class="fa fa-plus-circle" style="font-size: 14px;"><span style="padding-left: 3px"
                                            data-i18n="add">增加</span></i>
                                    </button>
                                </div>
                            </div>
                            <table id="tableMainStationConfig" class="table table-bordered table-hover full-height">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</section>
<script type="text/javascript">
    initPageI18n();
    initMainStationConfigList();
    var MAIN_STATION_VALIDATE = {
        message: '',
        feedbackIcons: {
            validating: 'glyphicon glyphicon-refresh'
        },
        fields: {
            slaveAddress: {
                validators: {
                    regexp: {
                        regexp: /(.*)/,
                    },
                    callback: {
                        message: getI18nName('modbusAddressCheckTip'),
                        callback: function (value, validator) {
                            if (value.trim() === '' || typeof parseInt(value) !== 'number' || parseInt(
                                    value) < 0 || parseInt(value) > 254) {
                                return false;
                            } else {
                                return true;
                            }
                        }
                    }
                }
            },
        }
    };
</script>