<!--
 * @Author: liya<PERSON>u <EMAIL>
 * @Date: 2023-09-22 15:24:58
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-03-06 18:16:38
 * @FilePath: \pds_z058_web\indexTest.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!--<title>PMDTiSmart</title>-->
    <title id="prTitle">loading...</title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <script src="./plugins/jquery/jquery-2.2.3.min.js"></script>
    <!-- Pace style -->
    <link rel="stylesheet" href="./plugins/pace/pace.min.css">
    <link rel="stylesheet" href="./plugins/bootstrap/css/bootstrap.min.css">
    <!--<link rel="stylesheet" href="./plugins/multiselect/multiselect.css" media="all"/>-->
    <link rel="stylesheet" href="./plugins/select2/select2.css">
    <!--<link rel="stylesheet" href="./plugins/bootstrap-fileinput/fileinput.min.css" media="all"/>-->
    <!--<link rel="stylesheet" href="./plugins/bootstrap-switch/bootstrap-switch.css">-->
    <!--<link rel="stylesheet" href="./dist/css/skins/_all-skins.min.css">-->
    <link rel="stylesheet" href="./plugins/timepicker/bootstrap-timepicker.min.css">
    <link rel="stylesheet" href="./plugins/bootstrap-treeview/bootstrap-treeview.min.css">
    <link rel="stylesheet" href="./plugins/bootstrap-dialog/bootstrap-dialog.min.css">
    <link rel="stylesheet" href="./plugins/datatables/bootstrap-table.css">
    <link rel="stylesheet" href="./plugins/bootstrap-validator/bootstrapValidator.min.css">
    <link rel="stylesheet" href="./plugins/daterangepicker/daterangepicker.css">
    <link rel="stylesheet" href="./dist/css/font-awesome.css">
    <link rel="stylesheet" href="./plugins/bootstrap-notify/bootstrap-notify.css" media="all" />
    <link rel="stylesheet" href="./plugins/zTree/css/zTreeStyle/zTreeStyle.css" type="text/css">
    <link rel="stylesheet" href="./dist/css/AdminLTE.css">
    <!--<link rel="stylesheet" href="./dist/css/alt/AdminLTE-without-plugins.min.css">-->
    <link rel="stylesheet" href="./dist/css/skins/_all-skins.css">
    <link rel="stylesheet" href="./plugins/bootstrap-slider/slider.css">
    <link rel="stylesheet" href="./plugins/step/step.css">
    <link rel="stylesheet" href="./dist/css/switch.css">
    <script src="./plugins/bootstrap/js/bootstrap.min.js"></script>
    <script src="./dist/js/loading.js"></script>
    <!--<script src="./plugins/html5shiv/html5shiv.min.js"></script>-->
</head>
<style>
    .table th,
    .table td {
        text-align: center;
        vertical-align: middle !important;
    }

    a.close {
        margin: 0px 10px 5px 10px
    }

    #i-progress-detail i {
        height: 51px;
        width: 42px;
        background-color: transparent;
        margin-top: -15px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-left: 1px solid #dddddd;
        border-right: 1px solid #dddddd;
    }

    #i-progress-detail i:hover {
        background-color: #d2d6de;
    }

    #commonProgressDetial-btn-g {
        position: absolute;
        top: 0;
        height: 38px;
        right: 45px;
        display: flex;
    }

    #commonProgressDetial-btn-g button {
        background-color: transparent;
        border-radius: 0;
        border: none;
        border-left: 1px solid #ddd;
        border-right: 1px solid #ddd;
        padding: 5px;
        width: 80px;
    }

    #commonProgressDetial-success {
        padding: 0 15px;
        margin-top: 5px;
        overflow: auto;
        height: 88%;
    }

    #commonProgressDetial-success i {
        color: green;
        font-size: 18px;
        margin-right: 5px;
        padding-top: 5px;
    }
</style>

<body class="hold-transition skin-blue sidebar-mini sidebar-collapse" id="mainbody"
    style="display: block;min-width: 800px;">
    <div class="wrapper">
        <header class="main-header">
            <!--<a href="#" class="logo" onclick="goMonitorTb();">-->
            <!--<span class="logo-mini"><img src="dist/img/ic_logo_pmdt.png" class="img-circle"-->
            <!--style="margin-top: 0px;padding: 5px"></span>-->
            <!--<span class="logo-lg" style="font-size: 18px;"><img src="dist/img/ic_logo_pmdt.png"-->
            <!--class="img-circle"-->
            <!--style="margin-top: 0px;padding: 5px"-->
            <!--alt="Logo Image">PMDTiSmart</span>-->
            <!--</a>-->
            <a href="#" class="logo" onclick="goMonitorTb();">
                <span class="logo-mini"><img id="logoMini" src="dist/img/logo.png" class="img-circle"
                        style="margin-left: 4px;margin-top: -2px;padding: 5px;"></span>
                <span class="logo-lg" style="font-size: 16px;"><img id="logoFull" src="dist/img/logo.png"
                        class="img-circle" style="margin-top: 0px;margin-right: -5px;margin-left: 5px;padding:5px;"
                        alt="Logo Image">
                    <span style="margin-left: 0px;" id="menuTitle"></span>
                </span>
            </a>
            <nav class="navbar navbar-static-top">
                <a id="sidebar-toggle" href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
                </a>
                <a href="#" style="padding-left: 15px"><span class="page-title"></span></a>
                <div class="navbar-custom-menu">
                    <ul class="nav navbar-nav">
                        <li id="return">
                            <!--<a href='./pdchart/pdchart.html'><i class="fa fa-refresh"></i></a>-->
                            <a href="#" onclick="goMonitorTb();"><i class="fa fa-home"></i></a>
                        </li>
                        <li id="reload">
                            <!--<a href='./pdchart/pdchart.html'><i class="fa fa-refresh"></i></a>-->
                            <a href='index.html?t='><i class="fa fa-refresh"></i></a>
                        </li>
                        <li class="dropdown tasks-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <i class="fa fa-signal"></i>
                            </a>
                            <ul class="dropdown-menu">
                                <li>
                                    <ul class="menu">
                                        <li id="memory">
                                            <a href="#">
                                                <h3></h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar" role="progressbar" aria-valuemin="0"
                                                        aria-valuemax="100"></div>
                                                </div>
                                            </a>
                                        </li>
                                        <li id="cpu">
                                            <a href="#">
                                                <h3>
                                                    <small class="pull-right"></small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar" role="progressbar" aria-valuemin="0"
                                                        aria-valuemax="100"></div>
                                                </div>
                                            </a>
                                        </li>
                                        <li id="disk">
                                            <a href="#">
                                                <h3>
                                                    <small class="pull-right"></small>
                                                </h3>
                                                <div class="progress xs">
                                                    <div class="progress-bar" role="progressbar" aria-valuemin="0"
                                                        aria-valuemax="100"></div>
                                                </div>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            </ul>
                        </li>
                        <li class="dropdown user user-menu">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                <img src="dist/img/user-white.png" class="user-image" alt="User Image">
                                <span class="hidden-xs" id="showLoginName">Guest</span>
                            </a>
                            <ul class="dropdown-menu">
                                <!-- User image -->
                                <li class="user-header">
                                    <img src="dist/img/user-white.png" class="img-circle" alt="User Image">
                                    <div>
                                        <label for="loginTime" style="color: white;" data-i18n="loginTime">登录时间</label>
                                        <p id="loginTime" style="color: white;">2017/7/7 15:15:14
                                        </p>
                                    </div>
                                </li>
                                <!-- Menu Footer-->
                                <li class="user-footer">
                                    <div class="pull-left" id="user_change_pw">
                                        <a href="#" class="btn btn-default btn-flat" data-i18n="ChangePassword">修改密码</a>
                                    </div>
                                    <div class="pull-right" id="user_change">
                                        <a href="#" class="btn btn-default btn-flat" data-i18n="switch">切换</a>
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <li>
                            <a href="#" onclick="logOutConfirm()"><i class="fa fa-sign-out"></i></a>
                        </li>
                    </ul>
                </div>
            </nav>
        </header>
        <aside class="main-sidebar">
            <section class="sidebar">
                <ul class="sidebar-menu">
                </ul>
            </section>
        </aside>
        <div id="global" class="content-wrapper" style="min-height: 600px">
            <section class="content"></section>
        </div>
        <footer class="main-footer" style="height: 51px">
            <div class="pull-right hidden-xs">
                <b id="version-index">V1.0.24.1</b>
            </div>
            <div class="pull-right" id="i-progress-detail" style="margin: 0 8px;cursor: pointer;"
                onclick="progressUtil.showDetail(this)">
                <i class="fa fa-envelope">

                </i>
            </div>
            <div class="pull-right hide" id="i-progress" style="margin-right:5px">
                <span id="i-proglabel">
                </span>
                <div class="pull-right progress progress-striped active"
                    style="width:130px;height:19px;margin-left:5px;">
                    <div id="i-prog" class="progress-bar" role="progressbar" aria-valuenow="" aria-valuemin="0"
                        aria-valuemax="100" style="width:0%;align-items:center;justify-content:center;">
                        <span id="i-prognum"
                            style="display:block;width:100%;text-align:center;color:#444;margin-left:2px">0%
                        </span>
                    </div>
                </div>
            </div>

            <div class="pull-right hide" id="i-progress-common" style="margin-right:5px;cursor: pointer;">
                <span id="i-proglabel-common">
                </span>
                <div class="pull-right progress progress-striped active"
                    style="width:130px;height:19px;margin-left:5px;">
                    <div id="i-prog-common" class="progress-bar" role="progressbar" aria-valuenow="" aria-valuemin="0"
                        aria-valuemax="100" style="width:0%;align-items:center;justify-content:center;">
                        <span id="i-prognum-common"
                            style="display:block;width:100%;text-align:center;color:#444;margin-left:2px">0%
                        </span>
                    </div>
                </div>
            </div>

            <p data-i18n="copyRight"
                style="margin:0 0 0px;font-family: Arial,simsun,  'Source Sans Pro', 'Helvetica Neue', Helvetica, sans-serif;font-size: 14px">
                ©2020 华乘电气科技股份有限公司</p>
        </footer>
        <aside class="control-sidebar control-sidebar-dark">
            <div class="tab-content">
                <div class="tab-pane" id="control-sidebar-home-tab">
                </div>
            </div>
        </aside>
        <div class="control-sidebar-bg"></div>
        <div class='notifications top-right'></div>
    </div>
    <div id="projectContent" style="
     display: none;
     position: absolute;
     z-index: 1030;
     top: 0;
     height: 100%;
     width: 100%">

    </div>
    <div id="mainContent" style="
        display: none;
        position: absolute;
        z-index: 1000;
        top: 0;
        height: 100%;
        width: 100%">

    </div>
    <script src="./plugins/bootstrap-switch/bootstrap-switch.js"></script>
    <script src="./plugins/select2/select2.full.min.js"></script>
    <script src="./plugins/bootstrap-notify/bootstrap-notify.js"></script>
    <script src="./plugins/jquery/jquery.cookie.js"></script>
    <script src="./plugins/timepicker/bootstrap-timepicker.js"></script>
    <script src="./plugins/bootstrap-treeview/bootstrap-treeview.js"></script>
    <script src="./plugins/bootstrap-dialog/bootstrap-dialog.min.js"></script>
    <script src="./plugins/layer/layer.js"></script>
    <script src="./plugins/datatables/bootstrap-table.js"></script>
    <!--<script src="./plugins/datatables/bootstrap-table-locale-all.js"></script>-->
    <!--<script src="./plugins/datatables/locale/bootstrap-table-zh-CN.js"></script>-->
    <script src="./plugins/softkey/vk_loader.js?vk_layout=US US&vk_skin=flat_gray"></script>
    <script src="./plugins/select2/i18n/zh-CN.js"></script>
    <script src="./plugins/multiselect/multiselect.js"></script>
    <script src="./plugins/pace/pace.js"></script>
    <script src="./plugins/crypto-js/crypto-js.js"></script>

    <script src="./util/windowResize.js"></script>

    <script src="./config/config.js"></script>
    <script src="./dist/js/server.js"></script>
    <script src="./dist/js/apiUrl.js"></script>
    <script src="./dist/js/lang-i18n.js"></script>
    <script src="./plugins/daterangepicker/moment.js"></script>
    <script src="./plugins/daterangepicker/daterangepicker.js"></script>
    <script src="./plugins/bootstrap-validator/bootstrapValidator.min.js"></script>
    <!--<script src="./plugins/bootstrap-validator/bootstrapValidator.js"></script>-->
    <script role="reload" src="./dist/js/appMenu.js"></script>
    <script src="./dist/js/app.js"></script>
    <!--<script src="./dist/js/demo.js"></script>-->
    <!--自定义多语言管理模块 -->
    <script role="reload" src="./dist/js/base-modal.js"></script>
    <script role="reload" src="./dist/js/customPlugin.js"></script>
    <script role="reload" src="./dist/js/common.js"></script>
    <!--替换多语言标签 -->
    <script role="reload" src="dist/js/serverFunctions.js"></script>
    <!--<script src="./dist/js/skin-toggle.js"></script>-->
    <script src="./dist/js/jqSession.js"></script>
    <script src="./plugins/zTree/js/jquery.ztree.core.js"></script>
    <script src="./plugins/zTree/js/jquery.ztree.excheck.js"></script>

    <script role="reload" src="./dist/js/pdchart/data.js"></script>
    <script src="./dist/js/pdchart/pdcharts.js"></script>
    <script src="./dist/js/pdchart/canvg.js"></script>
    <script role="reload" src="./dist/js/progressUtil.js"></script>
    <script src="./plugins/bootstrap-slider/bootstrap-slider.js"></script>
    <script src="./plugins/echart/echarts.min.js"></script>
    <script src="./plugins/pdcharts/pdcharts.min.js"></script>

    <script role="reload" src="./common/commonEnum.js"></script>
    <script src="./plugins/rsa/jsencrypt.min.js"></script>
    <script src="./plugins/rsa/rsaUtil.js"></script>
    <script src="./plugins/step/step.js"></script>
    <script src="./util/common/commonFunction.js"></script>
    <script src="./util/jqueryExpendPlugins.js"></script>

    <script src="./pages/monitorTb/monitorTb.js"></script>
    <script src="./pages/alertmanager/alertmanager.js"></script>
    <script src="./pages/fileManage/file_manage.js"></script>
    <script src="./pages/instrumentSet/instrument_set.js"></script>
    <script src="./pages/modbus/modbus_setting.js"></script>
    <script src="./page_controller/arester/arrester_chart.js"></script>
    <script src="./page_controller/density_micro_water/dmwMain.js"></script>
    <script src="./page_controller/density_micro_water/history.js"></script>
    <script src="./page_controller/density_micro_water/trend.js"></script>
    <script src="./page_controller/vibration/vibration_page.js"></script>
    <script src="./pages/logmanager/logmanager.js"></script>
    <script src="./pages/checkManager/checkmanager.js"></script>
    <script src="./pages/instrumentSet/inst_out_sensor_control.js"></script>
    <script src="./pages/groundingcurrent/grounddingcurrent.js"></script>
    <script src="./pages/leakageCurrentChart/leakage_current.js"></script>
    <script src="./pages/alertSync/alertSync.js"></script>
    <script src="./pages/alertData/alertData.js"></script>
    <script src="./pages/groupCodeScan/groupCodeScan.js"></script>
    
    <script>
        var needLogin = true;

        var intent;
        var isS1010DevScreen = false;
        var version = 'V1.0.24.1';
        var versionSN = '0';
        var MONITOR_TYPE_CODE = '';
        var MONITOR_TYPE = '';
        var MONITOR_NAME = '';
        var initValueCount = 0;

        function checkNeedKeyboard() {
            if (EXPLORER_SCREEN_RECT_PARAMS.clientHeight <= 600 && EXPLORER_SCREEN_RECT_PARAMS.clientWidth <= 800) {
                //输入框vk=true，加载虚拟键盘
                $("input[vk=true]").focus(function () {
                    $.fn.keysoft($(this));
                });
            }
        }

        function checkLogin() {
            var isUserName = getCookie('user');

            var isLogOut = getCookie('isLogOut');
            var hasLogin = getCookie('hasLogin');
            if (hasLogin == 'true' || hasLogin == true) {
                $.session.clear();
                switch (isLogOut) {
                    case "logOut":
                        layer.msg(getI18nName('logOutTips'), {
                            icon: 1
                        });
                        break;
                    case "repeatLogin":
                        layer.msg(getI18nName('logOutTips'), {
                            icon: 1
                        });
                        break;
                    case "changePw":
                        layer.msg(getI18nName('PasswordChangedMSG'), {
                            icon: 1
                        });
                        break;
                    case "wsLogOut":
                        layer.msg(getI18nName('ServerDisConnectMSG'), {
                            icon: 1
                        });
                        break;
                }
                setTimeout(function(){
                    deleteCookie();
                    setCookie('isLogOut', null);
                },1000);
            }
            var loadUrl = ''
            isUserName === undefined || isUserName == 'null' ? loadUrl = 'loginContent.html' : loadUrl = 'mainContent.html';
            //            loadUrl = "mainContent.html"
            if (loadUrl == "loginContent.html") {
                $('#projectContent').show();
                $('#mainContent').load("./mainContent.html", function () {});
                $('#projectContent').load('./' + loadUrl);
            } else {
                var name = RSAUtil.encode(isUserName);
                //                var password = RSAUtil.encode(getStore('cookieName'));
                var password = getCookie('token');

                setSession('user', getCookie('user'));
                setSession('token', getCookie('token'));
                setSession('userInfo', getCookie('userInfo'));

                initWSonLogin(name, password);
                $('#mainContent').load("./mainContent.html", function () {
                    getADUStateColor(1, function () {
                        initUser();
                        $('.sidebar-menu').find('a')[0].click();
                    });
                });
            }

        }

        function initWSonLogin(tempName, password) {
            connectToWebSocket(true, function () {
                var message = {
                    "methodName": "WebSocketLogin",
                    "userName": tempName,
                    "password": password
                };
                webSocketSend(message);
            });
        }
        $(document).ready(function () {
            windowDebug = false;
            initLogin()
        });
        var initLoginFunc = function () {
            if (needLogin) {
                checkLogin()
            } else {
                $('#mainContent').load("./mainContent.html", function () {
                    Pace.stop();
                    getADUStateColor(1, function () {
                        initUser();
                        $('.sidebar-menu').find('a')[0].click();
                    });
                });
            }
        }

        function initLogin() {
            initDefServerUrl(function (resp) {
                var respData = resp.result;
                checkLanguage(() => {
                  initPageMonitoType(respData);
                  initGlobalParams();
                });
                poll('GET', GET_VERSION, "", function (response) {
                    version = 'V ' + response.result.version;
                    versionSN = response.result.SN;
                    $('#version-index').html(version);
                }, null, true);
            }, function () {
                layer.msg(getI18nName('CannotConnectServerMSG'));
                setTimeout(function () {
                    initLogin();
                }, 10000)
            });
        }

        function initPageMonitoType(respData) {
            if (respData.releaseEdition) {
                initBuildModeType(respData.releaseEdition)
            }
            MONITOR_TYPE_CODE = respData.MonitorType ? respData.MonitorType : 'MonitorTypeHanging';
            var monitorInfo = buildMode.monitorInfo[MONITOR_TYPE_CODE] ? buildMode.monitorInfo[
                MONITOR_TYPE_CODE] : buildMode.monitorInfo['MonitorTypeHanging'];
            MONITOR_TYPE = monitorInfo.monitorType;
            // MONITOR_NAME = monitorInfo.monitorName;
            MONITOR_NAME = getI18nName('S1010Name')
            document.getElementById('logoMini').src = buildMode.icon;
            document.getElementById('logoFull').src = buildMode.icon;
            document.getElementById('menuTitle').innerText = MONITOR_TYPE;
            document.getElementById('prTitle').innerText = MONITOR_TYPE;
        }

        function checkLanguage(callback) {
            poll('GET', GET_Sys_Info, {}, function (resp) {
                var respData = resp.result;
                var lan = respData.language;
                changeLanguage(lan, function () {
                    callback()
                    initValueCount++;
                    initLoginFunc();
                });
            }, null);
        }
    </script>
</body>

</html>