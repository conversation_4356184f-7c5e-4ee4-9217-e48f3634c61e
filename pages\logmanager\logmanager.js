var logSearchParams = {
    logUser: '',
    auditDataType: -1,
    auditFindStyle: '0',
    beginDate: '',
    endDate: '',
    refresh:0,
};

function searchLog() {
    logSearchParams.auditDataType = $('#auditDataTypeSelect').val();
    logSearchParams.auditFindStyle = $('#auditFindStyleSelect').val();
    var tempRanges = $('#logDataRange').val().split(' - ');
    logSearchParams.beginDate = tempRanges[0];
    logSearchParams.endDate = tempRanges[1];
    logSearchParams.refresh = new Date().getTime();
    if (tempAuditDataType == logSearchParams.auditDataType) {
        refreshLogTable();
    } else {
        tempAuditDataType = logSearchParams.auditDataType;
        $('#tableLog').bootstrapTable('selectPage', 1);
    }
}

var tempAuditDataType = -1;

function initLogSearchBar() {
    for (var key in AUDIT_DATA_TYPE_ENUM) {
        var enumAudit = AUDIT_DATA_TYPE_ENUM[key];
        $('#auditDataTypeSelect').append('<option value=' + enumAudit.code + ' >' + enumAudit.name + '</option>');
    }
    var tempBeginDate = new Date(new Date().getTime() - (86400000 * 30));
    var tempEndDate = new Date();
    $('#logDataRange').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": true,
        "timePicker24Hour": true,
        "locale": {
            format: 'YYYY/MM/DD HH:mm:ss',
        },
        "startDate": tempBeginDate,
        "endDate": tempEndDate,
    });
    logSearchParams.beginDate = tempBeginDate.pattern('yyyy/MM/dd HH:mm:ss');
    logSearchParams.endDate = tempEndDate.pattern('yyyy/MM/dd HH:mm:ss');
    $('#auditFindStyleSelect').trigger('change');
}

function onAuditFindStyleSelect(obj) {
    var $rangeContent = $('#logDataRange').parent().parent().parent();
    var $dataTypeContent = $('#auditDataTypeSelect').parent();
    switch (obj.value) {
        case "0":
            $rangeContent.hide();
            $dataTypeContent.hide();
            break;
        case "1":
            $rangeContent.hide();
            $dataTypeContent.show();
            break;
        case "2":
            $rangeContent.show();
            $dataTypeContent.hide();
            break;
    }
}

function initLogManagerTable() {
    initLogSearchBar();

    /**
     * 初始化表格内容
     */
    initCustomTable({
        id: 'tableLog',
        method: LOG_MANAGER_LIST,
        toolbar: '#logToolbar',
        height: $('#global').height() * 0.8,
        pageSize: 20,
        pageList: [20],
        responseHandler: function (res) {
            if (res.errorCode != 0) {
                if (res.errorCode == 901) {
                    logOut('repeatLogin');
                    return;
                } else {
                    $.notifyUtil.notifyWarning(convertErrorCode(res.errorCode));
                }
            }
            return res;
        },
        requestParam: function (params) {
            var req;
            switch (logSearchParams.auditFindStyle) {
                case "0":
                    req = {
                        page: params.offset / params.limit + 1,
                        size: params.limit,
                        auditFindStyle: logSearchParams.auditFindStyle,
                        // auditDataType: logSearchParams.auditDataType,
                        // beginDate: logSearchParams.beginDate,
                        // endDate: logSearchParams.endDate
                    };
                    break;
                case "1":
                    req = {
                        page: params.offset / params.limit + 1,
                        size: params.limit,
                        auditDataType: logSearchParams.auditDataType,
                        auditFindStyle: logSearchParams.auditFindStyle
                    };
                    break;
                case "2":
                    req = {
                        page: params.offset / params.limit + 1,
                        size: params.limit,
                        auditFindStyle: logSearchParams.auditFindStyle,
                        beginDate: logSearchParams.beginDate,
                        endDate: logSearchParams.endDate
                    };
                    break;
            }
            return req;
        },
        columns: [{
            field: 'account',
            title: getI18nName('userName')
        },
        {
            field: 'opType',
            title: getI18nName('AuditContent'),
            formatter: function (value, row, index) {
                //value：当前field的值，即id
                //row：当前行的数据
                var a = '<div>[&nbsp;<span style="color: #e08e0b">' + row.account + '</span>&nbsp;]&nbsp;[&nbsp;' + row.time + '&nbsp;]&nbsp;'+getI18nName('Executed')+':&nbsp;[' +
                    '&nbsp;<span style="color: #e08e0b">' + getAuditOpTypeNameByCode(row.opType) + '</span>&nbsp;]&nbsp;</div>';
                return a;
            }
        },
        {
            field: 'dataType',
            title: getI18nName('CheckManagerCheckType'),
            formatter: function (value, row, index) {
                //value：当前field的值，即id
                //row：当前行的数据
                return getAuditDataTypeNameByCode(row.dataType);
            }
        },
        {
            field: 'time',
            title: getI18nName('OPTime')
        }
        ]
    });
}


function refreshLogTable() {
    $('#tableLog').bootstrapTable('refresh', {
        silent: true
    });
}

function getAuditOpTypeNameByCode(code) {
    return getCodeName(code, AUDIT_OPREATE_TYPE_ENUM);
}

function getAuditDataTypeNameByCode(code) {
    return getCodeName(code, AUDIT_DATA_TYPE_ENUM);
}