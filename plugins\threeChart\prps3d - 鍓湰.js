function TD() {
	this.container = null,
		this.camera = null,
		this.scene = null,
		this.renderer = null,
		this.stats = null,


		this.drawArea = { width: 500, height: 500 },//绘图区域大小
		this.colorMode = "Rainbow",
		this.t = 50;
		this.phase = 72;
		this.maxY = 200,//Y轴量程
		this.xNum = null,
		this.Font = { fontface: "YaHei", fontsize: 100, fontstyle: "" },//标签字体及大小
		this.cube = { x: 1, y: 1, z: 0.5 },//柱状图长宽


		this.initObject = function (lineColor) {
			var _this = this;
			var geometry = new THREE.Geometry();
			// B begin
			geometry.vertices.push(new THREE.Vector3(-100, 0, 0));
			geometry.vertices.push(new THREE.Vector3(100, 0, 0));
			// B end
			for (var i = -2; i <= 2; i++) {
				var line = new THREE.Line(geometry, new THREE.LineBasicMaterial({
					color: lineColor,
					opacity: 0.2
				}));
				line.position.z = (i * 50);
				line.position.y = -100;
				_this.scene.add(line);

				var line = new THREE.Line(geometry, new THREE.LineBasicMaterial({
					color: lineColor,
					opacity: 0.2
				}));
				line.position.x = (i * 50);
				line.position.y = -100;
				line.rotation.y = 90 * Math.PI / 180;
				_this.scene.add(line);

				var line = new THREE.Line(geometry, new THREE.LineBasicMaterial({
					color: lineColor,
					opacity: 0.2
				}));
				line.position.z = -100;
				line.position.y = (i * 50);
				_this.scene.add(line);
				var line = new THREE.Line(geometry, new THREE.LineBasicMaterial({
					color: lineColor,
					opacity: 0.2
				}));
				line.position.z = -100;
				line.position.x = (i * 50);
				line.rotation.z = 90 * Math.PI / 180;
				_this.scene.add(line);
			}
		},
		this.makeTextSprite = function (message, parameters) {
			if (parameters === undefined) parameters = {};
			var fontface = parameters.hasOwnProperty("fontface") ? parameters["fontface"] : "YaHei";
			var fontsize = parameters.hasOwnProperty("fontsize") ? parameters["fontsize"] : 18;
			var fontstyle = parameters.hasOwnProperty("fontstyle") ? parameters["fontstyle"] : " ";
			var borderThickness = parameters.hasOwnProperty("borderThickness") ? parameters["borderThickness"] : 4;
			var textColor = parameters.hasOwnProperty("textColor") ? parameters["textColor"] : { r: 0, g: 0, b: 0, a: 1.0 };

			var canvas = document.createElement('canvas');
			var context = canvas.getContext('2d');
			context.font = fontstyle + " " + fontsize + "px " + fontface;
			var metrics = context.measureText(message);
			var textWidth = metrics.width;

			context.lineWidth = borderThickness;

			context.fillStyle = "rgba(" + textColor.r + ", " + textColor.g + ", " + textColor.b + ", 1.0)";
			context.fillText(message, borderThickness, fontsize + borderThickness);

			var texture = new THREE.Texture(canvas)
			texture.needsUpdate = true;

			var spriteMaterial = new THREE.SpriteMaterial({ map: texture, useScreenCoordinates: false });
			var sprite = new THREE.Sprite(spriteMaterial);
			sprite.scale.set(0.5 * fontsize, 0.25 * fontsize, 0.75 * fontsize);
			return sprite;
		},
		this.addAxisText = function () {
			var _this = this;
			var inLen = 200;
			// //创建Z            
			// var spritey = makeTextSprite( "Z", { fontsize: 100} );
			// spritey.position.set(-100,-100,110);
			// _this.scene.add(spritey);

			// //创建X
			// spritey = makeTextSprite( "X", { fontsize: 100 } );
			// spritey.position.set(130,-100,-120);
			// _this.scene.add(spritey);

			// //创建Y
			// spritey = _this.makeTextSprite( "Y", { fontsize: 100} );
			// spritey.position.set(-100,100,-100);
			// _this.scene.add(spritey);
			//x标签
			for (var i = 0; i < 5; i++) {
				var spritey = _this.makeTextSprite(90 * i + '°', this.Font);
				spritey.position.set(-100 + i * 50 + 10, -100, 120);
				_this.scene.add(spritey);
			}
			//z标签
			for (var i = 1; i < 5; i++) {
				var spritey = _this.makeTextSprite(10 * (i + 1), this.Font);
				spritey.position.set(130, -100, 100 - i * 50);
				_this.scene.add(spritey);
			}
			//y标签
			// for (var i = 0; i < 5; i++) {
			// }
			var spritey = _this.makeTextSprite(100 + '%', this.Font);
			spritey.position.set(140, 100, -100);
			_this.scene.add(spritey);
			spritey = _this.makeTextSprite(50 + '%', this.Font);
			spritey.position.set(140, 0, -100);
			_this.scene.add(spritey);
			spritey = _this.makeTextSprite(0 + '%', this.Font);
			spritey.position.set(140, -80, -100);
			_this.scene.add(spritey);
		},

		this.addBasicData = function () {
			var cube;
			var _this = this;
			var _cube_x = _this.cube.hasOwnProperty("x") ? _this.cube["x"] : 0.5;
			var _cube_y = _this.cube.hasOwnProperty("y") ? _this.cube["y"] : 0.5;

			for (var z = 0; z < 50; z++) {
				for (var x = 0; x < 72; x++) {
					var num = Math.random() * 200;
					var tempcolor = GetColorStr(_this.colorMode, num / 200.0);
					cube = new THREE.Mesh(new THREE.CubeGeometry(_cube_x, num, _cube_y),
						new THREE.MeshBasicMaterial({
							color: tempcolor
						}));
					cube.position.z = 100 - (z * 4);
					cube.position.x = -100 + (x * 2.8);
					cube.position.y = - (200 - num) / 2;
					_this.scene.add(cube);
					cube.visible = false;
				}
			}
		},
		this.addData = function (data, n) {
			var _this = this;
			var startIndex = 32;
			// for(var x = 0; x < data.length; x = x + 72)
			for (var z = 0; z < 50; z++) {
				for (var x = 0; x < 72; x++) {
					if (_this.scene.children[x + z * 72 + startIndex].type == "Mesh") {
						var num = _this.maxY * data[x + z].Z / 200;
						var tempcolor = GetColorStr(_this.colorMode, num / 200.0);
						if (num > 0 && z < n) {
							_this.scene.children[x + z * 72 + startIndex].visible = true;
						}
						_this.scene.children[x + z * 72 + startIndex].material.color = new THREE.Color(tempcolor);
						_this.scene.children[x + z * 72 + startIndex].scale.y = num / _this.scene.children[x + z * 72 + startIndex].geometry.parameters.height;
						_this.scene.children[x + z * 72 + startIndex].position.y = - (200 - num) / 2;
					}
				}
			}



			// for (var i = 0; i < _this.scene.children.length; i++) { 
			// 		if (_this.scene.children[i].type == "Mesh") {
			// 			var num = Math.random() * 200;
			// 			var tempcolor = GetColorStr(_this.colorMode, num / 200.0);
			// 			if(num>0)
			// 			{
			// 				_this.scene.children[i].visible = true;
			// 			}
			// 			_this.scene.children[i].material.color = new THREE.Color(tempcolor);
			// 			_this.scene.children[i].scale.y = num / _this.scene.children[i].geometry.parameters.height;
			// 			_this.scene.children[i].position.y = - (200 - num) / 2;
			// 		}
			// 	}
		},
		this.init = function (divstr) {
			var _this = this;
			_this.container = document.getElementById(divstr);
			var info = _this.container;

			var width = _this.drawArea.hasOwnProperty("width") ? _this.drawArea["width"] : 500;
			var height = _this.drawArea.hasOwnProperty("height") ? _this.drawArea["height"] : 500;
			//var prpsHeight = info.clientWidth * 0.7;

			// _this.camera = new THREE.PerspectiveCamera(70, info.clientWidth / prpsHeight, 1, 1000);
			_this.camera = new THREE.OrthographicCamera(-width / 2, width / 2, height / 2, -height / 2, 10, 1000);//正投影
			//_this.camera.lookVertical = true;
			// _this.camera.position.x = 140;
			// _this.camera.position.y = 170;
			// _this.camera.position.z = 230;
			_this.camera.position.set(100, 60, 70);

			_this.camera.lookAt({
				x: 0,
				y: 0,
				z: 0
			});

			_this.controls = new THREE.OrbitControls(_this.camera);
			_this.controls.minDistance = 200;
			_this.controls.maxDistance = 700;

			_this.scene = new THREE.Scene();

			//_this.scene.add( new THREE.GridHelper( 200, 4 ) );

			_this.initObject(0x5c5c5c);
			//_this.addData();
			_this.addAxisText();
			_this.addBasicData();//初始化绘制数据并设置数据不显示；

			//实时刷新加数据			

			_this.renderer = new THREE.WebGLRenderer({
				antialias: true
			});
			_this.renderer.setSize(width, height);
			_this.container.appendChild(_this.renderer.domElement);
			_this.renderer.setClearColor(0xf0f0f0, 0.99);

			_this.render();

			_this.stats = new Stats();
			_this.container.appendChild(_this.stats.dom);
		},
		this.render = function () {
			var _this = this;
			_this.renderer.render(_this.scene, _this.camera);
		}
}
