【产品】带电检测数据管理平台（这个产品名称在TB的任务分组名范围里找，前缀不包含 项目 字样的都是产品，不要超过范围，不要出现什么PDSCloud之类的）
1. 2021.8~2021.9，负责了xx模块的{设计/开发/测试}工作，主要工作内容是xxxx，主要产出是xxxx，该工作目前的状态为：{已经发布上线 / 技术部试用中 / 完成开发，正在软件内部测试 / 已完成设计工作，待评审开发} ；
2. xxxx ；
3. xxxx ；

【项目】xxx（这个产品名称在TB的任务分组名范围里找，前缀我都标了 项目 字样的）
1. xxx 参考上面的写法；
2. xxxx ；
3. xxxx ；

【技术】xxxx（这个关键技术，各种的都可以包含，比如IEC61850通讯协议实现、自动化测试平台的搭建技术验证和投产、Devops流程优化、组件库的设计与架构实现、IOT架构方案、OTA等等，你觉得在产品和项目中没有说清楚价值的，都可以写进来）
1. xxx 参考上面的写法，说清楚自己的贡献点，以及目前该技术的实际状态，用在了哪些产品中，产生了什么实际的效果，比如是效率提升了，还是xxx更加规范了等等；
2. xxxx ；
3. xxxx ；

【产品】带电检测数据管理APP
1. 2021.8-2021.12 负责离线任务-设备创建模块开发。主要产出为设备模板工具、创建模块、模板映射档案功能、拍照生成设备模板功能。该工作目前状态：技术部试用
2. 2022.1 负责离线任务接入接地电流开发、业务展示SDK数据解析开发。主要产出为离线任务接地电流展示和xml数据解析后展示。该工作目前状态：技术部试用
3. 2022.2-2022.3 负责APP与桌面交互工作中的发现服务工具开发。主要产出为APP发现服务和连接模块。该工作目前状态：技术部试用
4. 2022.4-2022.5 负责Flir接入离线模块开发（预览、数据入库）和DataProcesss接入H5绘图组件开发。主要产出为离线任务FLir预览展示和数据入库，DataProcess图谱展示。该工作目前状态：技术部试用
5. 2022.6-2022.7 负责新标二进制数据解析-PRPS PRPD转换开发（UHF HFCT）。主要产出为APP接入新版二进制文件PRPS PRPD解析展示。该工作目前状态：开发完成，正在软件内部测试。
6. 2022.7 负责技术部反馈问题处理。主要产出为国际化问题处理、自定义下拉菜单交互体验优化、下拉菜单样式统一。该工作目前状态：技术部试用


【产品】带电检测数据管理平台
1. 2022.6 负责Flir红外数据上云接入工作。主要产出为 Flir红外数据上云后在数据管理、离线任务、任务管理中的展示和可见光展示处理。该工作目前状态：已发布上线

【产品】智能监测数据管理平台
1. 2022.4-2022.5 负责适配新的标准图谱库组件升级适配和后台解析PRPS PRPD数据算法更新。主要产出为管理平台图谱库升级后数据展示及PRPS PRPD兼容解析。该工作目前状态：已发布上线

【产品】带电检测数据管理软件
1. 2021.8-2021.9 负责6.2.2.4版本开发。主要产出为档案排序功能和台账导出功能。该工作目前状态为：已发布
2. 2021.10-2021.12 负责6.3.1版本开发。主要产出物接地电流档案模块、用户登录功能、任务上传云功能。该工作目前状态为：已发布
3. 2022.1 负责6.3.1版本测试缺陷消缺。主要产出为6.3.1版本发布功能：接地电流数据接入、任务图片批量导出。该工作目前状态：已发布
4. 2022.2 负责APP任务数据互通版本界面开发。主要产出为APP互通数据接入展示开发工作完成。该工作目前状态：技术部试用中
5. 2022.3-2022.5 负责Flir数据互通展示、档案信息修改新增需求和批量导出功能开发。主要产出是Flir数据展示和新增优化需求开发。该工作目前状态：技术部试用中
6. 2022.6 负责二进制数据展示开发。主要产出为T95支持的所有格式二进制数据展示修改兼容验证开发。该工作目前状态：完成开发，正在软件内部测试

【项目】边缘计算-河北二期
1. 2021.8-2021-9 负责对比分析模块开发。主要产出为对比分析模块及对比页面子模块。该工作目前状态：已发布上线

【产品】带电检测业务SDK
1. 2021.10-2021.12 负责工程搭建与PRPS PRPD数据解析和展示。主要产出为SDK PRPS PRPD数据解析与展示功能。该工作目前状态为：技术部试用

【产品】数据处理SDK
1. 2021.10-2022.6 负责SDK UXDisplay前端组件库开发、DEMO开发。主要产出为AE、UHF、HFCT、机械特性、接地电流数据解析与绘图展示，对外发布示例Demo代码。该工作目前状态：已发布

【产品】T90接出SDK
1. 2021.8-2021.10 负责SDK1.0.0版本工程代码迁移修改及任务数据获取开发。主要产出为SDK1.0.0和T90局放数据展示

【项目】边缘计算SDK
1. 2021.8-2021.9 负责平高机器人中间件SDK开发、锦绣变/霍沙变边缘计算数据展示SDK外发版本开发。主要产出为平高机器人SDK和边缘计算展示SDK外发版本1.2.0.该工作目前状态为：已发布交付

【项目】嘉定数字化班组
1. 2021.8-2021.12 负责cordova框架下vue+dva框架搭建、任务模块开发，欣能环境联调工作。主要产出是APP联通框架、任务模块和欣能初版发布。该工作目前状态为：已在嘉定运检平台上线
2. 2022.1-2022.2 负责欣能环境上架联调和上架验证工作。主要产出工作内容是欣能环境联调消缺和上架后功能验证调试。该工作目前状态为：已在嘉定运检平台上线
3. 2022.3-2022.6 负责数据模块接入红外数据、接地电流数据开发，报告生成在欣能环境下调试及问题修复，反馈问题处理（显示、手势等）；主要产出内容是红外数据、接地电流数据接入后欣能环境联调。该工作目前状态：已在嘉定运检平台上线

【项目】中国电科院
1. 2022.6 负责外发文档整理与算法文档编写。主要产出为外发文档和算法文档。该工作目前状态为：已发布。

【产品】汇集主机
1. 2021.8-2021.9 负责主机休眠参数校验调整、主机编号调整及外发参数修改。主要产出为主机休眠参数修改、主机编号修改功能及四方项目参数修改。该工作状态：已发布交付
2. 2022.2 负责配合外购传感器显示修改及发货相关页面修改工作。主要工作内容是页面按发货需求修改，该工作状态：完成开发，已发货

【技术】Vue+dva在cordova中应用
1. 2021.8-2022.6 负责对应框架通信、插件问题处理。该框架在嘉定项目、i国网项目调研中均有应用

【技术】PDCharts图谱库
1. 2021.9-2022.6 负责接地电流图谱开发和展示规则更新、AE飞行、波形、相位图谱更新。主要产出为接地电流图谱展示及AE图谱更新。应用在平台、APP、桌面软件展示中。
2. 2022.5-2022.6 负责PRPS PRPD颜色池更新，二进制标准数据文件PRPS PRPD算法更新。主要产出为PRPS PRPD绘图展示更新和PRPS转PRPD算法更新，应用在后台、APP等数据转换场景；图谱库更新应用在平台、智能传感器SDK、APP、桌面软件展示中。


2022-10-18T08:07:46.918626000Z 634e5ed2 conn=1090 op=1 SEARCH RESULT tag=101 err=0 nentries=1 text=,
2022-10-18T08:07:46.920898000Z 634e5ed2 conn=1090 op=2 RESULT tag=97 err=0 text=,
2022-10-18T08:07:46.920663000Z 634e5ed2 conn=1090 op=2 BIND dn="uid=liyaoxu,ou=users,dc=company,dc=com" mech=SIMPLE ssf=0,
2022-10-18T08:07:46.920395000Z 634e5ed2 conn=1090 op=2 BIND dn="uid=liyaoxu,ou=users,dc=company,dc=com" method=128,
2022-10-18T08:07:46.920058000Z 634e5ed2 conn=1090 op=2 BIND anonymous mech=implicit ssf=0,
2022-10-18T08:07:46.918347000Z 634e5ed2 conn=1090 op=1 SRCH attr=dn,
2022-10-18T08:07:46.918072000Z 634e5ed2 conn=1090 op=1 SRCH base="ou=users,dc=company,dc=com" scope=2 deref=0 filter="(&(&(objectClass=*)(uid=*))(uid=liyaoxu))",
2022-10-18T08:07:46.916751000Z 634e5ed2 conn=1090 op=0 RESULT tag=97 err=0 text=,
2022-10-18T08:07:46.916507000Z 634e5ed2 conn=1090 op=0 BIND dn="cn=admin,dc=company,dc=com" mech=SIMPLE ssf=0,
2022-10-18T08:07:46.916240000Z 634e5ed2 conn=1090 op=0 BIND dn="cn=admin,dc=company,dc=com" method=128,2022-10-18T08:07:46.915885000Z 634e5ed2 conn=1090 fd=12 ACCEPT from IP=**********:53014 (IP=0.0.0.0:389)


2022-10-18T08:09:10.461244000Z 634e5f26 conn=1093 op=1 SEARCH RESULT tag=101 err=0 nentries=2 text=,
2022-10-18T08:09:10.460934000Z 634e5f26 conn=1093 op=1 SRCH base="ou=users,dc=company,dc=com" scope=2 deref=0 filter="(&(objectClass=*)(uid=*))",
2022-10-18T08:09:10.459735000Z 634e5f26 conn=1093 op=0 RESULT tag=97 err=0 text=,
2022-10-18T08:09:10.459433000Z 634e5f26 conn=1093 op=0 BIND dn="cn=admin,dc=company,dc=com" mech=SIMPLE ssf=0,
2022-10-18T08:09:10.458847000Z 634e5f26 conn=1093 fd=12 ACCEPT from IP=**********:53036 (IP=0.0.0.0:389),
2022-10-18T08:09:10.459126000Z 634e5f26 conn=1093 op=0 BIND dn="cn=admin,dc=company,dc=com" method=128

cat <<EOF > /etc/docker/daemon.json 
{
   "registry-mirrors": ["https://docker.mirrors.ustc.edu.cn"],
   "registry-mirrors": ["https://6kx4zyno.mirror.aliyuncs.com"],
   "registry-mirrors": ["https://**************:4430"]
}
EOF