var gdcChartIndex;
var chartDataNum;

function getGrounddingCurrentTendDatas(isInit) {
    /*     var dateArray = $('#da-dateRange').val().split('-');
        var beginDate = dateArray[0].trim();
        var endDate = dateArray[1].trim(); */

    var beginDate = isInit ? undefined : $('#gdcBeginDate').val();
    var endDate = isInit ? undefined : $('#gdcEndDate').val();
    if (moment(beginDate).isAfter(moment(endDate))) {
        layer.msg('<div style="text-align:center">' + getI18nName('dateStartIsAfterDateEnd') +
            '</div>');
        return
    }

    gdcChartIndex = 0;
    $('#charts').empty();
    var pointType = "GrounddingCurrent";
    var dataGet = "pointId=" + showChartPointId + "&dataId=" + showChartDataId + "&pointType=" + showChartPointType +
        "&startDate=" + beginDate + "&endDate=" + endDate;
    pollGet(GET_TEND_DATA, dataGet, function (response) {
        var respData = response.result;
        if (!respData) {
            return
        }

        // debugger;
        tempXval = respData[0].xValue[respData[0].xValue.length - 1];
        refeshGrounddingCurrentData();
        let dateInfo = {};
        // for (var i = 0; i < respData.length; i++) {
        for (var i = 1; i < respData.length; i++) {//临时去除第零位的阻性电流趋势图
            gdcChartIndex++;
            if (respData[i].dataList == undefined && respData[i].chartName == undefined) {
                dateInfo = respData[i];
                continue;
            }
            initGrounddingCurrentTendChart(respData[i], gdcChartIndex);
        }
        if (isInit) {
            dateInfo.startDate && $('#gdcBeginDate').val(new Date(dateInfo.startDate).pattern('yyyy/MM/dd'))
            dateInfo.endDate && $('#gdcEndDate').val(new Date(dateInfo.endDate).pattern('yyyy/MM/dd'))
        }
    });
}

function initGrounddingCurrentTendChart(chartData, chartID) {
    $('#charts').append(
        "<div id='" +
        chartID +
        "' style='width:100%;height:50%;'  onmousedown = 'refeshGrounddingCurrentData()' >" +
        "</div>"
    );
    //        debugger;
    var myChart = echarts.init(document.getElementById(chartID));
    var dataXValue = [" "];
    var colors = ['orange', '#00CC00', 'red'];
    var legendData = chartData.legendData;
    // for (var i = 0; i < legendData.length; i++) {
    //     legendData[i] = getI18nName(legendData[i]);
    // }
    legendData = ['A相接地电流', 'B相接地电流', 'C相接地电流']
    var option = {
        color: colors,
        tooltip: {
            trigger: 'axis',
            axisPointer: { // 坐标轴指示器，坐标轴触发有效
                type: 'line' // 默认为直线，可选为：'line' | 'shadow'
            },
            formatter: function (params) {
                var html = '';
                if (params.length > 0) {
                    tempXval = params[0].name;
                    html += tempXval + '<br>';
                    for (var int = 0; int < params.length; int++) {
                        html += formatUtil.getTooltipMarkerTemp(params[int].color) + params[int].seriesName + ': ' + params[int].data + '<br>';
                    }
                }
                return html;
            },
            confine: true
        },
        legend: {
            top: 'bottom',
            data: legendData
        },
        title: {
            left: 'center',
            // text: getI18nName(chartData.chartName),
            text: getI18nName('groundCurrent'),
            // x: 'left',
            // y: 'top',
            textAlign: 'left'
        },
        calculable: true,
        xAxis: [{
            type: 'category',
            data: dataXValue,
            boundaryGap: false,
            triggerEvent: true
        }],
        yAxis: [{
            type: 'value'
        }],
        series: []
    };
    if (option) {
        var xvalue = chartData.xValue;
        if (chartData.xUnit) {
            option.xAxis[0].name = stringFormat.format(getI18nName('UnitFomat'), chartData.xUnit);
        }
        if (chartData.yUnit) {
            option.yAxis[0].name = stringFormat.format(getI18nName('UnitFomat'), chartData.yUnit);
        }
        for (var j = 0; j < chartData.dataList.length; j++) {
            var dataName = chartData.dataList[j].name;
            switch (dataName) {
                case 'leakageCurrentA':
                    dataName = 'A相接地电流';
                    break;
                case 'leakageCurrentB':
                    dataName = 'B相接地电流';
                    break;
                case 'leakageCurrentC':
                    dataName = 'C相接地电流';
                    break;
                default:
                    dataName = getI18nName(dataName);
                    break;
            }
            chartData.dataList[j].name = dataName;

            var data = chartData.dataList[j];
            data.showSymbol = false;
        }
        // debugger;
        option.xAxis[0].data = xvalue;
        option.series = chartData.dataList;
        myChart.setOption(option);
    }
}

/**
 * 右侧参数控制
 */
var tempXval;

function refeshGrounddingCurrentData() {
    var dataGet = "pointId=" + showChartPointId + "&pointType" + showChartPointType + "&dateTime=" + tempXval;
    pollGet(GET_ARRESTER_CHART_PARAMDATA, dataGet, function (response) {
        var result = response.result;
        // debugger;
        initGrounddingCurrentParamItems();
        for (var item in result) {
            var itemData = result[item];
            if (item == "sensorType") {
                itemData = getI18nName(itemData);
            }
            $('#' + item).html(itemData + " " + (GrounddingCurrentParamItems[item] ? GrounddingCurrentParamItems[item] : ''));
        }
    }, function () {

    });
}
var GrounddingCurrentParamItems = {
    "pointName": "",
    "sensorCode": "",
    "sensorType": "",
    "sample_date": "",
    "sample_time": "",
    // "leakageCurrentA": "mA",
    // "leakageCurrentB": "mA",
    // "leakageCurrentC": "mA",
    "leakageCurrentA": "mA",
    "leakageCurrentB": "mA",
    "leakageCurrentC": "mA",
    // "referenceVoltageA": "V",
    // "referenceVoltageB": "V",
    // "referenceVoltageC": "V",
    // "resistiveCurrentA": "mA",
    // "resistiveCurrentB": "mA",
    // "resistiveCurrentC": "mA",
};

function initGrounddingCurrentParamItems() {
    var formContent = $('#GrounddingCurrentParams');
    formContent.empty();
    for (var id in GrounddingCurrentParamItems) {
        var name = '';
        switch (id) {
            case 'leakageCurrentA':
                name = 'A相接地电流';
                break;
            case 'leakageCurrentB':
                name = 'B相接地电流';
                break;
            case 'leakageCurrentC':
                name = 'C相接地电流';
                break;
            default:
                name = getI18nName(id);
                break;
        }
        if (id == 'sensorCode') {
            id = 'aduId';
        }
        // '                    <label class="col-sm-6 control-label" style="padding-right:5px">' + getI18nName(id) + '</label>',
        var htmlItem = ['   <div class="form-group">',
            '                    <label class="col-sm-6 control-label" style="padding-right:5px">' + name + '</label>',
            '                    <div class="col-sm-6">',
            '                        <label class=" control-label" id="' + id + '"></label>',
            '                    </div>',
            '                </div>'
        ].join("");
        formContent.append(htmlItem);
    }
}

var formatUtil = {};

formatUtil.getTooltipMarkerTemp = function (color, extraCssText) {
    return color ?
        '<span style="display:inline-block;margin-right:5px;' +
        'border-radius:10px;width:9px;height:9px;background-color:' +
        formatUtil.encodeHTMLTemp(color) + ';' + (extraCssText || '') + '"></span>' :
        '';
};
formatUtil.encodeHTMLTemp = function (source) {
    return String(source)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;');
};

//http://**************:8003/GetArresterChartParamData?pointId=536f3b49-1fb3-4592-8eaa-b34bc93d9ced&pointType=ArresterI&dateTime=2018/01/15%2016:37:23