<div class="col-sm-12 full-height" id="maingraph">
    <div class="col-sm-3" style="padding-left: 0px;padding-right: 8px;height: 100%">
        <div class="full-height nav-tabs-custom left" style="overflow:auto;border: 1px solid #f4f4f4;">
            <div id="dev_tree" class="ztree"></div>
        </div>
    </div>
    <div class="col-sm-9 no-padding nav-tabs-custom full-height">
        <div class="row col-sm-12 col-center-block" style="margin: 5px;margin-left:-8px">
            <div class="col-sm-7 input-group date" style="float: left">
                <div class="input-group-addon">
                    <i class="fa fa-calendar"></i>
                </div>
                <input id="da-dateRange" type="text" class="form-control" readonly="true">
            </div>
            <div class="col-sm-5">
                <button type="button" id="refreshBtn" class="btn btn-block btn-primary" style="width: 80px;"
                    data-i18n="scanData">
                    查询
                </button>
            </div>
        </div>
        <div>
            <ul class="nav nav-tabs" style="height:9%;">
                <!--<li class="active" onclick="getStationTree()"><a href="#stationSet_tab" data-toggle="tab">站点配置</a></li>-->
                <li id="trendTab" class="active"><a href="#trendSync" data-toggle="tab" data-i18n="trendSync">趋势分析</a>
                </li>
                <li id="historyDataTab"><a href="#historyData" data-toggle="tab" data-i18n="historyData">历史数据</a></li>
            </ul>
        </div>
        <div id="dmwContent" class="tab-content" style="padding:0px;height:91%">
            <div class="col-sm-12 tab-pane full-height active" id="trendSync">

                <div class="form-group" id="graph-water" style="height: 80%;margin-bottom:0px">
                    <div id="trendChart" style="width:100%;height:100%"></div>
                </div>
            </div>
            <div class="col-sm-12 tab-pane full-height table-responsive" id="historyData" style="padding: 0px;">
                <table id="dmwHistory" class="table table-bordered table-hover full-height" role="">
                    <!--<thead>-->
                    <!--<tr>-->
                    <!--<th class="sorting">序号</th>-->
                    <!--<th class="sorting">设备名称</th>-->
                    <!--&lt;!&ndash;<th>前端名称</th>&ndash;&gt;-->
                    <!--<th class="sorting">测点名称</th>-->
                    <!--<th class="sorting">传感器</th>-->
                    <!--<th class="sorting">状态</th>-->
                    <!--<th class="sorting">更新时间</th>-->
                    <!--&lt;!&ndash;<th>操作</th>&ndash;&gt;-->
                    <!--</tr>-->
                    <!--</thead>-->
                    <!--<tbody id="monitorTbShow">-->
                    <!--</tbody>-->
                </table>
            </div>
        </div>
    </div>
</div>
<style>
    .no-padding {
        padding: 0px;
    }
</style>
<script>
    var TREND = 'trend';
    var HISTORY = 'history';
    var currentState = TREND;
    var selectedHistoryArchives = {
        substation: '',
        device: showChartDeviceId,
        testPoint: showChartPointId,
        channelType: showChartPointType,
        treeNodeType: '0'
    }
    //@ sourceURL=instrument_set.js
    initPageI18n();

    $('#da-dateRange').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "startDate": moment().subtract(29, 'days'),
        "endDate": moment(),
    });

    $("input[vk=true]").focus(function () {
        $.fn.keysoft($(this));
    });

    $('.select2').select2({
        minimumResultsForSearch: Infinity,
        language: "zh-CN"
    });

    $('#trendTab').click(function () {
        currentState = TREND;
        tabSwitch();
    });

    $('#historyDataTab').click(function () {
        currentState = HISTORY;
        tabSwitch();
    });

    $("#refreshBtn").click(function () {
        tabSwitch();
    });
    myChart = echarts.init(document.getElementById('trendChart'));
    myChart.setOption(option);
    initTendChartWidth();

    function tabSwitch() {
        //        debugger;
        var treeObj = $.fn.zTree.getZTreeObj('dev_tree');
        var channelNode = treeObj.getNodeByParam('id', selectedHistoryArchives.channelType);
        virtualClickNode(treeObj, channelNode);
    }

    initDevTree();
    initHistoryData();
</script>