var exportStepBox;

function initExportStepBox() {
    exportStepBox = $('#exportStep').step({
        stepDirection: 'x',
        showStepButton: false,
        stepCount: 5,
        stepTitles: [getI18nName('LogExportStep1Title'), getI18nName('LogExportStep2Title'), getI18nName('LogExportStep3Title'), getI18nName('LogExportStep4Title'), getI18nName('LogExportStep5Title')],
    })[0];
}

function onExportLogData(message) {
    if (message.getADULogFile == 'begin') {
        checkExportLogState(checkExportStateConfig.enum.getLogFile);
    }
    else if (message.getADULogFile == 'end') {
        checkExportLogState(checkExportStateConfig.enum.getLogFile);
    }
    else if (message.LogFileCompress == 'begin') {
        checkExportLogState(checkExportStateConfig.enum.beginCompress);
    }
    else if (message.LogFileCompress == 'end') {
        checkExportLogState(checkExportStateConfig.enum.CompressFinish);
        setTimeout(function (params) {
            checkExportStateConfig.download(message.LogFilePath)
            checkExportLogState(checkExportStateConfig.enum.aduSelect);
        }, 500);
    } else if (message.CheckLogFilePath == 'empty') {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('exportLogError')
            },
            type: "danger",
        }).show();
        checkExportLogState(checkExportStateConfig.enum.aduSelect);
    } else if (message.ADUId) {
        var aduTypeText = aduType.children('option:selected').val();
        // var msg = '同步失败：' + aduTypeText + '_' + value.aduId;
        var msg = getI18nName('getLogFileError') + ":" + message.ADUId;
        if (validateStrLen(aduTypeText) == 0) {
            msg = msg = getI18nName('getLogFileError') + ":" + message.ADUId;
        }
        $('#da-progress').removeClass('hide').addClass('hide')
        $('#da-ok').removeClass('hide');
        $('#da-cancel').addClass('hide').prop('disabled', false);
        $('#da-cancel img').addClass('hide');
        if (message.ADUId != "") {
            $('.notifications').notify({
                fadeOut: {
                    enabled: false
                },
                message: {
                    text: msg
                },
                type: "danger",
            }).show();
        }
    }
}

function download() {
    window.open(serverUrl.substring(0, serverUrl.length - 1) + '/media/backup/download/tmp/backUp.tar.gz')
}

var cachedFormdata = {};
function cancelExportLog() {
    checkExportLogState(checkExportStateConfig.enum.aduSelect);
    requestExportData(cachedFormdata, false);
    layer.msg(getI18nName('LogExportCancelTips'))
}
var checkExportStateConfig = {
    exportId: {
        "ck-data-base": {
            need: true,
            path: '/media/data/database'
        },
        "ck-data-file": {
            need: true,
            path: '/media/data/datafile'
        },
        "ck-daily-file": {
            need: true,
            path: '/media/data/log'
        },
        "ck-config-file": {
            need: true,
            path: '/home/<USER>/config.xml'
        },
    },
    step: 'fileTypeSelect',
    enum: {
        aduSelect: 'aduSelect',
        getLogFile: 'getLogFile',
        getLogFileFinish: 'getLogFileFinish',
        beginCompress: 'beginCompress',
        CompressFinish: 'CompressFinish'
    },
    download: function (path) {
        /*  $('#downloadExportData').parent().attr('href', serverUrl.substring(0, serverUrl.length - 1) + path);
         $('#downloadExportData').trigger('click'); */
        window.open(serverUrl.substring(0, serverUrl.length - 1) + path);
    }
};

function checkExportLogState(step) {
    if (step) {
        checkExportStateConfig.step = step;
    }
    switch (checkExportStateConfig.step) {
        case checkExportStateConfig.enum.aduSelect:
            $.session.set('logExporting', '');
            $('#cancelExportLog').addClass('hide');
            $('#ExportLog').removeClass('hide');
            $('input[name=exportGroup]').removeAttr('disabled');
            $('#da-adus_leftAll').trigger('click');
            aduType.prop("disabled", false);
            exportStepBox.stepMove(0)
            break;
        case checkExportStateConfig.enum.getLogFile:
            $('#ExportLog').addClass('hide');
            $('#cancelExportLog').removeClass('hide');
            $('input[name=exportGroup]').attr('disabled', 'disabled');
            aduType.prop("disabled", true);
            exportStepBox.stepMove(1)
            break;
        case checkExportStateConfig.enum.getLogFileFinish:
            exportStepBox.stepMove(2)
            break;
        case checkExportStateConfig.enum.beginCompress:
            exportStepBox.stepMove(3)
            break;
        case checkExportStateConfig.enum.CompressFinish:
            $.session.set('logExporting', '');
            $('#cancelExportLog').addClass('hide');
            $('#ExportLog').removeClass('hide');
            $('input[name=exportGroup]').removeAttr('disabled');
            aduType.prop("disabled", false);
            $('#da-adus_leftAll').trigger('click');
            exportStepBox.stepMove(4)
            break;
    }
}

function requestExportData(formdata, enablePush) {
    var message = {
        enablePush: enablePush,
        methodName: "PushGetADULogProgress",
    };
    if (!enablePush) {
        checkExportStateConfig.step = checkExportStateConfig.enum.aduSelect;
        message.parameters = formdata;
        checkExportLogState();
    } else {
        $.session.set('logExporting', 'logExporting');
        checkExportStateConfig.step = checkExportStateConfig.enum.getLogFile;
        checkExportLogState();
        message.parameters = formdata;
        cachedFormdata = formdata;
    }
    // console.log(message)
    webSocketSend(message);
}