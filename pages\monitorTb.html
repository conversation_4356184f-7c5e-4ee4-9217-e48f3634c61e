<!--
 * @Author: liyaoxu <EMAIL>
 * @Date: 2023-06-01 15:17:49
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2025-04-25 18:59:38
 * @FilePath: \pds_z058_web\pages\monitorTb.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style type="text/css">
  .table {
    table-layout: fixed;
  }

  thead {
    table-layout: fixed;
  }

  .table_list {
    table-layout: fixed !important;
    width: 100% !important;
  }

  .table-item-switch.on,
  .table-item-switch.off {
    border: none;
    background: lightgray;
    margin: 10px 0;
    padding: 5px 10px;
    color: #fff;
    outline: none;
  }

  .table-item-switch.on {
    border-radius: 3px 0 0 3px;
  }

  .table-item-switch.off {
    border-radius: 0 3px 3px 0;
  }

  .table-item-switch.on:focus,
  .table-item-switch.off:focus {
    border: none;
  }

  .table-item-switch.on.active {
    background: #367fa9;
  }

  .table-item-switch.off.active {
    background: lightcoral;
  }

  .table-item-btn {
    /* width: 68px; */
    height: 32px;
  }

  .tableTitleBoxChild {
    margin-right: 15px;
    display: flex;
    align-items: center;
    float: left;
  }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
  <div class="nav-tabs-custom full-height">
    <ul class="nav nav-tabs">
      <li class="active"><a href="#all_tab" style="height: 55px;
                display: flex;
                justify-content: center;
                align-items: center;
                min-width: 100px;" data-toggle="tab" data-i18n="monitoringTable">监测表</a></li>
      <li id="sensor_status_tab_title"><a href="#sensor_status_tab" data-toggle="tab" style="height: 55px;
              display: flex;
              justify-content: center;
              align-items: center;
              min-width: 100px;" data-i18n="smartSensorStatus">状态表</a>
      </li>
      <!--<li><a href="#ae_tab" data-toggle="tab">AE</a></li>-->
      <!--<li><a href="#tev_tab" data-toggle="tab">TEV</a></li>-->
      <!--<li><a href="#uhf_tab" data-toggle="tab">UHF</a></li>-->
      <!--<li><a href="#hfct_tab" data-toggle="tab">HFCT</a></li>-->
      <!--<li><a href="#saw_tab" data-toggle="tab">TEV</a></li>-->
      <!--<li><a href="#ax8_tab" data-toggle="tab">UHF</a></li>-->
      <!--<li><a href="#mp_tab" data-toggle="tab">MP</a></li>-->
      <li style="float: right;">
        <button class="btn btn-primary" id="singleCollect" data-i18n="singleCollect">批量采集
        </button>
        <button class="btn btn-primary" id="tbRefresh" data-i18n="refresh">刷新</button>
        <button class="btn btn-primary hide" id="uhf">UHF</button>
        <button class="btn btn-primary hide" id="ae">AE</button>
        <button class="btn btn-primary hide" id="hfct">HFCT</button>
        <button class="btn btn-primary hide" id="mech">机械特性</button>
        <button class="btn btn-primary hide" id="tev">TEV</button>
      </li>
    </ul>
    <div class="tab-content " style="padding:0px;height: 90%">
      <!--all-->
      <div class="tab-pane full-height active" id="all_tab">
        <table id="tableAll" class="table table-striped table-bordered table-hover full-height table_list" role="">
          <!--<thead>-->
          <!--<tr>-->
          <!--<th class="sorting">序号</th>-->
          <!--<th class="sorting">设备名称</th>-->
          <!--&lt;!&ndash;<th>前端名称</th>&ndash;&gt;-->
          <!--<th class="sorting">测点名称</th>-->
          <!--<th class="sorting">传感器</th>-->
          <!--<th class="sorting">状态</th>-->
          <!--<th class="sorting">更新时间</th>-->
          <!--&lt;!&ndash;<th>操作</th>&ndash;&gt;-->
          <!--</tr>-->
          <!--</thead>-->
          <!--<tbody id="monitorTbShow">-->
          <!--</tbody>-->
        </table>
      </div>
      <!--all-->
      <!-- sensorStatus -->
      <div class="tab-pane full-height" id="sensor_status_tab">
        <div id="sensorToolbar" class="tableTitleBoxChild" style="position: absolute;
                right: 224px;
                top: 0px;">
          <div class="tableTitleBoxChild">
            <div class="tableTitleBoxChild">
              <div class="tableTitleBoxChild" style="display: block;">
                <div class="" style="" data-i18n="sensorID">传感器ID</div>
                <input id="searchAduId" type="text" autocomplete="off"
                  class="col-sm-4 form-control no-complete-input" />
              </div>
            </div>
            <div class="tableTitleBoxChild" style="display: block;">
              <div class="" style="" data-i18n="connectedState">网络状态</div>
              <select id="aduStateSelect" class="select2" style="width: 150px" onchange="onAuditFindStyleSelect(this)">
                <option value="all" data-i18n="all">全部</option>
                <option value="online" data-i18n="onlineState">在线</option>
                <option value="offline" data-i18n="disconnected">断开</option>
              </select>
            </div>
          </div>
          <div class="btn-group" style="margin-top: 20px;">
            <button type="button" class="btn btn-default btn-sm" onclick="searchAduState()">
              <i class="fa fa-search"><span style="padding-left: 3px" data-i18n="search">搜索</span></i>
            </button>
          </div>
        </div>
        <label id="onlineRate" style="margin: 18px;
                position: absolute;
                top: 8px;
                right: 50px;">[在线率:-/-]</label>
        <table id="tableSensorStatus" class="table text-nowrap table-bordered table-hover full-height table_list"
          role="">
        </table>
      </div>
      <!-- /#substation -->
      <!-- power-equipment-->
      <div class="tab-pane full-height" id="tev_tab">
        <div class="content-footer">
          <button type="button" id="power-equipment-save" class="btn btn-default" style="width: 100px">Save
          </button>
        </div>
      </div>
      <!-- /#power-equipment -->
      <!-- test-point-->
      <div class="tab-pane full-height" id="uhf_tab">
        <div class="content-footer">
          <button type="button" id="test-point-save" class="btn btn-default" style="width: 100px">Save
          </button>
        </div>
      </div>
      <!-- /#test-point -->
      <!-- LDCU-->
      <div class="tab-pane full-height" id="hfct_tab">
        hfct
      </div>
      <!-- /#LDCU -->
      <div class="tab-pane full-height" id="saw_tab">
        saw
        <div class="content-footer">
          <button type="button" id="ldcu-save" class="btn btn-default" style="width: 100px">Save
          </button>
        </div>
      </div>
      <div class="tab-pane full-height" id="ax8_tab">
        zx8

      </div>
      <div class="tab-pane full-height" id="mp_tab">
        mp
      </div>
    </div>

    <!-- <div id="da-progress" class="progress-group hide">
            <span class="progress-text" data-i18n="sampling">正在采集...</span> -->
    <!--<span class="progress-number"></span>-->
    <!--  <div class="progress progress-lg active">
                <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                    aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
        </div> -->
  </div>
  <!-- /.box -->
</div>
<script>
  var winHeight = $(window).height() * 0.82;
  //@ sourceURL=monitorTb.js
  initPageI18n();
  $(window).resize(refreshTableOptions);

  checkNeedKeyboard();
  $('#aduStateSelect').select2({
    minimumResultsForSearch: Infinity,
  })
  $(function () {
    checkSensorStatusTabState();
  });

  function checkSensorStatusTabState () {
    var check = getCookie('user');
    if (check == 'Guest') {
      $('#sensor_status_tab_title').hide();
    } else {
      $('#sensor_status_tab_title').show();
    }
  }

  function getCheckedAduState (state, colorList, needChangeToStatus, unit = '') {
    var result = '';
    var statePic;
    if (state || state == 0) {
      var msg = state + unit;
      if (state > colorList[0]) {
        statePic = "./dist/img/zhjh_dian.png";
        if (needChangeToStatus) {
          msg = getI18nName('strong');
        }
      } else if (colorList[1] < state && state <= colorList[0]) {
        statePic = "./dist/img/zhjh_yellow_dian.png";
        if (needChangeToStatus) {
          msg = getI18nName('weak');
        }
      } else {
        statePic = "./dist/img/zhjh_red_dian.png";
        if (needChangeToStatus) {
          msg = getI18nName('poor');
        }
      }
      result = '<div style="display: flex;height: 100%;align-items: center;justify-content: center;">' +
        '<img style="width: 16px;height: 16px;" src="' + statePic + '"><span>&nbsp;' + msg + '</span>' +
        '</div>'
    } else {
      result = '<div style="display: flex;height: 100%;align-items: center;justify-content: center;">' +
        '-' +
        '</div>';
    }
    return result;
  }

  //切换标签页
  $(".nav-tabs").find("li").click(function () {
    if (!$(this).find("a")[0])
      return;
    var currentTab = $(this).find("a")[0].getAttribute("href");
    switch (currentTab) {
      case "#all_tab":
        $('#singleCollect').show();
        $('#tbRefresh').show();
        break;
      case "#sensor_status_tab":
        $('#singleCollect').hide();
        $('#tbRefresh').hide();
        break;
      case "#power-equipment":

        break;
      case "#test-point":

        break;
      case "#ldcu":

        break;
    }
  });

  $("#tbRefresh").click(function () {
    Pace.restart();
    $('#tableAll').bootstrapTable('refresh', {
      silent: true
    });
    $.notifyUtil.notifySuccess(getI18nName('RefreshComplet'));
    Pace.stop();
  });

  $("#singleCollect").click(function () {
    var message = {
      "methodName": "PushSampleProgress",
      "enablePush": true,
      "parameters": {
        "aduOperationType": 1
      }
    }
    Pace.restart();
    webSocketSend(message);
    $.notifyUtil.notifySuccess(getI18nName('PreStartCollect'));
    Pace.stop();
  });

  function pointSingleCollect (pointId, index, pointName, pointType) {
    var message = {
      "methodName": "PushSampleProgress",
      "enablePush": true,
      "parameters": {
        "aduOperationType": 4,
        "pointId": pointId
      }
    }
    Pace.restart();
    webSocketSend(message);
    // PushSampleProgressNew({
    /*  PushSetAduInfoProgress({
         "id": "00:00:00:00:51:5C:F9:82",
         "index": 4,
         "total": 4,
         "progress": 3,
         "successArr": "00:00:00:00:51:58:45:5E",
         "failedArr": "00:00:00:00:51:58:45:2E,",
         "allArr": "00:00:00:00:51:58:45:2E,00:00:00:00:51:58:45:5E,00:00:00:00:3C:8D:B9:58,00:00:00:00:51:5C:F9:82"
     }); */
    $.notifyUtil.notifySuccess(getI18nName('PreStartCollect'));
    Pace.stop();
  }

  var user = getCookie('user');
  var currentSensorPage = 1;
  var currentSensorPageSize = getSession('sensorStatusPageSize');
  $('#aduStateSelect').val(aduStateSearchParams.aduIsOnline).select2()
  $('#searchAduId').val(aduStateSearchParams.aduId)

  $('#tableSensorStatus').bootstrapTable({
    formatLoadingMessage: function () {
      return getI18nName('loadingData');
    },
    formatRecordsPerPage: function (pageNumber) {
      //            return '每页 ' + pageNumber + ' 条';
      return stringFormat.format(getI18nName('pageSize'), pageNumber);
    },
    formatShowingRows: function (pageFrom, pageTo, totalRows) {
      //            return '显示：' + pageFrom + '-' + pageTo + '，共 ' + totalRows + ' 条';
      return stringFormat.format(getI18nName('pageSelecter'), pageFrom, pageTo, totalRows);
    },
    formatSearch: function () {
      return getI18nName('search');
    },
    formatNoMatches: function () {
      return getI18nName('noMatch');
    },
    //        onClickRow: function (row, element) {
    //            checkChart(row.pointType, row.dataId, row.pointId);
    //        },
    onLoadSuccess: function (data) {
      var onlineRateMsg = `[${getI18nName('ONLINE_RATE')}:${data.onlineRate}]`
      $('#onlineRate').html(onlineRateMsg);
      $('#tableSensorStatus').bootstrapTable('hideLoading');
      if (getSession('sensorStatusIndex') != currentSensorPage) {
        $('#tableSensorStatus').bootstrapTable('selectPage', parseInt(getSession(
          'sensorStatusIndex')));
        //                $('li.bootstrap-table-page-go').find('select').val(getSession('monitorIndex')).trigger('change');
      }
      $('div .pull-left.pagination-detail').css("margin-left", "25px")
      $('div .pull-right.pagination').css("margin-right", "25px")
      Pace.stop();
    },
    onPageChange: function (number, size) {
      currentSensorPage = number;
      currentSensorPageSize = size;
      setSession('sensorStatusIndex', number);
      setSession('sensorStatusPageSize', size);
    },
    method: 'get',
    pagination: true, //分页
    singleSelect: false,
    cache: false,
    striped: true,
    sidePagination: 'server',
    showRefresh: false,
    paginationLoop: false,
    searchAlign: 'left',
    pageList: [7, 10, 25],
    url: serverUrl + GET_ADU_STATE_INFOR_LIST,
    height: $(".nav-tabs-custom").height() - 160,
    search: false,
    paginationPreText: '<<',
    paginationNextText: '>>',
    pageSize: currentSensorPageSize,
    queryParams: function (params) {
      //            debugger;
      return {
        page: params.offset / params.limit + 1,
        size: params.limit,
        aduIsOnline: aduStateSearchParams.aduIsOnline,
        aduId: aduStateSearchParams.aduId,
      };
    },
    columns: [
      //            {
      //                field: 'index',
      //                title: getI18nName('index')
      //            },
      {
        field: 'aduType',
        title: getI18nName('sensorType'),
        formatter: function (value, row, index) {
          var a = getI18nName(row.aduType);
          return '<div style="white-space: pre-wrap;">' + a + '</div>';
        }
      }, {
        field: 'deviceName',
        title: getI18nName('deviceNameTable'),
        formatter: function (value, row, index) {
          var a = value ? value : '-';
          return '<div style="white-space: pre-wrap;">' + a + '</div>';
        }
      }, {
        field: 'aduId',
        title: getI18nName('aduId'),
        formatter: function (value, row, index) {
          var a = row.aduId;
          return '<div title=' + a +
            ' style="white-space: pre-wrap;word-wrap : break-word ;user-select: text;">' +
            a +
            '</div>';
        }
      }, {
        field: 'connectedState',
        title: getI18nName('connectedState'),
        width: language != 'zh-CN' && language != 'zh-TW' ? '200px' : undefined,
        formatter: function (value, row, index) {
          var a = value;
          var contentWidth = language != 'zh-CN' && language != 'zh-TW' ? '50%' : '40%';
          switch (row.connectedState) {
            case 'aduIsConnected':
              // 在线
              a = '<img title="' + getI18nName('onlineState') +
                '" style="width: 16px;height: 16px;" src="./dist/img/zhjh_dian.png"/>' +
                '<span>&nbsp' + getI18nName('onlineState') + '&nbsp</span>';
              break;
            case 'aduIsDisconnected':
              // 失联
              a = '<img title="' + getI18nName('offlineState') +
                '" style="width: 16px;height: 16px;" src="./dist/img/zhjh_red_dian.png"/>' +
                '<span>&nbsp' + getI18nName('offlineState') + '&nbsp</span>';
              break;
            case 'aduNeverConnected':
              // 未连接
              a = '<img title="' + getI18nName('aduNeverConnectedState') +
                '" style="width: 16px;height: 16px;" src="./dist/img/zhjh_yellow_dian.png"/>' +
                '<span>&nbsp' + getI18nName('aduNeverConnectedState') + '</span>';
              break;
            case 'aduBreakOff':
              // 断开
              a = '<img title="' + getI18nName('disconnected') +
                '" style="width: 16px;height: 16px;" src="./dist/img/zhjh_gray_dian.png"/>' +
                '<span>&nbsp' + getI18nName('disconnected') + '</span>';
              break;
          }
          return '<div style="display: flex;height: 100%;align-items: center;justify-content: center;"><div style="width: ' + contentWidth + ';display: flex">' +
            a + '</div></div>';
        }
      },
      {
        field: 'commType',
        title: getI18nName('commType')
      }, {
        field: 'loraSignalLevel',
        title: getI18nName('loraSignalLevel'),
        formatter: function (value, row, index) {
          var loraSignalLevel = row.loraSignalLevel;
          var needChangeToState = true;
          var unit = ''
          // if (user == 'SuperUser') {
          needChangeToState = false;
          unit = loraSignalLevel != undefined ? 'dBm' : '';
          // }
          //2.0.0 信号强度为-32768视为无效值，展示'-'
          let result = getCheckedAduState(row.loraSignalLevel, ADUStateColor[0],
            needChangeToState, unit);
          if (loraSignalLevel == -32768) result = '-';
          return result;
        }
      }, {
        field: 'loraSNR',
        title: getI18nName('loraSNR'),
        formatter: function (value, row, index) {
          var loraSNR = row.loraSNR;
          var needChangeToState = true;
          var unit = ''
          // if (user == 'SuperUser') {
          needChangeToState = false;
          unit = loraSNR != undefined ? 'dB' : '';
          // }
          let result = getCheckedAduState(loraSNR, ADUStateColor[1], needChangeToState, unit);
          //2.0.0 信噪比为-32768视为无效值，展示'-'
          if (loraSNR == -32768) result = '-';
          return result;
        }
      }, {
        field: 'powerSupplyMode',
        title: getI18nName('powerSupplyMode'),
        formatter: function (value, row, index) {
          var a = row.powerSupplyMode;

          switch (row.powerSupplyMode) {
            case 'byBattery':
              a = getI18nName('byBattery');
              break;
            case 'byCable':
              a = getI18nName('byCable');
              break;
          }

          return a;
        }
      }, {
        field: 'battaryState',
        title: getI18nName('batteryVoltage'),
        formatter: function (value, row, index) {
          var state = row.battaryState;
          var battaryPercent = (state == -1 || state == undefined) ? getI18nName('unknown') :
            row.battaryPercent == undefined ? '-' : row.battaryPercent + '%';
          var color = '';
          //根据状态改变颜色并展示状态，如果是权限用户则展示百分比
          switch (state) {
            case undefined:
            case -1:
              // state = getI18nName('unknown')
              color = '#7f7f7f'
              break;
            case 0:
              // state = getI18nName('fault')
              color = '#7f7f7f'
              break;
            case 1:
              // state = getI18nName('lowPower')
              color = '#f42e07'
              break;
            case 2:
              // state = getI18nName('mediumPower')
              color = '#ffc100'
              break;
            case 3:
              // state = getI18nName('highPower')
              color = '#00a65a'
              break;
          }
          //电池状态修改为电池电压展示，电压无或者0时认为无效值展示'-'
          state = row.battaryVoltage == undefined || row.battaryVoltage == 0 ? '-' : row.battaryVoltage + 'mV';
          if (row.powerSupplyMode == "byCable") {
            state = "-"
          }
          var content = '<sapn style="color:' + color + '">' + state + '</span>'
          var item =
            '<div style="display: flex;height: 100%;align-items: center;justify-content: center;">' +
            content + '</div>'
          return item;
        }
      }
      // , {
      //     field: 'battaryPercent',
      //     title: getI18nName('battaryPercent'),
      //     formatter: function (value, row, index) {
      //         var a = row.battaryPercent;
      //         return getCheckedAduState(a, ADUStateColor[2]);
      //     }
      // }
      // , {
      //     field: 'battaryLife',
      //     title: getI18nName('battaryLife')
      // }
      , {
        field: 'updateTime',
        title: getI18nName('updateTime'),
        formatter: function (value, row, index) {
          let result = value;
          if (value == undefined || value == '') {
            result = '-';
          } else {
            var time = result.replace(' ', '</br>');
            result = `<div>${time}</div>`
          }
          return result;
        }
      }, {
        field: 'disconnectTime',
        title: getI18nName('DISCONNECT_TIME'),
        formatter: function (value, row, index) {
          let result = value;
          if (value == undefined || value == '') {
            result = '-';
          } else {
            var time = result.replace(' ', '</br>');
            result = `<div>${time}</div>`
          }
          return result;
        }
      }
    ],
    onRefresh: function () {
      var test = {
        "index": "1",
        "aduId": "test1",
        "deviceName": "TestDev",
        "isConnection": "false",
        "isConnected": "false",
        "pointId": "123",
        "pointName": "TestPoint",
        "pointType": "FLOOD",
        "time": "2019/02/15 17:01:45",
      }
    }
  });


  var currentPage = 1;
  var currentPageSize = 7;
  $('#tableAll').bootstrapTable({
    formatLoadingMessage: function () {
      return getI18nName('loadingData');
    },
    formatRecordsPerPage: function (pageNumber) {
      //            return '每页 ' + pageNumber + ' 条';
      return stringFormat.format(getI18nName('pageSize'), pageNumber);
    },
    formatShowingRows: function (pageFrom, pageTo, totalRows) {
      //            return '显示：' + pageFrom + '-' + pageTo + '，共 ' + totalRows + ' 条';
      return stringFormat.format(getI18nName('pageSelecter'), pageFrom, pageTo, totalRows);
    },
    formatSearch: function () {
      return getI18nName('search');
    },
    formatNoMatches: function () {
      return getI18nName('noMatch');
    },
    //        onClickRow: function (row, element) {
    //            checkChart(row.pointType, row.dataId, row.pointId);
    //        },
    onLoadSuccess: function (data) {
      $('#tableAll').bootstrapTable('hideLoading');
      if (getSession('monitorIndex') != currentPage) {
        currentPage = getSession('monitorIndex');
        $('#tableAll').bootstrapTable('selectPage', parseInt(getSession('monitorIndex')));
        //                $('li.bootstrap-table-page-go').find('select').val(getSession('monitorIndex')).trigger('change');
      }
      $('div .pull-left.pagination-detail').css("margin-left", "25px")
      $('div .pull-right.pagination').css("margin-right", "25px")
      Pace.stop();
    },
    onPageChange: function (number, size) {
      currentPage = number;
      currentPageSize = size;
      setSession('monitorIndex', number);
      setSession('monitorPageSize', size);
    },
    method: 'get',
    pagination: true, //分页
    singleSelect: false,
    cache: false,
    striped: true,
    sidePagination: 'server',
    showRefresh: false,
    paginationLoop: false,
    searchAlign: 'left',
    pageList: [7, 10, 25],
    url: serverUrl + "GetMonitorTableInfo",
    //        url: mokerServer + "GetMonitorTableInfo",
    height: winHeight * 0.89,
    search: false,
    paginationPreText: '<<',
    paginationNextText: '>>',
    pageSize: getSession('monitorPageSize'),
    rowStyle: function (row, index) {
      var style = {};
      style = {
        css: {
          'font-size': '14px'
        }
      };
      return style;
    },
    queryParams: function (params) {
      //            debugger;
      return {
        page: params.offset / params.limit + 1,
        size: params.limit,
      };
    },
    columns: [{
      field: 'index',
      title: getI18nName('index')
    },
    {
      field: 'deviceName',
      title: getI18nName('deviceNameTable')
    }, {
      field: 'pointName',
      title: getI18nName('pointName')
    }, {
      field: 'aduId',
      title: getI18nName('sensorID'),
      formatter: function (value, row, index) {
        var a =
          '<span style="word-break:normal;width:auto;display:block;white-space:pre-wrap;word-wrap : break-word ;overflow: hidden ;">' +
          value + '</span>';
        return a;
      }
    }, {
      field: 'pointType',
      title: getI18nName('pointType'),
      formatter: function (value, row, index) {
        //value：当前field的值，即id
        //row：当前行的数据
        var tempType = row.pointType;
        if (tempType == 'MEU') {
          tempType = 'MP';
        }
        var a = getI18nName(tempType);;
        return a;
      }
    },
    {
      field: 'totalDataNumber',
      title: getI18nName('DATA_TOTAL_COUNT'),
    },
    // 临时去除-by liyaoxu 20201013
    // {
    //     field: 'isconnected',
    //     title: getI18nName('isconnected'),
    //     formatter: function (value, row, index) {
    //         //value：当前field的值，即id
    //         //row：当前行的数据
    //         var a = row.isconnected === true ? getI18nName('connected') : getI18nName(
    //             'disconnected');
    //         return a;
    //     }
    // }, 
    {
      field: 'time',
      title: getI18nName('updateTime')
    }
      //            , {//暂时为密度微水独有
      //                field: 'data',
      //                title: getI18nName('data'),
      //                formatter: function (value, row, index) {
      //                    var a = "-";
      //                    if (row.data) {
      //                        a = row.data;
      //                    }
      //                    return a;
      //                }
      //            }
      , {
      field: 'check',
      title: getI18nName('chart'),
      formatter: function (value, row, index) {
        //value：当前field的值，即id
        //row：当前行的数据

        var a = '<button class="btn btn-primary table-item-btn" onclick="checkChart(' +
          '\'' + row.pointType + '\'' +
          ',' +
          row.dataId +
          ',' +
          '\'' + row.pointId + '\'' +
          ',' +
          '\'' + row.time + '\'' +
          ',' +
          '\'' + row.aduId + '\'' +
          ')">' + getI18nName('lookUp') + '</button>';
        if (checkDetailNeedDisable(row.pointType)) { //row.pointType == "SF6YN"
          a = '<button class="btn table-item-btn disabled" >' + getI18nName('lookUp') +
            '</button>';
        }
        return a;
      }
    }, {
      field: 'check',
      title: getI18nName('operate'),
      formatter: function (value, row, index) {
        //value：当前field的值，即id
        //row：当前行的数据
        var tempType = row.pointType;
        if (tempType == 'MEU') {
          tempType = 'MP';
        }
        var pointType = getI18nName(tempType);;
        var a =
          '<button class="btn btn-primary table-item-btn" onclick="pointSingleCollect(' +
          '\'' + row.pointId + '\',' +
          '\'' + index + '\',' +
          '\'' + row.pointName + '\',' +
          '\'' + pointType + '\'' +
          ')">' + getI18nName('collect') + '</button>';
        if (row.pointType == "MEU" || row.pointType == "Vibration") {
          a = '<button class="btn table-item-btn disabled" >' + getI18nName('collect') +
            '</button>';
        }
        if (row.pointType == "FAN" || row.pointType == "LIGHT") {
          var loading = 'none';
          var onClass = row.data == 0 ? ' active' : '';
          var offClass = row.data == 0 ? '' : ' active';
          a += '<div style="position: relative">' +
            '<img src="./dist/img/loader-dark.gif" style="width: 24px;position: absolute;left: 0;top: 12px;display:' +
            loading + '"/>' +
            '<button class="table-item-switch on' + onClass +
            '" onclick="onItemSwitchClick(this,' +
            '\'' + row.aduId + '\'' + ',\'' + row.data + '\'' +
            ')"><span>开</span></button>' +
            '<button class="table-item-switch off' + offClass +
            '" onclick="onItemSwitchClick(this,' +
            '\'' + row.aduId + '\'' + ',\'' + row.data + '\'' +
            ')"><span>关</span></button>' +
            '</div>'
        }
        return a;
      }
    }
    ],
    onRefresh: function () {

    }
  });

  function getChannelData (rowData) {
    var value = "";
    switch (rowData.pointType) {
      case "FrostPointRaw":
      case "FrostPointATM":
      case "DewPointRaw":
      case "DewPointATM":
      case "Moisture":
      case "AbsolutePressure":
      case "NormalPressure":
      case "Density":
      case "Oxygen":
      case "SF6":
      case "SPTR":
        value = "  /  " + rowData.data;
        break;
      default:
        break;
    }
    return value;
  }

  $('#tableAll').bootstrapTable('showLoading');
  setTimeout(function () {
    var message = {
      "methodName": "PushMonitorTbInfo",
      "enablePush": true,
      "parameters": ""
    }
    webSocketSend(message);
  }, 2 * 1000); //延迟2000毫秒

  function PushMonitorTbInfoCallback (respData) {
    if (respData == true) {
      $('#tableAll').bootstrapTable('refresh', {
        silent: true
      });
      $('#tableSensorStatus').bootstrapTable('refresh', {
        silent: true
      });
    }
  }

  function refreshTableOptions () {
    var height = $(".nav-tabs-custom").height() - 60;
    $(".tab-content").height(height);
    //        $('#tableAll').bootstrapTable('refreshOptions', option);
    $('#tableAll').bootstrapTable('resetView', {
      height: height
    });
    $('#tableSensorStatus').bootstrapTable('resetView', {
      height: height
    });
  }

  function loadChartCommon (pageUrl, pageId) {
    //加载页面内容
    var href = pageUrl + '.html';
    var id = pageId ? pageId : pageUrl;
    $('.content-wrapper>.content').empty();
    var content = $("<div />", {
      id: id,
      class: "row full-height"
    });
    $('.content-wrapper>.content').append(content);
    $('#' + id).load("pages/" + href);

    //修改页面名称
    $('.page-title').html(getI18nName(pageId ? pageId : pageUrl));
  }
</script>