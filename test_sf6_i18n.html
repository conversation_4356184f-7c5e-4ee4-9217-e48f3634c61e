<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SF6 国际化测试</title>
    <script src="./dist/js/jquery.min.js"></script>
    <script src="./config/config.js"></script>
    <script src="./dist/js/lang-i18n.js"></script>
</head>
<body>
    <h1>SF6 国际化测试页面</h1>
    
    <h2>测试新增的翻译项：</h2>
    <ul>
        <li data-i18n="dataType">数据类型</li>
        <li data-i18n="startTime">开始时间</li>
        <li data-i18n="endTime">结束时间</li>
        <li data-i18n="dataDetails">数据详情</li>
        <li data-i18n="refreshTrend">刷新趋势图</li>
        <li data-i18n="sf6TrendChart">SF6数据趋势</li>
        <li data-i18n="trendChart">趋势图</li>
        <li data-i18n="noTrendData">暂无趋势数据</li>
        <li data-i18n="loadTrendDataFailed">加载趋势数据失败</li>
        <li data-i18n="startTimeAfterEndTime">开始时间不能晚于结束时间</li>
        <li data-i18n="SF6_Pressure">SF6气体压力</li>
        <li data-i18n="SF6_Temperature">SF6气体温度</li>
        <li data-i18n="SF6_EnvTemperature">设备环境温度</li>
        <li data-i18n="SF6_SuperCapVoltage">超级电容电压</li>
    </ul>
    
    <h2>测试已存在的翻译项：</h2>
    <ul>
        <li data-i18n="pointName">测点名称</li>
        <li data-i18n="sensorCode">传感器编码</li>
        <li data-i18n="sensorType">传感器类型</li>
        <li data-i18n="sample_date">采样日期</li>
        <li data-i18n="sample_time">采样时间</li>
        <li data-i18n="SF6_Density">SF6气体密度</li>
        <li data-i18n="SF6YN">SF6气体压力</li>
        <li data-i18n="SF6_Temp">SF6气体温度</li>
        <li data-i18n="Device_env_Temp">设备环境温度</li>
        <li data-i18n="Alarm_Status">告警状态</li>
        <li data-i18n="batteryVoltage">电池电压</li>
        <li data-i18n="superCapvoltage">超级电容电压</li>
        <li data-i18n="preData">上一条</li>
        <li data-i18n="nextData">下一条</li>
    </ul>
    
    <h2>语言切换测试：</h2>
    <button onclick="changeLanguage('zh-CN')">中文</button>
    <button onclick="changeLanguage('en-US')">English</button>
    
    <h2>当前语言：</h2>
    <p id="currentLang"></p>
    
    <script>
        // 初始化国际化
        initGlobalParams();
        
        // 显示当前语言
        function updateCurrentLang() {
            document.getElementById('currentLang').textContent = language;
        }
        
        // 页面加载完成后初始化
        $(document).ready(function() {
            setTimeout(function() {
                initPageI18n();
                updateCurrentLang();
            }, 500);
        });
        
        // 重写changeLanguage函数以更新显示
        var originalChangeLanguage = changeLanguage;
        changeLanguage = function(lan, callback) {
            originalChangeLanguage(lan, function() {
                updateCurrentLang();
                if (callback) callback();
            });
        };
    </script>
</body>
</html>
