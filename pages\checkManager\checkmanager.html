<style>
    .tableTitleBoxChild {
        margin-right: 15px;
        display: flex;
        align-items: center;
        float: left;
    }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin  full-height">
        <div class="box-body">
            <div class="tab-content " style="padding:0px;height: 90%">
                <div class="tab-pane full-height active" id="all_tab">
                    <table id="tableCheck" class="table table-bordered table-hover full-height">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var winHeight = $(window).height() * 0.82;
    $(window).resize(refreshTableOptions);
    initPageI18n();

    function refreshTableOptions() {
        var height = $(".nav-tabs-custom").height() - 120;
        $(".tab-content").height(height);
        var option = $('#tableCheck').bootstrapTable('getOptions');
        option.length = winHeight * 0.89;
    }
    initCheckManagerTable();
</script>