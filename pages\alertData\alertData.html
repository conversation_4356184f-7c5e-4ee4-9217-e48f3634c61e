<!--
 * @Author: liyaoxu <EMAIL>
 * @LastEditors: liyaoxu <EMAIL>
 * @FilePath: \docroot\pages\alertData\alertData.html
-->
<style>
    .tableTitleBoxChild {
        margin-right: 15px;
        display: flex;
        align-items: center;
        float: left;
        width: 400px;
    }

    .bs-bars {
        margin-left: 0 !important;
    }

    td {
        user-select: text !important;
    }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin  full-height">
        <div class="box-body">
            <div class="tab-content " style="padding:0px;height: 90%">
                <div class="tab-pane full-height active" id="all_tab">
                    <div id="alertToolbar" class="tableTitleBoxChild">
                        <div class="tableTitleBoxChild">
                            <div class="tableTitleBoxChild" style="display: block;">
                                <div class="" style="" data-i18n="sensorID">传感器ID</div>
                                <input id="sensorId" type="text" autocomplete="off"
                                    class="col-sm-4 form-control no-complete-input" />
                            </div>
                        </div>
                        <div class="btn-group" style="margin-left: 70px;margin-top: 35px;">
                            <button type="button" class="btn btn-default btn-sm" onclick="searchAlertData()">
                                <i class="fa  fa-search"><span style="padding-left: 3px"
                                        data-i18n="search">搜索</span></i>
                            </button>
                        </div>
                    </div>
                    <table id="tableAlertData" class="table table-bordered table-hover full-height">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var winHeight = $(window).height() * 0.82;
    $('.select2').select2({
        minimumResultsForSearch: Infinity,
    });
    $(window).resize(refreshTableOptions);
    initPageI18n();

    function refreshTableOptions() {
        var height = $(".nav-tabs-custom").height() - 120;
        $(".tab-content").height(height);
        var option = $('#tableAlertData').bootstrapTable('getOptions');
        option.length = winHeight * 0.89;
    }
    initAlertDataManagerTable();
</script>