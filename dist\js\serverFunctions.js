/*
 * 初始化websocket
 * -----------------------
 */
function initWebSocket() {
  clientGUID = generateGUID();
  websocket = new WebSocket(wsUri);
  //开启
  websocket.onopen = function (ev) {
    Pace.stop();
    // console.log('Connected to WebSocket server');
  };
  //回送消息
  websocket.onmessage = function (ev) {
    try {
      var msg = JSON.parse(ev.data);
      if (msg.success != undefined) {
        if (msg.success == 'true' || msg.success == true) {
          if (
            msg.errorCode == 900 ||
            msg.errorCode == 901 ||
            msg.errorCode == 907
          ) {
            if (msg.errorCode == 901 && getSession('isLogOut') == 'false') {
              $('.notifications')
              .notify({
                message: {
                  text: getI18nName('WS_901_ERROR_TIPS'),
                },
                type: 'warning',
              })
              .show();
              return;
            }
            var msg = convertErrorCode(msg.errorCode);

            $('.notifications')
              .notify({
                message: {
                  text: msg,
                },
                type: 'warning',
              })
              .show();

            if (msg.errorCode == 907) {
              logOut('logOut');
            }
          } else {
            $.each(msg.result, function (k, v) {
              if (k != 'UpdateSysState') {
                // console.log(ev);
              }
              switch (k) {
                case 'UpdateSysState':
                  updateSysState(v);
                  break;
                case 'UpdateADU':
                  updateADUState(v);
                  break;
                case 'SyncAduDataProgress':
                  syncAduDataState(v);
                  break;
                case 'PowerControl':
                  todoPower(v);
                  break;
                case 'PushMonitorTbInfo':
                  PushMonitorTbInfoCallback(v);
                  break;
                case 'PushSampleProgress':
                  //暂定取消该推送 2018/03/21an
                  // PushSampleProgress(v);
                  PushSampleProgressNew(v, msg.errorCode);
                  break;
                case 'PushSetAduInfoProgress':
                case 'PushSetAduChannelInfoProgress':
                  PushSetAduInfoProgress(v, msg.errorCode, k);
                  break;
                case 'repeatLogin':
                  logOut(k);
                  break;
                case 'PushLogInfo':
                  // console.log(v);
                  break;
                case 'ExportData':
                  onExportData(v);
                  break;
                case 'ImportData':
                  onImportData(v, msg.errorCode);
                  break;
                case 'PushGetADULogProgress':
                  onExportLogData(v);
                  break;
                case 'PushGetADUAlarmProgress':
                  syncAlertDataProgress(v);
                  break;
                case 'Logout':
                  logOut('logOut');
                  break;
                case 'PushFindAduGroupNoProgress':
                  onFindAduGroupNo(v, msg.errorCode, k);
                  break;
                case 'cancelFindResult':
                  onFindCancel();
                  break;
                case 'PushFastSyncDataStatus':
                  dataFastSync &&
                    dataFastSync.onPushFastSyncDataStatus(v, msg.errorCode, k);
                  break;
              }
            });
            // checkSocketMsg(msg);
          }
        } else {
          if (msg.errorCode == 907) {
            logOut('logOut');
          } else {
            $('.notifications')
              .notify({
                fadeOut: {
                  enabled: false,
                },
                message: {
                  text: stringFormat.format(
                    getI18nName('errorTip'),
                    convertErrorCode(msg.errorCode, msg.result)
                  ),
                },
                type: 'danger',
              })
              .show();
            dismissIProgress();
          }
        }
      }
    } catch (err) {
      console.log(ev);
      dismissIProgress();
    }
  };
  //错误
  websocket.onerror = function (ev) {
    console.log('WebSocket error occured: ' + ev.data);
    Pace.stop();
  };
  //关闭
  websocket.onclose = function (ev) {
    console.log('WebSocket connection closed');
    Pace.stop();
    logOut('wsLogOut');
  };
}

function commHttpFaliedCallback(result) {
  if (!result) {
    return;
  }
  switch (result.errorCode) {
    case 900:
      $.notifyUtil.notifyWarning(convertErrorCode(result.errorCode));
      break;
    case 901:
      $.notifyUtil.notifyWarning(convertErrorCode(result.errorCode));
      logOut('repeatLogin');
      break;
    case 2200202:
      $.notifyUtil.notifyError(convertErrorCode(result.errorCode));
      break;
    case 601:
      $.notifyUtil.notifyError(convertErrorCode(result.errorCode));
      break;
    case 801:
    case 802:
      $('.notifications')
        .notify({
          fadeOut: {
            enabled: false,
          },
          message: {
            text: stringFormat.format(
              getI18nName('errorTip'),
              convertErrorCode(result.errorCode)
            ),
          },
          type: 'danger',
        })
        .show();
      break;
    // case 901:
    //     layer.alert("非法用户");
    //     window.location.reload();
    //     break;
    // case 10001:
    //     $.notifyUtil.notifyError("测点已存在");
    //     break;
    default:
      return;
  }
}

function getFileTestPointTree() {
  var data = null;
  var zNodes = [];

  var setting = {
    data: {
      simpleData: {
        idKey: 'id',
        pIdKey: 'pId',
        name: 'name',
        enable: true,
      },
    },
    async: {
      enable: true,
      url: serverUrl + 'GetRelationList',
      type: 'GET',
      dataFilter: ajaxDataFilterShowRelationList,
      autoParam: ['id', 'type'],
    },
    callback: {
      onExpand: zTreeOnExpandPointSet,
      onClick: zTreeOnclickPointSet,
    },
  };

  poll('GET', 'GetStation', data, function (text) {
    var respData = text.result;
    if (respData.stationName === null || respData.stationName.length === 0)
      return;
    var tempdata = {
      id: respData.id,
      pId: 0,
      name: respData.stationName,
      open: true,
      type: 2,
      iconOpen: './plugins/zTree/css/zTreeStyle/img/diy/1_open.png',
      iconClose: './plugins/zTree/css/zTreeStyle/img/diy/1_close.png',
      isParent: true,
    };
    zNodes.push(tempdata);
    selectedArchives.substation = respData.id;
    $('#station').append(
      $('<option />', {
        value: '' + 0 + '',
      }).append(respData.stationName)
    );
    $.fn.zTree.init($('#station_tree'), setting, zNodes);
  });

  data = 'type=2';
  poll('GET', 'GetRelationList', data, function (text) {
    $('#dev').empty();
    var respData = text.result;
    if (respData === null || respData.length === 0) return;

    respData.forEach(function (value, index) {
      var option = $('<option />', {
        value: '' + value.id + '',
      }).append(value.name);
      $('#dev').append(option);
    });
    selTestPoint_onChange();
  });
}

function ajaxDataFilterShowRelationList(treeId, parentNode, responseData) {
  // debugger;
  if (parentNode.level === 0) {
    for (var i = 0; i < responseData.result.length; i++) {
      responseData.result[i].type = 3;
    }
    return responseData.result;
  } else if (parentNode.level === 1) {
    for (var i = 0; i < responseData.result.length; i++) {
      responseData.result[i].type = 4;
      if (!responseData.result[i].isParent) {
        responseData.result[i].name = responseData.result[i].pointName;
      }
      if (validateStrLen(responseData.result[i].name) == 0) {
        responseData.result[i].name = responseData.result[i].pointName;
      }
    }
    return responseData.result;
  } else if (parentNode.level === 2) {
    return responseData.result;
  }
}

function getRelationTestPoint(testPointId) {
  var dataP = 'testPointId=' + testPointId;
  poll('GET', 'GetRelation', dataP, function (text) {
    var respData = text.result;
    if (respData === null || respData.length === 0) return;

    respData.forEach(function (value, index) {
      var option = $('<option />', {
        value: '' + value.pointId + '',
      }).append(value.pointName);
      $('#dev').append(option);
    });
    selTestPoint_onChange();
  });
}

function GetAduTypeListRelation() {
  var data = 'type=3';
  poll('GET', 'GetCommonType', data, function (text) {
    var respData = text.result;
    if (respData.length <= 0) return;

    $('#inst_type').empty();
    $('#inst_chns').empty();
    var data = respData;
    if (data.length > 0) {
      data.forEach(function (value, index) {
        var option = $(
          '<option />',
          {
            value: '' + value + '',
          },
          {
            onclick: 'selChns_onchange();',
          }
        ).append(getI18nName(value));
        $('#inst_type').append(option);
      });
    }
    selAduType_onChange();
  });
}

function GetChannelListRelation(aduId) {
  var data = 'aduId=' + aduId;
  poll('GET', 'GetChannelList', data, function (text) {
    $('#inst_chns').empty();
    var respData = text.result;
    if (respData.length <= 0) return;
    for (var index = 0; index < respData.length; index++) {
      var item = respData[index];
      // if (respData.length == 1) {
      if (index == 0) {
        var temp = $('<option />', {
          value: '-99',
        }).append(getI18nName('none'));
        $('#inst_chns').append(temp);
      }
      var option = $('<option />', {
        value: '' + item.channelIndex + '',
      }).append(item.channelType + '_' + item.channelIndex);
      $('#inst_chns').append(option);
    }
    // debugger;
    if (mCallback != undefined) {
      mCallback(mCallData);
      mCallback = undefined;
    }
    // selChns_onchange();
  });
}

/*
 * 更新系统状态
 * -----------------------
 */
function updateSysState(respData) {
  for (var key in respData) {
    var value = respData[key];
    switch (key) {
      case 'network':
        {
          value > 4 ? (value = 4) : {};
          value < 0 ? (value = 0) : {};
          var imgurl = 'url(/dist/img/signal';
          if ($('body').attr('class').indexOf('black') > -1) {
            imgurl = imgurl + '-dark-' + value + '.png)';
          } else {
            imgurl = imgurl + '-' + value + '.png)';
          }
          $('.dropdown>a>i.signal').css({
            'background-image': imgurl,
          });
        }
        break;
      case 'dateTime':
        {
          var date = new Date(value);
          $('.time-mini>p').html(date.pattern('MM/dd<br>HH:mm'));
          $('.time-lg>p').html(date.pattern('yyyy/MM/dd HH:mm:ss'));
        }
        break;
      case 'cpu':
      case 'disk':
      case 'memory':
        {
          $('#' + key)
            .find('.progress-bar')
            .removeClass('progress-bar-green');
          $('#' + key)
            .find('.progress-bar')
            .removeClass('progress-bar-yellow');
          $('#' + key)
            .find('.progress-bar')
            .removeClass('progress-bar-red');
          var state = 'green';
          var fValue = parseFloat(value);
          if (fValue >= 60 && fValue < 80) {
            state = 'yellow';
          } else if (fValue >= 80) {
            state = 'red';
          }
          var showPar = '';
          if (key.toUpperCase() === 'DISK') showPar = getI18nName('DISK');
          else if (key.toUpperCase() === 'MEMORY')
            showPar = getI18nName('MEMORY');
          else if (key.toUpperCase() === 'CPU') showPar = 'CPU';
          $('#' + key)
            .find('h3')
            .html(showPar + '<small class="pull-right">' + value + '</small>');
          $('#' + key)
            .find('.progress-bar')
            .addClass('progress-bar-' + state)
            .css({
              width: value,
            });
        }
        break;
      default:
        break;
    }
  }
}

/*
 * 获取系统参数信息
 * -----------------------
 */
function getSysConfig() {
  var data = null;
  poll('GET', 'GetSysConfig', data, function (text) {
    var respData = text.result;
    for (var item in respData) {
      $('#' + item).empty();
      var data = respData[item];
      if (data.length > 0) {
        data.forEach(function (value, index) {
          var option = $('<option />', {
            value: '' + value + '',
          }).append(value);
          $('#' + item).append(option);
        });
      }
    }
    getSysInfo();
  });
}

/*
 * 获取系统全局信息
 * -----------------------
 */
function getSysInfo() {
  var data = null;
  poll(
    'GET',
    GET_Sys_Info,
    data,
    function (text) {
      var respData = text.result;
      (function () {
        for (var i = 0; i < 24; i++) {
          $('#wakeStartTime').append(
            '<option value="' + i + '">' + i + '</option>'
          );
        }
        var $MonitorType = $('#MonitorType');
        var cacheValue =
          MONITOR_TYPE_CODE == 'MonitorTypeCollectionNode'
            ? MONITOR_TYPE_CODE
            : respData.MonitorType;
        currentMonitorType = cacheValue;
        switch (cacheValue) {
          case 'MonitorTypeHanging':
          case 'MonitorTypeLowPower':
            $MonitorType.append(
              '<option value="MonitorTypeHanging" selected="selected" data-i18n="MonitorTypeHanging">壁挂 </option>'
            );
            $MonitorType.append(
              '<option value="MonitorTypeLowPower" data-i18n="MonitorTypeLowPower"> 低功耗太阳能主机 </option>'
            );
            break;
          case 'MonitorTypeCollectionNode':
          case 'MonitorType2U':
            $MonitorType.append(
              '<option value = "MonitorTypeCollectionNode" data-i18n = "MonitorTypeCollectionNode"> 汇集节点 < / option >'
            );
            $MonitorType.append(
              '<option value="MonitorTypeHanging" data-i18n="MonitorTypeHanging">壁挂 </option>'
            );
            $MonitorType.append(
              '<option value="MonitorType2U" data-i18n="MonitorType2U">2U</option>'
            );
            $MonitorType.append(
              '<option value = "MonitorTypeLowPower" data-i18n = "MonitorTypeLowPower" > 低功耗太阳能主机 < / option >'
            );
            $MonitorType.attr('disabled', 'disabled');
            break;
        }
        if (cacheValue == 'MonitorTypeLowPower') {
          $('#monitorSleepSpace-d').removeClass('hide');
        } else {
          $('#monitorSleepSpace-d').removeClass('hide').addClass('hide');
        }
        initPageI18n();
      })();
      for (var item in respData) {
        switch (item) {
          case 'MonitorType':
            $('#MonitorType-d').removeClass('hide');
            $('#MonitorType').val(respData[item]).select2();
            break;
          case 'date':
            $('#systemDate').daterangepicker({
              opens: 'right',
              autoApply: true,
              timePicker: false,
              singleDatePicker: true,
              locale: {
                format: 'YYYY-MM-DD',
              },
              startDate: new Date(respData[item]),
            });
            break;
          case 'time':
            $('#systemTime').timepicker('setTime', respData[item]); //不获取配置文件的
            break;
          case 'frequency':
            $('#frequency').val(respData[item]).select2();
            break;
          case 'monitorWorkGroup':
            $('#monitor_work_group-d').removeClass('hide');
            $('#monitor_work_group').val(respData[item]);
            break;
          case 'monitorAwakeTime':
            $('#monitorAwakeTime-d').removeClass('hide');
            $('#monitorAwakeTime').val(respData[item]);
            break;
          case 'monitorSleepSpace':
            $('#monitorSleepSpace-d').removeClass('hide');
            $('#monitorSleepSpace').val(respData[item]);
            break;
          case 'wakeStartTime':
            $('#wakeStartTime-d').removeClass('hide');
            $('#wakeStartTime').val(respData[item]);
            break;
          case 'pdSampleInterval':
            $('#pdSampleInterval-d').removeClass('hide');
            $('#pdSampleInterval').val(respData[item]);
            break;
          case 'meuSampleInterval':
            $('#meuSampleInterval-d').removeClass('hide');
            $('#meuSampleInterval').val(respData[item]);
            break;
          case 'uploadInterval':
            $('#uploadInterval-d').removeClass('hide');
            $('#uploadInterval').val(respData[item]);
            break;
          case 'sampleInterval':
            $('#sampleInterval-d').removeClass('hide');
            $('#sampleInterval').val(respData[item]);
            break;
          case 'monitorSampleTime':
            $('#monitorSampleTime-d').removeClass('hide');
            $('#monitorSampleTime').val(respData[item]);
            break;
          case 'monitorSampleInterval':
            $('#monitorSampleInterval-d').removeClass('hide');
            $('#monitorSampleInterval').val(respData[item]);
            break;
          case 'lora_frequency':
            $('#LoraFrequency-d').removeClass('hide');
            $('#LoraFrequency').val(respData[item]).trigger('change');
            break;
          // case "isDaq":
          //     $("#isDaq").bootstrapSwitch('state', respData[item]);
          //     break;
          default:
            $('#' + item)
              .val(respData[item])
              .trigger('change');
            break;
        }
      }
      if (MONITOR_TYPE_CODE == 'MonitorTypeCollectionNode') {
        $('#MonitorType').val('MonitorTypeCollectionNode').trigger('change');
      }
    },
    function () {
      initPageI18n();
    }
  );
}

/*
 * 保存系统全局信息
 * -----------------------
 */
function saveSysInfo(formData) {
  poll(
    'POST',
    'SaveSysInfo',
    formData,
    function (text) {
      poll('Get', GET_Sys_Info, null, function (text) {
        var respData = text.result;
        initPageMonitoType(respData);
      });
      $('.notifications')
        .notify({
          message: {
            text: getI18nName('saveSuccess'),
          },
          type: 'success',
        })
        .show();
    },
    function (errorCode) {
      $('.notifications')
        .notify({
          fadeOut: {
            enabled: false,
          },
          message: {
            text: getI18nName('saveFailed') + convertErrorCode(errorCode),
          },
          type: 'danger',
        })
        .show();
    }
  );
}

/*
 * 获取前端类型-全部
 * -----------------------
 */
function getADUType(element) {
  var data = null;
  poll('GET', 'GetADUType', data, function (text) {
    var respData = text.result;
    respData.forEach(function (item, index) {
      if (item.value.length > 0) {
        var option = $('<option />', {
          value: '' + item.value + '',
        }).append(getI18nName(item.value));
        element.append(option);
      }
      if (index === 0 && item.value.length > 0) {
        getADUVersionList(item.value);
      }
    });
  });
}

/*
 * 获取前端版本信息列表
 * -----------------------
 * aduType：前端类型
 */
function getADUVersionList(aduType) {
  var data = {
    aduType: aduType,
  };
  poll('GET', 'GetADUVersionList', data, function (text) {
    var select = $('#shuttle');
    select.empty();
    $('#shuttle_to').empty();
    var respData = text.result;
    respData.forEach(function (item, index) {
      var option = $('<option />', {
        value: '' + item.aduId + '',
      }).append(
        item.aduId +
          '-' +
          item.aduName +
          '-[' +
          getI18nName('currentVersion') +
          '-' +
          item.aduVersion +
          ']'
      );
      select.append(option);
    });
  });
}

/*
 * 获取前端信息列表
 * -----------------------
 * aduType：前端类型
 * version：是否显示版本号
 */
function getADUList(aduType) {
  var data = {
    aduType: aduType,
  };
  poll('GET', 'GetADUVersionList', data, function (text) {
    var select = $('#da-adus');
    select.empty();
    $('#da-adus_to').empty();
    var respData = text.result;
    respData.forEach(function (item, index) {
      var option = $('<option />', {
        value: '' + item.aduId + '',
      }).append(item.aduId + '-' + item.aduName);
      select.append(option);
    });
  });
}

/*
 * 更新前端固件
 * -----------------------
 * formData:post表单
 */
function updateADU(formData) {
  $('#fu-update').addClass('hide');
  $('#fu-cancel').removeClass('hide');
  $('#i_progressText').text(getI18nName('updatingSensor'));
  $.ajax({
    url: serverUrl + 'UpdateADU',
    type: 'POST',
    data: formData,
    // 告诉jQuery不要去处理发送的数据
    processData: false,
    // 告诉jQuery不要去设置Content-Type请求头
    contentType: false,
    success: function (responseStr) {
      if (responseStr.success == 'true' && responseStr.errorCode == 0) {
        updateProgressFlag = true;
        //服务端处理进度
        var message = {
          methodName: 'PushUpdateADUProgress',
          enablePush: true,
          parameters: '',
        };
        webSocketSend(message);
      } else {
        updateProgressFlag = false;
        $('.notifications')
          .notify({
            fadeOut: {
              enabled: false,
            },
            message: {
              text: stringFormat.format(
                getI18nName('errorTip'),
                convertErrorCode(responseStr.errorCode)
              ),
            },
            type: 'danger',
          })
          .show();
      }
      checkUpdateProgress();
    },
    error: function (responseStr) {
      console.log(responseStr);
      $('#fu-update').removeClass('hide');
      $('#fu-cancel').addClass('hide').prop('disabled', false);
      $('#fu-cancel img').addClass('hide');
      dismissIProgress();
    },
  });
}
var updateProgressFlag = false;

function checkUpdateProgress() {
  if (updateProgressFlag) {
    //显示自定义进度条
    $('#fu-progress').removeClass('hide');
  } else {
    $('#fu-update').removeClass('hide');
    $('#fu-cancel').addClass('hide').prop('disabled', false);
    $('#fu-cancel img').addClass('hide');
  }
}

/*
 *更新前端固件进度
 * -----------------------
 * value:服务端返回的进度
 */
function updateADUState(value) {
  var progress = $('#fu-progress');
  var progressBar = $('#fu-progress>.progress>.progress-bar');
  var progressText = $('#fu-progress>.progress-text');
  var progressNum = $('#fu-progress>.progress-number');

  var i_progress = $('#i-progress');
  var i_progressBar = $('#i-prog');
  var i_progressText = $('#i-proglabel');
  var i_progressNum = $('#i-prognum');

  var aduType = $('#fu-aduType');
  if (value.progress) {
    aduType.prop('disabled', true);
    progress.removeClass('hide');
    progressBar.css({
      width: value.percent + '%',
    });
    progressText.text(
      stringFormat.format(getI18nName('updatingSensorProgress'), value.progress)
    );
    progressNum.text(value.percent + '%');

    i_progress.removeClass('hide');
    i_progressBar.css({
      width: value.percent + '%',
    });
    i_progressText.text(
      stringFormat.format(
        getI18nName('updatingSensorProgress2'),
        value.progress
      )
    );
    i_progressNum.text(value.percent + '%');
  }
  if (value.aduId) {
    //var failadu = $("#aduItems  option[value='" + value.aduId + "']");
    var aduTypeText = aduType.children('option:selected').val();
    // var msg = '更新失败：' + aduTypeText + '_' + value.aduId;
    var msg = getI18nName('updatingFailed') + value.aduId;
    if (validateStrLen(aduTypeText) == 0) {
      msg = getI18nName('updatingFailed') + value.aduId;
    }
    if (value.aduId != '') {
      $('.notifications')
        .notify({
          fadeOut: {
            enabled: false,
          },
          message: {
            text: msg,
          },
          type: 'danger',
        })
        .show();
    }
  }
  if (value.isDone) {
    $('#fu-update').removeClass('hide');
    $('#fu-cancel').addClass('hide').prop('disabled', false);
    $('#fu-cancel img').addClass('hide');
    aduType.prop('disabled', false);
    progress.addClass('hide');
    progressBar.css({
      width: '',
    });
    progressText.text(getI18nName('updating'));
    progressNum.text('');

    if (value.isDone == 'update') {
      $('.notifications')
        .notify({
          message: {
            text: getI18nName('updatingComplete'),
          },
          type: 'success',
        })
        .show();
    }
    if (value.isDone == 'cancel') {
      //取消成功
      $('.notifications')
        .notify({
          message: {
            text: getI18nName('updatingCanceled'),
          },
          type: 'warning',
        })
        .show();
    }
    dismissIProgress();
    //刷新前端版本
    var aduTypeText = aduType.children('option:selected').val();
    getADUVersionList(aduTypeText);
  }
}

function dismissIProgress() {
  $('#da-progress').removeClass('hide').addClass('hide');
  var i_progress = $('#i-progress');
  var i_progressBar = $('#i-prog');
  var i_progressText = $('#i-proglabel');
  var i_progressNum = $('#i-prognum');
  i_progress.addClass('hide');
  i_progressBar.css({
    width: '',
  });
  i_progressText.text('');
  i_progressNum.text('');
}

/*
 * 获取前端类型-仅包含前端中有数据的类型
 * -----------------------
 */
function getSyncDataADUType(element) {
  var data = null;
  poll('GET', 'GetSyncDataADUType', data, function (text) {
    var respData = text.result;
    respData.forEach(function (item, index) {
      if (item.value.length > 0) {
        var option = $('<option />', {
          value: '' + item.value + '',
        }).append(getI18nName(item.value));
        element.append(option);
      }
      if (index === 0 && item.value.length > 0) {
        getADUList(item.value);
      }
    });
  });
}

/*
 * 同步前端数据
 * -----------------------
 * formData:post表单
 */
function syncAduData(formData) {
  $('#da-ok').addClass('hide');
  $('#da-cancel').removeClass('hide');
  $('#i_progressText').text(getI18nName('syncingData'));
  poll(
    'POST',
    'SyncAduData',
    formData,
    function (responseStr) {
      //显示自定义进度条
      // $('#da-progress').removeClass("hide");
      $('#i-progress').removeClass('hide');
      $.session.set('i-progress-sync', true);
      //服务端处理进度
      var message = {
        methodName: 'PushSyncAduDataProgress',
        enablePush: true,
        parameters: formData,
      };
      webSocketSend(message);
    },
    function (errorCode) {
      $.session.set('i-progress-sync', false);
      console.log(errorCode);
      $('#da-ok').removeClass('hide');
      $('#da-cancel').addClass('hide').prop('disabled', false);
      $('#da-cancel img').addClass('hide');
      $('.notifications')
        .notify({
          fadeOut: {
            enabled: false,
          },
          message: {
            text: stringFormat.format(
              getI18nName('errorTip'),
              convertErrorCode(errorCode)
            ),
          },
          type: 'danger',
        })
        .show();
    }
  );
}

/*
 *同步前端数据进度
 * -----------------------
 * value:服务端返回的进度
 */
function syncAduDataState(value) {
  var progress = $('#da-progress');
  var progressBar = $('#da-progress>.progress>.progress-bar');
  var progressText = $('#da-progress>.progress-text');
  var progressNum = $('#da-progress>.progress-number');

  var i_progress = $('#i-progress');
  var i_progressBar = $('#i-prog');
  var i_progressText = $('#i-proglabel');
  var i_progressNum = $('#i-prognum');

  var aduType = $('#da-aduType');
  if (value.progress) {
    $('#da-ok').addClass('hide');
    $('#da-cancel').removeClass('hide');
    $('#i_progressText').text(getI18nName('syncingData'));
    aduType.prop('disabled', true);
    progress.removeClass('hide');
    progressText.text(
      stringFormat.format(getI18nName('syncingDataProgress'), value.progress)
    );
    progressNum.text(value.percent + '%');

    i_progress.removeClass('hide');
    i_progressText.text(
      stringFormat.format(getI18nName('syncingDataProgress2'), value.progress)
    );
    i_progressNum.text(value.percent + '%');
    if (value.percent >= 0 && value.percent < 100) {
      $.session.set('i-progress-sync', true);
    } else {
      $.session.set('i-progress-sync', false);
    }
    var percent = 0;
    // var num = value.percent.split('/');
    // if (parseInt(num[1]) > 0) {
    //     percent = (parseInt(num[0]) / parseInt(num[1])) * 100;
    //     progressBar.css({"width": percent + "%"});
    //     i_progressBar.css({"width": percent + "%"});
    // } else {
    //     percent = 100;
    // }
    percent = value.percent;
    progressBar.css({
      width: percent + '%',
    });
    i_progressBar.css({
      width: percent + '%',
    });
  }
  if (value.cancel == 'done') {
    var msg = getI18nName('syncingDataCancel');
    if (value.aduId) {
      //var failadu = $("#aduItems  option[value='" + value.aduId + "']");
      var aduTypeText = aduType.children('option:selected').val();
      // msg += ":" + aduTypeText + '_' + value.aduId;
      msg += ':' + value.aduId;
    }
    $.session.set('i-progress-sync', false);
    //取消成功
    $('.notifications')
      .notify({
        message: {
          text: msg,
        },
        type: 'warning',
      })
      .show();
    dismissIProgress();
    $('#da-ok').removeClass('hide');
    aduType.prop('disabled', false);
    $('#da-cancel').addClass('hide').prop('disabled', false);
    $('#da-cancel img').addClass('hide');
    return;
  }

  if (value.aduId) {
    //var failadu = $("#aduItems  option[value='" + value.aduId + "']");
    var aduTypeText = aduType.children('option:selected').val();
    // var msg = '同步失败：' + aduTypeText + '_' + value.aduId;
    var msg = getI18nName('syncingDataFailed') + ':' + value.aduId;
    if (validateStrLen(aduTypeText) == 0) {
      msg = msg = getI18nName('syncingDataFailed') + ':' + value.aduId;
    }
    // lyx 20231122 报错的传感器不再取消进度条和相关功能显示，取消只根据isDone和cancel状态决定
    /* $.session.set('i-progress-sync', false);
        dismissIProgress();
        $('#da-progress').removeClass('hide').addClass('hide')
        $('#da-ok').removeClass('hide');
        aduType.prop("disabled", false);
        $('#da-cancel').addClass('hide').prop('disabled', false);
        $('#da-cancel img').addClass('hide'); */
    if (value.aduId != '') {
      $('.notifications')
        .notify({
          fadeOut: {
            enabled: false,
          },
          message: {
            text: msg,
          },
          type: 'danger',
        })
        .show();
    }
  }

  if (value.isDone) {
    $('#da-ok').removeClass('hide');
    $('#da-cancel').addClass('hide').prop('disabled', false);
    $('#da-cancel img').addClass('hide');
    $.session.set('i-progress-sync', false);
    aduType.prop('disabled', false);
    progress.addClass('hide');
    progressBar.css({
      width: '',
    });
    progressText.text(getI18nName('syncingData'));
    progressNum.text('');

    i_progress.addClass('hide');
    i_progressBar.css({
      width: '',
    });
    i_progressText.text('');
    i_progressNum.text('');

    //更新成功，刷新前端版本
    var aduTypeText = aduType.children('option:selected').val();
    getADUVersionList(aduTypeText);

    var successCount =
      value.isDone.success == undefined ? 0 : value.isDone.success;
    var failCount = value.isDone.fail == undefined ? 0 : value.isDone.fail;
    $('.notifications')
      .notify({
        fadeOut: {
          enabled: failCount == 0,
        },
        message: {
          // text: '成功同步数据! 成功:' + successCount + ',失败:' + failCount
          text: getI18nName('syncingDataSuccess'),
        },
        type: failCount == 0 ? 'success' : 'warning',
      })
      .show();
  }
}

/*
 *电源操作
 * -----------------------
 * value:服务端返回的进度
 */
function todoPower(respData) {
  for (var key in respData) {
    var value = respData[key];
    switch (key) {
      case 'powerControl': {
        if (value === 0) {
          //关机
          var text = getI18nName('systemShutDownTip');
          // var time1 = 60000;
          // var timer1 = window.setTimeout(function () {
          //     var data = "type=1";
          //     poll('GET', 'TodoPowerControl', data, function (text) {
          //         var respData = text.success;
          //         console.log(getI18nName('systemShutDownTip'));
          //         // if (respData === "true") {
          //         //     alert(text.errorCode);
          //         // } else {
          //         //     alert("错误：" + text.errorCode);
          //         // }
          //     });
          // }, time1);

          $.fn.alertMsg('warning', text, [
            {
              id: 'no',
              text: getI18nName('systemShutDownCancel'),
              callback: function () {
                // window.clearInterval(timer1) / window.clearTimeout(time1);
                var data = 'type=0';
                poll('GET', 'TodoPowerControl', data, function (text) {
                  var respData = text.success;
                });
                console.log('取消关机！');
              },
            },
            {
              id: 'yes',
              text: '立即关机',
              callback: function () {
                var data = 'type=1';
                poll('GET', 'TodoPowerControl', data, function (text) {
                  var respData = text.success;
                  console.log('立即关机');
                  // if (respData === "true") {
                  //     alert(text.errorCode);
                  // } else {
                  //     alert("错误：" + text.errorCode);
                  // }
                });
              },
            },
          ]);
        } else if (value === 1) {
          //睡眠
          //TODO}
        }
        break;
      }
    }
  }
}

/*
 *电源操作
 * -----------------------
 * value:服务端返回的进度
 */
var showChartADUId;
var showChartDataId;
var showChartPointId;
var showChartPointType;
var showChartDeviceId;
var showChartSampleTime;

function checkChart(sensorType, dataId, pointId, sampleTime, aduId) {
  var data = 'pointId=' + pointId + '&pointType=' + sensorType;
  // debugger
  poll(
    'GET',
    'GetLastDataID',
    data,
    function (response) {
      var respData = response.result;
      var testDataId = respData.dataId;
      showChartPointType = sensorType;
      // if (testDataId === 0 || testDataId == undefined || testDataId == '') {
      //     layer.alert(getI18nName('noTestData'), {
      //         title: "",
      //         btn: [getI18nName('close')]
      //     });
      //     return;
      // }
      showChartDataId = testDataId;
      showChartPointId = pointId;
      showChartSampleTime = sampleTime;
      showChartDeviceId = respData.deviceId;
      showChartADUId = aduId;
      switch (sensorType) {
        case 'AE':
          // loadAEChart();
          // loadChartCommon('aeChart/ae_chart', 'ae_chart');
          loadChartCommon('aeChart/ae_all_chart', 'ae_chart');
          break;
        case 'UHF':
          // loadUHFChart();

          // loadChartCommon("uhf_chart");
          loadChartCommon('uhfChart/uhf_chart', 'uhf_chart');
          //lyx-TO DEBUG
          // loadChartCommon("SF6Data/sf6_data", "sf6_sensor");
          break;
        case 'HFCT':
          // loadHFCTChart();
          // loadChartCommon("hfct_chart");
          loadChartCommon('hfctChart/hfct_chart', 'hfct_chart');
          break;
        case 'TEVPRPS':
          // loadHFCTChart();
          loadChartCommon('tevPRPS/tev_prps_chart', 'tev_prps_chart');
          break;
        case 'TEV':
          // loadTevChart();
          loadChartCommon('tev_chart');
          break;
        case 'Humidity':
          // loadHumidityChart();
          loadChartCommon('humidity_chart');
          break;
        case 'MEU':
          // loadMechChart();
          loadChartCommon('mechanical_chart');
          break;
        case 'ArresterI':
          loadChartCommon('arrester_chart');
          break;
        case 'Vibration':
          loadChartCommon('vibration_pickup_chart');
          break;
        case 'TEMP':
          loadChartCommon('temperature_chart');
          // loadChartCommon("core_grounding_current");
          break;
        case 'SPTR':
          loadChartCommon('core_grounding_current');
          break;
        case 'FLOOD':
          loadChartCommon('waterImmersion/waterImmersion', 'water_immersion');
          break;
        case 'SMOKE':
          loadChartCommon('smokeSensor/smokeSensor', 'smoke_sensor');
          break;
        case 'VIDEO':
          loadChartCommon('hikVideo/hikVideo', 'video_sensor');
          break;
        case 'GrounddingCurrent':
          loadChartCommon(
            'groundingcurrent/grounddingcurrent_chart',
            'grounddingcurrent_chart'
          );
          break;
        case 'LeakageCurrent':
          loadChartCommon(
            'leakageCurrentChart/leakage_current_chart',
            'leakage_current_chart'
          );
          break;
        case 'NOISE':
          loadChartCommon('noise/noise_chart', 'noise_chart');
          break;
        case 'ENVGas':
          // loadChartCommon("temperature_chart");
          loadTempChartInMonitorTb(sensorType);
          break;
        case 'SF6':
          loadChartCommon('SF6Data/sf6_data', 'sf6_sensor');
          break;
        case 'FrostPointRaw':
        case 'FrostPointATM':
        case 'DewPointRaw':
        case 'DewPointATM':
        case 'Moisture':
        case 'AbsolutePressure':
        case 'NormalPressure':
        case 'Density':
        case 'Oxygen':
          // loadDensity();
          loadChartCommon('density_micro_water_history');
          break;
        default:
          alert('not support-' + sensorType + '|id:' + pointId);
          break;
      }
    },
    function (errorCode) {
      // layer.alert(getI18nName('noTestData'), {
      //     title: "",
      //     btn: [getI18nName('close')]
      // });
      // showChartPointId = pointId;
      // showChartSampleTime = sampleTime;
      // loadChartCommon("groundingcurrent/grounddingcurrent_chart", "grounddingcurrent_chart");
    }
  );
}
//暂定取消该推送 2018/03/21
/* function PushSampleProgress(value) {
    console.info("PushSampleProgress");
    var progress = $('#da-progress');
    var progressBar = $('#da-progress>.progress>.progress-bar');
    var progressText = $('#da-progress>.progress-text');

    var i_progress = $('#i-progress');
    var i_progressBar = $('#i-prog');
    var i_progressText = $('#i-proglabel');
    var i_progressNum = $('#i-prognum');

    var colCtrl = $("#singleCollect");
    if (value.percent >= 0 && value.percent < 100) {
        colCtrl.prop("disabled", true);
        progress.removeClass("hide");
        progressText.text(stringFormat.format(getI18nName('collectOnceProgress'), value.percent, value.progressInfo));

        i_progress.removeClass("hide");
        i_progressText.text(stringFormat.format(getI18nName('collectOnceProgress2'), value.percent, value.progressInfo));
        i_progressNum.text(value.percent + "%");

        var num = value.percent;
        progressBar.css({
            "width": num + "%"
        });
        i_progressBar.css({
            "width": num + "%"
        });
    } else {
        colCtrl.prop("disabled", false);
        progress.addClass("hide");
        progressBar.css({
            "width": ""
        });
        progressText.text(getI18nName('sampling'));

        i_progress.addClass("hide");
        i_progressBar.css({
            "width": ""
        });
        i_progressText.text("");
        i_progressNum.text("");

        $('.notifications').notify({
            message: {
                text: getI18nName('collectOnceEnd')
            },
            type: "success"
        }).show();
    }
} */

// function commonProgress

/**
 * 采集进度回调监听
 *
 * @param {any} value ws报文
 */
function PushSampleProgressNew(value, errorCode) {
  console.info('PushSampleProgress');

  var currentId = value.id;
  var index = value.index;
  var total = value.total;
  var progress = value.progress;

  var successArr = value.successArr.split(',');
  var failedArr = value.failedArr.split(',');
  var allArr = value.allArr ? value.allArr.split(',') : [];

  var SampleProgressEnum = getEnumByCode(progress, ADU_PROGRESS_ENUM);

  if (SampleProgressEnum.code == ADU_PROGRESS_ENUM.FAILED.code) {
    $('.notifications')
      .notify({
        fadeOut: {
          enabled: false,
        },
        message: {
          text: stringFormat.format(
            getI18nName('errorTip'),
            `${currentId} ${convertProgressErrorCode(errorCode)}`
          ),
        },
        type: 'danger',
      })
      .show();
  }

  // debugger
  var percent =
    SampleProgressEnum.percent * (1 / total) + ((index - 1) / total) * 100;
  var progressInfo = `${currentId} ${getI18nName('isconnected')}:${
    SampleProgressEnum.name
  }`;
  var progressTextMsg = getI18nName('sampling');
  var iProgressTextMsg = '';

  if (percent >= 0 && percent < 100) {
    progressTextMsg = stringFormat.format(
      getI18nName('collectOnceProgress'),
      `${index}/${total}`,
      progressInfo
    );
    iProgressTextMsg = stringFormat.format(
      getI18nName('collectOnceProgress2'),
      `${index}/${total}`,
      progressInfo
    );
  }

  progressUtil.updateProgress({
    colCtrlId: 'singleCollect',
    currentId: currentId,
    currentIndex: index,
    currentTitle: getI18nName('aduDataSample'),
    percent: percent,
    currentProgress: SampleProgressEnum.percent,
    successArr: successArr,
    failedArr: failedArr,
    allArr: allArr,
    progressTextMsg: progressTextMsg,
    iProgressTextMsg: iProgressTextMsg,
  });
}

/**
 * 配置传感器进度
 */

function PushSetAduInfoProgress(value, errorCode, wsCode) {
  var currentId = value.id;
  var index = value.index;
  var total = value.total;
  var progress = value.progress;

  var successArr = value.successArr.split(',');
  var failedArr = value.failedArr.split(',');
  var allArr = value.allArr ? value.allArr.split(',') : [];

  var SampleProgressEnum = getEnumByCode(progress, ADU_PROGRESS_ENUM);

  if (SampleProgressEnum.code == ADU_PROGRESS_ENUM.FAILED.code) {
    $('.notifications')
      .notify({
        fadeOut: {
          enabled: false,
        },
        message: {
          text: stringFormat.format(
            getI18nName('errorTip'),
            `${currentId} ${convertProgressErrorCode(errorCode)}`
          ),
        },
        type: 'danger',
      })
      .show();
  }

  // debugger
  var percent =
    SampleProgressEnum.percent * (1 / total) + ((index - 1) / total) * 100;
  var progressInfo = `${currentId} ${getI18nName('isconnected')}:${
    SampleProgressEnum.name
  }`;
  var progressTextMsg = getI18nName('aduInfoSetting');
  var iProgressTextMsg = '';

  if (percent >= 0 && percent < 100) {
    progressTextMsg = stringFormat.format(
      getI18nName('aduInfoSetProgress'),
      `${index}/${total}`,
      progressInfo
    );
    iProgressTextMsg = stringFormat.format(
      getI18nName('aduInfoSetProgress2'),
      `${index}/${total}`,
      progressInfo
    );
  }
  progressUtil.updateProgress({
    colCtrlId: '',
    currentId: currentId,
    currentIndex: index,
    currentTitle: getI18nName('aduInfoSet'),
    percent: percent,
    currentProgress: SampleProgressEnum.percent,
    successArr: successArr,
    failedArr: failedArr,
    allArr: allArr,
    progressTextMsg: progressTextMsg,
    iProgressTextMsg: iProgressTextMsg,
    successMsg: getI18nName('aduInfoSetEnd'),
  });
}
