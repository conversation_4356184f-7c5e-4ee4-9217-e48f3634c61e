function initCheckManagerTable() {
    /**
     * 初始化表格内容
     */
    initCustomTable({
        id: 'tableCheck',
        method: 'getCheckList',
        responseHandler: function (res) {
            res.result.total = res.result.rows.length;
            return res.result;
        },
        requestParam: function (params) {
            return {
                page: params.offset / params.limit + 1,
                size: params.limit,
            };
        },
        columns: [{
            field: 'sponsor',
            title: getI18nName('CheckManagerSponsor')
        }, {
            field: 'checkType',
            title:  getI18nName('CheckManagerCheckType'),
            formatter: function (value, row, index) {
                return getI18nName(getCheckStateName(value));
            }
        }, {
            field: 'dateTime',
            title: getI18nName('CheckManagerDate'),
        }, {
            field: 'target',
            title: getI18nName('CheckManagerTarget'),
        }, {
            field: 'extraInfo',
            title: getI18nName('CheckManagerExtraInfo'),
        }, {
            field: 'result',
            title: getI18nName('CheckManagerResult'),
            events: 'checkManagerBtnGroup',
            formatter: function (value, row) {
                return '<div><button class="checkRefuse">'+getI18nName('Refuse')+'</button><button class="checkAgree">'+getI18nName('Agree')+'</button></div>'
            }
        }, ]
    });
    window['checkManagerBtnGroup'] = {
        'click .checkRefuse': function (e, value, row, index) {
            pollPost('checkManager', {
                sponsor: row.sponsor,
                checkType: row.checkType,
                dateTime: row.dateTime,
                target: row.target,
                result: 0,

            }, function () {
                refreshCheckTable()
            })
        },
        'click .checkAgree': function (e, value, row, index) {
            pollPost('checkManager', {
                sponsor: row.sponsor,
                checkType: row.checkType,
                dateTime: row.dateTime,
                target: row.target,
                result: 1,
            }, function () {
                refreshCheckTable()
            })
        },
    }
}

function refreshCheckTable() {
    $('#tableCheck').bootstrapTable('refresh', {
        silent: true
    });
}