// 页面缓存对象
const aeChartPageCache = {
  preId: 0,
  nextId: 0,
};

$(function () {
  initPageI18n();

  // 处理窗口大小变化
  function handleResize() {
    if (!document.getElementById('aeDiv')) {
      //js判断元素是否存在
      return;
    }
    var height = $('#graph-AE').height() - $('.content-footer').height() - 20;
    $('#aeDiv').height(height);

    var svgEls = document.querySelector('svg');
    if (svgEls) {
      svgEls.setAttribute('width', $('#aeDiv').width());
      svgEls.setAttribute('height', $('#aeDiv').height());
    }
  }

  $(window).resize(handleResize);
  handleResize();

  $('.slider').slider();

  // 加载AE测试数据
  function loadAETest(dataId, pointId) {
    var data =
      'dataId=' + dataId + '&pointId=' + pointId + '&pointType=AE&dataType=AE';
    poll('GET', 'GetChartData', data, function (text) {
      var respData = text.result;
      //调试用数据
      // var tempAeData = getTempAeData();
      // tempAeData.result.preDataId = respData.preDataId;
      // tempAeData.result.nextDataId = respData.nextDataId;
      // respData = tempAeData.result;
      renderAEChartAndParameter(respData);
    });
  }

  // 初始加载数据
  loadAETest(showChartDataId, showChartPointId);

  // 绑定按钮事件
  $('#preData').click(function () {
    if (aeChartPageCache.preId === 0) {
      layer.alert(getI18nName('noTestData'), {
        title: getI18nName('tips'),
        btn: [getI18nName('close')],
      });
      return;
    }
    loadAETest(aeChartPageCache.preId, showChartPointId);
  });

  $('#nextData').click(function () {
    if (aeChartPageCache.nextId === 0) {
      $.fn.alertMsg('warning', getI18nName(''), [
        {
          id: 'no',
          text: getI18nName('confirm'),
        },
      ]);
      return;
    }
    loadAETest(aeChartPageCache.nextId, showChartPointId);
  });
});

function getAEAmpChartBody(respData) {
  // 导入ChartConfigUtil模块
  var chartConfigUtil = pdcharts.ChartConfigUtil;
  // 定义配置类型常量
  var CONFIG_TYPE = chartConfigUtil.CONFIG_TYPE;

  // 获取图表数据的单位
  let unit = respData.chartBody[0]
    ? respData.chartBody[0].axisInfo.xUnit
    : 'dB';

  aeChartPageCache.aeAmpOrgUnit = unit;

  // 初始化图表配置对象
  let chartBody = {
    title: getI18nName('aeChartTitle'),
    needBgData: false,
    axisInfo: {
      valDesc: getI18nName('Measurement'),
      groupNameId: 'pd_ae_chart_data',
      valColor: '#00b099',
      bgValDesc: '',
      bgColor: '#00b09933',
      unit: unit,
      frequecy: respData.params.frequency,
      isUnitChanged: aeChartPageCache.isUnitChanged,
      displayUnit: aeChartPageCache.displayUnit,
      onUnitChanged: function (flag, displayUnit) {
        aeChartPageCache.isUnitChanged = flag;
        aeChartPageCache.displayUnit = displayUnit;
        preCheckAEParams(aeChartPageCache.params);
        AEChartUpdateParamsDisplay(aeChartPageCache.params);
      },
    },
    series: [],
  };

  // 处理图表数据，只处理前4个对象用于组装AE幅值图谱
  const maxChartIndex = Math.min(4, respData.chartBody.length); // 取4和实际长度的较小值

  for (var chartIndex = 0; chartIndex < maxChartIndex; chartIndex++) {
    var chartData = respData.chartBody[chartIndex];
    var xDesc = globalParms[chartData.axisInfo.xDesc];
    if (validateStrLen(xDesc) != 0) {
      chartData.axisInfo.xDesc = xDesc;
    }

    var seriesItem = {
      max: chartData.axisInfo.xRangeMax,
      min: chartData.axisInfo.xRangeMin,
      dataList: [chartData.dataList[1].x, chartData.dataList[0].x],
      name: xDesc,
    };

    // respData.params[checkParamsNameList[chartIndex]] = checkAEChartValue(
    //   chartData.dataList[0].x
    // );

    chartBody.series.push(seriesItem);
  }

  return chartBody;
}

// 检查AE图表值的函数
function checkAEChartValue(value) {
  // 导入ChartConfigUtil模块
  var chartConfigUtil = pdcharts.ChartConfigUtil;
  let { getValueByConfig, getVisualTransedValueByConfig } = chartConfigUtil;
  let checkFunc = aeChartPageCache.isUnitChanged
    ? getVisualTransedValueByConfig
    : getValueByConfig;
  var resultObj = checkFunc({
    // dataType: CONFIG_TYPE.AE,
    chartType: pdcharts.chartType.amplitude,
    value: value,
    unit: aeChartPageCache.aeAmpOrgUnit,
    displayUnit: aeChartPageCache.displayUnit,
  });
  return resultObj.valueStr;
}

function preCheckAEParams(params) {
  // 需要检查的参数名列表
  let checkParamsNameList = ['rms', 'cycle_max', 'frequency1', 'frequency2'];
  for (let index = 0; index < checkParamsNameList.length; index++) {
    const key = checkParamsNameList[index];
    if (params[`${key}_org`] == undefined) {
      params[`${key}_org`] = params[key];
    }

    // 更新参数值
    let checkvalue = params[`${key}_org`].replace(
      aeChartPageCache.aeAmpOrgUnit,
      ''
    );
    params[key] = checkAEChartValue(parseFloat(checkvalue));
  }
}

function AEChartUpdateParamsDisplay(params) {
  var sensorType = getI18nName(params.sensorType);
  $('#pointName').html(params.pointName);
  $('#sensorCode').html(params.aduId);
  $('#sensorType').html(sensorType);
  $('#gain').html(params.gain);
  $('#sample_date').html(params.sample_date);
  $('#sample_time').html(params.sample_time);
  $('#rms').html(params.rms);
  $('#cycle_max').html(params.cycle_max);
  $('#frequency1').html(params.frequency1);
  $('#frequency2').html(params.frequency2);
}

// 获取AE波形图谱数据
function getAEWaveChartBody(respData) {
  // 处理第5个chartBody对象
  let chartBody = respData.chartBody[4];
  // 初始化图表配置对象
  chartBody.title = getI18nName('aeWaveChartTitle');
  chartBody.axisInfo.groupNameId = 'pd_ae_chart_data';
  chartBody.axisInfo.xDesc = getI18nName('LOCAL_PARAMS_TIME');
  chartBody.axisInfo.yDesc = getI18nName('AMP');
  chartBody.trigger.color = '#ff00ff';
  chartBody.axisInfo.isUnitChanged = aeChartPageCache.isUnitChanged;
  chartBody.axisInfo.displayUnit = aeChartPageCache.displayUnit;
  chartBody.axisInfo.onUnitChanged = function (flag, displayUnit) {
    aeChartPageCache.isUnitChanged = flag;
    aeChartPageCache.displayUnit = displayUnit;
    preCheckAEParams(aeChartPageCache.params);
    AEChartUpdateParamsDisplay(aeChartPageCache.params);
  };
  chartBody.series[0].color = '#02FDD4FF';
  return chartBody;
}

// 获取AE相位图谱数据
function getAEPhaseChartBody(respData) {
  // 处理第6个chartBody对象
  let chartBody = respData.chartBody[5];
  // 初始化图表配置对象
  chartBody.title = getI18nName('aePhaseChartTitle');
  chartBody.axisInfo.groupNameId = 'pd_ae_chart_data';
  chartBody.axisInfo.xDesc = getI18nName('phase');
  chartBody.axisInfo.yDesc = getI18nName('AMP');
  chartBody.axisInfo.zDesc = getI18nName('LOCAL_CHART_COLORDENSITY');
  chartBody.axisInfo.zUnit = '';
  chartBody.trigger.color = '#ff00ff';
  chartBody.axisInfo.isUnitChanged = aeChartPageCache.isUnitChanged;
  chartBody.axisInfo.displayUnit = aeChartPageCache.displayUnit;
  chartBody.axisInfo.onUnitChanged = function (flag, displayUnit) {
    aeChartPageCache.isUnitChanged = flag;
    aeChartPageCache.displayUnit = displayUnit;
    preCheckAEParams(aeChartPageCache.params);
    AEChartUpdateParamsDisplay(aeChartPageCache.params);
  };
  chartBody.series[0].color = 'jet';
  return chartBody;
}

// 获取AE飞行图谱数据
function getAEFlyChartBody(respData) {
  // 处理第7个chartBody对象
  let chartBody = respData.chartBody[6];
  // 初始化图表配置对象
  chartBody.title = getI18nName('aeFlyChartTitle');
  chartBody.axisInfo.groupNameId = 'pd_ae_chart_data';
  chartBody.axisInfo.xDesc = getI18nName('time_interval');
  chartBody.axisInfo.yDesc = getI18nName('AMP');
  chartBody.axisInfo.zDesc = getI18nName('LOCAL_CHART_COLORDENSITY');
  chartBody.axisInfo.zUnit = '';
  chartBody.trigger.color = '#ff00ff';
  chartBody.axisInfo.isUnitChanged = aeChartPageCache.isUnitChanged;
  chartBody.axisInfo.displayUnit = aeChartPageCache.displayUnit;
  chartBody.axisInfo.onUnitChanged = function (flag, displayUnit) {
    aeChartPageCache.isUnitChanged = flag;
    aeChartPageCache.displayUnit = displayUnit;
    preCheckAEParams(aeChartPageCache.params);
    AEChartUpdateParamsDisplay(aeChartPageCache.params);
  };
  if (chartBody.axisInfo.xRangeMax == 0) chartBody.axisInfo.xRangeMax = 1;
  chartBody.series[0].color = 'jet';
  return chartBody;
}

function renderAEChart(respData) {
  var chartDiv = document.getElementById('aeDiv');

  // 清除旧图表
  chartDiv.innerHTML = '';

  // 处理单图谱情况（幅值图谱）
  if (respData.chartBody.length <= 4) {
    var pdChartBody = getAEAmpChartBody(respData);
    let ampDiv = document.createElement('div');
    chartDiv.appendChild(ampDiv);
    pdcharts.draw(ampDiv, {
      width: $('#aeDiv').width(),
      height: $('#aeDiv').height(),
      type: pdcharts.chartType.amplitude,
      data: pdChartBody,
    });
    return;
  }

  // 处理四图谱情况
  if (respData.chartBody.length > 4) {
    // 创建2x2网格容器
    var gridContainer = document.createElement('div');
    gridContainer.style.display = 'grid';
    gridContainer.style.gridTemplateColumns = '1fr 1fr';
    gridContainer.style.gridTemplateRows = '1fr 1fr';
    gridContainer.style.width = '100%';
    gridContainer.style.height = '100%';
    gridContainer.style.gap = '10px'; // 添加间距
    chartDiv.appendChild(gridContainer);

    // 幅值图谱
    let ampDiv = document.createElement('div');
    gridContainer.appendChild(ampDiv);
    pdcharts.draw(ampDiv, {
      width: ampDiv.clientWidth,
      height: ampDiv.clientHeight,
      type: pdcharts.chartType.amplitude,
      data: getAEAmpChartBody(respData),
    });

    // 波形图谱
    if (respData.chartBody[4]) {
      let waveDiv = document.createElement('div');
      gridContainer.appendChild(waveDiv);
      pdcharts.draw(waveDiv, {
        width: waveDiv.clientWidth,
        height: waveDiv.clientHeight,
        type: pdcharts.chartType.wave,
        data: getAEWaveChartBody(respData),
      });
    }

    // 相位图谱
    if (respData.chartBody[5]) {
      let phaseDiv = document.createElement('div');
      gridContainer.appendChild(phaseDiv);
      pdcharts.draw(phaseDiv, {
        width: phaseDiv.clientWidth,
        height: phaseDiv.clientHeight,
        type: pdcharts.chartType.phase,
        data: getAEPhaseChartBody(respData),
      });
    }

    // 飞行图谱
    if (respData.chartBody[6]) {
      let flyDiv = document.createElement('div');
      gridContainer.appendChild(flyDiv);
      pdcharts.draw(flyDiv, {
        width: flyDiv.clientWidth,
        height: flyDiv.clientHeight,
        type: pdcharts.chartType.fly,
        data: getAEFlyChartBody(respData),
      });
    }
  }
}

function renderAEChartAndParameter(respData) {
  aeChartPageCache.preId = respData.preDataId;
  aeChartPageCache.nextId = respData.nextDataId;

  //判断上一条/下一条按钮是否可用并改变状态
  if (aeChartPageCache.preId === 0) {
    $('#preData').attr('disabled', true);
  } else {
    $('#preData').removeAttr('disabled');
  }
  if (aeChartPageCache.nextId === 0) {
    $('#nextData').attr('disabled', true);
  } else {
    $('#nextData').removeAttr('disabled');
  }

  // 渲染AE图表
  renderAEChart(respData);
  // 更新参数显示
  aeChartPageCache.params = respData.params;
  preCheckAEParams(respData.params);

  AEChartUpdateParamsDisplay(respData.params);
}
