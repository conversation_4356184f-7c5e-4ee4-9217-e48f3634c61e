<!--<body class="hold-transition skin-blue sidebar-mini" id="mainbody" style="display: block">-->
<style>
  #preData,
  #nextData {
    width: 140px !important;
  }
</style>
<div class="modal-dialog hide" id="showShutdown">
  <div class="modal-content">
    <div class="modal-header">
      <button type="button" class="close" data-dismiss="modal" aria-label="Close">
        <span aria-hidden="true">×</span></button>
      <h4 class="modal-title">Warning Modal</h4>
    </div>
    <div class="modal-body">
      <p>One fine body…</p>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-outline pull-left" data-dismiss="modal">Close
      </button>
      <button type="button" class="btn btn-outline">Save changes</button>
    </div>
  </div>
  <!-- /.modal-content -->
</div>
<script>
  function setUser (user) {
    // var checkMenuList = MenuControllUtil.MenuConfig.SAFE_CHECK[authority];
    /*  var checkMenuList = MenuControllUtil.MenuConfig.PMDT[authority];
     if (checkMenuList) {
         MenuControllUtil.checkUserMenu(authority);
         $("#loginModal").modal('hide');
     } else {
         // modals.error(this, '', "error");
         layer.open({
             btn: [getI18nName('close')],
             formType: 2,
             title: getI18nName('error'),
             content: getI18nName('userErrorTip'),
             yes: function (index) {
                 layer.close(index);
             }
         });
         return false;
     } */
    MenuControllUtil.checkUserMenu();
    $("#loginModal").modal('hide');
    return true;
  }

  function initUser () {
    var tempUser = getCookie('user');
    setTimeout(function () {
      document.getElementById('showLoginName').innerHTML = tempUser ? tempUser : 'Guest';
      setUser(tempUser ? tempUser : 'Guest');
      Pace.stop();
    }, 600);
  }

  $(document).ready(function () {
    initPageI18n();
    pdcharts.updatePDChartsOption({ theme: 'light' });
    pdcharts.ChartConfigUtil.getChartConfigCache();
    Pace.stop()
    $('#version-index').html(version);
    pollGet(GET_VERSION, "", function (response) {
      version = "V " + response.result.version;
      $('#version-index').html(version);
    }, function () {

    });
    $.session.set('i-progress-sync', false);
    setSession('monitorIndex', 1);
    setSession('monitorPageSize', 7);
    var date = new Date();
    document.getElementById('loginTime').innerHTML = "&nbsp;" + date.pattern("yyyy/MM/dd HH:mm:ss");

    $('#user_change_pw').click(function () {
      var change_pw_modal = createModal("changePwModal");
      change_pw_modal.find(".modal-title").html(getI18nName('ChangePassword'));
      var content = $('<div/>', {
        class: 'box-body'
      })
        .append('<div class="modal-body"> ' +
          '<div action="" class="form col-md-12 center-block"> ' +
          '<div class="form-group"> ' +
          '<input type="password" id="user_old_pw" vk="true" class="form-control input-lg" placeholder="' +
          getI18nName('OldPassword') + '" value=""> ' +
          '</div><div class="form-group"> <input type="password"  id="user_new_password" vk="true" class="form-control input-lg" placeholder="' +
          getI18nName('NewPassword') + '" value=""> ' +
          '</div><div class="form-group"> <input type="password"  id="user_new_pwd_check" vk="true" class="form-control input-lg" placeholder="' +
          getI18nName('RepeatNewPassword') + '" value=""> ' +
          '</div> <div class="form-group">' +
          '<button id="btnChangePw" class="btn btn-primary btn-lg btn-block">' +
          getI18nName('ChangePassword') + '</button>' +
          '</div></div></div><div class="modal-footer"></div>');
      change_pw_modal.find(".modal-body")
        .append(content);

      change_pw_modal.find(".modal-footer").css({
        display: 'none'
      });

      //关闭modal
      change_pw_modal.on('hidden.bs.modal', function () {
        $(this).remove();
      });

      change_pw_modal.modal('show');

      checkNeedKeyboard();
      $('#btnChangePw').click(function () {
        var userName = getSession('user')
        var oldpw = $('#user_old_pw').val();
        var newpw = $('#user_new_password').val();
        var newpwRep = $('#user_new_pwd_check').val();

        if (oldpw == '') {
          layer.msg(getI18nName('OldPasswordPlaceholder'));
          return;
        }

        if (newpwRep !== newpw) {
          layer.msg(getI18nName('PasswordCompareTips'))
          return;
        }
        if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[$@$!%*?&])[A-Za-z\d$@$!%*?&]{8,}/
          .test(newpw)) {
          layer.msg(getI18nName('PasswordFormatTipes'))
          return;
        }

        pollPost(POST_CHANGE_USER_PAS, {
          userName: RSAUtil.encode(userName),
          oldPassword: RSAUtil.encode(oldpw),
          newPassword: RSAUtil.encode(newpw),
        }, function (res) {
          // logOut('changePw');
          logOutWS();
        }, function () {
          layer.msg(getI18nName('OldPasswordError'));
        })
      });
    });

    $('#user_change').click(function () {
      var ablout_modal = createModal("loginModal");
      ablout_modal.find(".modal-title").html(getI18nName('changerUser'));
      var content = $('<div/>', {
        class: 'box-body'
      })
        .append('<div class="modal-body" onkeydown="onSwitchUserEnter()"> ' +
          '<div action="" class="form col-md-12 center-block"> ' +
          '<div class="form-group"> ' +
          '<input type="text" id="user_Account" vk="true" class="form-control input-lg" placeholder="' +
          getI18nName('userName') + '" value=""> ' +
          '</div><div class="form-group"> <input type="password"  id="user_pwd" vk="true" class="form-control input-lg" placeholder="' +
          getI18nName('passWord') + '" value=""> ' +
          '</div> <div class="form-group">' +
          '<button id="btnChangeUser" class="btn btn-primary btn-lg btn-block">' +
          getI18nName("change") + '</button>' +
          '</div></div></div><div class="modal-footer"></div>');
      ablout_modal.find(".modal-body")
        .append(content);

      ablout_modal.find(".modal-footer").css({
        display: 'none'
      });

      //关闭modal
      ablout_modal.on('hidden.bs.modal', function () {
        $(this).remove();
      });

      ablout_modal.modal('show');

      checkNeedKeyboard();

      $('#btnChangeUser').click(function () {
        var check = true;
        var formData = {};
        formData.userAct = $("#user_Account").val();
        formData.userPwd = $("#user_pwd").val();

        changeUser(formData.userAct, formData.userPwd);
      });
    });
    $(window).resize(function () {
      var winHeight = window.screen.height;
      var winWidth = window.screen.width;
      if (winHeight <= 600 && winWidth <= 800) {
        $('#sidebar-toggle').hide();
        isS1010DevScreen = true;
      } else {
        $('#sidebar-toggle').show();
        isS1010DevScreen = false;
      }
    });
    $(window).resize();
  });

  function onSwitchUserEnter () {
    if (window.event.key == 'Enter') {
      $('#btnChangeUser').click();
    }
  }

  var MenuControllUtil = {
    MenuControllableList: [
      'datetime_set', 'soft_setting', 'file_manage',
      'instrument_set', 'file_point_set', 'modbus_setting',
      'hardware_update', 'audit_view', 'alarm_manager', 'net_set', 'main_station_params', 'sync_data',
      'user_manager', 'check_manager'
    ],
    MenuConfig: {
      PMDT: {
        SuperUser: ['datetime_set', 'language_set', 'file_manage', 'instrument_set', 'file_point_set',
          'soft_setting',
          'modbus_setting', 'hardware_update', 'net_set', 'main_station_params', 'sync_data'
        ],
        Admin: ['datetime_set', 'language_set', 'modbus_setting', 'hardware_update', 'collect_mode',
          'file_manage',
          'instrument_set', 'file_point_set', 'soft_setting', 'alarm_param_set', 'sync_data',
          'net_set'
        ],
        Guest: ['datetime_set', 'language_set', 'sync_data'],
        SystemAdmin: ['datetime_set', 'language_set', 'soft_setting', 'file_manage',
          'instrument_set', 'file_point_set', 'modbus_setting', 'main_station_params',
          'hardware_update', 'net_set', 'sync_data'
        ],
        NormalUser: ['language_set', 'alarm_manager'],
        AuditAdmin: ['language_set', 'audit_view'],
        SecurityAdmin: ['language_set'],
      },
      SAFE_CHECK: {
        SystemAdmin: ['datetime_set', 'language_set', 'soft_setting', 'file_manage',
          'instrument_set', 'file_point_set', 'modbus_setting',
          'hardware_update', 'net_set', 'sync_data'
        ],
        NormalUser: ['language_set', 'alarm_manager'],
        AuditAdmin: ['language_set', 'audit_view'],
        SecurityAdmin: ['language_set'],
        Guest: ['language_set'],
      }
    },
    MenuSafeTestEnum: {
      DATA_SYNC: {
        code: 2,
        name: 'sync_data',
        menuName: '数据同步'
      },
      TIME_SET: {
        code: 3,
        name: 'datetime_set',
        menuName: '时间设置'
      },
      SYS_SET: {
        code: 4,
        name: 'soft_setting',
        menuName: '系统设置'
      },
      ARCHIVE_MANA: {
        code: 5,
        name: 'file_manage',
        menuName: '档案管理'
      },
      ADU_MANA: {
        code: 6,
        name: 'instrument_set',
        menuName: '前端管理'
      },
      POINT_MANA: {
        code: 7,
        name: 'file_point_set',
        menuName: '测点管理'
      },
      MODBUS_SET: {
        code: 8,
        name: 'modbus_setting',
        menuName: 'modbus设置'
      },
      FIRM_FRESH: {
        code: 9,
        name: 'hardware_update',
        menuName: '固件更新'
      },
      NET_SET: {
        code: 10,
        name: 'net_set',
        menuName: '网络设置'
      },
      AUDIT_DATA: {
        code: 18,
        codeName: '审计数据',
        name: 'audit_view',
        menuName: '审计查看'
      },
      MAIN_STATION: {
        code: 27,
        name: 'main_station_params',
        menuName: '主站设置'
      },
      ADD_USER: {
        code: 11,
        codeName: '添加用户',
        name: 'user_manager',
        menuName: '用户管理'
      },
      DEL_USER: {
        code: 12,
        codeName: '删除用户',
        name: 'user_manager',
        menuName: '用户管理'
      },
      AUDIT_CONFIRM: {
        code: 21,
        codeName: '审核权限',
        name: 'check_manager',
        menuName: '审核管理'
      },
      CHANGE_PASS_NORMAL_USER: {
        code: 28,
        codeName: '修改普通用户密码',
        name: 'user_manager',
        menuName: '用户管理'
      },
      CHANGE_LANGUAGE: {
        code: 29,
        codeName: '语言设置',
        name: 'language_set',
        menuName: '语言设置'
      },
      // EXPOR_DATA: {
      //     code: 30,
      //     codeName: '数据导出',
      //     name: 'export_data',
      //     menuName: '数据导出'
      // },
      SENSOR_OPRATE: {
        code: 31,
        codeName: '传感器操作',
        name: 'sensor-option',
        menuName: '数据导出'
      },
    },
    checkUserMenu: function (user) {
      //            layer.alert(user);
      var userInfo = JSON.parse(new Base64Util().decode(getSession('userInfo')));
      var _this = this;
      _this.MenuControllableList.forEach(function (menuId) {
        _this.hideMenu(menuId);
      });
      // var visableMenus = _this.MenuConfig.SAFE_CHECK[user];
      for (var key in _this.MenuSafeTestEnum) {
        var menuEnum = _this.MenuSafeTestEnum[key];
        // _this.showMenu(menuEnum.name);
        if (userInfo.userPermission.includes(menuEnum.code)) {
          _this.showMenu(menuEnum.name);
        } else {
          _this.hideMenu(menuEnum.name);
        }
      }
      // var visableMenus = _this.MenuConfig.PMDT[user];
      // visableMenus.forEach(function (menuId) {
      //     _this.showMenu(menuId);
      // })
    },
    showMenu: function (nameId) {
      if (document.getElementById(nameId)) {
        document.getElementById(nameId).parentNode.parentNode.style.display = 'block';
      }
    },
    hideMenu: function (nameId) {
      if (document.getElementById(nameId)) {
        document.getElementById(nameId).parentNode.parentNode.style.display = 'none';
      }
    }
  };

  function setUserOld (user) {
    var authority = getSession('user');
    if (user) {
      authority = user;
    }
    if (authority === "NormalUser" || authority === "Admin") {
      document.getElementById('modbus_setting').parentNode.parentNode.style.display = 'none';
      document.getElementById('hardware_update').parentNode.parentNode.style.display = 'none';
      //                document.getElementById('collect_mode').parentNode.parentNode.style.display='none';

      document.getElementById('file_manage').parentNode.parentNode.style.display = 'block';
      document.getElementById('instrument_set').parentNode.parentNode.style.display = 'block';
      document.getElementById('file_point_set').parentNode.parentNode.style.display = 'block';
      document.getElementById('soft_setting').parentNode.parentNode.style.display = 'block';
      //                document.getElementById('alarm_param_set').parentNode.parentNode.style.display = 'block';
      document.getElementById('net_set').parentNode.parentNode.style.display = 'block';
      $("#loginModal").modal('hide');
    } else if (authority === "SuperUser" || authority === "SafeUser" || authority === "ConfirmUser") {
      //                    document.getElementById('stationMap').parentNode.parentNode.style.display = 'block';
      document.getElementById('file_manage').parentNode.parentNode.style.display = 'block';
      document.getElementById('instrument_set').parentNode.parentNode.style.display = 'block';
      document.getElementById('file_point_set').parentNode.parentNode.style.display = 'block';
      document.getElementById('soft_setting').parentNode.parentNode.style.display = 'block';
      //                document.getElementById('alarm_param_set').parentNode.parentNode.style.display = 'block';

      document.getElementById('modbus_setting').parentNode.parentNode.style.display = 'block';
      document.getElementById('hardware_update').parentNode.parentNode.style.display = 'block';
      //                document.getElementById('collect_mode').parentNode.parentNode.style.display='block';
      document.getElementById('net_set').parentNode.parentNode.style.display = 'block';
      $("#loginModal").modal('hide');
    } else if (authority === "Guest") {
      document.getElementById('file_manage').parentNode.parentNode.style.display = 'none';
      document.getElementById('instrument_set').parentNode.parentNode.style.display = 'none';
      document.getElementById('file_point_set').parentNode.parentNode.style.display = 'none';
      document.getElementById('soft_setting').parentNode.parentNode.style.display = 'none';

      document.getElementById('modbus_setting').parentNode.parentNode.style.display = 'none';
      document.getElementById('hardware_update').parentNode.parentNode.style.display = 'none';
      //                document.getElementById('collect_mode').parentNode.parentNode.style.display='none';
      document.getElementById('net_set').parentNode.parentNode.style.display = 'none';
      $("#loginModal").modal('hide');
    } else {
      // modals.error(this, '', "error");
      layer.open({
        btn: [getI18nName('close')],
        formType: 2,
        title: getI18nName('error'),
        content: getI18nName('userErrorTip'),
        yes: function (index) {
          layer.close(index);
        }
      });
      return false;
    }
    return true;
  }
  //changeUser("Guest","");
  function changeUser (user_Account, user_pwd) {
    var check = true;
    var postForm = {};
    postForm.userName = RSAUtil.encode(user_Account);
    postForm.password = RSAUtil.encode(user_pwd);
    /*    if (needLogin) {
           postForm.userName = user_Account;
           postForm.password = user_pwd;
       } else {
           var basU = new Base64Util();
           postForm.userName = basU.encode($("#user_Account").val());
           postForm.password = basU.encode($("#user_pwd").val());
       } */
    /*
     * 1.Guest:去除设置模块中系统设置权限
     * 2.Admin:①增加网络设置权限②增加档案管理权限③增加前端配置权限④增加测点配置权限
     *
     */
    if (user_Account == "") {
      layer.open({
        btn: [getI18nName('close')],
        formType: 2,
        title: getI18nName('error'),
        content: getI18nName('userErrorTip'),
        yes: function (index) {
          layer.close(index);
        }
      });
      return;
    }
    // setSession('changeUserLogOut','true');
    // logOutWS();
    pollPost(POST_SWITCH_USER, postForm, function (text) {
      //            console.info(text);
      var respData = text.result;
      if (respData.loginResult === 'loginSuccess') {
        // $.session.clear();
        // setSession('changeUserLogOut','true');

        var message = {
          "methodName": "WebSocketLogin",
          "userName": postForm.userName,
          "password": postForm.password
        };
        setTimeout(function () {
          $.session.set('user', user_Account);
          $.session.set('token', postForm.password);
          $.session.set('isLogOut', false);
          $.session.set('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));
          setCookie('user', user_Account);
          setCookie('token', postForm.password);
          setCookie('isLogOut', false);
          setCookie('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));
          webSocketSend(message);
          document.getElementById('showLoginName').innerHTML = user_Account;
          var date = new Date();
          document.getElementById('loginTime').innerHTML = "&nbsp;" + date.pattern(
            "yyyy/MM/dd HH:mm:ss");
          var ablout_modal = $('#loginModal');
          ablout_modal.remove();
          $('.modal-backdrop.in').remove();
          setUser(user_Account);
          goMonitorTb();
        }, 500)

      } else {
        layer.open({
          btn: [getI18nName('close')],
          formType: 2,
          title: getI18nName('error'),
          content: getI18nName('userErrorTip'),
          yes: function (index) {
            layer.close(index);
          }
        });
      }

    }, function (errorCode) {
      $('.notifications').notify({
        message: {
          text: 'ErrCode：' + convertErrorCode(errorCode)
        },
        type: "warning",
      }).show();
    });

  }

  function goMonitorTb () {
    //加载页面内容
    var href = 'monitorTb.html';
    var pageId = '实时监测';
    $('.content-wrapper>.content').empty();
    var content = $("<div />", {
      id: pageId,
      class: "row full-height main-content-pd"
    });
    $('.content-wrapper>.content').append(content);
    $('#' + pageId).load("pages/" + href + '?t=' + Date.parse(new Date()), function () {
      Pace.stop()
    });

    //修改页面名称
    $('.page-title').html(getI18nName('monitor'));
  }

  function logOutConfirm () {
    layer.confirm(getI18nName('logOutConfirm'), {
      title: getI18nName('logOutTitle'),
      btn: [getI18nName('logOut'), getI18nName('cancel')] //按钮
    }, function () {
      logOutWS();
    }, function () { });
  }

  /**
   * 通知所有ws注销 
   */
  function logOutWS () {
    var userName = RSAUtil.encode(getSession('user'));
    pollPost('Logout', { userName: userName }, function (text) { })
    /* var message = {
        "methodName": "logOut",
        "userName": userName,
    };
    setTimeout(function () {
        webSocketSend(message);
    }, 100) */
  }

  /*
  当前页面注销
  */
  function logOut (type) {
    if (windowDebug == true) {
      // console.error('debuging-logOut-passs');
      // return;
    }
    // debugger
    //lyx-TO DEBUG
    // return;

    //切换用户时，当前页面不响应本次登出ws推送
    var changeUserLogOut = getSession('changeUserLogOut');
    if (changeUserLogOut == 'true') {
      setSession('changeUserLogOut', null);
      return;
    }

    if (!type || typeof type != "string") {
      type = 'logOut';
    }
    if (type != 'wsLogOut') {
      console.log({
        type: type,
        msg: '重复登录'
      });
      // debugger
      $.session.clear();

      setCookie('hasLogin', "true");
      setCookie('isLogOut', type);
      setCookie('user', null);
      window.location.reload();
    } else {
      console.log({
        type,
        msg: 'ws断开请检查'
      });
      // debugger
      var isLogOut = getCookie('isLogOut');
      // if(!isLogOut){
      $.session.clear();
      setCookie('hasLogin', "true");
      setCookie('isLogOut', type);
      setCookie('user', null);
      window.location.reload();
      // }
    }
    /* debugger
    $.session.clear();
    setCookie('hasLogin', "true");
    setCookie('isLogOut', type);
    window.location.reload(); */
  }
</script>