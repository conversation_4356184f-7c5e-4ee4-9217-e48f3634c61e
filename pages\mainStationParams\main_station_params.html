<style>
    .main-station-label-content {
        display: flex;
        align-items: center;
    }

    .ms-common-param-content {
        display: flex;
        margin: 10px -18px;
        border-bottom: 1px solid #f4f4f4;
        padding: 8px;
    }

    .ms-common-param-item {
        align-items: center;
        margin: 8px;
    }
</style>
<section class="content-header" style="padding: 25px">
    <h1 style="display: flex;">
        <i class="fa fa-dashboard"></i> <div style="margin-left:8px;" data-i18n="settings">系统设置</div>
        <small style="display: flex;margin:8px"> > <div style="margin-left:8px;" data-i18n="main_station_params">主站设置</div></small>
    </h1>
</section>
<section class="content">
    <div class="row" style="margin: auto;">
        <div class="col-md-12">
            <div class="box-body">
                <div class="ms-common-param-content">
                    <div class="ms-common-param-item">
                        <label class="control-label" data-i18n="MainStationAuxRtuID">辅控RtuID</label>
                        <input id="auxRtuID" class="form-control" type="number" autocomplete="off"
                            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                            style="ime-mode:Disabled" value="999"
                            onkeyup="this.value=this.value === '' ? 0 :parseInt(this.value)>100000 ? 100000: this.value.match(/\d+(\.\d{0,0})?/) ? this.value.match(/\d+(\.\d{0,0})?/)[0] : ''">
                    </div>
                    <div class="ms-common-param-item">
                        <label class="control-label" data-i18n="MainStationIedRtuID">汇集RtuID</label>
                        <input id="iedRtuID" class="form-control" type="number" autocomplete="off"
                            onkeypress="return (/[\d]/.test(String.fromCharCode(event.keyCode)))"
                            style="ime-mode:Disabled" value="1000"
                            onkeyup="this.value=this.value === '' ? 0 :parseInt(this.value)>100000 ? 100000:  this.value.match(/\d+(\.\d{0,0})?/) ? this.value.match(/\d+(\.\d{0,0})?/)[0] : ''">
                    </div>
                </div>
                <div class="row" style="text-align: right;">
                    <div class="nav-tabs-custom">
                        <ul class="nav nav-tabs">
                            <li class="stationNav active" data-id="0"><a href="#stationConfig0Content" data-toggle="tab"
                                    aria-expanded="true" data-i18n="ManstationParams1">主站参数1</a></li>
                            <li class="stationNav" data-id="1"><a href="#stationConfig1Content" data-toggle="tab"
                                    aria-expanded="true" data-i18n="ManstationParams2">主站参数2</a>
                            </li>
                        </ul>
                        <div class="tab-content" style="margin-top: 14px;overflow-y:auto;">
                            <div class="tab-pane active" id="stationConfig0Content">
                                <div class="col-md-10 col-md-offset-2">
                                    <div class="form-group main-station-label-content">
                                        <label for="cagIp0" class="col-sm-3 control-label" data-i18n="MainstationInterface1">主站1接口：</label>
                                        <input type="text" class="col-sm-3 form-control" id="cagIp0" data-i18n-placeholder="IPAddress"
                                            type="text" style="width:34%">
                                        <label for="cagPort0" class="col-sm-1 control-label" style="width:4%">:</label>
                                        <input type="text" class="form-control" id="cagPort0" data-i18n-placeholder="Port"
                                            type="text" style="width:12%">
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="cagUri0" class="col-sm-3 control-label">URI:</label>
                                        <input type="text" id="cagUri0" class="form-control" data-i18n-placeholder="EnterUriTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="user0" class="col-sm-3 control-label" data-i18n="ConnectUserName">通信用户名：</label>
                                        <input type="text" id="user0" class="form-control" data-i18n-placeholder="EnterConnectUserNameTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="password0" class="col-sm-3 control-label" data-i18n="ConnectPass">通信密码：</label>
                                        <input type="text" id="password0" class="form-control" data-i18n-placeholder="EnterConnectPassTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="dataAcquireTime0" class="col-sm-3 control-label" data-i18n="DataSubmissionInterval">数据上送间隔：</label>
                                        <div class="input-group" style="width:50%">
                                            <input class="form-control" id="dataAcquireTime0" type="text"
                                                data-i18n-placeholder="EnderDataSBIntervalTips" />
                                            <div class="input-group-addon">
                                                <i class="fa"></i>S
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="heartBeatTime0" class="col-sm-3 control-label" data-i18n="HeartbeatInterval">心跳间隔：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="heartBeatTime0" type="text"
                                                data-i18n-placeholder="EnterHertbeatIntervalTips" />
                                            <div class="input-group-addon">
                                                <i class="fa"></i>S
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="netState0" class="col-sm-3 control-label" data-i18n="connectedState">网络状态：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="netState0" type="text"
                                                disabled="disabled" />
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content hide">
                                        <label for="commState0" class="col-sm-3 control-label" data-i18n="ConnectStatus">连接状态：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="commState0" type="text"
                                                disabled="disabled" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane" id="stationConfig1Content">
                                <div class="col-md-10 col-md-offset-2">
                                    <div class="form-group main-station-label-content">
                                        <label for="cagIp1" class="col-sm-3 control-label" data-i18n="MainstationInterface2">主站2接口：</label>
                                        <input type="text" class="col-sm-3 form-control" id="cagIp1" data-i18n-placeholder="IPAddress"
                                            type="text" style="width:34%">
                                        <label for="cagPort1" class="col-sm-1 control-label" style="width:4%">:</label>
                                        <input type="text" class="form-control" id="cagPort1" data-i18n-placeholder="Port"
                                            type="text" style="width:12%">
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="cagUri1" class="col-sm-3 control-label">URI:</label>
                                        <input type="text" id="cagUri1" class="form-control" data-i18n-placeholder="EnterUriTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="user1" class="col-sm-3 control-label" data-i18n="ConnectUserName">通信用户名：</label>
                                        <input type="text" id="user1" class="form-control" data-i18n-placeholder="EnterConnectUserNameTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="password1" class="col-sm-3 control-label" data-i18n="ConnectPass">通信密码：</label>
                                        <input type="text" id="password1" class="form-control" data-i18n-placeholder="EnterConnectPassTips"
                                            style="width:50%" />
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="dataAcquireTime1" class="col-sm-3 control-label" data-i18n="DataSubmissionInterval">数据上送间隔：</label>
                                        <div class="input-group" style="width:50%">
                                            <input class="form-control" id="dataAcquireTime1" type="text"
                                                data-i18n-placeholder="EnderDataSBIntervalTips" />
                                            <div class="input-group-addon">
                                                <i class="fa"></i>S
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="heartBeatTime1" class="col-sm-3 control-label" data-i18n="HeartbeatInterval">心跳间隔：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="heartBeatTime1" type="text"
                                                data-i18n-placeholder="EnterHertbeatIntervalTips" />
                                            <div class="input-group-addon">
                                                <i class="fa"></i>S
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content">
                                        <label for="netState1" class="col-sm-3 control-label" data-i18n="connectedState">网络状态：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="netState1" type="text"
                                                disabled="disabled" />
                                        </div>
                                    </div>
                                    <div class="form-group main-station-label-content hide">
                                        <label for="commState1" class="col-sm-3 control-label" data-i18n="ConnectStatus">连接状态：</label>
                                        <div class="input-group date" style="width:50%">
                                            <input class="form-control" id="commState1" type="text"
                                                disabled="disabled" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="form-group main-station-label-content">
                        <label for="" class="col-sm-3 control-label">通信状态：</label>
                        <div class="row">
                            <div class="col-md-4" style="margin-left:-15px">
                                <label class="control-label"  for="testConfig">网络状态：</label>
                                <span class="fa fa-check-circle"></span>
                                <button type="button" class="btn btn-default">测试</button>
                            </div>
                            <div class="col-md-4">
                                <label class="control-label"  for="testConfig">接口状态：</label>
                                <span class="fa  fa-times-circle"></span>
                                <button type="button" class="btn btn-default">连通</button>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="box-footer">
            <div class="row">
                <div class="col-md-2 col-md-offset-8" style="display:flex;align-items: center;justify-content: center;">
                    <button type="button" id="resetMainStationParams_btn" class="btn btn-default hide" data-i18n="Reset">重置
                    </button>
                    <button type="submit" id="saveMainStationParams_btn" class="btn btn-primary" data-i18n="Submit">提交
                    </button>
                </div>
            </div>
        </div>
    </div>
    </div>
</section>
<script type="text/javascript">
    initPageI18n();
    //参数原始模板
    var mainStationParamsEg = {
        "cagIp": "",
        "cagPort": "",
        "cagUri": "",
        "user": "",
        "password": "",
        "dataAcquireTime": 2,
        "heartBeatTime": 1,
        "netState": "",
        "commState": ""
    };

    var mainStationParamsObj = {}

    mainStationParamsObj.readMainStationParamsFunc = function (id) {
        var data = {
            id: id
        };
        pollGet(GET_MAINSTATION_PARAMETER, data, function (response) {
            var mainStationParams = response.result;
            for (var item in mainStationParamsEg) {
                $('#' + item + id).val(mainStationParams[item]);
                if (item == "netState") {
                    var netStateEnum = getI18nName(getConnectedStateName(mainStationParams[item]));
                    $('#' + item + id).val(netStateEnum);
                }
                if (item == "commState") {
                    var connectStateEnum = getNetStateName(mainStationParams[item]);
                    $('#' + item + id).val(connectStateEnum);
                }
            }
            $("#auxRtuID").val(mainStationParams.auxRtuID ? mainStationParams.auxRtuID : 999);
            $("#iedRtuID").val(mainStationParams.iedRtuID ? mainStationParams.iedRtuID : 1000);
            //                $("#cagIp").val(mainStationParams.cagIp);
            //                $("#cagPort").val(mainStationParams.cagPort);
            //                $("#cagUri").val(mainStationParams.cagUri);
            //                $("#user").val(mainStationParams.user);
            //                $("#password").val(mainStationParams.password);
            //                $("#dataAcquireTime").val(mainStationParams.dataAcquireTime);
            //                $("#heartBeatTime").val(mainStationParams.heartBeatTime);
        })
    };
    mainStationParamsObj.resetMainStationParamsFunc = function (id) {
        var data = {
            id: id
        };
        pollGet(GET_DEFAULT_STATION_PARAMETER, data, function (response) {
            var mainStationParams = response.result;
            for (var item in mainStationParamsEg) {
                $('#' + item + id).val(mainStationParams[item]);
                if (item == "netState") {
                    var netStateEnum = getI18nName(getNetStateName(mainStationParams[item]));
                    $('#' + item + id).val(netStateEnum);
                }
                if (item == "commState") {
                    var connectStateEnum = getI18nName(getConnectedStateName(mainStationParams[item]));
                    $('#' + item + id).val(connectStateEnum);
                }
            }
        });
    };
    mainStationParamsObj.saveMainStationParamsFunc = function (id) {
        var saveMainStationParams = {
            "id": id
        };
        for (var item in mainStationParamsEg) {
            saveMainStationParams[item] = $('#' + item + id).val();
        }
        var auxRtuID = $("#auxRtuID").val();
        var iedRtuID = $("#iedRtuID").val();
        var dataAcquireTime = $("#dataAcquireTime" + id).val();
        var heartBeatTime = $("#heartBeatTime" + id).val();
        if (auxRtuID === iedRtuID) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('RtuidRepeatTips')
                },
                type: "warning",
            }).show();
            return;
        }
        if (dataAcquireTime < 60) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('DataSubmissionIntervalTips')
                },
                type: "warning",
            }).show();
            return;
        }
        if (heartBeatTime < 60) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('HeartbeatIntervalTips')
                },
                type: "warning",
            }).show();
            return;
        }

        saveMainStationParams.auxRtuID = auxRtuID;
        saveMainStationParams.iedRtuID = iedRtuID;

        console.log(saveMainStationParams.cagIp);
        pollGet(SAVE_MAINSTATION_PARAMETER, saveMainStationParams, function (response) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('MainstationParamsSaveSuccess')
                },
                type: "success",
            }).show();
            console.log(getI18nName('MainstationParamsSaveSuccess'));
        }, function () {
            $('.notifications').notify({
                message: {
                    text: getI18nName('MainstationParamsSaveFailed')
                },
                type: "error",
            }).show();
            console.log(getI18nName('MainstationParamsSaveFailed'));
        });

    };

    //    //注册状态监听
    //    registSocketAction(UPDATE_MAINSTATION_STATE, function (res) {
    //        var result = res.result;
    //        var netStateEnum = getNetStateName(result.netState);
    //        $('#' + "netState" + result.id).val(netStateEnum);
    //        var connectStateEnum = getConnectedStateName(result.commState);
    //        $('#' + "commState" + result.id).val(connectStateEnum);
    //    });

    $(function () {
        mainStationParamsObj.readMainStationParamsFunc(0);
        mainStationParamsObj.readMainStationParamsFunc(1);

        $("#saveMainStationParams_btn").click(function () {
            mainStationParamsObj.saveMainStationParamsFunc($(".stationNav.active").data('id'));
        });

        $("#resetMainStationParams_btn").click(function () {
            mainStationParamsObj.resetMainStationParamsFunc($(".stationNav.active").data('id'));
        });
    });
</script>