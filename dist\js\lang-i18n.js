var language = 'zh-CN'; //语言内容
// var language = 'en-US'; //语言内容
// 枚举
var globalParms;
var defGlobalParms;


var languageParentPath = "./";
/**
 * 内嵌页调用公共i18n资源相对路径初始化工具
 * @layer  layer代表引用{lang-i18n.js}页所在包相对根目录层，根目录为0层，默认引用为根目录
 */
function initLanParentPath(layer) {
    if (layer == 0) {
        languageParentPath = "./";
    } else {
        languageParentPath = "";
        for (var i = 0; i < layer; i++) {
            languageParentPath += "../"
        }
        initGlobalParams();
    }
}

// initGlobalParams();

function initGlobalParams() {
    // $.ajaxSettings.async = false;
    $.get(languageParentPath + 'language/' + language + '.json', function (data) {
        // debugger;
        Object.keys(buildMode.commonLangJson[language]).forEach(function (key) {
            data[key] = buildMode.commonLangJson[language][key];
        })
        globalParms = data; // data即为json文件内容里的json数据
        defGlobalParms = data;
        initJSfile();
    }, 'json');
    // $.ajaxSettings.async = true;
}

function initJSfile() {
    $("[role='reload']").each(function () {
        var file = $(this).attr('src');
        $(this).remove();
        loadScript(file);
    });
}

function loadScript(url, callback) {
    var script = document.createElement("script")
    script.type = "text/javascript";
    script.setAttribute("role", "reload");
    if (script.readyState) { //IE
        script.onreadystatechange = function () {
            if (script.readyState == "loaded" || script.readyState == "complete") {
                script.onreadystatechange = null;
                if (callback) {
                    callback();
                }
            }
        };
    } else { //Others
        script.onload = function () {
            if (callback) {
                callback();
            }
        };
    }
    script.src = url;
    document.getElementsByTagName("head")[0].appendChild(script);
    // var test = $("[role='reload']");
}

/**
 * 切换语言
 * @param language
 */
function changeLanguage(lan, callback) {
    language = lan;
    var head = languageParentPath + 'language/';
    var suffix = '.json';
    var file = head + language + suffix;
    // $.ajaxSettings.async = false;
    $.get(file, function (data) {
        Object.keys(buildMode.commonLangJson[language]).forEach(function (key) {
            data[key] = buildMode.commonLangJson[language][key];
        })
        globalParms = data; // data即为json文件内容里的json数据
        initPageI18n();
        initJSfile();
        if (callback) {
            callback();
        }
    }, 'json');
    // $.ajaxSettings.async = true;
}

/**
 * 页面初始化国际化赋值
 */
function initPageI18n() {
    $("[data-i18n]").each(function () {
        $(this).html(getI18nName($(this).data("i18n")));
    });
    $("[data-i18n-placeholder]").each(function () {
        $(this).attr('data-placeholder', getI18nName($(this).data("i18n-placeholder")));
        $(this).attr('placeholder', getI18nName($(this).data("i18n-placeholder")));
    });
}

/**
 * 旧-filter国际化
 * @param index
 * @returns {*}
 */
function getFilter(index) {
    var filterMsg;
    filterMsg = globalParms[index];
    if (filterMsg == undefined || filterMsg == "") {
        filterMsg = defGlobalParms[index];
    }
    return filterMsg;
}

/**
 * 国际化名称获取方法
 * @param value
 * @returns {*}
 */
//根据类型返回中文名称 -add in 2017/08/26 liyaoxu
function getI18nName(value, def) {
    if (!globalParms) {
        return;
    }
    var valueI18n;
    valueI18n = globalParms[value];
    if (validateLen(valueI18n) == 0) {
        valueI18n = defGlobalParms[value];
        if (validateLen(valueI18n) == 0) {
            if (def != undefined && def != "") {
                valueI18n = def;
            } else {
                valueI18n = value;
            }
        }
    }
    return valueI18n;
}

function validateLen(str) {
    var len = 0;
    if (str != null) {
        for (var i = 0; i < str.length; i++) {
            var c = str.charCodeAt(i);
            //单字节加1
            if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
                len++;
            } else {
                len += 2;
            }
        }
    }
    return len;
}

function getCurrentJson() {
    var tempJson = {};
    $("[data-i18n]").each(function () {
        var key = $(this).data("i18n");
        var value = $(this).html();
        tempJson[key] = value;
    });
    console.info(tempJson);
    alert(JSON.stringify(tempJson));
    return tempJson;
}

function getTimeI18nFormatter(isFull) {
    switch (language) {
        case 'zh-CN':
            break;
        case 'en-US':
            break;
        default:
            break
    }
}