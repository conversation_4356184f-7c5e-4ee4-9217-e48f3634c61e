<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin">
        <!--<div class="box-header with-border">-->
        <!--<h3 class="box-title">LDCU Upgrade</h3>-->
        <!--</div>-->
        <!-- /.box-header -->
        <div class="box-body">
            <label for="fu-upload" data-i18n="fileSelect">文件选择</label>
            <div class="input-group file-caption-main">
                <div tabindex="500" class="form-control file-caption  kv-fileinput-caption">
                    <div style="color: #999" data-i18n="selectPlease">请选择</div>
                </div>
                <div class="input-group-btn">
                    <div tabindex="500" class="btn btn-primary btn-file">
                        <i class="glyphicon glyphicon-folder-open"></i>&nbsp; <span class="hidden-xs"
                            data-i18n="selectPlease">请选择</span>
                        <input id="fu-upload" type="file" class="file" data-upload-url="#">
                    </div>
                </div>
            </div>
            <!-- /.fu-upload -->
            <label for="fu-aduType" data-i18n="sensorType">传感器类型</label>
            <select id="fu-aduType" class="form-control select2" data-placeholder="" style="width: 100%;"></select>
            <!-- /.fu-aduType -->
            <label for="fu-aduItems" data-i18n="sensorList">传感器列表</label>
            <div id="fu-aduItems" class="multiselect">
                <div class="col-xs-5 no-padding full-height">
                    <select name="from[]" id="shuttle" class="form-control" style="height: 100%;" multiple="multiple">
                    </select>
                </div>
                <div class="col-xs-2">
                    <button type="button" id="shuttle_rightAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-forward"></i></button>
                    <br>
                    <button type="button" id="shuttle_rightSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-right"></i></button>
                    <br>
                    <button type="button" id="shuttle_leftSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-left"></i></button>
                    <br>
                    <button type="button" id="shuttle_leftAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-backward"></i></button>
                </div>
                <div class="col-xs-5 no-padding full-height">
                    <select name="to[]" id="shuttle_to" class="form-control" style="height: 100%;"
                        multiple="multiple"></select>
                </div>
            </div>
            <!-- /.fu-aduItems -->
            <br>
            <div id="fu-progress" class="progress-group hide">
                <span class="progress-text" data-i18n="updating">正在更新...</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- /.progress-group -->
        </div>
        <!-- /.box-body -->
        <div class="content-footer">
            <!--<button type="button" id="fu-cancel" class="btn btn-default hide" style="width: 100px"><img src="dist/img/loading.gif" class="img-loading hide" alt="">取消</button>-->
            <button type="button" id="fu-update" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 60px 10px auto" style="width: 100px" data-i18n="update">更新</button>
        </div>
        <br />
    </div>
    <!-- /.box -->

    <!-- /.content-footer -->
</div>
<!--/.col (left) -->



<script>
    //@ sourceURL=hardware_update.js
    //前端类型下拉框
    initPageI18n();
    var aduType = $("#fu-aduType");
    aduType.select2(getADUType(aduType));
    aduType.select2({
        minimumResultsForSearch: Infinity
    });
    aduType.change(function () {
        $("#shuttle").empty();
        $("#shuttle_to").empty();
        var selectText = $(this).children('option:selected').val();
        getADUVersionList(selectText);
    });

    //文件上传组件
    $('#fu-upload').change(function () {
        var files = $(this)[0].files;
        var types = [".bin"];
        inputFile(files, types)
    });

    //前端版本列表穿梭框
    $('#shuttle').multiselect();
    $("#fu-aduItems").height($('#fu-aduItems .col-xs-2').height());

    //上传固件更新文件
    $('#fu-update').click(function () {
        var file = $("#fu-upload")[0];
        // debugger
        if (file != undefined && file.value != "") {
            var shuttleTo = $('#shuttle_to').find('option');
            if (shuttleTo.length > 0) {
                var aduList = new Array();
                for (var i = 0; i < shuttleTo.length; i++) {
                    aduList.push(shuttleTo[i].value);
                }
                var formData = new FormData();
                formData.append("aduType", aduType.children('option:selected').val());
                formData.append("aduList", aduList.toString());
                formData.append("file_data", file.files[0]);
                updateADU(formData);
            } else {
                $('.notifications').notify({
                    message: {
                        text: getI18nName('chosseSensorUpdate') //'Please select LDCU!'
                    },
                    type: "warning",
                }).show();
            }
        } else {
            $('.notifications').notify({
                message: {
                    text: getI18nName('chooseFileUpdate') //'Please select upgrade file!'
                },
                type: "warning"
            }).show();
        }

    })

    $('#fu-cancel').click(function () {
        $.fn.alertMsg(
            'warning',
            getI18nName('cancelUpdateSensorConfirm'), [{
                id: 'no',
                text: 'No'
            }, {
                id: 'yes',
                text: 'Yes',
                callback: function () {
                    //禁用取消按钮
                    $('#fu-cancel').prop('disabled', true);
                    $('#fu-cancel img').removeClass('hide');
                    //关闭websocket请求
                    var message = {
                        "methodName": "PushUpdateADUProgress",
                        "enablePush": false,
                        "parameters": ""
                    }
                    webSocketSend(message);
                }
            }]
        );
    })
</script>