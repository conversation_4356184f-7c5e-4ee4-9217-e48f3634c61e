<!--<div id="AE图谱" class="full-height">-->
<div class="col-sm-12 full-height" id="maingraph">
    <div class="form-group" style="height: 55px;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <label class="col-sm-2  control-label" style="margin-top: 15px" data-i18n="sensorType">传感器类型:</label>
        <label class="col-sm-2  control-label" style="margin-top: 15px;text-align: left" id="sensorType"
            ></label>
        <label class="col-sm-2  control-label" style="margin-top: 15px" data-i18n="acquisitionTime">采集时间:</label>
        <label class="col-sm-2  control-label" style="margin-top: 15px;text-align: left" id="simpleTime">
            </label>
        <label class="col-sm-2  control-label" style="margin-top: 15px; text-align: right;" data-i18n="TEMP">温度:
        </label>
        <label class="col-sm-2  control-label" style="margin-top: 15px;text-align: left" id="amp"></label>
    </div>
    <div class="form-group"
        style="height: 55px;border-left: 1px solid #b9b7b7;border-right: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <div class="col-sm-5" style="margin-top: 10px">
            <label for="dateStart" class="col-sm-4 control-label" style="margin-top: 7px;"
                data-i18n="startDate">起始日期</label>
            <div class="col-sm-8">
                <div class="bootstrap-datepicker">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="dateStart" type="text" class="form-control pull-right" readonly="true">
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-sm-5" style="margin-top: 10px">
            <label for="dateEnd" class="col-sm-4 control-label" style="margin-top: 7px;"
                data-i18n="endDate">结束日期</label>
            <div class="col-sm-8">
                <div class="bootstrap-datepicker">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="dateEnd" type="text" class="form-control pull-right" readonly="true">
                    </div>
                </div>
            </div>
        </div>
        <div class=" col-sm-2" style="z-index: 3;">
            <button type="button" id="refreshBtn" class="btn btn-block btn-primary"
                style="width: 80px; margin: 10px 0px 10px auto" data-i18n="refresh">刷新
            </button>
        </div>
    </div>
    <div class="form-group" id="graph"
        style="height: 80%;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">
        <div id="tmpchart" style="width:100%;height:100%"></div>
    </div>
</div>
<!--</div>-->

<script type="text/javascript">
    initPageI18n();
    $(function () {
        // 基于准备好的dom，初始化echarts图表
        //@ sourceURL=tev_chart.js

        //Daterangepicker
        $('#dateStart').daterangepicker({
            "opens": "right",
            "autoApply": true,
            "timePicker": false,
            "singleDatePicker": true,
            format: 'YYYY-MM-DD HH:mm:ss'
        });

        //Daterangepicker
        $('#dateEnd').daterangepicker({
            "opens": "right",
            "autoApply": true,
            "timePicker": false,
            "singleDatePicker": true,
            format: 'YYYY-MM-DD HH:mm:ss'
        });

        window.addEventListener("resize", function () {
            if (myChart)
                myChart.resize();
        });

        $(window).resize(function () {

            if (!document.getElementById("tmpchart")) { //js判断元素是否存在
                return;
            }
            var height = $("#maingraph").height() - 120;
            $("#graph").height(height);
        });
        $(window).resize();

        var myChart = echarts.init(document.getElementById('tmpchart'));

        var dataXValue = [" "],
            dataYValue = [0];

        var option = {
            tooltip: {
                trigger: 'axis',
                axisPointer: { // 坐标轴指示器，坐标轴触发有效
                    type: 'line' // 默认为直线，可选为：'line' | 'shadow'
                },
                confine: true
            },
            title: {
                text: getI18nName('coreGroundingCurrentChartTitle'),
                x: 'center',
                y: 'top',
                textAlign: 'left'
            },
            //            toolbox: {
            //                show: true,
            //                feature: {
            //                    mark: {show: true},
            //                    magicType: {show: true, type: ['line']}
            //                }
            //            },
            calculable: true,
            xAxis: [{
                type: 'category',
                data: dataXValue,
                boundaryGap: false
            }],
            yAxis: [{
                type: 'value'
            }],
            series: [{
                name: getI18nName('SPTR'),
                type: 'line',
                data: dataYValue,
                itemStyle: {
                    normal: {
                        color: '#3c8dbc'
                    }
                },
                markPoint: {
                    data: [{
                            type: 'max',
                            name: getI18nName('maxValue')
                        },
                        {
                            type: 'min',
                            name: getI18nName('minValue')
                        }
                    ]
                },
                markLine: {
                    precision: 1,
                    data: [{
                        type: 'average',
                        name: getI18nName('average')
                    }]
                }
            }]
        };
        myChart.setOption(option);

        $("#refreshBtn").click(function () {
            var dateStartValue = $("#dateStart").val();
            var dateEndValue = $("#dateEnd").val();
            if (moment(dateStartValue).isAfter(moment(dateEndValue))) {
                layer.msg('<div style="text-align:center">' + getI18nName(
                    'dateStartIsAfterDateEnd') + '</div>')
            } else {
                loadTev(showChartPointId, showChartDataId, $("#dateStart")
                    .val(), $("#dateEnd").val());
            }
        });

        loadTev(showChartPointId, showChartDataId, "", "");

        function loadTev(pointId, dataId, startDate, endDate) {
            //            var pointType = "SPTR";
            var pointType = "TEMP";
            var dataGet = "pointId=" + pointId + "&dataId=" + dataId + "&pointType=" +
                pointType +
                "&startDate=" + startDate + "&endDate=" + endDate;
            poll('GET', 'GetTendData', dataGet, function (text) {
                var respData = text.result;

                if (!respData)
                    return;

                if (option) {
                    option.xAxis[0].data = respData.dataChart.xValue;
                    option.series[0].data = respData.dataChart.yValue;
                    myChart.setOption(option);
                }

                var sensorType = getI18nName(respData.sensorType);
                $("#sensorType").html(sensorType);
                $("#simpleTime").html(respData.sample_time);
                $("#amp").html(respData.AMP != undefined ? respData.AMP +
                    respData.unit : "0" + respData.unit);
                if (respData.startDate) {
                    $('#dateStart').daterangepicker({
                        "opens": "right",
                        "autoApply": true,
                        "timePicker": true,
                        "singleDatePicker": true,
                        "timePicker24Hour": true,
                        "format": 'yyyy/MM/dd HH:mm:ss',
                        "startDate": new Date(respData.startDate)
                    });
                }
                if (respData.endDate) {
                    $('#dateEnd').daterangepicker({
                        "opens": "right",
                        "autoApply": true,
                        "timePicker": true,
                        "singleDatePicker": true,
                        "timePicker24Hour": true,
                        "format": 'yyyy/MM/dd HH:mm:ss',
                        "startDate": new Date(respData.endDate)
                    });
                }
            });
        }
    });
</script>