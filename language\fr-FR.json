{"loginTime": "Heure de connexion", "changerUser": "Changement d'utilisateur", "change": "Commuter", "login": "Se connecter", "systemShutDownConfirmTip": "Le système s'arrête dans 1 minute, l'opération est - elle annulée?", "systemShutDownTip": "1 minute pour arriver, arrêt automatique!", "systemShutDownCancel": "<PERSON><PERSON>r l'arrêt", "loadingData": "Vous travaillez dur pour charger les données, veuillez patienter...", "pageSize": "{1} barre par page", "pageSelecter": "Affichage: {1} - {2}, sur {3} barres", "search": "<PERSON><PERSON><PERSON>", "noMatch": "Aucun enregistrement correspondant trouvé", "index": "Numéro de série", "type": "Type", "aduId": "Codage", "connectedState": "État du réseau", "commType": "Moyens de communication", "loraSignalLevel": "Puissance du signal Lora", "loraSNR": "Rapport signal sur bruit", "powerSupplyMode": "Mode d'alimentation électrique", "byBattery": "Batterie intég<PERSON>e", "byCable": "Alimentation externe", "battaryPercent": "Quantité d'électricité", "battaryLife": "<PERSON><PERSON><PERSON> (jours)", "deviceNameTable": "Nom de l'appareil", "pointType": "Capteurs", "isconnected": "Statut", "onlineState": "En ligne", "offlineState": "Perte de connexion", "aduNeverConnectedState": "Non connecté", "updateTime": "Temps de mise à jour", "data": "<PERSON><PERSON><PERSON>", "chart": "Atlas", "operate": "Opérations", "lookUp": "Voir", "tbSampleDate": "Temps de collecte", "collect": "Acquisition", "errorTip": "Erreur: {1}", "AEamp": "Valeur AE", "aeChartTitle": "Carte de taille AE", "humidityChartTitle": "Courbe d'humidité", "temperatureChartTitle": "Courbe de température", "noiseChartTitle": "<PERSON><PERSON><PERSON> de bruit", "coreGroundingCurrentChartTitle": "Courbe du courant de terre du noyau", "TEVamp": "<PERSON>ur de tev", "TEVYunit": "Unité [DB]", "UnitFomat": "Unité [{1}]", "unitParse": "Unité [{1}]", "maxValue": "Valeur maximale", "minValue": "Valeur minimale", "average": "<PERSON><PERSON><PERSON>", "AT&T": "États - Unis AT & T", "China Unicom": "Chine Unicom", "China Mobile": "Chine mobile", "Transformer": "Transformateur", "Breaker": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disconnector": "Interrupteur d'isolement", "Isolator": "<PERSON><PERSON> co<PERSON>au", "Arrester": "Parafoudre", "PT": "Transformateur de tension", "CT": "Transformateur de courant", "Busbar": "Barre omnibus", "Circuit": "Union mère", "Switchgear": "Armoire de commutation", "Power Cable": "Câbles électriques", "Lightning Rod": "Paratonnerre", "Wall Bushing": "Manchons à travers les murs", "Reactor": "Le réacteur", "Electric Conductor": "Conducteurs de puissance (lignes conductrices)", "Power Capacitor": "Condensateurs de puissance", "Discharge Coil": "<PERSON><PERSON>", "Load Switch": "Interrupteur de charge", "Grounding Transformer": "Changement de terre", "Grounding Resistance": "Résistance à la terre", "Grounding Grid": "Réseau de mise à la terre", "Combined Filter": "Combiner les filtres", "Insulator": "L'isolateur", "Coupling Capacitor": "Condensateur de couplage", "Cabinet": "Armoire à écran", "Other": "Autres", "Fuse": "Fusibles", "Using Transformer": "Changement utilisé", "Arc Suppression Device": "Dispositif d'extinction d'arc", "Main Transformer": "Transformateur principal", "Wave Trap": "Bloqueur d'onde", "Combined Electric Appliance": "Appareils combinés", "Combined Transformer": "Transformateur combiné", "monitor": "Surveillance en temps réel", "dataSelect": "Re<PERSON><PERSON><PERSON><PERSON> de donn<PERSON>", "themeSkin": "La peau", "themeBlue": "Bleu", "settings": "Configuration du système", "datetime_set": "Paramètres de temps", "language_set": "Paramètres de langue", "soft_setting": "Paramètres du système", "file_manage": "Gestion des fichiers", "instrument_set": "Configuration du capteur", "file_point_set": "Configuration des points de mesure", "alarm_param_set": "Configuration d'alarme", "alarm_manager": "Gestion des alarmes", "audit_view": "Audit voir", "modbus_setting": "Paramètres Modbus", "hardware_update": "Mise à jour du firmware", "collect_mode": "Mode d'acquisition", "net_set": "Paramètres réseau", "main_station_params": "Configuration de la station principale", "sync_data": "Synchronisation des données", "syncingData": "Synchronisation des données...", "syncingDataCancel": "Annuler la synchronisation", "syncingDataFailed": "La synchronisation a échoué", "syncingDataSuccess": "Synchronisation des données terminée", "syncingDataProgress": "Synchronisation de la progression des données: {1}", "syncingDataProgress2": "Synchronisation de la progression des données [{1}]", "export_data": "Exporter les données", "system_info": "Informations système", "monitoringTable": "Tableau de données", "PD": "Capteur de placement local intégré", "PD_THREE": "Capteur de placement local intégré (3 en 1)", "PD_FIVE": "Capteur de placement local intégré (5 en 1)", "OLD_IS": "Capteur intelligent à très haute fréquence", "MEU": "Capteur de caractéristiques mécaniques", "UHF_IS": "Capteur intelligent à très haute fréquence", "HFCT_IS": "Capteur intelligent haute fréquence", "PD_IS": "Capteur intelligent externe 3 en 1", "TRANSFORMER_AE_IS": "Capteur à ultrasons pour transformateur", "GIS_AE_IS": "Capteurs à ultrasons GIS", "ENV_IS": "Capteurs intelligents pour l'environnement", "Arrester_U_IS": "Capteur intelligent de tension de parafoudre", "Arrester_I_IS": "Détecteur intelligent de courant parafoudre", "LeakageCurrent_IS": "Capteur intelligent de courant de fuite", "Vibration_IS": "Capteur intelligent de vibration", "MECH_IS": "Caractéristiques mécaniques Smart Monitor", "TEMP_HUM_IS": "Capteur de température et d'humidité", "GrounddingCurrent_IS": "Capteur intelligent de courant de terre", "MECH_PD_TEMP": "Intégré dans le placement local caractéristiques mécaniques capteur de température 3 en 1", "GIS_LOWTEN": "Capteur basse pression", "CHANNEL_OPTICAL_TEMP": "Capteur thermométrique à fibre optique", "VaisalaDTP145": "Vaisala dtp145", "Wika_GDT20": "Le weika gdt20", "Wika_GDHT20": "Le waika gdht20", "SHQiuqi_SC75D_SF6": "Shanghai 虬祺 sc75d", "SPTR_IS": "Capteur de courant de terre Core", "TEMPFC_OUT": "Capteur de température (clmd)", "TEMPXP_OUT": "Capteur de température (sps061)", "SF6YN_OUT": "Capteur de pression de gaz SF6 (FTP - 18)", "FLOOD_IS": "Capteur intelligent immergé dans l'eau", "TEMPKY_OUT": "Capteurs de température (RFC - 01)", "SMOKERK_OUT": "Détecteur de fumée (RS - YG - N01)", "HIKVIDEO_OUT": "Capteur v<PERSON><PERSON><PERSON> (hik)", "TEMPSB_OUT": "Capteur de température (DS18B20)", "TEMP_HUM_JDRK_OUT": "Capteur de température et d'humidité (RS - WS - n01 - 2)", "SF6_OUT": "Capteur SF6 & O2 (WFS - s1p - SO)", "FAN_OUT": "Contrôleur de ventilateur", "LIGHT_OUT": "Contrôleur d'éclairage", "NOISE_JDRK_OUT": "Capteur de bruit (RS - WS - N01)", "IR_DETECTION_OUT": "Détecteur infrarouge à double Identification", "TEMPWS_OUT": "Capteur de température et d'humidité (WS - dmc100)", "FLOODWS_OUT": "Capteur d'immersion dans l'eau (WS - dmc100)", "NOISEWS_OUT": "Capteur de bruit (WS - dmc100)", "VibrationSY_OUT": "Capteur <PERSON> <PERSON>", "TEVPRPS_IS": "Capteur intelligent de tension terrestre temporaire", "SF6_IS": "Capteur intelligent SF6", "phase": "Phase", "period": "Le cycle", "AMP": "Grandeur valeur", "RMS": "Valeur effective", "max": "Maximum de période", "fre1Value": "Composante de fréquence 1", "fre2Value": "Composante de fréquence 2", "All-pass": "Tout à fait", "Low-pass": "Passe - Bas", "High-pass": "Qualcomm", "No-Record": "Non enregistré", "TEV": "Tension de terre temporaire", "AE": "Ultrasons", "TEMP": "Température", "HFCT": "Courant haute fréquence", "UHF": "Très haute fréquence", "Humidity": "<PERSON><PERSON><PERSON><PERSON>", "Decibels": "Décibels", "Noisy": "Pression sonore", "Noise": "Le bruit", "ENVGas": "Atmosphère", "envgas": "Tendances atmosphériques", "MP": "Caractéristiques mécaniques", "FLOOD": "Immersion dans l'eau", "SMOKE": "Sensation de fumée", "SF6": "Hexafluorure de soufre", "FAN": "Le ventilateur", "LIGHT": "Éclairage", "NOISE": "Le bruit", "IRDETECTION": "Infrarouge", "SF6YN": "Pression du gaz SF6", "ArresterU": "Tension du parafoudre", "ArresterI": "<PERSON>urant parafoudre", "LeakageCurrent": "Fuite de courant", "Vibration": "Vibrations", "FrostPointRaw": "Points de givre", "FrostPointATM": "Point de givre (pression atmosphérique standard)", "DewPointRaw": "Point de rosée", "DewPointATM": "Point de rosée (pression atmosphérique standard)", "Moisture": "Micro - eau", "AbsolutePressure": "Pression absolue", "NormalPressure": "Pression standard", "Density": "Densité", "Oxygen": "Teneur en oxygène", "SPTR": "Courant de terre du noyau", "GrounddingCurrent": "Courant de terre", "VIDEO": "Images vidéo", "TEVPRPS": "Tension de terre provisoire prps", "confirm": "Déterminer", "close": "<PERSON><PERSON><PERSON>", "yes": "O<PERSON>", "no": "Non", "continue": "<PERSON><PERSON><PERSON>", "none": "Aucun", "DISK": "Stockage", "MEMORY": "M<PERSON><PERSON><PERSON>", "noTestData": "<PERSON><PERSON>ne donn<PERSON> de <PERSON>", "connected": "Connexion", "disconnected": "Déconnecter", "virtualKeyBord": "<PERSON><PERSON><PERSON>", "pleaseInput": "Veuillez entrer", "pleaseInputFormat": "Veuillez entrer {1}.", "tips": "Conseils", "confirmTips": "Conseils de confirmation", "getDirFailed": "L'obtention du Répertoire a échoué:", "backupSuccessTip": "Données les données ont été sauvegardées avec succès, voir sous {1} Table des matières", "backupFailedTip": "La sauvegarde des données a échoué", "exportFailedTip": "L'exportation des données a échoué: {1}", "historyDataTypeNotSuport": "Cette requête de données historiques de type de capteur n'est pas prise en charge", "stopExportTips": "Faut - il arrêter l'exportation des données?", "stationNameTips": "Veuillez remplir le nom du site", "powerUnitNameTips": "Veuillez remplir l'unité d'électricité", "selectDeviceTips": "Veuillez sélectionner un appareil une fois", "selectPointTips": "Veuillez sélectionner un point de mesure", "selectDeviceOfPointTips": "Veuillez sélectionner un appareil une fois que vous devez ajouter un point de mesure", "saveSuccess": "Sauvegarder le succès", "saveFailed": "La sauvegarde a échoué:", "operatFailed": "L'opération a échoué: {1}", "chosseSensorUpdate": "Veuillez sélectionner un capteur qui doit être mis à jour", "chooseFileUpdate": "Veuillez sélectionner mettre à jour le fichier", "cancelUpdateSensorConfirm": "Confirmer annuler la mise à jour du programme de capteur?", "enterServerUrlTips": "Veuillez entrer l'adresse du serveur", "enterServerUrlError": "L'adresse du serveur a été saisie incorrectement, veuillez la saisir à nouveau.", "enterServerPortTips": "Veuillez entrer le port du serveur.", "enterServerPortError": "Erreur d'entrée du port du serveur, plage d'entrée du port: [165535].", "NTPserverIpError": "L'adresse du serveur a été saisie incorrectement, veuillez la saisir à nouveau.", "NTPserverPortError": "Erreur d'entrée du port du serveur de synchronisation, plage d'entrée du port: [165535].", "enterNetName": "Veuillez entrer le nom du réseau.", "enterIP": "Veuillez entrer IP.", "enterIPError": "L'adresse IP est entrée incorrectement, par exemple: ***********", "enterNetMask": "Veuillez entrer un masque de sous - réseau", "enterNetError": "Erreur d'entrée du masque de sous - réseau, par exemple: *************", "enterGateWay": "Veuillez entrer la passerelle", "enterGateWayError": "Erreurs de passerelle de configuration telles que: ***********", "enterAPN": "Veuillez sélectionner APN", "pdSampleIntervalTip": "Pd - intervalle d'échantillonnage entre 20S et 594 099 s (99h99m99s)", "meuSampleIntervalTip": "Meu - intervalle d'échantillonnage entre 10S et 86400", "monitorAwakeTimeTip": "Intervalle de dormance entre 1S et 86400", "monitorSleepSpaceTip": "Intervalle de réveil: [{1}, {2}] Min", "uploadIntervalTip": "Intervalle de téléchargement entre 60s et 86400", "sampleIntervalTip": "Nombre entier avec intervalle d'échantillonnage compris entre 1 heure et 72 heures", "monitorSampleTimeTip": "Instant entier où l'instant d'échantillonnage de départ de l'hôte est compris entre 0 et 23 heures", "monitorSampleIntervalTip": "Nombre entier d'intervalles d'échantillonnage de l'hôte compris entre 1 heure et 168 heures", "updatingSensor": "Mise à jour des capteurs...", "updatingSensorProgress": "Progression de la mise à jour du capteur: {1}", "updatingSensorProgress2": "Progression de la mise à jour du capteur [{1}]", "selectInstTip": "Veuillez sélectionner le capteur à modifier", "selectChannelTip": "Veuillez sélectionner le Canal à modifier", "instCodeTip": "Le codage du capteur ne peut pas être vide", "commLinkTypeTip": "Veuillez sélectionner un type de lien de communication", "commLinkPortTip": "Veuillez sélectionner un port de communication", "continuSampTimeTip": "Les temps d'acquisition successifs sont compris entre {1} min et {2} min.", "continuSampTimeCompareTip": "Le temps d'acquisition continu doit être inférieur à l'intervalle d'échantillonnage.", "instSampleSpaceTip": "L'intervalle d'échantillonnage est compris entre {1} min et {2} min.", "instSampleStartTimeTip": "L'instant d'échantillonnage de départ est un instant entier compris entre 0 et 23 heures.", "instNumberPattenTip": "Ne peut contenir que des chiffres 0 - 9, des lettres a - ZA - Z et:.", "instIPPattenTip": "Formats d'IP et de port tels que: 0.0.0.0: 1", "deleteInstTip": "Veuillez sélectionner le capteur que vous devez supprimer", "selectSensorTip": "Veuillez sélectionner un capteur", "deleteInstConfirmTip": "OK supprimer < / BR > {1} < / BR > codage du capteur: {2}?", "activeInstConfigTip": "OK appliquez cette configuration à un capteur similaire < / BR > {1}?", "activeBatchConfigsSuccess": "Capteurs de configuration par lots ré<PERSON>is", "activeBatchConfigsBegin": "Début de la configuration par lots des capteurs", "activeBatchConfigsFailed": "Le capteur de configuration par lots a échoué", "collectStartOnceTip": "Êtes - vous sûr de commencer à acquérir des données pour < / BR > {1} capteurs similaires?", "collectStartTip": "Déterminer le début de l'acquisition < / BR > {1} < / BR > des données codées par le capteur: {2}?", "wakeUpInstrumentOnceTip": "OK commencer à réveiller < / BR > {1} capteurs similaires?", "wakeUpInstrumentTip": "OK commencer à réveiller < / BR > {1} < / BR > capteur: {2}?", "emptySensorDataOnceTip": "<PERSON><PERSON><PERSON><PERSON><PERSON> - vous de vider < / BR > {1} toutes les données d'un capteur similaire?", "emptySensorDataTip": "Déterminer vider < / BR > {1} < / BR > toutes les données du capteur codage: {2}?", "emptySensorDataSuccess": "Nettoyage réussi des données du capteur", "emptySensorDataFailed": "L'effacement des données du capteur a échoué:", "ae_chart": "Surveillance en temps réel - AE Atlas", "uhf_chart": "Surveillance en temps réel - UHF Atlas", "hfct_chart": "Surveillance en temps réel - hfct Atlas", "tev_chart": "Surveillance en temps réel - format tev", "tev_prps_chart": "Surveillance en temps réel - carte prps de la tension terrestre transitoire", "temperature_chart": "Surveillance en temps réel - température", "humidity_chart": "Surveillance en temps réel - humidité", "mechanical_chart": "Surveillance en temps réel - caractéristiques mécaniques", "arrester_chart": "Surveillance en temps réel - parafoudre", "leakage_current_chart": "Surveillance en temps réel - courants de fuite", "grounddingcurrent_chart": "Surveillance en temps réel - courant de terre", "vibration_pickup_chart": "Surveillance en temps réel - vibrations", "density_micro_water_history": "Surveillance en temps réel - microeau dense", "core_grounding_current": "Surveillance en temps réel - Core Ground Current", "noise_chart": "Surveillance en temps réel - bruit", "water_immersion": "Surveillance en temps réel - immersion dans l'eau", "smoke_sensor": "Surveillance en temps réel - sensation de fumée", "video_sensor": "Surveillance en temps réel - vidéo", "sf6_sensor": "Surveillance en temps réel - SF6", "error": "<PERSON><PERSON><PERSON>", "warning": "Avertissement", "success": "Su<PERSON>ès", "userErrorTip": "Nom d'utilisateur ou mot de passe incorrect", "errorNoDeviceId": "L'id de l'appareil n'existe pas", "errorNoSensorId": "L'id du capteur n'existe pas", "errorExistSensorId": "L'id du capteur existe déjà", "errorNoChannelId": "L'id du canal n'existe pas", "errorNoPointId": "Le point de mesure n'existe pas", "errorDataExportErrDir": "Exportation de données, chemin de fi<PERSON> incorrect.", "errorDataExportErrTime": "Erreur de temps d'exportation des données", "errorDataExportNoData": "Exportation de données, pas de données.", "errorDataExportNoSpace": "Exportation de données, disque U plein", "errorDataExportNoDisk": "Exportation de données, sans clé USB.", "errorDataExportNoInfo": "Exportation de données, aucune information sur le site.", "errorDataExportOther": "Exportation de données, autres erreurs.", "errorSetModeBus": "Erreur de configuration modebus", "errorSameNamePoint": "Le point de mesure du même nom existe déjà", "errorIP": "Erreur IP", "errorGPRSSet": "E<PERSON>ur de réglage GPRS", "errorSenorUpdateErrFile": "<PERSON><PERSON><PERSON> de fichier de mise à jour du capteur", "errorSenorUpdateNotMatch": "Mise à jour du capteur, le type de capteur ne correspond pas au fichier de mise à niveau", "errorSenorUpdating": "Mise à jour du capteur, mise à jour en cours", "errorSenorUpdateSizeCheckFailed": "Échec du contrôle de la taille du firmware", "errorSenorUpdateVersionCheckFailed": "Échec du contrôle du numéro de version du firmware", "errorSenorUpdateDeviceTypeCheckFailed": "Échec du contrôle du type de périphérique du firmware", "errorSenorUpdateCRCCheckFailed": "Le contrôle CRC du firmware a échoué", "errorSenorUpdateFailed": "La mise à niveau du capteur a échoué", "errorSenorUpdateConnectErr": "Mise à jour du firmware Newsletter exceptions", "errorDataCleanFailed": "La vidange des données a échoué!", "errorCodeWorkGroupNotExist": "Le Groupe de travail n'existe pas!", "errorCodeInvalidChannel": "Passage illégal!", "errorCodeInvalidWirignMode": "Méthode de câblage illégale!", "errorCodeInvalidAlarmMode": "<PERSON><PERSON><PERSON>ode illégale d'alarme!", "errorNoPermission": "Cet utilisateur n'a pas ce droit", "illegalUser": "Utilisateurs illégaux!", "legalPattenMsg": "Ne peut contenir que des caractères (sans espaces): caractères chinois alphanumériques romains Ⅰ-Ⅻ ` . - () [] # 、 _", "groundCurrent": "Diagramme de tendance du courant de terre", "groundCurrentA": "Courant de masse de phase a", "groundCurrentB": "B courant de masse de phase", "groundCurrentC": "C courant de masse de phase", "leakageCurrent": "Graphique de tendance courant complet", "leakageCurrentA": "Phase a courant complet", "leakageCurrentB": "Phase B courant complet", "leakageCurrentC": "Phase C courant complet", "ResistiveCurrent": "Diagramme de tendance du courant résistif", "ResistiveCurrentA": "Courant résistif de phase a", "ResistiveCurrentB": "B courant résistif de phase", "ResistiveCurrentC": "C courant résistif de phase", "resistiveCurrentA": "Courant résistif de phase a", "resistiveCurrentB": "B courant résistif de phase", "resistiveCurrentC": "C courant résistif de phase", "referenceVoltageA": "Tension de référence de la phase a", "referenceVoltageB": "Tension de référence de la phase B", "referenceVoltageC": "Tension de référence de la phase C", "grounddingCurrent": "Diagramme de tendance du courant de terre", "grounddingCurrentA": "Courant de masse de phase a", "grounddingCurrentB": "B courant de masse de phase", "grounddingCurrentC": "C courant de masse de phase", "timeDomain": "Diagramme de Domaine temporel", "frequencyDomain": "Diagramme de domaine de fréquence", "characterParam": "Paramètres caractéristiques", "TimeDomainDataX": "Signal de vibration de l'axe X", "TimeDomainDataY": "Signal de vibration de l'axe Y", "TimeDomainDataZ": "Signal de vibration de l'axe Z", "FrequencyDomainDataX": "Spectrogramme du signal de vibration - Axe X", "FrequencyDomainDataY": "Spectrogramme du signal de vibration - Axe Y", "FrequencyDomainDataZ": "Spectrogramme du signal de vibration - Axe Z", "ACCAVGX": "Moyenne de l'accélération de l'axe X", "ACCAVGY": "Moyenne des accélérations de l'axe Y", "ACCAVGZ": "Moyenne de l'accélération de l'axe Z", "ACCMAXX": "Max. Accélération de l'axe X", "ACCMAXY": "Accélération maximale de l'axe Y", "ACCMAXZ": "Accélération maximale de l'axe Z", "AMPAVGX": "Moyenne de l'amplitude de l'axe X", "AMPAVGY": "Moyenne de l'amplitude de l'axe Y", "AMPAVGZ": "Moyenne de l'amplitude de l'axe Z", "AMPMAXX": "Amplitude maximale de l'axe X", "AMPMAXY": "Amplitude maximale de l'axe Y", "AMPMAXZ": "Amplitude maximale de l'axe Z", "MAXFreqX0": "Axe des x Extreme point fréquence 1", "MAXFreqY0": "Axe Y extrema fréquence du point 1", "MAXFreqZ0": "Axe Z extrema fréquence du point 1", "MAXFreqX1": "Axe des x Extreme point fréquence 2", "MAXFreqY1": "Axe Y extrema fréquence du point 2", "MAXFreqZ1": "Axe Z extrema fréquence du point 2", "MAXFreqX2": "Axe des x Extreme point fréquence 3", "MAXFreqY2": "Axe Y Extreme fréquence du point 3", "MAXFreqZ2": "Axe Z Extreme fréquence du point 3", "sensorType": "Type de capteur", "sensorList": "Liste des capteurs", "gain": "Le gain", "trigger": "Grandeur de déclenchement", "wave_filter": "Bande de fréquences", "sample_date": "Date d'échantillonnage", "sample_time": "Temps d'échantillonnage", "rms": "Valeur effective", "cycle_max": "Maximum de période", "frequency1": "Composante de fréquence 1", "frequency2": "Composante de fréquence 2", "time_interval": "Intervalle de temps", "realData": "Données en temps réel", "historyData": "Données historiques", "trendSync": "Analyse des tendances", "preData": "Article précédent", "nextData": "Article suivant", "systemDate": "Date du système", "systemTime": "Temps système", "backupType": "Type de sauvegarde", "plusBackup": "Sauvegarde incrémentale", "allBackup": "<PERSON><PERSON><PERSON><PERSON> compl<PERSON>", "timeRange": "Période de temps", "exportPath": "Exporter le chemin", "getExportDir": "Obt<PERSON><PERSON> le catalogue", "checkDir": "Vérifier le catalogue", "exportingData": "Exportation en cours...", "cancel": "Annulation", "export": "Exporter", "stationConfig": "Configuration du site", "stationDelSuccess": "Suppression réussie du site", "stationDelFailed": "La suppression du site a échoué:", "deviceConfig": "Configuration de l'appareil une fois", "pointConfig": "Configuration des points de mesure", "stationName": "Nom du site", "stationPMS": "Codage du site", "stationLevel": "Niveau de tension du site", "powerUnit": "Unités électriques", "save": "<PERSON><PERSON><PERSON><PERSON>", "operationSuccess": "Opération réussie", "deviceAddSuccessTips": "Augmenter le succès avec un appareil", "deviceEditSuccessTips": "Une modification d'équipement réussie", "deviceDelSuccessTips": "Suppression réussie d'un appareil en une seule fois", "pointAddSuccessTips": "Point de mesure augmente le succès", "pointEditSuccessTips": "Modification réussie du point de mesure", "pointDelSuccessTips": "Point de mesure supprimé avec succès", "deleteFailedTips": "La suppression a échoué", "save_add": "Préserver / augmenter", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deviceType": "Type d'équipement", "deviceLevel": "Niveau de tension de l'équipement", "add": "Augmentation", "channelTypeAlarm": "Type de données", "alarmThreshold": "<PERSON><PERSON> d'alarme", "alarmRecoveryThreshold": "Seuil de récupération d'alarme", "alarmChannel": "Sélection du canal d'alarme", "built_in_channel_1": "Canal intégré 1", "External_IO_module": "Module io externe", "not_associated": "Non associé", "alarmExternalIOSN": "Codage du module io externe", "alarmExternalIOChannel": "Canal de module io externe", "wiringMode": "Mode de câblage", "alarmMode": "Mode d'alarme", "alarmTime": "Timing d'alarme", "alarmInterval": "Intervalle d'alarme", "alarmDuration": "Du<PERSON>e de l'alarme", "normal_open": "Normalement ouvert", "normal_close": "Normalement fermé", "continuous": "<PERSON><PERSON><PERSON>", "timing": "Timing", "interval": "Intervalle", "alarmSaveSuccess": "Configuration d'alarme sauvegardée avec succès", "alarmDelSuccess": "Alarme configuration supprimé avec succès", "deviceName": "Nom de l'appareil une fois", "pointName": "Nom du point de mesure", "testStation": "Site de test", "device": "Équipement une fois", "pointList": "Liste des points de mesure", "sensorID": "ID du capteur", "sensorChannel": "Canal de capteur", "channelList": "Liste des canaux", "showPoint": "Afficher les points de mesure", "fileSelect": "Sélection de fichiers", "selectPlease": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>ner", "deviceList": "Liste des capteurs", "updating": "Mise à jour en cours...", "updatingFailed": "La mise à jour a échoué:", "updatingCanceled": "Annuler la mise à jour", "updatingComplete": "<PERSON><PERSON><PERSON> la mise à jour", "update": "Mise à jour", "sampleDate": "Date d'échantillonnage", "sampleTime": "Temps d'échantillonnage", "pdMax": "Amplitude de décharge maximale", "pdAvg": "Grandeur moyenne de décharge", "pdNum": "Nombre d'impulsions", "acquisitionTime": "Temps de collecte", "humidity": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scanData": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sensorCode": "Codage des capteurs", "sensorTypeSet": "Type de capteur", "senorName": "Nom du capteur", "aduAddress": "<PERSON><PERSON><PERSON> du capteur", "senorWorkMode": "Envoi actif", "On": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Off": "<PERSON><PERSON><PERSON>", "ADUMode": "Mode de travail", "normalMode": "Mode de maintenance", "lowPowerMode": "Mode basse consommation", "monitorMode": "Mode de surveillance", "artificialStateStartTime": "Instant de début de l'état d'intervention humaine (point entier)", "artificialStateStartTimeTip": "État d'intervention artificielle plage de temps de début 0 - 23", "artificialStateEndTime": "Moment de fin de l'état d'intervention humaine (point entier)", "artificialStateEndTimeTip": "Intervalle de temps de fin d'état d'intervention artificielle 0 - 23", "artificialStateWakeUpSpace": "Intervalle de réveil de l'état d'intervention artificielle (points)", "unartificialStateWakeUpSpace": "Intervalle de réveil de l'état d'intervention non artificielle (points)", "isAutoChangeMode": "Changement de mode automatique ou non", "monitorModeSampleSapce": "Intervalle d'acquisition du mode de surveillance", "workGroup": "Numéro du Groupe de travail", "warnTemp": "Alarme température", "taskGroup": "Numéro du Groupe de tâches", "commLinkType": "Type de lien de communication", "commLinkPort": "Port de communication", "frequencyUnit": "Fréquence du réseau électrique (Hz)", "numInGroup": "Numéro dans le Groupe", "commSpeed": "Taux de communication", "commLoad": "Canal de communication", "sampleSpace": "Intervalle d'échantillonnage (min)", "sleepTime": "Stratégie de consommation d'énergie", "samleStartTime": "Temps d'échantillonnage de départ (point entier)", "applyData": "Opérations", "applyAllSensor": "Application à la même espèce", "collectOnce": "Acquisition par lots", "collectOnceEnd": "Fin de la collecte des données", "collectOnceProgress": "Acquisition de données: {1} ({2})", "collectOnceProgress2": "Acquisition de données [{1}] ({2})", "activeOnce": "Applications par lots", "wakeUp": "Réveillez - vous", "wakeUpInstrument": "Capteur de réveil", "wakeUpInstrumentOnce": "Capteur de réveil par lots", "orderSend": "L'ordre a été envoyé", "cleanData": "Vider les données", "sensorAddSuccess": "Capteur augmente le succès", "sensorEditSuccess": "Modification du capteur réussie", "sensorEditBegin": "Début de la modification du capteur", "sensorDelSuccess": "Capteur supprim<PERSON> avec succès", "cleanSensorData": "Vider les données du capteur", "getSensor": "A<PERSON>q<PERSON><PERSON>r des capteurs", "channelType": "Type de canal", "channelName": "Nom du canal", "channelInfoSaveSuccess": "Channel information sauvegardé avec succès", "channelInfoSaveBegin": "Sensor channel Information enregistrer commencer", "bias": "<PERSON><PERSON><PERSON> [DB]", "waveFilter": "Réglage de la bande de fréquence", "gainMode": "Mode de gain", "gain_unit": "<PERSON><PERSON> [DB]", "samCycle": "Nombre de périodes d'échantillonnage", "samPointNum": "Points d'échantillonnage par période", "samRate": "Points d'échantillonnage par période", "ratio": "Ratio de variation", "channelPhase": "<PERSON>ff<PERSON><PERSON><PERSON>", "mecLoopCurrentThred": "<PERSON><PERSON> de courant de bob<PERSON> [ma]", "mecMotorCurrentThred": "<PERSON><PERSON> de courant moteur [ma]", "mecSwitchState": "état initial du commutateur", "awaysOpen": "Normalement ouvert", "awaysClose": "Normalement fermé", "mecBreakerType": "Configuration du mécanisme du disjoncteur", "oneMech": "Liaison mécanique triphasée (un mécanisme)", "threeMech": "Liaison électrique triphasée (trois mécanismes)", "mecMotorFunctionType": "Type de travail moteur", "threePhaseAsynMotor": "Mo<PERSON>ur asynchrone triphasé", "onePhaseMotor": "Moteur monophasé / DC", "setCurrent": "Définir le présent", "setAll": "Application au capteur du même type", "showCoil": "Carte de courant de bobine", "showSwitch": "Atlas des commutateurs", "showMotor": "Carte du courant moteur", "showOrig": "Carte de courant originale", "actionDate": "Date de l'action", "actionTime": "Temps d'action", "phaseA": "Phase a", "phaseB": "Phase B", "phaseC": "Phase C", "coil_charge_time": "Temps de mise sous tension de la bobine", "coil_cutout_time": "Temps de mise hors tension de la bobine", "max_current": "Courant maximal de la bobine", "hit_time": "Temps d'encliquetage", "subswitch_close_time": "Temps de commutation du commutateur auxiliaire", "a_close_time": "Temps de fermeture de phase a", "a_close_coil_charge_time": "Temps de mise sous tension de la bobine de phase a", "a_close_coil_cutout_time": "Temps de mise hors tension de la bobine de phase a", "a_close_max_current": "Courant maximal de bobine de porte de phase a", "a_close_hit_time": "A verrouillage de verrouillage de verrouillage de verrouillage temps", "a_close_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase a", "b_close_time": "Temps de fermeture de phase B", "b_close_coil_charge_time": "B temps de mise sous tension de la bobine de phase", "b_close_coil_cutout_time": "B temps de mise hors tension de la bobine de phase", "b_close_max_current": "Courant maximal de bobine de fermeture de phase B", "b_close_hit_time": "Temps de verrouillage de verrouillage de phase B", "b_close_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase B", "c_close_time": "C temps de fermeture de phase", "c_close_coil_charge_time": "C temps de mise sous tension de la bobine de phase", "c_close_coil_cutout_time": "C temps de mise hors tension de la bobine de phase", "c_close_max_current": "C phase Gate Coil courant maximal", "c_close_hit_time": "C verrouillage verrouillage verrouillage temps", "c_close_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase C", "close_sync": "Simultanéité de fermeture", "close_time": "Temps de fermeture", "a_open_time": "Temps de séparation de phase a", "a_open_coil_charge_time": "Temps de mise sous tension de la bobine de phase a", "a_open_coil_cutout_time": "Temps de mise hors tension de la bobine de phase a", "a_open_max_current": "A phase shunt Coil courant maximal", "a_open_hit_time": "Temps de déblocage du verrou de séparation de phase a", "a_open_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase a", "b_open_time": "Temps de séparation de phase B", "b_open_coil_charge_time": "B temps de mise sous tension de la bobine de phase", "b_open_coil_cutout_time": "B temps de mise hors tension de la bobine de phase", "b_open_max_current": "Courant maximal de la bobine de dérivation de phase B", "b_open_hit_time": "Temps de déverrouillage du verrou de séparation de phase B", "b_open_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase B", "c_open_time": "C temps de séparation de phase", "c_open_coil_charge_time": "C temps de mise sous tension de la bobine de phase", "c_open_coil_cutout_time": "C temps de mise hors tension de la bobine de phase", "c_open_max_current": "C phase shunt Coil courant maximal", "c_open_hit_time": "Temps de déblocage du verrou d'obturation de phase C", "c_open_subswitch_close_time": "Temps de commutation de l'interrupteur auxiliaire de phase C", "open_sync": "Simultanéité de la fermeture", "open_time": "Temps de fermeture", "a_twice_open_time": "Temps de déclenchement secondaire de la phase a", "a_twice_open_coil_charge_time": "Moment de mise sous tension de la bobine secondaire de dérivation de phase a", "a_twice_open_coil_cutout_time": "Moment de panne de courant de la bobine secondaire de dérivation de phase a", "a_twice_open_max_current": "A phase secondaire shunt Coil courant maximal", "a_twice_open_hit_time": "Temps de verrouillage à deux points de phase a", "a_twice_open_subswitch_close_time": "Moment d'ouverture de l'interrupteur auxiliaire de déclenchement secondaire de phase a", "b_twice_open_time": "Temps de déclenchement secondaire de la phase B", "b_twice_open_coil_cutout_time": "Moment de coupure de courant de la bobine de dérivation secondaire de phase B", "b_twice_open_max_current": "Courant maximal de la bobine de dérivation secondaire de phase B", "b_twice_open_hit_time": "Temps de verrouillage de la phase B", "b_twice_open_subswitch_close_time": "Moment d'ouverture de l'interrupteur auxiliaire de déclenchement secondaire de phase B", "c_twice_open_time": "Temps d'obturation secondaire de phase C", "c_twice_open_coil_cutout_time": "Moment de coupure de courant de la bobine de dérivation secondaire de phase C", "c_twice_open_max_current": "C phase secondaire shunt Coil courant maximal", "c_twice_open_hit_time": "Temps de verrouillage de la phase C", "c_twice_open_subswitch_close_time": "Moment d'ouverture de l'interrupteur auxiliaire de déclenchement secondaire de phase C", "twice_open_sync": "Simultanéité des vannes secondaires", "twice_open_time_text": "Temps de fermeture secondaire", "a_switch_shot_time": "Phase a or courte durée", "b_switch_shot_time": "Phase B or courte durée", "c_switch_shot_time": "Phase C or court temps", "a_switch_no_current_time": "Temps sans courant pour la phase a", "b_switch_no_current_time": "Phase B temps sans courant", "c_switch_no_current_time": "Temps sans courant pour la phase C", "motor_start_current": "Courant de démarrage du moteur", "motor_max_current": "Courant maximal du moteur", "storage_time": "Temps de stockage d'énergie du moteur", "Chan_A_motor_start_current": "Courant de démarrage du moteur de phase a", "Chan_A_motor_max_current": "Courant maximal du moteur de phase a", "Chan_A_storage_time": "Temps de stockage d'énergie du moteur de phase a", "Chan_B_motor_start_current": "Courant de démarrage du moteur de phase B", "Chan_B_motor_max_current": "Courant maximal du moteur de phase B", "Chan_B_storage_time": "Temps de stockage d'énergie du moteur de phase B", "Chan_C_motor_start_current": "Courant de démarrage du moteur de phase C", "Chan_C_motor_max_current": "Courant maximal du moteur de phase C", "Chan_C_storage_time": "Temps de stockage d'énergie de moteur de phase C", "serialPort": "Port série", "baudRate": "<PERSON><PERSON>", "dataBit": "Bits de données", "stopBit": "Posi<PERSON> <PERSON><PERSON><PERSON><PERSON>", "checkBit": "Bit de contrôle", "singleCollect": "Acquisition par lots", "sampling": "En cours de collecte...", "serverIP": "IP du serveur", "serverPort": "Port du serveur", "NTPserverIp": "IP du serveur NTP", "NTPserverPort": "Port du serveur NTP", "netType": "Type de réseau", "deviceIp": "IP hôte", "subnetMask": "Masque de sous - rés<PERSON>", "gateway": "Passerelle par défaut", "networkAPN": "Accès réseau APN", "userName": "Nom d'utilisateur", "passWord": "Mot de passe", "deviceWorkGroup": "Numéro du Groupe de travail hôte", "frequency": "Fréquence du réseau électrique", "pdSampleInterval": "Pd - intervalle d'échantillonnage", "spaceSecond": "& nbsp; Secondes", "meuSampleInterval": "Meu - intervalle d'échantillonnage", "monitorAwakeTime": "Intervalle de dormance", "monitorSleepSpace": "Intervalle de réveil", "wakeStartTime": "<PERSON>ure de réveil de <PERSON> (point entier)", "intervalServer": "Intervalle de téléchargement (serveur)", "intervalMinus": "Intervalle d'échantillonnage", "continuousAcquisitionTime": "Temps d'acquisition continu", "startSampleTime": "Heure d'échantillonnage de départ de l'hôte", "spacePoint": "& nbsp; Le point", "startSampleInterval": "Intervalle d'échantillonnage de départ de l'hôte", "hour": "<PERSON><PERSON>", "poerOffSpace": "Intervalle d<PERSON>", "powerOnSpace": "Intervalle de démarrage", "dateRange": "Plage de dates", "synchronizing": "Synchronisation en cours...", "synchronize": "Synchronisation", "amplitude": "Grandeur valeur", "switch": "Commuter", "copyRight": "© 2020 PMDT Ltd.", "S1010Name": "Hôte d'acquisition de données", "modbusCompanySelf": "<PERSON><PERSON><PERSON>", "logOut": "<PERSON><PERSON><PERSON>", "logOutTitle": "Confirmation de déconnexion", "logOutConfirm": "Se déconnecter connexion utilisateur actuelle?", "logOutTips": "Déconnecté du login", "enterHost": "Veuillez entrer le nom de l'hôte", "selectDevTip": "Veuillez sélectionner un appareil", "stopSyncDataTip": "Faut - il arrêter la synchronisation des données?", "modbusAddress": "<PERSON><PERSON><PERSON>", "modbusAddressCheckTip": "Les adresses Modbus vont de 1 à 254 entiers", "deviceWorkGroupCheckTip": "Le numéro du Groupe de travail hôte est un entier, plage: [{1} ~ {2}]", "emptyMonitorDatas": "Vider les données hôte", "emptyMonitorDatasTip": "Vider toutes les données historiques à l'intérieur de l'hôte d'acquisition", "emptyMonitorDatasSuccess": "Vider les données avec succès", "emptyMonitorDatasApply": "De<PERSON>e de données vidées soumise, en attente de révision", "resetSoftSet": "Restaurer les paramètres d'usine", "resetSoftSetTip": "Restaurer toutes les configurations à l'intérieur de l'hôte d'acquisition à l'état d'usine", "resetSoftSetSuccess": "Demande de restauration des paramètres d'usine soumise, en attente d'audit", "continueConfirmTip": "L'opération n'est pas récupérable, voulez - vous continuer?", "bias_data": "Les valeurs de biais vont de - 100 à 100", "back_ground_data": "Les valeurs de fond vont de 0 à 100", "sam_point": "Fréquence d'échantillonnage par période de 20 à 2000", "sam_rate": "Fréquence d'échantillonnage par période de 20 à 2000", "smartSensorStatus": "Tableau d'état", "upThreshold": "Limite supérieure du seuil", "lowerThreshold": "Limite inférieure du seuil", "changeThreshold": "<PERSON><PERSON> de changement", "changeThresholdLimit": "Le réglage du seuil ne prend en charge qu'une décimale", "upThresholdTip": "Plage de seuil supérieure", "lowerThresholdTip": "Plage de seuil inférieure", "changeThresholdTip": "Plage de seuils de variation", "upLowerThresholderrTip": "La limite inférieure du seuil ne peut pas être supérieure à la limite supérieure du seuil", "monitorName": "Nom de l'hôte", "monitorType": "Type d'hôte", "MonitorTypeHanging": "Tentures murales", "MonitorType2U": "2u", "MonitorTypeCollectionNode": "Rassembler les nœuds", "MonitorTypeLowPower": "<PERSON><PERSON><PERSON> solaire basse consommation", "devicePMSCode": "Codage des équipements", "forceChange": "Changement de mode", "forceChangeSuccess": "Changement de mode réussi", "currentVersion": "Version actuelle", "abnormalRecover": "Auto - récupération anormale", "fromLabel": "<PERSON><PERSON>", "toLabel": "Heure de fin", "select": "Sélection", "sunday": "Jour", "monday": "Un", "tuesday": "<PERSON><PERSON>", "wednesday": "Trois", "thursday": "Quatre", "friday": "Cinq", "saturday": "Six", "January": "<PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON><PERSON>", "March": "Mars", "April": "Avril", "May": "<PERSON>", "June": "Juin", "July": "<PERSON><PERSON><PERSON>", "August": "Août", "September": "Septembre", "October": "Octobre", "November": "Novembre", "December": "Décembre", "all": "Tous", "strong": "Forte", "weak": "Faible", "poor": "La différence", "min": "Points", "second": "Secondes", "uploadInterval": "Intervalle de téléchargement (min)", "loginWelcome": "Bienvenue login", "dateStartIsAfterDateEnd": "Date de début supérieure à la date de fin, veuillez sélectionner à nouveau", "maxCurrent": "Courant maximal", "standMobus": "Fabricant de capteurs", "config1": "Configuration 1", "config2": "Configuration 2", "fWrnThreshold": "Attention au seuil", "fAlmThreshold": "<PERSON><PERSON> d'alerte", "regularDormancy": "Hibernation temporelle", "noDormancy": "Pas dormant", "soakingWater": "Immersion dans l'eau", "dry": "Séchage", "normal": "Normale", "alarm": "Alarme", "lightGunCamera": "Machine à feu visible", "lightBallCamera": "Machine à boules de lumière visible", "infraredGunCamera": "Machine à feu binoculaire infrarouge", "infraredBallCamera": "Machine binoculaire infrarouge", "maxTemp": "Température maximale", "maxTempPosition": "Position de température maximale", "devIPError": "Cette IP est occupée", "unknown": "Inconnu", "fault": "La faute", "lowPower": "Faible quantité d'électricité", "mediumPower": "Puissance moyenne", "highPower": "Électricité élevée", "slaveid": "Id de l'esclave", "LoraFrequency": "Zone hôte", "AREA_CHINA": "Chine < 1 >", "AREA_VIETNAM": "Vietnam (as1)", "AREA_MALAYSIA": "Mal<PERSON>ie (as1)", "AREA_EUROPE": "Europe", "AREA_US": "Amériques", "AREA_INDONESIA": "Indonésie (as2)", "AREA_INDIA": "Inde", "AREA_KOREA": "Corée du Sud", "AREA_CHINA_RSV": "Chine (réserve) < 2 >", "getLogFileError": "L'obtention du fichier journal du capteur a échoué", "exportLogError": "Le fichier journal n'existe pas, l'exportation a échoué", "alertSyncProgress": "Progression de la synchronisation des données d'alerte: {1}", "alertSyncProgress2": "Progression de la mise à jour de la synchronisation des données d'alerte [{1}]", "FIRMWARE_EXCEPTION": "Anomalies du firmware", "AD_INITIALIZATION_EXCEPTION": "Exception d'initialisation ad", "REFERENCE_VOLTAGE_EXCEPTION": "Anomalie de tension de référence", "ONCHIP_FLASH_EXCEPTION": "Anomalies flash dans le film", "OFFCHIP_FLASH_EXCEPTION": "Anomalies Flash hors puce", "SYSTEM_PARAMETERS_EXCEPTION": "Anomalies des paramètres du système", "SAMPLE_PARAMETERS_EXCEPTION": "Anomalies des paramètres d'acquisition", "CALIBRATION_PARAMETERS_EXCEPTION": "Anomalies des paramètres de Calibration", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "Récupération anormale des paramètres système", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "Récupération anormale des paramètres d'acquisition", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "Récupération anormale des paramètres d'étalonnage", "LORA_MODULE_EXCEPTION": "Anomalies du module Lora", "PHASE_NUM": "Nombre de phases", "DISCONNECT_TIME": "Temps de déconnexion", "DATA_TOTAL_COUNT": "Total des données", "ONLINE_RATE": "Taux en ligne", "aduInfoSetProgress": "Configuration du capteur: {1} ({2})", "aduInfoSetProgress2": "Configuration du capteur [{1}] ({2})", "aduInfoSetting": "Configuration des capteurs...", "aduInfoSetEnd": "Configuration du capteur terminée", "aduDataSample": "Acquisition de données", "aduInfoSet": "Configuration du capteur", "errorDataSampleRunning": "Une entreprise d'acquisition de capteurs est actuellement en cours, Veuillez réessayer une fois terminé", "errorAduInfoSetRunning": "Actuellement en cours d'exécution pour les paramètres du capteur, Veuillez réessayer une fois terminé", "SAMPLE_PERIOD": "Nombre d'ondes hebdomadaires d'échantillonnage", "SF6_Density": "SF6 densité du gaz (p20)", "SF6_Temp": "SF6 température du gaz", "Device_env_Temp": "Température ambiante de l'équipement", "Alarm_Status": "Identification du statut d'alerte", "SF6_Alarm_Low_Pressure": "Alarme basse tension", "SF6_Alarm_Low_Voltage_Block": "Verrouillage basse pression", "SF6_Alarm_Over_Voltage": "Alarme de surpression", "AlarmType": "Type d'alerte", "AlarmData": "Contenu de l'alerte", "AlarmTime": "Temps d'alerte", "AlarmLevel": "Niveau d'alarme", "AlarmStateWarning": "<PERSON><PERSON><PERSON>", "WarningValue": "Valeur d'alerte précoce", "AlarmValue": "<PERSON><PERSON> d'alarme", "SetSuccess": "Configurer avec succès", "AlarmDate": "<PERSON><PERSON> d'alarme", "AlarmConfirm": "Confirmation d'alarme", "Confirm": "Confirmation", "Confirmed": "<PERSON><PERSON><PERSON><PERSON>", "AllData": "Toutes les données", "CheckManagerSponsor": "Initiateur", "CheckManagerCheckType": "Type d'audit", "CheckManagerDate": "Date de lancement", "CheckManagerTarget": "Initiateur", "CheckManagerExtraInfo": "Informations supplémentaires", "CheckManagerResult": "Consentement ou non", "Refuse": "<PERSON><PERSON><PERSON><PERSON>", "Agree": "D'accord.", "DataExport": "Exportation de données", "DataExportStep1": "Sélectionnez les données à exporter", "DataExportStep1Title": "Exporter la sélection de fichiers", "DataExportStep2": "Calcul de la taille des données...", "DataExportStep2Title": "Calculer la taille des données brutes", "DataExportStep3": "Packaging de données compressées...", "DataExportStep3Title": "Packaging de données compressées", "DataExportStep4": "Compression des données terminée", "DataExportStep4Title": "Compression des données terminée", "DataExportCheckData": "Base de données [/ media / data / database]", "DataExportCheckDataFile": "Fichier de données [/ media / data / DATAFILE]", "DataExportCheckLog": "Fichier journal [/ media / data / log]", "DataExportCheckConfig": "Fichier de configuration [/ home / root / config.xml]", "DataExportBegin": "Commencer à exporter", "SelectAtLeastOneTips": "Choisissez au moins un", "DataExportFileSizeError": "La taille du fichier est supérieure à 2000mb, veuillez exporter les données à l'aide d'outils tels que winscp", "DataExportFileSizeZeroError": "La taille de fichier sélectionnée est 0", "DataExportCancelTips": "L'exportation des données a été annulée", "DataExportDataCompressTips": "La taille des données brutes est {1} MB, temps de compression estimé [{2} min {3} s]", "LogExportStep1": "<PERSON><PERSON> un capteur", "LogExportStep1Title": "<PERSON><PERSON> un capteur", "LogExportStep2": "Obtenir le fichier journal...", "LogExportStep2Title": "Obtenir un fichier journal", "LogExportStep3": "Obtenir le fichier journal terminé", "LogExportStep3Title": "Obtenir le fichier journal terminé", "LogExportStep4": "Package de fichiers journaux compressés...", "LogExportStep4Title": "Packaging de fichiers journaux compressés", "LogExportStep5": "Compression terminée", "LogExportStep5Title": "Compression terminée", "LogExportCancelTips": "L'exportation du journal a été annulée", "batteryVoltage": "Tension de la batterie", "superCapvoltage": "Tension Supercapacitive", "OverPressureThreshold": "Seuil de surpression", "LowPressureThreshold": "<PERSON><PERSON> de basse pression", "ShutThreshold": "<PERSON><PERSON>", "PhysicalChannelType": "Types de canaux physiques", "GasAbsPressure": "Pression absolue de gaz", "GasGaugePressure": "Pression manométrique de gaz", "CameraType": "Type de caméra", "DEFAULT_CHECK_Tips": "Le format de numérotation est M: n, tel que 1: 100, où m varie [1 - 255] et n varie [0 - 65535]", "VibrationSY_OUT_CHECK_Tips": "Le format de numérotation est Address: Modbus: ID, tel que 1: 0: 1917, où address range, est [1 - 255] et Modbus / id est [0 - 65535].", "NOISEWS_OUT_CHECK_Tips": "Le format de numérotation est IP: X: y, par exemple *********: 1321: 4512, où le format IP [xxx.xxx.xxx.xxx] et X / y sont tous des chaînes de longueur 4.", "IR_DETECTION_OUT_IO_CHECK_Tips": "Le format de numérotation est IP: port: SS: se: CS: ce, par exemple 1: 100, où le format IP [xxx.xxx.xxx.xxx] port plage de ports [0 - 65535] ss plage [0 - 255], se plage 0 - 255], CS plage 0 - 255], ce plage 0 - 255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "Le format de numérotation est IP: port: X: y, par exemple 1: 100, où le format IP [xxx.xxx.xxx.xxx] plage de ports [0 - 65535] plage x [0 - 255] plage y [0 - 65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "Le format de numérotation est M: n, tel que 1: 100, où m varie [1 - 255] et n varie [0 - 65535]", "fWrnThreshold_CHECK_Tips": "Notez que la plage de seuil est [10 - 1000] ma", "fAlmThreshold_CHECK_1_Tips": "<PERSON><PERSON> d'alerte allant de [50 - 5000] ma", "fAlmThreshold_CHECK_2_Tips": "Le seuil d'alerte doit être supérieur au seuil d'attention", "AuditContent": "Contenu de l'audit", "OPTime": "Temps de fonctionnement", "Executed": "Exécution", "UserManager": "Gestion des utilisateurs", "SystemManagement": "Gestion du système", "BusinessManagement": "Gestion des affaires", "LanguageChange": "Changement de langue", "AlarmManagement": "Gestion des alertes", "DataOperation": "Opérations de données", "BackupRecovery": "Restauration de sauvegarde", "AddUser": "Ajouter un utilisateur", "AddUserCheck": "Ajouter un audit utilisateur", "UserRightSet": "Paramètres des droits utilisateur", "UserRightSetCheck": "Audit des paramètres de droits utilisateur", "DeleteUser": "Supprimer un utilisateur", "DeleteUserConfirm": "Supprimer la confirmation de l'utilisateur", "FreezeUser": "Utilisateurs gelés", "FreezeUserConfirm": "Confirmation de l'utilisateur gelé", "UnlockUser": "Déverrouiller l'utilisateur", "UnlockUserConfirm": "Déverrouiller la confirmation de l'utilisateur", "FileStationSet": "Paramètres du fichier (site)", "FileDeviceSet": "Paramètres du fichier (appareil)", "SenserSet": "Paramètres du capteur", "AlarmSet": "Paramètres d'alerte", "SavePointSet": "Enregistrer les paramètres du point de mesure", "DelPointSet": "Supprimer les paramètres de point de mesure", "SingleSample": "Une seule acquisition", "DataBackup": "Sauvegarde des données", "DataRecovery": "Récupération de données", "ClearDataCheck": "Audit des données vides", "RestoreFactorySettingsReview": "Restaurer l'audit des paramètres d'usine", "SaveMainStation": "Sauvegarder la station principale", "DataView": "Vue des données", "DataSync": "Synchronisation des données", "RightSet": "Configuration des permissions", "GetUserList": "Obtenir la liste des utilisateurs", "AuditData": "Données d'audit", "AuditPermissions": "Autorisation d'audit", "MainStationSet": "Paramètres de la station principale", "ChangePasswordSelfOnly": "Changer votre mot de passe (uniquement pour cet utilisateur)", "ChangePasswordForNormal": "Modifier le mot de passe de l'utilisateur normal", "ChangeUserRight": "Modifier / définir les autorisations utilisateur", "ChangePassword": "Modifier le mot de passe", "ChangeLoginInfo": "Modifier les informations de connexion", "UserStatus": "Statut de l'utilisateur", "UserLanguage": "Langue de l'utilisateur", "HasLogin": "A - t - il atterri", "RoleType": "Types de rôles", "IsPassTimeout": "Le mot de passe a - t - il expiré", "AuditsManagement": "Gestion des audits", "SensorOperation": "Fonctionnement du capteur", "SensorLogExport": "Export du Journal des capteurs", "SensorAlarmDataSync": "Synchronisation des données d'alerte de capteur", "ViewSensorAlarmData": "Affichage des données d'alerte de capteur", "Block": "<PERSON><PERSON><PERSON>", "BlockPendingReview": "Congelé - à auditer", "UnlockPendingReview": "Décongeler - à auditer", "DeletePendingReview": "Supprimer - en attente d'audit", "AddUserPendingReview": "Nouveau - à auditer", "ChangePassPendingReview": "Modifier le mot de passe - à vérifier", "ChangeRightPendingReview": "Modifier les permissions - à vérifier", "abnormal": "Anomalies", "DataQueryNotSupported": "Cette requête de données d'historique de type frontal n'est pas prise en charge", "DeviceCodeExists": "Le nom de l'appareil ou le codage de l'appareil existe déjà", "OutputSwitchConfig": "Configuration du commutateur de sortie", "OutputSwitch": "Commutateur de sortie", "MainStationAuxRtuID": "Contrôle supplémentaire rtuid", "MainStationIedRtuID": "Mutualiser rtuid", "MainstationParams1": "Paramètres de la station principale 1", "MainstationParams2": "Paramètres de la station principale 2", "MainstationInterface1": "Interface de la station principale 1", "MainstationInterface2": "Interface de la station principale 2", "Port": "Ports", "IPAddress": "Adresse IP", "EnterUriTips": "Veuillez entrer le chemin Uri", "ConnectUserName": "Nom d'utilisateur communication", "EnterConnectUserNameTips": "Veuillez entrer un nom d'utilisateur de communication", "ConnectPass": "Mot de passe pour la communication", "EnterConnectPassTips": "Veuillez entrer votre mot de passe de communication", "DataSubmissionInterval": "Intervalle d'envoi sur les données", "EnderDataSBIntervalTips": "Veuillez entrer l'intervalle d'envoi sur les données", "HeartbeatInterval": "Intervalle de battements de coeur", "EnterHertbeatIntervalTips": "Veuillez entrer l'intervalle de battement de coeur", "ConnectStatus": "État de connexion", "Reset": "Réinitialisation", "Submit": "So<PERSON><PERSON><PERSON>", "RtuidRepeatTips": "Rtuid auxiliaire ne peut pas être identique à rtuid mutualisé", "DataSubmissionIntervalTips": "L'intervalle d'envoi sur les données ne peut pas être inférieur à 60", "HeartbeatIntervalTips": "L'intervalle entre les battements cardiaques ne peut pas être inférieur à 60", "MainstationParamsSaveSuccess": "Paramètres de la station principale sauvegardés avec succès", "MainstationParamsSaveFailed": "L'enregistrement des paramètres de la station principale a échoué", "OutSwitchSaveSuccess": "Configuration du commutateur de sortie enregistrer avec succès", "OutSwitchSaveFailed": "La sauvegarde de la configuration du commutateur de sortie a échoué", "ChartConfig": "Configuration de l'Atlas", "Measurement": "Mesure", "SENSOR_TIMEOUT_COUNT": "Nombre de Détections du Capteur Hors Ligne", "OUTWARD_CONFIG_ENABLE_STATE": "État de la configuration", "OUTWARD_CONFIG_ENABLED": "Activé", "OUTWARD_CONFIG_DISABLED": "Désactivé", "OUTWARD_CONFIG_ENABLING": "Activation en cours", "SENSOR_TIMEOUT_DISABLING": "Désactivation en cours", "ERROR_NO_CONFIG": "La configuration n'existe pas", "ERROR_INPUT_PARAMS": "<PERSON><PERSON><PERSON> de paramètre d'entrée", "ERROR_INTEGER": "Veuillez entrer un nombre entier", "ERROR_TIP_RANGE": "Plage de valeurs [{1}~{2}]", "ERROR_IP_FORMAT": "Le format IP est incorrect", "ERROR_FILE_NAME": "Le nom du fichier doit être {1}", "ERROR_FILE_SUFFIX": "L'extension du fichier doit être {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "Le fichier doit être inférieur à {1} Mo", "ERROR_T2_T1": "T2 doit être inférieur à T1", "LENGTH_COMM_LIMIT": "Veuillez noter que ce champ ne peut pas dépasser {1} caractères.", "MANU_FAST_SYNC_DATA": "Synchronisation rapide", "SYNC_DATA_STATE_LIST": "Liste des états de synchronisation des capteurs", "LAST_SYNC_DATE_RANGE": "Plage de dates de la dernière synchronisation", "NoError": "<PERSON><PERSON><PERSON> erreur", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "D<PERSON>lai de synchronisation dépassé", "SYNC_DATA_ERROR_DISCONNECT": "Déconnexion", "SYNC_DATA_STATUS_WAITING": "En attente de connexion", "SYNC_DATA_STATUS_CONNECTED": "Connecté", "SYNC_DATA_STATUS_SYNCING": "Synchronisation en cours", "SYNC_DATA_STATUS_SUCCESS": "Synchronisation réussie", "SYNC_DATA_STATUS_FAILED": "Échec de la synchronisation", "SYNC_DATA_STATUS_CANCELED": "Synchronisation annulée", "Today": "<PERSON><PERSON><PERSON>'hui", "Yesterday": "<PERSON>er", "LAST_7_DAYS": "7 derniers jours", "LAST_30_DAYS": "30 derniers jours", "THIS_MONTH": "Ce mois-ci", "LAST_MONTH": "Le mois dernier", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Échec du démarrage du service de communication", "FastSyncDataCancelTips": "Êtes-vous sûr de vouloir annuler la synchronisation des données ?", "FastSyncDataSelectTips": "Veuillez sélectionner les capteurs pour la synchronisation des données", "Band-pass": "Passe-bande", "aeWaveChartTitle": "Spectre de forme d'onde AE", "aePhaseChartTitle": "Spectre de phase AE", "aeFlyChartTitle": "Spectre de vol AE", "LOCAL_PARAMS_TIME": "Temps", "LOCAL_CHART_COLORDENSITY": "Densité de couleur", "openTime": "Temps d'ouverture", "closeTime": "Temps de fermeture", "triggerUnit": "Amplitude de déclenchement[μV]", "openTimeUnit": "Temps d'ouverture[μs]", "closeTimeUnit": "Temps de fermeture[μs]", "triggerUnitTips": "Gamme d'amplitude de déclenchement: [{1}, {2}] μv", "TOP_DISCHARGE_AMPLITUDE": "TOP3 amplitude de décharge", "WS_901_ERROR_TIPS": "Le service de diffusion des commandes est en cours d'enregistrement, ve<PERSON><PERSON>z réessayer après 2 secondes"}