/*
 * @Author: liyaoxu <EMAIL>
 * @Date: 2023-09-21 16:20:03
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-11-30 10:14:40
 * @FilePath: \pds_z058_web\pages\mainStationConfig\mainStationConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
var MainstatinConfigController = function(){
    
}

function initEnumCache(){
    pollMokerGet(GET_STATION_PROTOCOL_LIST,{},function(res){
        MainStationConfigLayerUtil.STATION_PROTOCOL = res.result;
        MainStationParamsManager.COMMON_PARAM_CONFIGS[0].data= [];
        MainStationParamsManager.COMMON_PARAM_CONFIGS[0].value = MainStationConfigLayerUtil.STATION_PROTOCOL[0].protocolTypeID;
        MainStationConfigLayerUtil.STATION_PROTOCOL.map((item,index) => {
            MainStationParamsManager.COMMON_PARAM_CONFIGS[0].data.push({
                key:item.protocolTypeID,
                name:getCodeName(item.protocolTypeID,COMMON_PROTOCOL),
            });
            if(index==0){
                MainStationParamsManager.COMMON_PARAM_CONFIGS[1].data=[];
                item.businessProtocolTypeIDs.map((BprotocoTypeID,index) => {
                    MainStationParamsManager.COMMON_PARAM_CONFIGS[1].data.push({
                        key:BprotocoTypeID,
                        name:getCodeName(BprotocoTypeID,BUSI_PROTOCOL),
                    });
                    if(index == 0){
                        MainStationParamsManager.COMMON_PARAM_CONFIGS[1].value = BprotocoTypeID; 
                    }
                });
            }
        });
        MainStationConfigLayerUtil.changeProtocol(getCodeName(MainStationConfigLayerUtil.STATION_PROTOCOL[0].protocolTypeID,COMMON_PROTOCOL));
    });
}

function initMainStationConfigList(){
        initEnumCache();
        /**
         * 初始化表格内容
         */
        initCustomTable({
            id: 'tableMainStationConfig',
            method: MAINSTATION_COMM_CONFIG,
            toolbar: '#mainStationToolbar',
            height: $('#global').height() * 0.8,
            useMocker:true,
            pageSize: 50,
            pageList: [50],
            responseHandler: function (res) {
                if (res.errorCode != 0) {
                    if (res.errorCode == 901) {
                        logOut('repeatLogin');
                        return;
                    } else {
                        $.notifyUtil.notifyWarning(convertErrorCode(res.errorCode));
                    }
                }
                var rows = res.result.protocolConfigList;
                var total = rows.length;
                return {
                    rows:rows,
                    total:total
                };
            },
            requestParam: function (params) {
                var req={};
                return req;
            },
            columns: [{
                field: 'index',
                title: getI18nName('index')
            },
            {
                field: 'protocolTypeID',
                title: getI18nName('CommunicatProtocol'),
                formatter: function (value, row, index) {
                    var commPortocolVal = getCodeName(value,COMMON_PROTOCOL);
                    return commPortocolVal;
                }
            },
            {
                field: 'businessProtocolTypeID',
                title: getI18nName('BusiProtocol'),
                formatter: function (value, row, index) {
                    var busiAgreementVal = getCodeName(value,BUSI_PROTOCOL);
                    return busiAgreementVal;
                }
            },{
                field: 'remark',
                title: getI18nName('Remark')
            },{
                field: 'option',
                title: getI18nName('operate'),
                width: '30%',
                events: 'mainStationConfigOperateEvents',
                formatter: function (value, row, index) {
                    var content = '<div class="option">';

                    var detailBtn = 
                    `<div class="option-icon config-detail" title="${getI18nName('Edit')}">
                        <span class='fa fa-file-text-o'></span>
                    </div>`;

                    var editBtn = 
                    `<div class="option-icon config-edit" title="${getI18nName('Details')}">
                        <span class='fa fa-edit'></span>
                    </div>`;

                    var stateIcon = row.isEnable?'fa-ban':'fa-play-circle-o';
                    var stateTitle = row.isEnable?getI18nName('Deactivate'):getI18nName('Enable');
                    var stateBtn = 
                    `<div class="option-icon config-state" title="${getI18nName(stateTitle)}">
                        <span class='fa ${stateIcon}'></span>
                    </div>`;
                    
                    var deleteBtn = 
                    `<div class="option-icon config-delete" title="${getI18nName('delete')}">
                        <span class='glyphicon glyphicon-trash' style='font-size: 18px;font-weight: 600;'></span>
                    </div>`;
                    
                    content += detailBtn + editBtn + stateBtn +deleteBtn +'</div>';
                    return content;
                }
            },]
        });
}

function addMainStationConfig(row){
    var protocolTypeID = MainStationConfigLayerUtil.currentParamConfig.find(function(item){return item.id=="protocolTypeID"}).value;
    var businessProtocolTypeID = MainStationConfigLayerUtil.currentParamConfig.find(function(item){return item.id=="businessProtocolTypeID"}).value
    MainStationConfigLayerUtil.cleanCurrentValue();
    MainStationConfigLayerUtil.updateCurrentValue({
        protocolTypeID:protocolTypeID,
        businessProtocolTypeID:businessProtocolTypeID,
    });
    MainStationConfigLayerUtil.open('add');
}

function showMainStationConfigDetail(row){
    var param = {
        protocolTypeID:row.protocolTypeID,
        configID:row.configID,
    }
    pollMokerGet(GET_STATION_PROTOCOL_DETAIL,param,function(res){
        console.log(res);
        MainStationConfigLayerUtil.cleanCurrentValue();
        MainStationConfigLayerUtil.changeBprotocolArr(row.protocolTypeID);
        MainStationConfigLayerUtil.changeProtocol(getCodeName(row.protocolTypeID,COMMON_PROTOCOL));
        var detail = res.result? res.result:{};
        detail.protocolTypeID = row.protocolTypeID;
        detail.businessProtocolTypeID = row.businessProtocolTypeID;
        MainStationConfigLayerUtil.updateCurrentValue(detail);
        MainStationConfigLayerUtil.open('detail',detail);
    });
}

function editMainStationConfig(row){
    var param = {
        protocolTypeID:row.protocolTypeID,
        configID:row.configID,
    }
    pollMokerGet(GET_STATION_PROTOCOL_DETAIL,param,function(res){
        console.log(res);
        MainStationConfigLayerUtil.cleanCurrentValue();
        MainStationConfigLayerUtil.changeBprotocolArr(row.protocolTypeID);
        MainStationConfigLayerUtil.changeProtocol(getCodeName(row.protocolTypeID,COMMON_PROTOCOL));
        var detail = res.result? res.result:{};
        detail.protocolTypeID = row.protocolTypeID;
        detail.businessProtocolTypeID = row.businessProtocolTypeID;
        MainStationConfigLayerUtil.updateCurrentValue(detail);
        MainStationConfigLayerUtil.open('edit',detail);
    });
}

function deleteMainStationConfig(row){
    var param = {
        protocolTypeID:row.protocolTypeID,
        configID:row.configID,
    }
    pollMokerGet(DELETE_OUTWARD_PROTOCOL,param,function(res){
        refreshMainStationTable();
    });
}

function changMainStationConfigState(row){
    var isEnable = row.isEnable;
    var url = isEnable?DISABLE_OUTWARD_PROTOCOL:ENABLE_OUTWARD_PROTOCOL;
    pollMokerGet(url,{
        protocolTypeID:row.protocolTypeID,
        configID:row.configID,
    },function(res){
        refreshMainStationTable();
    });
}



/**
 * 监听列表点击事件
 */
window.mainStationConfigOperateEvents = {
    'click .config-detail': function (e, value, row, index) {
        e.stopPropagation();
        showMainStationConfigDetail(row);
    },
    'click .config-edit': function (e, value, row, index) {
        e.stopPropagation();
        editMainStationConfig(row)
    },
    'click .config-state': function (e, value, row, index) {
        e.stopPropagation();
        changMainStationConfigState(row);
    },
    'click .config-delete': function (e, value, row, index) {
        e.stopPropagation();
        deleteMainStationConfig(row);
    },
};


function refreshMainStationTable() {
    $('#tableMainStationConfig').bootstrapTable('refresh', {
        silent: true
    });
}

var STATION_PARAM_ENUM = {
    INPUT:'input',
    TEXT_AREA:'text_area',
    SELECT:'select',
    SWITCH:'switch',
    CHECK:'check',
    FILE_INPUT:'file_input',
}

var MainStationConfigLayerUtil = {
    cleanCurrentValue:function(){
        MainStationConfigLayerUtil.currentParamConfig.map(function(item){
            item.value = '';
        });
    },
    updateCurrentValue:function(params){
        MainStationConfigLayerUtil.currentParamConfig.map((item) => {
            if(params[item.id]!=undefined){
                item.value = params[item.id];
            }
        });
    },
    renderParam:function(paramConfig){
        var outerContent =  $('#stationConfigParams');
        var $itemContent = $('<div>', {
            class: 'form-group station-config-param-content',
            style: 'margin:15px 0',
        }).appendTo(outerContent);

        var $lable = $('<label>',{
            class:'col-sm-4 control-label',
            'data-i18n':paramConfig.name,
            for:paramConfig.id,
        }).appendTo($itemContent);

        var $paramContent = $('<div>', {
            class: 'col-sm-6',
        }).appendTo($itemContent);

        if(paramConfig.type == STATION_PARAM_ENUM.INPUT){
            var $param = $('<input>', {
                id:paramConfig.id,
                name:paramConfig.id,
                class: 'col-sm-4 form-control',
                vk: 'true',
                autocomplete: "off",
                style:paramConfig.unit?'padding-right: 4rem;':'',
                type: paramConfig.inputType?paramConfig.inputType:'text', 
            }).appendTo($paramContent);
            if(paramConfig.value!=undefined){
                $param.val(paramConfig.value);
            }
            if(paramConfig.unit){
                $('<div>',{
                    class:'station-config-unit',
                    html:paramConfig.unit,
                }).appendTo($paramContent);
            }
        } else if(paramConfig.type == STATION_PARAM_ENUM.SELECT){

            var $param = $('<select>', {
                id:paramConfig.id,
                class: 'col-sm-4 form-control station-select',
                change:paramConfig.onChange
            }).appendTo($paramContent);

            if(paramConfig.data){
                $param.empty();
                paramConfig.data.map((item) => {
                    var key = name = item;
                    if(typeof item == 'object') {
                        key = item.key;
                        name = item.name;
                    }
                    $param.append(`<option value=${key}>${name}</option>`)
                });
            }
            if(paramConfig.value != undefined){
                $param.val(paramConfig.value);
            }

        } else if(paramConfig.type == STATION_PARAM_ENUM.SWITCH){
                {/* <label id="switchVersionName" style="margin: 0 15px;"></label>
                <input id="switchVersionBox" class="switch-box switch-box-anim" type="checkbox" checked="checked" /> */}
                var $param = $('<input>', {
                    id:paramConfig.id,
                    class: 'switch-box switch-box-anim form-control',
                    style:'outline: none;',
                    checked:paramConfig.value,
                    type: 'checkbox', 
                }).appendTo($paramContent);
                $('<label>',{
                    style:'position: absolute;margin-left: 10px;top: 7px;',
                    text:paramConfig.unit,
                }).appendTo($paramContent);
        } else if(paramConfig.type == STATION_PARAM_ENUM.FILE_INPUT){

        } else if(paramConfig.type == STATION_PARAM_ENUM.TEXT_AREA){
            if(paramConfig.length == undefined) paramConfig.length=200;

            var $areaContent = $('<div>',{
                class:"textarea-group",
            }).appendTo($paramContent);

            var $param = $('<textarea>', {
                id:paramConfig.id,
                class: 'col-sm-4 form-control',
                style:'min-height: 120px;resize: vertical;max-height: 160px;',
                vk: 'true',
            }).appendTo($areaContent);

            var $limitCount=$('<div>',{
                class:'limit statistics',
                text:'0/' + paramConfig.length
            }).appendTo($areaContent);


            var bind_name = 'input';
            if (navigator.userAgent.indexOf("MSIE") != -1) { //（此处是为了兼容IE）
                bind_name = 'propertychange';
            }
            if (navigator.userAgent.match(/android/i) == "android") {
                bind_name = "keyup";
            }
            $param.bind(bind_name, function() {
                var limitSub = $(this).val().substr(0, paramConfig.length);
                $(this).val(limitSub); //截取字符长度
                $(this).next('.statistics').html(limitSub.length + '/'+ paramConfig.length); //获取实时输入字符长度并显示
                if (limitSub.length == paramConfig.length) {
                    $('.limit').css('color', '#367fa9'); //超出指定字符长度标红提示
                    // alert(`你已超出${paramConfig.length}个字!`);
                } else {
                    $('.limit').css('color', '#333');
                }
            });
            $param.val(paramConfig.value).trigger('input');
        }
    },
    changeProtocol:function(protocolType){
        this.currentParamConfig = [].concat(MainStationParamsManager.COMMON_PARAM_CONFIGS,MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS[protocolType]);
    },
    renderProtocolParams:function(){
        $('#stationConfigParams').empty();
        this.currentParamConfig.map((config) => {
            this.renderParam(config)
        });
        $('.station-select').select2();
        initPageI18n();
    },
    open:function(type){
        var layerTitle = getI18nName('add');
        var btn = [getI18nName('confirm'),getI18nName('close')];
        if(type=='edit'){
            layerTitle = getI18nName('Edit');
        } else if (type == 'detail'){
            layerTitle = getI18nName('Details');
            btn = [getI18nName('close')];
        }
        var _that = this;
        layer.open({
            title: getI18nName(layerTitle),
            resize: false,
            type: 1,
            zIndex: 1035,
            area:(language == 'zh-CN'||language == 'zh-TW')?'35%':"55%",
            offset:'100px',
            content: '<div id="mainStationLayerContent" class="full-height"></div>',
            success: function () {
                $('#mainStationLayerContent').loadSync('./pages/mainStationConfig/mainstationConfigLayerTemp.html');
                _that.renderProtocolParams();
                _that.initValidater();
                if(type == 'detail'){
                    $('#stationConfigParams .form-control').attr('disabled','disabled');
                }else if(type == 'edit'){
                    $('#protocolTypeID').attr('disabled','disabled');
                    $('#businessProtocolTypeID').attr('disabled','disabled');
                }
            },
            btn:btn,
            yes:function(index){
                layer.close(index);
            },
            btn2:function(index){

            },
            end: function () {
                // $.cleanPdstChartsCache();
            }
        });
    },
    initValidater:function(){
        var $submitForm = $('#stationConfigParams')
        this.submitForm = $submitForm;
        $submitForm.bootstrapValidator(MAIN_STATION_VALIDATE);
    },
    validateSubmitForm : function () {
        var validater = this.submitForm.data('bootstrapValidator');
        validater.validate();
        return validater.isValid();
    },
    onProtocolChange:function(ele){
        var tempEnum = getEnumByCode(ele.target.value,COMMON_PROTOCOL);
        MainStationConfigLayerUtil.changeBprotocolArr(tempEnum.code);
        MainStationConfigLayerUtil.changeProtocol(tempEnum.name);
        MainStationConfigLayerUtil.renderProtocolParams();
    },
    changeBprotocolArr:function(typeCode){
        MainStationParamsManager.COMMON_PARAM_CONFIGS[0].value = typeCode;
        MainStationConfigLayerUtil.STATION_PROTOCOL.map((item) => {
            if(typeCode == item.protocolTypeID){
                MainStationParamsManager.COMMON_PARAM_CONFIGS[1].data=[];
                item.businessProtocolTypeIDs.map((BprotocoTypeID, index) => {
                    MainStationParamsManager.COMMON_PARAM_CONFIGS[1].data.push({
                        key:BprotocoTypeID,
                        name:getCodeName(BprotocoTypeID,BUSI_PROTOCOL),
                    });
                    if(index == 0){
                        MainStationParamsManager.COMMON_PARAM_CONFIGS[1].value = BprotocoTypeID; 
                    }
                });
            }
        });
    },
    currentParamConfig:[],
    STATION_PROTOCOL:[],
};

MainStationParamsManager = {
    COMMON_PARAM_CONFIGS:[{
        id:'protocolTypeID',
        type:STATION_PARAM_ENUM.SELECT,
        name:'CommunicatProtocol',
        data:[],
        onChange:MainStationConfigLayerUtil.onProtocolChange,
        value:'',
    },{
        id:'businessProtocolTypeID',
        type:STATION_PARAM_ENUM.SELECT,
        name:'BusiProtocol',
        data:[],
        value:'',
    },],
    STATION_PROTOCOL_PARAM_CONFIGS:{
        I2:[{
            id:'serverIP',
            type:STATION_PARAM_ENUM.INPUT,
            name:'serverIP',
            value:'',
        },{
            id:'port',
            type:STATION_PARAM_ENUM.INPUT,
            name:'serverPort',
            value:'',
        },{
            id:'URI',
            type:STATION_PARAM_ENUM.INPUT,
            name:'URI',
            value:'',
        },{
            id:'user',
            type:STATION_PARAM_ENUM.INPUT,
            name:'userName',
            value:'',
        },{
            id:'passwd',
            type:STATION_PARAM_ENUM.INPUT,
            name:'passWord',
            inputType:'password',
            value:'',
        },{
            id:'heartInterval',
            type:STATION_PARAM_ENUM.INPUT,
            name:'HeartbeatTransmissionInterval',
            inputType:'number',
            unit:'S',
            value:'',
        },{
            id:'dataInterval',
            type:STATION_PARAM_ENUM.INPUT,
            name:'DataUploadInterval',
            inputType:'number',
            unit:'S',
            value:'',
        },{
            id:'remark',
            type:STATION_PARAM_ENUM.TEXT_AREA,
            name:'Remark',
            length:200,
            value:'',
        }],
        IEC104:[{
            id:'serverIP',
            type:STATION_PARAM_ENUM.INPUT,
            name:'serverIP',
            value:'',
        },{
            id:'port',
            type:STATION_PARAM_ENUM.INPUT,
            name:'serverPort',
            value:'',
        },{
            id:'K',
            type:STATION_PARAM_ENUM.INPUT,
            name:'K',
            inputType:'number',
            value:'',
        },{
            id:'W',
            type:STATION_PARAM_ENUM.INPUT,
            name:'W',
            inputType:'number',
            value:'',
        },{
            id:'t0',
            type:STATION_PARAM_ENUM.INPUT,
            name:'timeoutT0',
            inputType:'number',
            value:'',
        },{
            id:'t1',
            type:STATION_PARAM_ENUM.INPUT,
            name:'timeoutT1',
            inputType:'number',
            value:'',
        },{
            id:'t2',
            type:STATION_PARAM_ENUM.INPUT,
            name:'timeoutT2',
            inputType:'number',
            value:'',
        },{
            id:'t3',
            type:STATION_PARAM_ENUM.INPUT,
            name:'timeoutT3',
            inputType:'number',
            value:'',
        },{
            id:'maxClientConnCount',
            type:STATION_PARAM_ENUM.INPUT,
            name:'MaxNumOfClientConnect',
            inputType:'number',
            value:'',
        },{
            id:'remark',
            type:STATION_PARAM_ENUM.TEXT_AREA,
            name:'Remark',
            length:200,
            value:'',
        }],
        IEC61850:[{
            id:'mms',
            type:STATION_PARAM_ENUM.INPUT,
            name:'MMSprofile',
            value:'',
        },{
            id:'startup',
            type:STATION_PARAM_ENUM.INPUT,
            name:'StartupProfile',
            value:'',
        },{
            id:'usermap',
            type:STATION_PARAM_ENUM.INPUT,
            name:'UsermapProfile',
            value:'',
        },{
            id:'cid',
            type:STATION_PARAM_ENUM.INPUT,
            name:'CIDfile',
            value:'',
        },{
            id:'remark',
            type:STATION_PARAM_ENUM.TEXT_AREA,
            name:'Remark',
            length:200,
            value:'',
        }],
        Modbus:[{
            id:'slaveAddress',
            type:STATION_PARAM_ENUM.INPUT,
            name:'WorkerAddress',
            value:'',
        },{
            id:'isEnableDbToDbm',
            type:STATION_PARAM_ENUM.SWITCH,
            name:'UnitConversion',
            unit:'[Db -> Dbm]',
            value:'',
        },{
            id:'remark',
            type:STATION_PARAM_ENUM.TEXT_AREA,
            name:'Remark',
            length:200,
            value:'',
        }],
    }
}
