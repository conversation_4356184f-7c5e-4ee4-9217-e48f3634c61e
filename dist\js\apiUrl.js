/*
 * @Author: liyaoxu <EMAIL>
 * @LastEditors: liyaoxu <EMAIL>
 * @FilePath: \docroot\dist\js\apiUrl.js
 */
'use strict';
var serverUrl, wsUri, websocket, clientGUID;


var GET_VERSION = "GetVersion"; //获取程序版本号
var GET_TEND_DATA = "GetTendData"; //获取趋势图数据
var POST_LOGIN = "Login"; //登录
var POST_SWITCH_USER = "SwitchUser"; //切换用户
var POST_CHANGE_USER_PAS = "ChangeUserPas"; //切换用户

var GET_Change_Language = "changeLanguage"; //获取前端配置
var GET_Sys_Info = "GetSysInfo"; //获取前端配置

var GET_ADU_CONFIG = "GetADUConfig"; //获取前端配置
var APPLY_ADU_CONFIG = "ApplyADUConfig"; //应用到同种类型前端（批量配置）
var GET_FORCE_CHANGE_ADUMODEL = "forceChangeAduModel"; //应用到同种类型前端（批量配置）
var GET_ALARM_CONFIG_INFO = "GetAlarmConfigInfo"; //获取报警信息(密度微水)
var GET_ALARM_CONFIG_INFO_PARAM = "GetAlarmConfigInfoParam"; //获取报警设置参数(密度微水)-界面初始化使用
var SAVE_ALARM_CONFIG = "SaveAlarmConfig"; //保存报警配置
var REMOVE_ALARM_CONFIG = "RemoveAlarmConfig"; //删除报警配置
var GET_ALARM_CONFIG_LIST = "GetAlarmConfigList"; //获取报警配置表
var GET_HISTORY_DATAS = "GetHistoryDatas"; //获取密度微水历史数据列表
var GET_HISTORY_TREE_LIST = "GetHistoryTreeList"; //获取密度微水历史数据树

var WAKE_UP_ADU = "WakeUpADU"; //唤醒前端

var GET_CHART_DATA = "GetChartData"; //获取趋势图数据
var GET_CHART_PARAM_DATA = "GetChartParamData"; //获取图谱对应参数（振动传感器）
var GET_ARRESTER_CHART_PARAMDATA = "GetArresterChartParamData"; //获取图谱对应参数（避雷器传感器）

var GET_ADU_STATE_INFOR_LIST = "GetADUStateInfo"; //获取智能传感器状态列表
var GET_ADU_STATE_COLOR_THRED = "GetADUStateColorThred"; //获取智能传感器状态颜色阈值

var GET_CLEAR_ALL_DATA = "ClearAllData"; //清空数据
var GET_FACTORY_SETTINGS = "FactorySettings"; //恢复出厂设置

/**
 * 主站配置模块
 */
var GET_MAINSTATION_PARAMETER = 'getMainStationParameter'; //读取主站参数
var SAVE_MAINSTATION_PARAMETER = 'saveMainStationParameter'; //保存主站参数
var GET_DEFAULT_STATION_PARAMETER = 'getDefaultStationParameter'; //保存主站参数

var GET_OUTWARD_SWITCH = 'getOutwardSwitch'//读取接出开关配置信息
var SAVE_OUTWARD_SWITCH = 'saveOutwardSwitch'//读取接出开关配置信息

var GET_SUPPORTED_BProtocols = 'getSupportedBusinessProtocols'//获取通信协议支持的业务协议
var GET_OUTWARD_PROTOCOL_CONFIG ='getOutwardProtocolConfig'//获取通信协议配置
var SAVE_OUTWARD_PROTOCOL_CONFIG ='saveOutwardProtocolConfig'//保存通信协议配置
var ENABLE_OUTWARD_PROTOCOL='enableOutwardProtocolConfig'//启用指定协议配置
var DISABLE_OUTWARD_PROTOCOL='disableOutwardProtocolConfig'//停用指定协议配置

/* var MAINSTATION_COMM_CONFIG = 'getMainStationCommCofigList'//获取主站通讯配置列表
var GET_STATION_PROTOCOL_LIST='getMainStationProtocolList'//获取主站协议列表
var GET_STATION_PROTOCOL_DETAIL='getOutwardProtocolConfigDetails'//获取主站协议详情
var ENABLE_OUTWARD_PROTOCOL='enableOutwardProtocolsConfig'//启用指定协议配置
var DISABLE_OUTWARD_PROTOCOL='disableOutwardProtocolsConfig'//停用指定协议配置
var DELETE_OUTWARD_PROTOCOL='deleteOutwardProtocolsConfig'//停用指定协议配置 */

/**
 * 用户管理模块接口
 * @type {string}
 */
var GET_NORMAL_USER_LIST = 'getNormalUserList'; //用户管理-获取用户列表
var ADD_NORMAL_USER = 'addNormalUser'; //用户管理-新增用户
var DELETE_NORMAL_USER = 'deleteNormalUser'; //用户管理-删除用户
var GET_NORMAL_USER_PERLIST = 'getNormalUserPerList'; //用户管理-获取普通用户权限列表
var GET_NORMAL_USER_LOGINLIST = 'getNormalUserLoginList'; //用户管理-获取普通用户信息
var SAVE_NORMAL_USER_PERMISSON = 'saveNormalUserPermisson'; //用户管理-保存普通用户权限

/**
 * 送检验证模块接口
 * @type {string}
 */
var GET_SAFE_ALARM = "GetSafeAlarm"; //获取报警数据列表
var SAFE_ALARM_CONFIRM = "SafeAlarmConfirm"; //获取报警数据列表
var LOG_MANAGER_LIST = "GetAuditData"; //获取审计数据列表

/**
 * 告警数据管理
 */
var GET_ADU_ALARM_INFO = "GetAduAlarmInfo";//获取告警数据列表


/**
 * WebSocket动作接口(非系统级监听动作)
 */
var UPDATE_MAINSTATION_STATE = "updateMainStationState"; //通知主站参数刷新

/**
 * 快速同步模块接口
 */
//获取传感器设备树
var FASTSYNCDATA_GETSTATIONDEVICETREE = "fastSyncData/getStationDeviceTree"
//获取同步任务状态
var FASTSYNCDATA_GETSTATUS = "fastSyncData/getStatus"
//快速同步数据
var FASTSYNCDATA_START = "fastSyncData/start"
//取消快速同步数据
var FASTSYNCDATA_CANCEL = "fastSyncData/cancel"