<style>
  .tevParam {
    height: 32px;
  }

  .tevParam label {
    text-align: right;
  }
</style>
<div class="col-sm-12 full-height" id="maingraph" style="padding: 0px">
  <div
    class="form-group"
    style="
      height: 55px;
      border-left: 1px solid #b9b7b7;
      border-right: 1px solid #b9b7b7;
      background-color: white;
      margin-bottom: 0px;
    "
  >
    <div class="col-sm-5" style="margin-top: 10px">
      <label
        for="dateStart"
        class="col-sm-4 control-label"
        style="margin-top: 7px"
        data-i18n="startDate"
        >起始日期</label
      >
      <div class="col-sm-8">
        <div class="bootstrap-datepicker">
          <div class="input-group">
            <div class="input-group-addon">
              <i class="fa fa-calendar"></i>
            </div>
            <input
              id="dateStart"
              type="text"
              class="form-control pull-right"
              readonly="true"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-5" style="margin-top: 10px">
      <label
        for="dateEnd"
        class="col-sm-4 control-label"
        style="margin-top: 7px"
        data-i18n="endDate"
        >结束日期</label
      >
      <div class="col-sm-8">
        <div class="bootstrap-datepicker">
          <div class="input-group">
            <div class="input-group-addon">
              <i class="fa fa-calendar"></i>
            </div>
            <input
              id="dateEnd"
              type="text"
              class="form-control pull-right"
              readonly="true"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="col-sm-2">
      <button
        type="button"
        id="refreshBtn"
        class="btn btn-block btn-primary"
        style="width: 80px; margin: 10px 0px 10px auto"
        data-i18n="refresh"
      >
        刷新
      </button>
    </div>
  </div>
  <div class="row">
    <div
    class="form-group col-lg-9 col-md-9 col-sm-9"
      id="graph"
      style="
        height: 90%;
        border: 1px solid #b9b7b7;
        background-color: white;
        margin-bottom: 0px;
      "
    >
      <div id="tevchart" style="width: 100%; height: 100%"></div>
    </div>
    <div
    class="form-group col-lg-3 col-md-3 col-sm-3"
      id="params"
      style="
        height: 100%;
        border: 1px solid #b9b7b7;
        background-color: white;
        margin-bottom: 0px;
        padding-left: 0px;
        min-width: 350px;
      "
    >
      <div style="height: 10%"></div>
      <div class="form-group tevParam">
        <label class="col-sm-5 control-label" data-i18n="pointName"
          >测点名称:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="pointName"></label>
        </div>
      </div>
      <div class="form-group tevParam">
        <label class="col-sm-5 control-label" data-i18n="sensorCode"
          >传感器编码:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="sensorCode"></label>
        </div>
      </div>
      <div class="form-group tevParam">
        <label class="col-sm-5 control-label" data-i18n="sensorType"
          >传感器类型:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="sensorType"></label>
        </div>
      </div>
      <div class="form-group tevParam">
        <label class="col-sm-5 control-label" data-i18n="amplitude"
          >幅值:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="amp"></label>
        </div>
      </div>
      <div class="form-group tevParam">
        <label class="col-sm-5 control-label" data-i18n="acquisitionTime"
          >采集时间:</label
        >
        <div class="col-sm-7">
          <label class="control-label" id="simpleTime"></label>
        </div>
      </div>
      <!--      <label class="col-sm-6  control-label" data-i18n="sensorType"
                style="text-align: right;padding: 0px">传感器类型:</label>
            <label class="col-sm-6  control-label" id="sensorType">TEV</label>

            <label class="col-sm-6  control-label" data-i18n="amplitude" style="text-align: right;padding: 0px">幅值:
            </label>
            <label class="col-sm-6  control-label" id="amp"></label>
            <label class="col-sm-6  control-label" data-i18n="acquisitionTime"
                style="text-align: right;padding: 0px">采集时间:</label>
            <label class="col-sm-6  control-label" id="simpleTime"></label> -->
    </div>
    <!--<div class="form-group col-lg-6" id="graphPulse"-->
    <!--style="height: 80%;width:50%;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">-->
    <!--<div id="tevPulsechart" style="width:100%;height:100%"></div>-->
    <!--</div>-->
  </div>
</div>
<!--</div>-->

<script type="text/javascript">
  initPageI18n();
  var dataUnit = '';
  $(function () {
    // 基于准备好的dom，初始化echarts图表
    //@ sourceURL=tev_chart.js

    //Daterangepicker
    $('#dateStart').daterangepicker({
      opens: 'right',
      autoApply: true,
      timePicker: true,
      singleDatePicker: true,
      timePicker24Hour: true,
      locale: {
        format: 'YYYY/MM/DD HH:mm:ss',
      },
      startDate: new Date(new Date().toDateString()).pattern(
        'yyyy/MM/dd HH:mm:ss'
      ),
    });

    //Daterangepicker
    $('#dateEnd').daterangepicker({
      opens: 'right',
      autoApply: true,
      timePicker: true,
      singleDatePicker: true,
      timePicker24Hour: true,
      locale: {
        format: 'YYYY/MM/DD HH:mm:ss',
      },
      startDate: new Date().pattern('yyyy/MM/dd HH:mm:ss'),
    });

    $('#dateStart').click(function () {
      $('.minuteselect').attr('disabled', 'disabled');
      $('.minuteselect').css('background', 'lightgray');
    });

    $('#dateEnd').click(function () {
      $('.minuteselect').attr('disabled', 'disabled');
      $('.minuteselect').css('background', 'lightgray');
    });
    window.addEventListener('resize', function () {
      // if (myChart)
      // myChart.resize();
    });

    $(window).resize(function () {
      if (!document.getElementById('tevchart')) {
        //js判断元素是否存在
        return;
      }
      var height = $('#maingraph').height() - 50;
      $('#graph').height(height);
      $('#params').height(height);
      var paramsWidth = $('#params')[0].offsetWidth;
      if(paramsWidth == 350){
        $('#graph').width($('#params').parent().width() - 350 - 32)
      }
    });
    $(window).resize();

    // var myChart = echarts.init(document.getElementById('tevchart'));
    //        var myPulseChart = echarts.init(document.getElementById('tevPulsechart'));

    var dataXValue = [' '],
      dataYValue = [0];

    var option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          // 坐标轴指示器，坐标轴触发有效
          type: 'line', // 默认为直线，可选为：'line' | 'shadow'
        },
        confine: true,
        formatter: function (params) {
          let htmlStr = `<div style="padding:2px;display:block;text-align:start;">`;
          let color = params[0].color; //图例颜色
          htmlStr += `<span style="text-align: start;line-height:13px;font-size:12px">${params[0].axisValue}</span><br/>`;
          //为了保证和原来的效果一样，这里自己实现了一个点的效果
          htmlStr += `<div style="display: flex;align-items: center;"><span style="display:inline-block;width:10px;height:10px;border-radius:5px;margin-right:5px;background-color:${color};"></span>`;
          let dataValue = params[0].data;
          htmlStr += `<span style="text-align: start;line-height:13px;font-size:12px">${dataValue.value}${dataUnit}</span><br/>`;
          htmlStr += '</div>';
          return htmlStr;
        },
      },
      title: {
        text: getI18nName('TEVamp'),
        x: 'center',
        y: 'top',
        textAlign: 'left',
      },
      toolbox: {
        show: true,
        right: '5%',
        feature: {
          mark: {
            show: true,
          },
          magicType: {
            show: true,
            type: ['bar', 'line'],
            title: {
              line: getI18nName('SwitchToLine'),
              bar: getI18nName('SwitchToBar'),
            },
          },
        },
      },
      calculable: true,
      xAxis: [
        {
          type: 'category',
          data: dataXValue,
          axisTick: {
            alignWithLabel: true,
          },
          axisLabel: {
            showMinLabel: true,
            showMaxLabel: true,
            // formatter: function (value) {
            //   if (value != null) {
            //     return `${value.replace(' ', '\n')}`
            //   }
            // }
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: getI18nName('TEVYunit'),
        },
      ],
      series: [
        {
          name: getI18nName('TEVamp'),
          type: 'bar',
          data: dataYValue,
          barMinHeight: 5,
          itemStyle: {
            normal: {
              color: '#3c8dbc',
            },
          },
          markPoint: {
            symbolSize:65,
            data: [
              {
                type: 'max',
                name: getI18nName('maxValue'),
              },
              {
                type: 'min',
                name: getI18nName('min'),
              },
            ],
          },
          //                    markLine: {
          //                        data: [
          //                            {type: 'average', name: '平均值'}
          //                        ]
          //                    }
        },
      ],
    };

    // myChart.setOption(option);
    //        var pulseOption=option;
    //        pulseOption.title.text='TEV脉冲';
    //        pulseOption.series[0].name='TEV脉冲';
    pdcharts.draw(document.getElementById('tevchart'), {
      width: $('#tevchart').width(),
      height: $('#tevchart').height(),
      type: pdcharts.chartType.trend,
      cover: true,
      data: {
        axisInfo: {
          dataType: 'TEV',
          chartType: pdcharts.chartType.tevamplitude,
          unit: 'dB',
        },
      },
      orgOption: option,
    });
    $('#refreshBtn').click(function () {
      // loadTev(showChartPointId, showChartDataId, $("#dateStart").val(), $("#dateEnd").val());
      //            loadTevPulse(showChartPointId, showChartDataId, $("#dateStart").val(), $("#dateEnd").val());
      var dateStartValue = $('#dateStart').val();
      var dateEndValue = $('#dateEnd').val();
      if (moment(dateStartValue).isAfter(moment(dateEndValue))) {
        layer.msg(
          '<div style="text-align:center">' +
            getI18nName('dateStartIsAfterDateEnd') +
            '</div>'
        );
      } else {
        loadTev(
          showChartPointId,
          showChartDataId,
          $('#dateStart').val(),
          $('#dateEnd').val()
        );
      }
    });

    loadTev(showChartPointId, showChartDataId, '', '');
    //        loadTevPulse(showChartPointId, showChartDataId, "", "");

    function loadTev(pointId, dataId, startDate, endDate) {
      var pointType = 'TEV';
      //            var dataGet = "pointId=" + pointId + "&dataId=" + dataId + "&pointType=" + pointType +
      //                    "&startDate=" + startDate + "&endDate=" + endDate;
      var dataGet = {
        pointId: pointId,
        dataId: dataId,
        pointType: pointType,
        startDate: startDate,
        endDate: endDate,
      };
      poll('GET', 'GetTendData', dataGet, function (text) {
        var respData = text.result;

        if (!respData) return;
        dataUnit = respData.unit;
        if (option) {
          option.xAxis[0].data = respData.dataChart.xValue;
          option.series[0].data = respData.dataChart.yValue;
          var checkObj = pdcharts.ChartConfigUtil.getValueByConfig({
            dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEMP,
            chartType: pdcharts.chartType.temperature,
            value: 1.111111,
            unit: dataUnit,
          });
          option.series[0].markPoint.label = {
            formatter(params) {
              // 自定义精度，保留两位小数
              if (params.data.type === 'max' || params.data.type === 'min') {
                let tempData = pdcharts.ChartConfigUtil.getValueByConfig({
                  dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEV,
                  chartType: pdcharts.chartType.tevamplitude,
                  value: params.data.value,
                  unit: checkObj.unit,
                });
                return tempData.value;
              }
              return params.data.value;
            },
          };
          pdcharts.draw(document.getElementById('tevchart'), {
            width: $('#tevchart').width(),
            height: $('#tevchart').height(),
            type: pdcharts.chartType.trend,
            cover: true,
            data: {
              axisInfo: {
                dataType: 'TEV',
                unit: dataUnit,
                chartType: pdcharts.chartType.tevamplitude,
              },
            },
            orgOption: option,
          });
        }
        $('#pointName').html(respData.pointName);
        $('#sensorCode').html(respData.aduId);
        var sensorType = getI18nName(respData.sensorType);
        $('#sensorType').html(sensorType);
        $('#simpleTime').html(
          respData.sample_time ? respData.sample_time : respData.endDate
        );
        var ampText = respData.AMP + respData.unit;
        var resultObj = pdcharts.ChartConfigUtil.getValueByConfig({
          dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEV,
          chartType: pdcharts.chartType.tevamplitude,
          value: respData.AMP,
          unit: respData.unit,
        });
        ampText = resultObj.value + resultObj.unit;
        if (respData.AMP == undefined) {
          ampText = '0' + respData.unit;
        } else if (respData.AMP < 0) {
          ampText = '<0' + respData.unit;
        }
        $('#amp').html(ampText);
        if (startDate !== '' && endDate !== '') {
          return;
        }
        var tempStartD = new Date(respData.startDate.split(' ')[0]);
        var tempEndD = new Date(respData.endDate.split(' ')[0]);
        tempEndD.setDate(tempEndD.getDate() + 1);
        $('#dateStart').daterangepicker({
          opens: 'right',
          autoApply: true,
          timePicker: true,
          singleDatePicker: true,
          timePicker24Hour: true,
          locale: {
            format: 'YYYY/MM/DD HH:mm:ss',
          },
          startDate: tempStartD.pattern('yyyy/MM/dd HH:mm:ss'),
        });

        //Daterangepicker
        $('#dateEnd').daterangepicker({
          opens: 'right',
          autoApply: true,
          timePicker: true,
          singleDatePicker: true,
          timePicker24Hour: true,
          locale: {
            format: 'YYYY/MM/DD HH:mm:ss',
          },
          startDate: tempEndD.pattern('yyyy/MM/dd HH:mm:ss'),
        });
        $('.minuteselect').attr('disabled', 'disabled');
        $('.minuteselect').css('background', 'lightgray');
      });
    }

    function loadTevPulse(pointId, dataId, startDate, endDate) {
      var pointType = 'TEV';
      var dataGet =
        'pointId=' +
        pointId +
        '&dataId=' +
        dataId +
        '&pointType=' +
        pointType +
        '&startDate=' +
        startDate +
        '&endDate=' +
        endDate;
      poll('GET', 'GetTendData', dataGet, function (text) {
        var respData = text.result;

        if (!respData) return;

        if (pulseOption) {
          pulseOption.xAxis[0].data = respData.dataChart.xValue;
          pulseOption.series[0].data = respData.dataChart.chartInit;
          myPulseChart.setOption(option);
        }
        var sensorType = getI18nName(respData.sensorType);
        $('#pointName').html(respData.pointName);
        $('#sensorCode').html(respData.aduId);
        $('#sensorType').html(sensorType);
        $('#simpleTime').html(respData.sample_time);
        // 根据配置类型和值获取对应的显示值
        var resultObj = pdcharts.ChartConfigUtil.getValueByConfig({
          dataType: pdcharts.ChartConfigUtil.CONFIG_TYPE.TEV,
          chartType: pdcharts.chartType.tevamplitude,
          value: respData.AMP,
          unit: respData.unit,
        });
        var ampText = resultObj.value + respData.unit;
        if (respData.AMP == undefined) {
          ampText = '0' + respData.unit;
        } else if (respData.AMP < 0) {
          ampText = '<0' + respData.unit;
        }
        $('#amp').html(ampText);

        if (respData.startDate) {
          $('#dateStart').daterangepicker({
            opens: 'right',
            autoApply: true,
            timePicker: false,
            singleDatePicker: true,
            format: 'yyyy/MM/dd',
            startDate: new Date(respData.startDate.split(' ')[0]),
          });
        }
        if (respData.endDate) {
          $('#dateEnd').daterangepicker({
            opens: 'right',
            autoApply: true,
            timePicker: false,
            singleDatePicker: true,
            format: 'yyyy/MM/dd',
            startDate: new Date(respData.endDate.split(' ')[0]),
          });
        }
      });
    }
  });
</script>
