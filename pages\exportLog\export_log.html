<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin">
        <!--<div class="box-header with-border">-->
        <!--<h3 class="box-title">Sync Data</h3>-->
        <!--</div>-->
        <!-- /.box-header -->
        <div class="box-body">
            <label for="da-dateRange" data-i18n="dateRange">日期范围</label>
            <div class="input-group date">
                <div class="input-group-addon">
                    <i class="fa fa-calendar"></i>
                </div>
                <input id="da-dateRange" type="text" class="form-control pull-right" readonly="true">
            </div>
            <!-- /.da-dateRange -->
            <br>
            <label for="da-aduType" data-i18n="sensorType">传感器类型</label>
            <select id="da-aduType" class="form-control select2" data-i18n-placeholder="selectPlease"
                style="width: 100%;"></select>
            <!-- /.da-aduType -->
            <label for="da-aduItems" data-i18n="sensorList">传感器列表</label>
            <div id="da-aduItems">
                <div class="col-xs-5 no-padding full-height">
                    <select name="from[]" id="da-adus" class="form-control" style="height: 200px;" multiple="multiple">
                    </select>
                </div>
                <div class="col-xs-2">
                    <button type="button" id="da-adus_rightAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-forward"></i></button>
                    <br>
                    <button type="button" id="da-adus_rightSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-right"></i></button>
                    <br>
                    <button type="button" id="da-adus_leftSelected" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-chevron-left"></i></button>
                    <br>
                    <button type="button" id="da-adus_leftAll" class="btn btn-default btn-block"><i
                            class="glyphicon glyphicon-backward"></i></button>
                </div>
                <div class="col-xs-5 no-padding full-height">
                    <select name="to[]" id="da-adus_to" class="form-control" style="height: 200px;"
                        multiple="multiple"></select>
                </div>
            </div>
            <!-- /.da-aduItems -->
            <br>
            <!--  <div id="da-progress" class="progress-group hide">
                <span class="progress-text" data-i18n="exportingData">正在导出...</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div> -->
            <div id="exportStep" class="box eis-horizontal-steps"
                style="border-top: none;padding: 12px;height: 180px;position: relative;width: calc(100% - 40px);">
                <div class="block" style="width: 100%;
                height: 100%;
                z-index: 3;
                position: absolute;"></div>
                <div class="eis-stepContents">
                    <div class="eis-stepContent">
                        <h2 data-i18n="LogExportStep1">选择传感器</h2>
                    </div>
                    <div class="eis-stepContent" style="display: none;">
                        <h2 data-i18n="LogExportStep2">获取日志文件...</h2>
                        <img src="./dist/img/loading1.gif" style="margin-left: 8px;"></img>
                    </div>
                    <div class="eis-stepContent" style="display: none;">
                        <h2 data-i18n="LogExportStep3">获取日志文件完成</h2>
                    </div>
                    <div class="eis-stepContent" style="display: none;">
                        <h2 id="orgFileSize" style="font-size: 30px;"></h2>
                        <h2 data-i18n="LogExportStep4">打包压缩日志文件...</h2>
                        <img src="./dist/img/loading1.gif" style="margin-left: 8px;"></img>
                    </div>
                    <div class="eis-stepContent" style="display: none;">
                        <h2 data-i18n="LogExportStep5">压缩完成</h2>
                    </div>
                </div>
            </div>
            <!-- /.progress-group -->
        </div>
        <!-- /.box-body -->
        <div class="content-footer" style="height: 80px;padding-right: 40px;">
            <button id="ExportLog" type="button" id="da-ok" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 10px 10px auto" data-i18n="export">导出
            </button>
            <button id="cancelExportLog" class="btn btn-block btn-default hide"
                style="width: 200px; margin: 10px 10px 10px auto"
                onclick="cancelExportLog()" data-i18n="cancel">取消</button>
            <a class="hide">
                <li id="downloadExportData">download</li>
            </a>
        </div>
        <br />
    </div>
    <!-- /.box -->

    <!-- /.content-footer -->
</div>
<!--/.col (left) -->
<script src="./pages/exportLog/export_log.js"></script>

<script>
    initPageI18n();

    /**
     * 检查状态
     */
    function chectState() {
        var flag = $.session.get('i-progress-sync');
        if (flag === "true") {
            $('#da-ok').addClass('hide');
            $('#da-cancel').removeClass('hide');
            $('#i_progressText').text(getI18nName('syncingData'));
        } else {
            $('#da-cancel').addClass('hide');
            $('#da-ok').removeClass('hide');
        }
    }
    chectState();
    //@ sourceURL=sync_data.js
    //前端类型下拉框
    var aduType = $('#da-aduType');
    aduType.select2(getSyncDataADUType(aduType));
    aduType.select2({
        minimumResultsForSearch: Infinity
    });
    aduType.change(function () {
        $("#da-adus").empty();
        $("#da-adus_to").empty();
        var selectText = $(this).children('option:selected').val();
        getADUList(selectText);
    });

    $('#da-dateRange').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "startDate": moment().subtract(29, 'days'),
        "endDate": moment(),
    });
    //前端版本列表穿梭框
    $('#da-adus').multiselect();
    $("#da-aduItems").height($('#da-aduItems .col-xs-2').height());


    $('#ExportLog').click(function () {
        var shuttleTo = $('#da-adus_to').find('option');
        if (shuttleTo.length > 0) {
            var dateArray = $('#da-dateRange').val().split('-');
            var aduList = new Array();
            for (var i = 0; i < shuttleTo.length; i++) {
                aduList.push(shuttleTo[i].value);
            }
            var formData = {
                beginDate: dateArray[0].trim(),
                endDate: dateArray[1].trim(),
                aduType: aduType.children('option:selected').val(),
                aduList: aduList.toString()
            };
            requestExportData(formData, true);
        } else {
            $('.notifications').notify({
                message: {
                    text: getI18nName('selectDevTip')
                },
                type: "warning",
            }).show();
        }
    })

    /*   $('#da-cancel').click(function () {
          $.fn.alertMsg(
              'warning',
              getI18nName('stopSyncDataTip'), [{
                  id: 'no',
                  text: getI18nName('no')
              }, {
                  id: 'yes',
                  text: getI18nName('yes'),
                  callback: function () {
                      //禁用取消按钮
                      $('#da-cancel').prop('disabled', true);
                      $('#da-cancel img').removeClass('hide');
                      //关闭websocket请求
                      var message = {
                          "methodName": "PushSyncAduDataProgress",
                          "enablePush": false,
                          "parameters": ""
                      }
                      webSocketSend(message);
                  }
              }]
          );
      }) */
    $(function () {
        initExportStepBox();
    })
</script>