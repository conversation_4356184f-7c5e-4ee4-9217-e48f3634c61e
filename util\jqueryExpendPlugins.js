(function ($) {
    var JqueryPlugins = {
        initAll: function () {
            for (var plugin in this) {
                if (plugin == 'initAll') {
                    continue;
                }
                this[plugin]();
            }
        },
        addGetIframeElByIdFunc: function () {
            $.fn.getIframeElById = function (elId) {
                return this.contents().find(elId);
            }
        },
        addJqueryLoadSyncFunc: function () {
            $.fn.loadSync = function (url, callback) {
                var $this = this;
                $.ajax({
                    url: url,
                    async: false,
                    success: function (res) {
                        $this.html(res)
                        if (initPageI18n) {
                            initPageI18n();
                        }
                        if (callback) {
                            callback();
                        }
                    }
                })
            }
        },
    };
    JqueryPlugins.initAll();
}(jQuery));