/*
 * 获取电压等级
 * */
function getVolLevel(type) {
    var data = "type=1";

    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        if (type === "GetDevVolLevel") {
            $("#dev_level").empty();
            respData.forEach(function (value, index, array) {
                var option = $("<option />", { "value": "" + value + "" }).append(value);
                $("#dev_level").append(option);
            });
            return;
        }
        $("#station_level").empty();
        respData.forEach(function (value, index, array) {
            var option = $("<option />", { "value": "" + value + "" }).append(value);
            $("#station_level").append(option);
        }, false);
        getStation();
    });
}

/*
 * 获取站点信息
 * -----------------------
 */
var station = "";
function getStation() {
    var data = null;
    poll('GET', 'GetStation', data, function (text) {
        var respData = text.result;
        station = respData.stationName;
        $("#station_input").val(respData.stationName);
        $("#station_pms").val(respData.stationPMS);
        $("#power_comp").val(respData.powerComp);
        if (respData.stationName == null || respData.stationName.length == 0) {
            station = "";
            return;
        }
        // $("#station_level option[value='" + respData.stationVolLevel + "']").attr("selected", true);
        $("#station_level").val(respData.stationVolLevel).select2();
        //初次进入页面时可能未完成电压等级加载，延迟50ms
        // setTimeout(function(){
        //     $("#station_level").val(respData.stationVolLevel).select2();
        // },50);
    });
}

/*
 * 保存站点信息
 * -----------------------
 */
function saveStationInfo(formData, control) {
    poll('POST', 'SaveStationInfo', formData, function (text) {
        station = formData.stationName;
        $('.notifications').notify({
            message: {
                text: getI18nName('saveSuccess')
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('saveFailed') + convertErrorCode(errorCode)
            },
            type: "danger",
        }).show();
    });
}

function delStation() {
    var data = null;
    poll('POST', 'DeleteStation', data, function (text) {
        station = "";
        $('.notifications').notify({
            message: {
                text: getI18nName('stationDelSuccess')
            },
            type: "success",
        }).show();
        getStation();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('stationDelFailed') + convertErrorCode(errorCode)
            },
            type: "danger",
        }).show();
    });
}

/*
 获取一次设备名称列表
 * */
function getDeviceNameList() {
    $("#station_name_dev").html(station);

    var data = null;
    poll('GET', 'GetDeviceNameList', data, function (text) {
        var respData = text.result;

        var setting = {
            data: {
                simpleData: {
                    idKey: "id",
                    pIdKey: "pId",
                    name: "name",
                    enable: true
                }
            },
            // async: {
            //     enable: true,
            //     url: "GetChannelList",
            //     type: "GET",
            //     dataFilter: ajaxDataFilterShowDevNode,
            //     autoParam: ["id=aduId"]
            // },
            callback: {
                onClick: showDevNode
            }
        };
        var zNodes = [{
            id: 0,
            pId: 0,
            name: station,
            open: true,
            iconOpen: "./plugins/zTree/css/zTreeStyle/img/diy/1_open.png",
            iconClose: "./plugins/zTree/css/zTreeStyle/img/diy/1_close.png",
            isParent: true
        }];

        if (respData.data.length <= 0) {
            $.fn.zTree.init($("#station_tree"), setting, zNodes);
            return;
        }
        for (var i = 0; i < respData.data.length; i++) {
            var tempdata = {
                id: respData.data[i].deviceId,
                pId: 0,
                name: respData.data[i].deviceName,
                icon: "./plugins/zTree/css/zTreeStyle/img/diy/3.png",
                isParent: false
            };
            zNodes.push(tempdata);
        }

        $.fn.zTree.init($("#station_tree"), setting, zNodes);
        devNodeCheck();
    });
}

/*
 * 获取设备类型
 *
 */
function getDevType() {
    var data = "type=2";
    poll('GET', 'GetCommonType', data, function (text) {
        var respData = text.result;
        $("#dev_type").empty();
        respData.forEach(function (value, index, array) {
            var typeName = getI18nName(value);
            var option = $("<option />", { "value": "" + value + "" }).append(typeName);
            $("#dev_type").append(option);
        });
    });
}

function ajaxDataFilterShowDevNode(treeId, parentNode, responseData) {
    if (responseData) {
        for (var i = 0; i < responseData.result.length; i++) {
            responseData.result[i].name = responseData.result[i].channelName;
        }
    }
    return responseData.result;
}

var devCode = null;
function showDevNode(event, treeId, treeNode, clickFlag) {
    var deviceId = treeNode.id;
    var data = "deviceId=" + deviceId;
    devCode = deviceId;
    tempDevNode = treeNode;
    // getDevType();
    // getVolLevel("GetDevVolLevel");
    poll('GET', 'GetDeviceInfo', data, function (text) {
        if (isSaving) {
            isSaving = false;
            return;
        }
        var respData = text.result;
        if (respData === null || respData.length <= 0)
            return;
        $("#dev_name").val(respData.deviceName);
        $("#dev_pms").val(respData.devicePMS);
        $("#dev_type").val(respData.deviceTypeName).select2();
        $("#dev_level").val(respData.deviceVolLevel).select2();
    }, function () {
        isSaving = false;
    });
}

var isSaving = false;
function saveDevInfo(formData, type) {
    poll('POST', 'SaveDeviceInfo', formData, function (text) {
        isSaving = true;
        var text = getI18nName('operationSuccess');
        if (type === "add") {
            text = getI18nName('deviceAddSuccessTips');
        }
        else {
            text = getI18nName('deviceEditSuccessTips');
        }
        getDeviceNameList();
        devCode = null;
        $('.notifications').notify({
            message: {
                text: text
            },
            type: "success"
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                // text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))
                text: stringFormat.format(getI18nName('operatFailed'), getI18nName('DeviceCodeExists'))
            },
            type: "danger"
        }).show();
    });
}

function delDev(delDevCode) {
    var data = {};
    data.deviceId = delDevCode;
    poll('POST', 'DeleteDeviceInfo', data, function (text) {
        $('.notifications').notify({
            message: {
                text: getI18nName('deviceDelSuccessTips')
            },
            type: "success"
        }).show();
        getDeviceNameList();
        devCode = null;
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('deleteFailedTips') + convertErrorCode(errorCode)
            },
            type: "danger"
        }).show();
    });
}
function ajaxDataFilterGetPointList(treeId, parentNode, responseData) {
    if (responseData) {
        var treeObj = $.fn.zTree.getZTreeObj(treeId);//获取ztree对象
        if (responseData.result.length == 0) {
            parentNode.isParent = false;
            cacheTempDeviceNodeIsParent = false;
            parentNode.children = undefined;
        } else {
            parentNode.isParent = true;
            cacheTempDeviceNodeIsParent = true;
        }
        treeObj.updateNode(parentNode);
        for (var i = 0; i < responseData.result.length; i++) {
            responseData.result[i].name = responseData.result[i].pointName;
            responseData.result[i].isParent = false;
        }
    }
    return responseData.result;
}

function getPointList(treeId, treeNode, clickFlag) {
    var deviceId = clickFlag.id;
    var data = "deviceId=" + deviceId;
    devCode = deviceId;
    poll('GET', 'GetPointNameList', data, function (text) {
        var respData = text.result;
        if (respData === null || respData.length <= 0)
            return;

        var zNodes = [];
        for (var i = 0; i < respData.length; i++) {
            var tempdata = {
                id: respData[i].pointId,
                pId: deviceId,
                name: respData[i].pointName
            };
            zNodes.push(tempdata);
        }

        var treeObj = $.fn.zTree.getZTreeObj("station_tree_point");//获取ztree对象
        var parentZNode = treeObj.getNodeByParam("id", deviceId, null); //获取父节点
        treeObj.addNodes(parentZNode, zNodes, true);

        // $.fn.zTree.getZTreeObj("station_tree_point").addNodes(treeNode, zNodes);
    });
    devCode = null;

}

var test_point_id_set = null;
function showPointNode(event, treeId, treeNode, clickFlag) {
    if (treeNode.id === 0) {
        return;
    }
    if (treeNode.pId === 0) {
        $("#dev_name_point").html(treeNode.name);
        devCode = treeNode.id;
        tempPointDevNode = treeNode;
        var isParent = treeNode.isParent;
        //若是父节点，则展开节点
        if (isParent) {
            var treeObj = $.fn.zTree.getZTreeObj("station_tree_point");
            treeObj.expandNode(treeNode, true, false, true);
        }
        return;
    }
    else {
        tempPointDevNode = treeNode.getParentNode();
        $("#dev_name_point").html(treeNode.getParentNode().name);
        $("#test_point_name").val(treeNode.name);
        test_point_id_set = treeNode.id;
        devCode = treeNode.pId;
    }
}

function getDevicePointNameList() {
    $("#station_name_point").html(station);

    var data = null;
    poll('GET', 'GetDeviceNameList', data, function (text) {
        var respData = text.result;
        // if (respData.data.length <= 0)
        //     return;

        var setting = {
            data: {
                simpleData: {
                    idKey: "id",
                    pIdKey: "pId",
                    name: "name",
                    enable: true
                }
            },
            async: {
                enable: true,
                url: serverUrl + "GetPointNameList",
                type: "GET",
                dataFilter: ajaxDataFilterGetPointList,
                autoParam: ["id=deviceId"]
            },
            callback: {
                // onExpand: getPointList,
                // beforeExpand: beforeExpandP,
                onClick: showPointNode
            }
        };
        var zNodes = [{
            id: 0,
            pId: 0,
            name: station,
            open: true,
            iconOpen: "./plugins/zTree/css/zTreeStyle/img/diy/1_open.png",
            iconClose: "./plugins/zTree/css/zTreeStyle/img/diy/1_close.png"
        }];

        for (var i = 0; i < respData.data.length; i++) {
            var tempdata = {
                id: respData.data[i].deviceId,
                pId: 0,
                name: respData.data[i].deviceName,
                icon: "./plugins/zTree/css/zTreeStyle/img/diy/3.png",
                isParent: respData.data[i].hasChild
            };
            zNodes.push(tempdata);
        }

        // $.fn.zTree.init($("#station_tree"), setting, zNodes);
        $.fn.zTree.init($("#station_tree_point"), setting, zNodes);
        pointDevNodeCheck();
    });
}

function saveTestPointInfo(formData, parentId, type) {
    poll('POST', 'SavePointInfo', formData, function (text) {
        var textshow = getI18nName('operationSuccess');
        if (type === "add") {
            textshow = getI18nName('pointAddSuccessTips');
        }
        else {
            textshow = getI18nName('pointEditSuccessTips');
        }
        // getDevicePointNameList();
        devCode = null;
        test_point_id_set = null;
        pointDevNodeCheck();
        $('.notifications').notify({
            message: {
                text: textshow
            },
            type: "success",
        }).show();
    }, function (errorCode) {
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: stringFormat.format(getI18nName('operatFailed'), convertErrorCode(errorCode))
            },
            type: "danger",
        }).show();
    });
}

function delTestPointInfo(del_test_point_id_set) {
    var zTree = $.fn.zTree.getZTreeObj('station_tree_point');
    var currentNode = zTree.getSelectedNodes()[0];
    if (currentNode && currentNode.level == 1) {
        $.notifyUtil.notifyWarning(getI18nName('selectPointTips'))
        return;
    }
    var data = {};
    data.pointId = del_test_point_id_set;
    poll('POST', 'DeletePointInfo', data, function (text) {
        $('.notifications').notify({
            message: {
                text: getI18nName('pointDelSuccessTips')
            },
            type: "success",
        }).show();
        // getDevicePointNameList();
        pointDevNodeCheck();
    }, function (errorCode) {
        if (errorCode == 1) {
            errorCode = 5;
        }
        $('.notifications').notify({
            fadeOut: {
                enabled: false
            },
            message: {
                text: getI18nName('deleteFailedTips') + convertErrorCode(errorCode)
            },
            type: "danger",
        }).show();
    });
}

function getTestPointList(deviceId) {
    $("#station_name_dev").html(station);
    var data = "deviceId=" + deviceId;
    poll('GET', 'GetPointNameList', data, function (text) {
        var respData = text.result;
        if (respData.length <= 0)
            return;

        for (var i = 0; i < respData.length; i++) {
            var tempdata = {
                text: respData[i].pointName,
                id: respData[i].pointId
            };

            var parentNode = $('#station_tree').treeview('getSelected');
            $('#station_tree').treeview('addNode', [tempdata, parentNode]);
        }
    });
}

/**
 * 检查缓存的设备节点并给页面设置更新
 */
var tempDevNode;
var tempPointDevNode;
function pointDevNodeCheck() {
    nodeCheck('station_tree_point', tempPointDevNode);
}
function devNodeCheck() {
    nodeCheck('station_tree', tempDevNode);
}
var cacheTempDeviceNodeIsParent;
function nodeCheck(treeId, treeNode) {
    var zTree = $.fn.zTree.getZTreeObj(treeId);
    var nodes = zTree.getNodes();
    var tempNode = nodes[0].children[0];
    if (treeNode && zTree.getNodeByParam('id', treeNode.id)) {
        tempNode = zTree.getNodeByParam('id', treeNode.id);
    }
    if (tempNode) {
        zTree.selectNode(tempNode);
        if (treeId == 'station_tree_point') {
            if (tempNode.children && tempNode.children.length > 0) {
                tempNode.children = [];
            }
            tempNode.isParent = true;
            zTree.updateNode(tempNode);
        }
        // if (tempNode.isParent) {
        // if (tempNode.getParentNode()) {
        //     zTree.reAsyncChildNodes(tempNode.getParentNode(), "refresh");
        // }
        $('.curSelectedNode').trigger('click');
        zTree.reAsyncChildNodes(tempNode, "refresh", false, function () {
            if (treeId == 'station_tree_point') {
                tempNode.isParent = cacheTempDeviceNodeIsParent;
                zTree.updateNode(tempNode);
            }
        });
        // }
    }
}