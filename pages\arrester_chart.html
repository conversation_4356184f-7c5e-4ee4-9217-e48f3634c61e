<div class="col-sm-12 full-height">
    <!--<div class="form-group"-->
    <!--style="height: 45px;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px">-->
    <!--<label class="col-sm-2  control-label" style="margin-top: 9px"-->
    <!--data-i18n="sensorType">传感器类型:</label>-->
    <!--<label class="col-sm-1  control-label" style="margin-top: 9px;text-align: left"-->
    <!--id="sensorType" data-i18n="Arrester">避雷器</label>-->
    <!--<label class="col-sm-2  control-label" style="margin-top: 9px" data-i18n="acquisitionTime">采集时间:</label>-->
    <!--<label class="col-sm-3  control-label" style="margin-top: 9px;text-align: left"-->
    <!--id="simpleTime">-->
    <!--2018/2/24 12:12:12</label>-->
    <!--&lt;!&ndash;<label class="col-sm-2  control-label"&ndash;&gt;-->
    <!--&lt;!&ndash;style="margin-top: 9px; text-align: right;" data-i18n="humidity">湿度: </label>&ndash;&gt;-->
    <!--<label class="col-sm-4  control-label" style="margin-top: 9px;text-align: left"-->
    <!--id="amp"></label>-->
    <!--</div>-->
    <div class="col-sm-12" style="height: 100%;margin-top: 6px;padding-left: 0px;padding-right: 0px">
        <div class="col-sm-9 no-padding full-height">
            <div class="nav-tabs-custom full-height" style="margin-right: 5px">
                <div class="row col-sm-12 col-center-block" style="margin: 5px;margin-left:-8px">
                    <div class="col-sm-5" style="">
                        <label for="dateStart" class="col-sm-12 control-label" style="margin-top: 7px;"
                            data-i18n="startDate">起始日期</label>
                        <div class="col-sm-12">
                            <div class="bootstrap-datepicker">
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <input id="arresterBeginDate" type="text" class="form-control pull-right"
                                        readonly="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class=" col-sm-5" style="">
                        <label for="dateEnd" class="col-sm-12 control-label" style="margin-top: 7px;"
                            data-i18n="endDate">结束日期</label>
                        <div class="col-sm-12">
                            <div class="bootstrap-datepicker">
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <input id="arresterEndDate" type="text" class="form-control pull-right"
                                        readonly="true">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-2">
                        <label for="dateEnd" class="col-sm-12 control-label" style="margin-top: 7px;">&nbsp;</label>
                        <button type="button" id="refreshBtn" class="btn btn-block btn-primary" style="width: 80px;"
                            data-i18n="scanData">查询</button>
                    </div>
                </div>
                <div class="col-sm-12" style="width:100%;height:100%">
                    <div id="charts" style="width:100%;height:95%">

                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-3 no-padding nav-tabs-custom full-height">
            <form id="arresterParams" class="form-horizontal full-height">
                <!--<div class="form-group">-->
                <!--<label class="col-sm-7 control-label" style="padding-right:5px">传感器类型:</label>-->
                <!--<div class="col-sm-5">-->
                <!--<label class=" control-label" id="sensorType">避雷器</label>-->
                <!--</div>-->
                <!--</div>-->
            </form>
        </div>
    </div>
</div>
<style>
    .no-padding {
        padding: 0px;
    }
</style>
<script>
    //@ sourceURL=instrument_set.js
    initPageI18n();

    /*   $('#da-dateRange').daterangepicker({
          "opens": "right",
          "autoApply": true,
          "timePicker": false,
          "startDate": moment().subtract(29, 'days'),
          "endDate": moment(),
      }); */
    $('#arresterBeginDate').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "singleDatePicker": true,
        "timePicker24Hour": true,
        locale: {
            'format': 'YYYY/MM/DD'
        },
        "startDate": new Date(new Date().toDateString()).pattern('yyyy/MM/dd')
    });
    $('#arresterEndDate').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "singleDatePicker": true,
        "timePicker24Hour": true,
        locale: {
            'format': 'YYYY/MM/DD'
        },
        "startDate": new Date(new Date().toDateString()).pattern('yyyy/MM/dd')
    });

    $("input[vk=true]").focus(function () {
        $.fn.keysoft($(this));
    });

    $('.select2').select2({
        minimumResultsForSearch: Infinity,
        language: "zh-CN"
    });
    $('#refreshBtn').click(function () {
        getArresterTendDatas();
    });
    getArresterTendDatas();
</script>