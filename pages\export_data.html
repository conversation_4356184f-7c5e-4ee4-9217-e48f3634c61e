<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin full-height">
        <!--<div class="box-header with-border">-->
        <!--<h3 class="box-title">Sync Data</h3>-->
        <!--</div>-->
        <!-- /.box-header -->
        <div class="box-body">

            <div class="form-group">
                <label data-i18n="backupType">备份类型</label>
                <div class="col-sm-12" style="height: 40px;">
                    <div class="col-sm-4">
                        <label>
                            <input type="radio" name="optionsRadios" id="optionsRadios1" value="option1" checked="">
                            <span data-i18n="plusBackup">增量备份</span>
                        </label>
                    </div>
                    <div class="col-sm-4">
                        <label>
                            <input type="radio" name="optionsRadios" id="optionsRadios2" value="option2">
                            <span data-i18n="allBackup">全量备份</span>
                        </label>
                    </div>
                    <div class="col-sm-4">
                        <label>
                            <input type="radio" name="optionsRadios" id="optionsRadios3" value="option3"
                                onchange="change()">
                            <span data-i18n="timeRange">时间范围</span>
                        </label>
                    </div>
                </div>
            </div>
            <label data-i18n="timeRange">时间范围</label>
            <div class="input-group date">
                <div class="input-group-addon">
                    <i class="fa fa-calendar"></i>
                </div>
                <input id="da-dateRange" type="text" class="form-control pull-right" readonly="true">
            </div>
            <!-- /.da-dateRange -->
            <br>
            <label for="showPath" data-i18n="exportPath">导出路径</label>
            <div class="input-group file-caption-main">
                <div tabindex="500" class="form-control file-caption  kv-fileinput-caption">
                    <div style="color: #999" id="showPath" data-i18n="getExportDir">获取目录</div>
                </div>
                <div class="input-group-btn">
                    <div tabindex="500" class="btn btn-primary btn-file" id="getPath">
                        <span data-i18n="checkDir">检查目录</span>
                        <!--<i class="glyphicon glyphicon-folder-open"></i>&nbsp; <span class="hidden-xs">请选择</span>-->
                        <!--<input id="fu-upload" type="file" class="file" data-upload-url="#">-->
                    </div>
                </div>
            </div>

            <br>
            <div id="da-progress-export" class="progress-group hide">
                <span class="progress-text" data-i18n="exportingData">正在导出...</span>
                <span class="progress-number"></span>
                <div class="progress progress-lg active">
                    <div class="progress-bar progress-bar-success progress-bar-striped" role="progressbar"
                        aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
            <!-- /.progress-group -->
        </div>
        <!-- /.box-body -->
        <div class="content-footer">
            <button type="button" id="export-cancel" class="btn btn-default hide"
                style="width: 200px; margin: 10px 10px 10px auto"><img src="dist/img/loading.gif"
                    class="img-loading hide" alt="">
                <span data-i18n="cancel">取消</span>
            </button>
            <button type="button" id="export-ok" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 10px 10px auto" disabled="">
                <img src="dist/img/loading.gif" class="img-loading hide" alt="">
                <span data-i18n="export">导出</span>
            </button>
        </div>
        <br />
    </div>
    <!-- /.box -->

    <!-- /.content-footer -->
</div>
<!--/.col (left) -->


<script>
    //@ sourceURL=export_data.js
    initPageI18n();
    $("input[name='optionsRadios']").change(function () {
        var val_payPlatform = $('input[name="optionsRadios"]:checked ').val();
        var sel_radio = $('input[name="optionsRadios"]:checked ')[0].id;
        if (val_payPlatform == 'option3') {
            $('#da-dateRange').daterangepicker({
                "opens": "right",
                "autoApply": true,
                "timePicker": false,
                "startDate": moment().subtract(29, 'days'),
                "endDate": moment()
            });
            $('#da-dateRange').removeAttr("disabled");
        } else {
            $('#da-dateRange').val("");
            $('#da-dateRange').attr("disabled", "");
        }
    });

    $('#da-dateRange').val("");
    $('#da-dateRange').attr("disabled", "");

    $('#getPath').click(function () {
        var data = "";
        poll('GET', 'GetExportDataPath', data, function (text) {
            var respData = text.result;
            if (text.errorCode == 0) {
                $("#showPath")[0].innerText = respData.file;
                $('#export-ok').removeAttr("disabled");
            } else {
                $("#showPath")[0].innerText = convertErrorCode(text.errorCode); //"无外部存储设备，请检查!";
            }
        }, function (errorCode) {
            $("#showPath")[0].innerText = getI18nName('getDirFailed') + convertErrorCode(errorCode)
            $('.notifications').notify({
                fadeOut: {
                    enabled: false
                },
                message: {
                    text: getI18nName('getDirFailed') + convertErrorCode(errorCode)
                },
                type: "danger",
            }).show();
        });
    });

    //上传固件更新文件
    $('#export-ok').click(function () {
        var val_payPlatform = $('input[name="optionsRadios"]:checked ').val();
        var sel_radio = $('input[name="optionsRadios"]:checked ')[0].id;
        var data = "";
        if (sel_radio === "optionsRadios2") {
            data = "type=1&exportPath=" + $("#showPath")[0].innerText;
        } else if (sel_radio === "optionsRadios3") {
            var dateArray = $('#da-dateRange').val().split('-');
            data = "type=2&exportPath=" + $("#showPath")[0].innerText +
                "&beginTime=" + dateArray[0].trim() + "&endTime=" + dateArray[1].trim();
        } else {
            data = "type=0&exportPath=" + $("#showPath")[0].innerText;
        }

        //        $('#export-cancel').removeClass('hide');
        //        $('#export-ok').addClass('hide').prop('disabled', false);
        $('#export-ok img').removeClass('hide');

        poll('GET', 'ExportData', data, function (text) {
            var respData = text.result;
            var retText = "";
            if (text.errorCode === 0) {
                retText = stringFormat.format(getI18nName('backupSuccessTip'), text.result.file);
                $('#export-ok img').addClass('hide');
            } else {
                retText = getI18nName('backupFailedTip') + text.errorMsg;
            }
            $.fn.alertMsg(
                'warning',
                retText, [{
                    id: 'no',
                    text: getI18nName('cancel')
                }, {
                    id: 'yes',
                    text: getI18nName('confirm'),
                    callback: function () {
                        $('#export-ok img').addClass('hide');
                        //                        //禁用取消按钮
                        //                        $('#export-cancel').prop('disabled', true);
                        //                        $('#export-cancel img').removeClass('hide');
                        //                        //关闭websocket请求
                        //                        var message = {
                        //                            "methodName": "PushSyncAduDataProgress",
                        //                            "enablePush": false,
                        //                            "parameters": ""
                        //                        }
                        //                        webSocketSend(message);
                    }
                }]
            );
            $('#no').addClass('hide');
        }, function (errorCode) {
            $('.notifications').notify({
                message: {
                    text: stringFormat.format(getI18nName('exportFailedTip'), convertErrorCode(
                        errorCode))
                },
                type: "danger",
            }).show();
            $('#export-ok img').addClass('hide');
        });
    });

    $('#export-cancel').click(function () {
        $.fn.alertMsg(
            'warning',
            getI18nName('stopExportTips'), [{
                id: 'no',
                text: getI18nName('no')
            }, {
                id: 'yes',
                text: getI18nName('yes'),
                callback: function () {
                    //禁用取消按钮
                    $('#export-cancel').prop('disabled', true);
                    $('#export-cancel img').removeClass('hide');
                    //关闭websocket请求
                    var message = {
                        "methodName": "PushSyncAduDataProgress",
                        "enablePush": false,
                        "parameters": ""
                    }
                    // webSocketSend(message);
                }
            }]
        );
    })
</script>