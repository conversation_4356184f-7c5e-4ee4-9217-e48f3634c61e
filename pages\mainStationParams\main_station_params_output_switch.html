<style>
    .tab-pane {
      text-align: right;
    }
    .main-station-label-content {
        display: flex;
        align-items: center;
    }

    .ms-common-param-content {
        display: flex;
        margin: 10px -18px;
        border-bottom: 1px solid #f4f4f4;
        padding: 8px;
    }

    .ms-common-param-item {
        display: flex;
        align-items: center;
    }

    .station-config-unit{
        position: absolute;
        right: 15px;
        border: 1px solid #dddddd;
        padding: 6px;
        text-align: center;
        min-width: 36px;
    }
    
    .station-config-param-content{
        display: flex;
        /* align-items: center; */
        margin: 8px 0;
    }
    .station-config-param-content .required{
        color: #f42e07;
        position: absolute;
        top: 3px;
        left: -12px;
    }

    .station-config-param-content .help-block{
        display: block;
        position: absolute;
        bottom: -17px;
    }

    .station-config-enable-btn {
      margin-left: 15px;
      cursor: pointer;
    }

    .i2-connect-state,
    .station-config-status {
      padding: 5px 10px; 
      border-radius: 5px; 
      background-color: lightgray; 
      color: white;
    }

    .i2-connect-state.red,
    .station-config-status.red {
      background-color: #f42e07; 
    }

    .i2-connect-state.yellow,
    .station-config-status.yellow {
      background-color: #ffc100; 
    }

    .i2-connect-state.green,
    .station-config-status.green {
      background-color: #00a65a; 
    }

    .main-station-label-content {
        display: flex;
        align-items: center;
    }

    .ms-common-param-content {
        display: flex;
        margin: 10px -18px;
        border-bottom: 1px solid #f4f4f4;
        padding: 8px;
    }

    .ms-common-param-item {
        align-items: center;
        margin: 8px;
    }

    .station-params-form-content {
      min-height: 430px;
      position: relative;
      padding-bottom: 4rem;
    }

</style>
<section class="content-header" style="padding: 25px">
    <h1 style="display: flex;">
        <i class="fa fa-dashboard"></i> <div style="margin-left:8px;" data-i18n="settings">系统设置</div>
        <small style="display: flex;margin:8px"> > <div style="margin-left:8px;" data-i18n="ConnectionProtocolConfiguration">接出协议配置</div></small>
    </h1>
</section>
<section class="content">
    <div class="row" style="margin: auto;">
        <div class="col-md-12">
            <div class="box-body">
                <div class="nav-tabs-custom" style="min-height:500px">
                  <ul class="nav nav-tabs">
                      <li class="stationNav active" data-id="0"><a href="#I2" data-toggle="tab"
                              aria-expanded="true" >I2</a></li>
                      <li class="stationNav hide" data-id="1"><a href="#IEC104" data-toggle="tab"
                              aria-expanded="true" >IEC104</a>
                      </li>
                      <li class="stationNav hide" data-id="2"><a href="#IEC61850" data-toggle="tab"
                        aria-expanded="true" >IEC61850</a>
                      </li>
                      <li class="stationNav hide" data-id="3"><a href="#modbus" data-toggle="tab"
                        aria-expanded="true" >Modbus</a>
                      </li>
                  </ul>
                  <div class="tab-content" style="margin-top: 14px;overflow-y:auto;">
                    <div class="tab-pane active" id="I2">
                      <div class="row" style="text-align: right;width:100%">
                        <div class="nav-tabs-custom" style="box-shadow: none;">
                            <ul class="nav nav-tabs">
                                <li class="stationNav active" data-id="0"><a href="#I2-1-content" data-toggle="tab"
                                        aria-expanded="true" data-i18n="ManstationParams1">主站参数1</a></li>
                                <li class="stationNav" data-id="1"><a href="#I2-2-content" data-toggle="tab"
                                        aria-expanded="true" data-i18n="ManstationParams2">主站参数2</a>
                                </li>
                            </ul>
                            <div class="tab-content" style="margin-top: 14px;overflow-y:auto;">
                                <div class="tab-pane active" id="I2-1-content">
                                    <div id="I2-1-form" class='station-params-form-content'>
                                    </div>
                                </div>
                                <div class="tab-pane" id="I2-2-content">
                                  <div id="I2-2-form" class='station-params-form-content'>
                                  </div>
                                </div>
                            </div>
                        </div>
                      </div>
                    </div>
                    <div class="tab-pane" id="IEC104">
                      <div id="IEC104-form" class='station-params-form-content'>
                      </div>
                    </div>
                    <div class="tab-pane" id="IEC61850">
                      <div id="IEC61850-form" class='station-params-form-content'>
                      </div>
                    </div>
                    <div class="tab-pane" id="modbus">
                      <div id="modbus-form" class='station-params-form-content'>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </div>
    </div>
    </div>
</section>
<script src="./pages/mainStationParams/const.js"></script>
<script src="./pages/mainStationParams/main_station_params_output_switch.js"></script>
<script type="text/javascript">
  initOutwardConfigLayer()
    // initPageI18n();
</script>