﻿<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title></title>
    <meta charset="utf-8" />
    <script type='text/javascript' src="pdcharts.js"></script>
    <script type='text/javascript' src="canvg.js"></script>
    <script type='text/javascript' src="data.js"></script>
    <script>
        window.onload = function() {
            var chartDiv = document.getElementById('chartDiv');
            //PRPS-3D
            var prps3dChart = PDChart.PRPS.draw3D(prps.chartBody, {
                title: 'UHF PRPS3D',
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, prps3dChart, {
                id: "prps3d",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            //PRPS-2D
            var prps2dChart = PDChart.PRPS.draw2D(prps.chartBody, {
                title: 'UHF PRPS2D',
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, prps2dChart, {
                id: "prps2d",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            //PRPD-3D
            var prps3dChart = PDChart.PRPD.draw3D(prpd.chartBody, {
                title: 'UHF PRPS3D',
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, prps3dChart, {
                id: "prpd3d",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            //PRPD-2D
            var prps2dChart = PDChart.PRPD.draw2D(prpd.chartBody, {
                title: 'UHF PRPS2D',
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, prps2dChart, {
                id: "prpd2d",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            var amplitudeChart = PDChart.AMPLITUDE.draw(amplitude.chartBody, {
                title: 'AE幅值图谱',
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, amplitudeChart, {
                id: "amplitude",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            var waveChart = PDChart.WAVE.draw(wave.chartBody, {
                title: 'AE时域波形图谱',
                showTriggerLine: true,
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, waveChart, {
                id: "wave",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            var phaseChart = PDChart.PHASE.draw(phase.chartBody, {
                title: 'AE相位图谱',
                showTriggerLine: true,
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, phaseChart, {
                id: "phase",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });

            var flyChart = PDChart.FLY.draw(fly.chartBody, {
                title: 'AE飞行图谱',
                showTriggerLine: true,
                backgroundColor: "#f1f1f1"
            });
            PDChart.loadChart(chartDiv, flyChart, {
                id: "fly",
                width: 500,
                height: 500,
                append: true,
                saveAsImage: true,
            });
        }


        function zoom(type) {
            var svgEls = document.querySelectorAll('svg');
            var size = 0;
            switch (type) {
                case 'in':
                    size = 20;
                    break;
                case 'out':
                    size = -20;
                    break;
            }
            for (var i = 0; i < svgEls.length; i++) {
                PDChart.zoom(svgEls[i], size);
            }
        }

        function localtion(type) {
            var svgEls = document.querySelectorAll('svg');
            var x = y = 0;
            switch (type) {
                case 'left':
                    x = 20;
                    break;
                case 'right':
                    x = -20;
                    break;
                case 'up':
                    y = 20;
                    break;
                case 'down':
                    y = -20;
                    break;
            }
            for (var i = 0; i < svgEls.length; i++) {
                PDChart.localtion(svgEls[i], x, y);
            }
        }

        function reset() {
            var svgEls = document.querySelectorAll('svg');
            for (var i = 0; i < svgEls.length; i++) {
                PDChart.reset(svgEls[i]);
            }
        }
    </script>
</head>

<body>
    <div class="gloablBtn" style="height:30px">
        <input type="button" value="放大" onclick="zoom('in')">
        <input type="button" value="缩小" onclick="zoom('out')">
        <input type="button" value="左移" onclick="localtion('left')">
        <input type="button" value="右移" onclick="localtion('right')">
        <input type="button" value="上移" onclick="localtion('up')">
        <input type="button" value="下移" onclick="localtion('down')">
        <input type="button" value="重置" onclick="reset()">
    </div>
    <div id="chartDiv"></div>
</body>

</html>