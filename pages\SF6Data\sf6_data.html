<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<style>
    .sf6-content {
        display: block;
    }
    .sf6-params-content{
        border: 1px solid #ddd;
        padding: 20px;
        background-color: #fff;
        border-radius: 4px;
        height: 640px;
        overflow-y: auto;
    }
    .sf6-history-btn-group{
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
        margin-top: 20px;
    }
    .sf6-trend-controls {
        border: 1px solid #ddd;
        padding: 20px;
        margin-bottom: 15px;
        background-color: #f9f9f9;
        border-radius: 4px;
    }
    .sf6-trend-controls .form-group {
        margin-bottom: 15px;
    }
    .sf6-trend-controls .form-group:last-child {
        margin-bottom: 0;
    }
    #sf6TrendChart {
        width: 100%;
        height: 500px;
    }
    .sf6-chart-container {
        height: 520px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: #fff;
        padding: 10px;
    }
    .sf6-left-panel {
        padding-right: 15px;
        display: flex;
        flex-direction: column;
    }
    .sf6-right-panel {
        padding-left: 15px;
        display: flex;
        flex-direction: column;
    }
    .sf6-params-content {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .sf6-left-panel, .sf6-right-panel {
            padding-left: 15px;
            padding-right: 15px;
        }
        .sf6-trend-controls .row .col-sm-6 {
            margin-bottom: 10px;
        }
        .sf6-params-content .form-group {
            margin-bottom: 10px;
        }
        .sf6-history-btn-group {
            text-align: center;
        }
        .sf6-history-btn-group button {
            margin: 5px;
            width: auto;
        }
    }

    /* 美化表单控件 */
    .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
        min-height: 34px;
    }

    /* 美化按钮 */
    .sf6-history-btn-group button {
        min-width: 100px;
        margin: 0 5px;
    }

    /* 修复数据详情页的垂直对齐 */
    .sf6-params-content .form-group {
        margin-bottom: 12px;
    }

    .sf6-params-content .control-label {
        padding-top: 7px;
        padding-bottom: 7px;
        line-height: 1.42857143;
        text-align: right;
        margin-bottom: 0;
    }

    .sf6-params-content .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        line-height: 1.42857143;
        margin-bottom: 0;
        min-height: 34px;
        display: block;
    }
</style>
<div class="col-sm-12">
    <div class="nav-tabs-custom sf6-content">
        <div id="graph-SF6" class="col-sm-8 sf6-left-panel">
            <!-- 趋势图控制区域 -->
            <div class="sf6-trend-controls">
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>数据类型:</label>
                            <select id="dataTypeSelect" class="form-control">
                                <option value="SF6_Pressure">SF6气体压力</option>
                                <option value="SF6_Density">SF6气体密度</option>
                                <option value="SF6_Temperature">SF6气体温度</option>
                                <option value="SF6_EnvTemperature">设备环境温度</option>
                                <option value="SF6_SuperCapVoltage">超级电容电压</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <button type="button" id="refreshTrendBtn" class="btn btn-primary btn-block">刷新趋势图</button>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>开始时间:</label>
                            <div class="input-group">
                                <div class="input-group-addon">
                                    <i class="fa fa-calendar"></i>
                                </div>
                                <input id="dateStart" type="text" class="form-control" readonly="true">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label>结束时间:</label>
                            <div class="input-group">
                                <div class="input-group-addon">
                                    <i class="fa fa-calendar"></i>
                                </div>
                                <input id="dateEnd" type="text" class="form-control" readonly="true">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 趋势图显示区域 -->
            <div class="sf6-chart-container">
                <div id="sf6TrendChart"></div>
            </div>
        </div>
        <div class="col-sm-4 sf6-right-panel">
            <div class="sf6-params-content">
                <h4 style="margin-top: 0; margin-bottom: 20px; color: #333; border-bottom: 2px solid #3c8dbc; padding-bottom: 10px;">
                    <i class="fa fa-info-circle"></i> 数据详情
                </h4>
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">测点名称:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="pointName"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">传感器编码:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="sensorCode"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">传感器类型:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="sensorType"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">采样日期:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="sample_date"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">采样时间:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="sample_time"></span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-4 control-label">SF6气体密度:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="SF6_Density">3.1MPa</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">SF6气体压力:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="SF6YN">2.3MPa</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">SF6气体温度:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="SF6_Temp">20.0℃</span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">设备环境温度:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="Device_env_Temp">16.1℃</span>
                        </div>
                    </div>


                    <div class="form-group">
                        <label class="col-sm-4 control-label">告警状态:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="Alarm_Status"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">电池电压:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="batteryVoltage"></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">超级电容电压:</label>
                        <div class="col-sm-8">
                            <span class="form-control-static" id="superCapvoltage"></span>
                        </div>
                    </div>
                    <!-- 操作按钮 -->
                    <div class="sf6-history-btn-group">
                        <button type="button" id="preData" class="btn btn-primary">
                            <i class="fa fa-chevron-left"></i> 上一条
                        </button>
                        <button type="button" id="nextData" class="btn btn-primary">
                            下一条 <i class="fa fa-chevron-right"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        <!-- <div class="content-footer">
            <div class="col-sm-3">
                <button type="button" id="now_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="realData">实时数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="history_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="historyData">
                    历史数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                    style="width: 140px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
                </button>
            </div>
        </div> -->
    </div>
</div>
<script type="text/javascript">
    initPageI18n();
    $(function () {
        //@ sourceURL=ae_chart.js
        $(window).resize(function () {
            if (!document.getElementById("sf6Div")) { //js判断元素是否存在
                return;
            }
            var height = $("#graph-SF6").height() - $(".content-footer").height() - 20;
            $("#sf6Div").height(height);

            var svgEls = document.querySelector('svg');
            if (svgEls) {
                svgEls.setAttribute('width', $("#sf6Div").width());
                svgEls.setAttribute('height', $("#sf6Div").height());
            }
            //            loadSF6Data();
        });
        $(window).resize();

        // 初始化时间选择器
        initDatePickers();

        // 初始化趋势图
        initSF6TrendChart();

        var preId = 0;
        var nextId = 0;
        var nowId;


        function getSF6AlarmState(state){
            switch(parseInt(state)){
                case -1:
                    return getI18nName('normal');
                case 0:
                    return getI18nName('SF6_Alarm_Low_Pressure');
                case 1:
                    return getI18nName('SF6_Alarm_Low_Voltage_Block');
                case 2:
                    return getI18nName('SF6_Alarm_Over_Voltage');

            }
        }

        function loadSF6Data(dataId, pointId,aduId) {
            $("#sf6Div").empty();
            var chartDiv = document.getElementById('sf6Div');
            poll('GET', 'GetChannelInfo', {
                aduId:aduId,
                channelIndex:0,
            }, function (channelInfo) {
                var channelInfoRes = channelInfo.result;
                var PhysicalChannelType=channelInfoRes.PhysicalChannelType;

                switch(parseInt(PhysicalChannelType)){
                    case 0:
                        $('#SF6YN_Label').html('SF6 '+getI18nName('GasAbsPressure'));
                        break;
                    case 1:
                        $('#SF6YN_Label').html('SF6 '+getI18nName('GasGaugePressure'));
                        break;
                    default:
                        $('#SF6YN_Label').html('SF6 '+getI18nName('SF6YN'));
                        break;
                }


                var data = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=SF6&dataType=SF6";
                poll('GET', 'GetChartData', data, function (text) {
                    var respData = text.result;

                    preId = respData.preDataId;
                    nextId = respData.nextDataId;
                    //判断上一条/下一条按钮是否可用并改变状态
                    if (preId === 0) {
                        $("#preData").attr("disabled", true);
                    } else {
                        $("#preData").removeAttr("disabled");
                    }
                    if (nextId === 0) {
                        $("#nextData").attr("disabled", true);
                    } else {
                        $("#nextData").removeAttr("disabled");
                    }
                    $("#sf6Div").empty();
                    var sensorType = getI18nName(respData.params.sensorType);
                    var alarmState= respData.params.alarmState;
                    if(alarmState==undefined||alarmState==""){
                        alarmState=[];
                    }
                    var alarmStateMsg=[];
                    alarmState.map(function(item){
                        alarmStateMsg.push('['+getSF6AlarmState(item)+']');
                    });
                    if(alarmStateMsg.length==0){
                        alarmStateMsg.push(getI18nName('none'));
                    }
                    $("#pointName").html(respData.params.pointName);
                    $("#sensorCode").html(respData.params.aduId);
                    $("#sensorType").html(sensorType);
                    $("#SF6_Density").html(respData.params.sf6Density&&respData.params.sf6Density!=''?respData.params.sf6Density +' MPa':'');
                    $("#sample_date").html(respData.params.sample_date);
                    $("#sample_time").html(respData.params.sample_time);
                    $("#SF6YN").html(respData.params.sf6Pressure+' MPa');
                    $("#SF6_Temp").html(respData.params.sf6Temprature+' ℃');
                    $("#Device_env_Temp").html(respData.params.envTemprature+' ℃');
                    $("#Alarm_Status").html(alarmStateMsg.join(','));
                    $("#batteryVoltage").html(respData.params.batteryVoltage+' V');
                    $("#superCapvoltage").html(respData.params.superCapvoltage+' V');
                });

            })
        }

        loadSF6Data(showChartDataId, showChartPointId,showChartADUId);

        $("#preData").click(function () {
            if (preId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            loadSF6Data(preId, showChartPointId,showChartADUId);
        });
        $("#nextData").click(function () {
            if (nextId === 0) {
                $.fn.alertMsg(
                    'warning',
                    getI18nName(''), [{
                        id: 'no',
                        text: getI18nName('confirm')
                    }]
                );
                return;
            }
            loadSF6Data(nextId, showChartPointId,showChartADUId);
        });

        // 初始化时间选择器
        function initDatePickers() {
            var endDate = new Date();
            var startDate = new Date();
            startDate.setDate(endDate.getDate() - 7); // 默认显示最近7天

            $('#dateStart').daterangepicker({
                "opens": "right",
                "autoApply": true,
                "timePicker": true,
                "timePicker24Hour": true,
                "timePickerSeconds": true,
                "singleDatePicker": true,
                "locale": {
                    "format": 'YYYY/MM/DD HH:mm:ss'
                },
                "startDate": startDate
            });

            $('#dateEnd').daterangepicker({
                "opens": "right",
                "autoApply": true,
                "timePicker": true,
                "timePicker24Hour": true,
                "timePickerSeconds": true,
                "singleDatePicker": true,
                "locale": {
                    "format": 'YYYY/MM/DD HH:mm:ss'
                },
                "startDate": endDate
            });
        }

        // 初始化SF6趋势图
        var sf6TrendChart;
        function initSF6TrendChart() {
            var chartContainer = document.getElementById('sf6TrendChart');

            if (!chartContainer) {
                return;
            }

            if (typeof echarts === 'undefined') {
                return;
            }

            sf6TrendChart = echarts.init(chartContainer);

            var option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'line'
                    },
                    confine: true,
                    formatter: function(params) {
                        var dataType = $("#dataTypeSelect").val();
                        var unit = getDataTypeUnit(dataType);
                        var result = params[0].name + '<br/>';

                        params.forEach(function(param) {
                            var formattedValue = formatDataValue(param.value, dataType);
                            result += param.marker + param.seriesName + ': ' + formattedValue + unit + '<br/>';
                        });

                        return result;
                    }
                },
                title: {
                    text: 'SF6数据趋势',
                    x: 'center',
                    y: 'top'
                },
                dataZoom: [
                    {
                        type: 'inside',
                        start: 0,
                        end: 100
                    }
                ],
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    top: '10%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    boundaryGap: false,
                    axisTick: {
                        alignWithLabel: false
                    }
                },
                yAxis: {
                    type: 'value',
                    name: ''
                },
                series: [
                    {
                        name: '',
                        type: 'line',
                        data: [],
                        itemStyle: {
                            normal: {
                                color: '#3c8dbc'
                            }
                        },
                        markPoint: {
                            data: [
                                {type: 'max', name: '最大值'},
                                {type: 'min', name: '最小值'}
                            ]
                        }
                    }
                ]
            };

            sf6TrendChart.setOption(option);

            // 加载初始数据
            loadSF6TrendData();
        }

        // 加载SF6趋势数据
        function loadSF6TrendData() {
            var startDate = $("#dateStart").val();
            var endDate = $("#dateEnd").val();
            var dataType = $("#dataTypeSelect").val();

            var dataGet = "pointId=" + showChartPointId + "&pointType=SF6" +
                "&dataType=" + dataType + "&startDate=" + startDate + "&endDate=" + endDate;

            poll('GET', 'GetTendData', dataGet, function (response) {
                var respData = response.result;

                if (!respData || !respData.dataChart) {
                    if (typeof layer !== 'undefined') {
                        layer.msg('<div style="text-align:center">暂无趋势数据</div>');
                    }
                    return;
                }

                // 更新图表标题和Y轴标签
                var option = sf6TrendChart.getOption();
                var dataTypeName = $("#dataTypeSelect option:selected").text();
                var unit = getDataTypeUnit(dataType);

                // 格式化数据精度
                var formattedData = (respData.dataChart.chartInit || []).map(function(value) {
                    return parseFloat(formatDataValue(value, dataType));
                });

                option.title[0].text = dataTypeName + '趋势图';
                option.yAxis[0].name = unit; // 只显示单位，不显示数据类型名称
                option.xAxis[0].data = respData.dataChart.xValue || [];
                option.series[0].name = dataTypeName;
                option.series[0].data = formattedData;

                sf6TrendChart.setOption(option);

                // 强制重新渲染
                setTimeout(function() {
                    if (sf6TrendChart) {
                        sf6TrendChart.resize();
                    }
                }, 100);

            }, function(error) {
                if (typeof layer !== 'undefined') {
                    layer.msg('<div style="text-align:center">加载趋势数据失败</div>');
                }
            });
        }

        // 获取数据类型对应的单位
        function getDataTypeUnit(dataType) {
            switch(dataType) {
                case 'SF6_Pressure':
                    return 'MPa';
                case 'SF6_Density':
                    return 'MPa';
                case 'SF6_Temperature':
                    return '℃';
                case 'SF6_EnvTemperature':
                    return '℃';
                case 'SF6_SuperCapVoltage':
                    return 'mV';
                default:
                    return '';
            }
        }

        // 格式化数据显示精度
        function formatDataValue(value, dataType) {
            if (value === null || value === undefined || value === '') {
                return value;
            }

            var numValue = parseFloat(value);
            if (isNaN(numValue)) {
                return value;
            }

            switch(dataType) {
                case 'SF6_Pressure':
                case 'SF6_Density':
                    return numValue.toFixed(5); // 5位小数
                case 'SF6_Temperature':
                case 'SF6_EnvTemperature':
                    return numValue.toFixed(2); // 2位小数
                case 'SF6_SuperCapVoltage':
                    return numValue.toFixed(2); // 2位小数
                default:
                    return value;
            }
        }

        // 数据类型选择器变化事件
        $("#dataTypeSelect").change(function () {
            loadSF6TrendData();
        });

        // 刷新按钮点击事件
        $("#refreshTrendBtn").click(function () {
            var startDate = $("#dateStart").val();
            var endDate = $("#dateEnd").val();

            if (moment && moment(startDate).isAfter(moment(endDate))) {
                if (typeof layer !== 'undefined') {
                    layer.msg('<div style="text-align:center">开始时间不能晚于结束时间</div>');
                }
                return;
            }

            loadSF6TrendData();
        });

        // 窗口大小改变时重新调整图表大小
        window.addEventListener("resize", function () {
            if (sf6TrendChart) {
                sf6TrendChart.resize();
            }
        });
    });
</script>