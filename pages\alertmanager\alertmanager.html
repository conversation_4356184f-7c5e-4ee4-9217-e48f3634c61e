<!--
 * @Author: liyaoxu <EMAIL>
 * @Date: 2023-11-16 15:17:03
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-11-20 14:48:54
 * @FilePath: \pds_z058_web\pages\alertmanager\alertmanager.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<style>
    .tableTitleBoxChild {
        margin-right: 15px;
        display: flex;
        align-items: center;
        float: left;
    }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin  full-height">
        <div class="box-body">
            <div class="tab-content " style="padding:0px;height: 90%">
                <div class=" tab-pane full-height active" id="all_tab">
                    <div id="alertToolbar" class="tableTitleBoxChild">
                        <div class="tableTitleBoxChild">
                            <div class="tableTitleBoxChild hide">
                                <label class="control-label" style="padding: 10px;" data-i18n="pointName">测点名称</label>
                                <div>
                                    <input type="text" class="form-control" id="alertPointName"
                                        name="alertPointName" data-i18n-placeholder="">
                                </div>
                            </div>
                            <div class="tableTitleBoxChild">
                                <label class="control-label" style="padding: 10px;" data-i18n="AlarmLevel">报警等级</label>
                                <select id="alertLevelSelect" class="select2" style="width: 150px">
                                    <option value="-1" selected="selected" data-i18n="all">全部</option>
                                    <option value="1" data-i18n="AlarmStateWarning">预警</option>
                                    <option value="2" data-i18n="alarm">告警</option>
                                </select>
                            </div>
                            <div class="tableTitleBoxChild">
                                <label class="control-label" style="padding: 10px;" data-i18n="channelType">通道类型</label>
                                <select id="dataTypeSelect" class="select2"  style="width: 150px">
                                    <option value="-1" selected="selected" data-i18n="all">全部</option>
                                    <option value="0">TEV</option>
                                    <option value="4">AE</option>
                                    <option value="13" data-i18n="MP">机械特性</option>
                                    <option value="26" data-i18n="TEMP">温度</option>
                                </select>
                            </div>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-default btn-sm"
                                    onclick="searchAlert()">
                                <i class="fa  fa-search" ><span style="padding-left: 3px" data-i18n="search">搜索</span></i>
                            </button>
                            <button type="button" class="btn btn-default btn-sm"
                                    onclick="setAlert()">
                                <i class="fa fa-gear" > <span style="padding-left: 3px" data-i18n="alarm_param_set">报警配置</span></i>
                            </button>
                        </div>
                    </div>
                    <table id="tableAlert" class="table table-bordered table-hover full-height">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    var winHeight = $(window).height() * 0.82;
    $('.select2').select2({
        minimumResultsForSearch: Infinity,
    });
    $(window).resize(refreshTableOptions);
    initPageI18n();
    function refreshTableOptions() {
        var height = $(".nav-tabs-custom").height() - 120;
        $(".tab-content").height(height);
        var option = $('#tableLog').bootstrapTable('getOptions');
        option.length = winHeight * 0.89;
    }
</script>