<style>
    .tableTitleBoxChild {
        margin-right: 15px;
        display: flex;
        align-items: center;
        float: left;
    }
</style>
<!-- left column -->
<div class="col-sm-12 full-height">
    <!-- general form elements -->
    <div class="box no-border no-margin  full-height">
        <div class="box-body">
            <div class="tab-content " style="padding:0px;height: 90%">
                <div class="tab-pane full-height active" id="all_tab">
                    <div id="logToolbar" class="tableTitleBoxChild">
                        <div class="tableTitleBoxChild">
                            <div class="tableTitleBoxChild" style="display: block;">
                                <div class="" style="" data-i18n="SearchType">查询类型</div>
                                <select id="auditFindStyleSelect" class="select2" style="width: 150px"
                                    onchange="onAuditFindStyleSelect(this)">
                                    <option value="0" data-i18n="all">全部</option>
                                    <option value="1" data-i18n="type">类型</option>
                                    <option value="2" data-i18n="Date">日期</option>
                                </select>
                            </div>
                            <div class="tableTitleBoxChild" style="display: block;">
                                <div class="" style="" data-i18n="CheckManagerCheckType">审计类型</div>
                                <select id="auditDataTypeSelect" class="select2" style="width: 250px">
                                </select>
                            </div>
                            <div class="tableTitleBoxChild" style="display: block;">
                                <div class="bootstrap-datepicker">
                                    <div class="input-group" style="margin-top: 35px;">
                                        <div class="input-group-addon">
                                            <i class="fa fa-calendar"></i>
                                        </div>
                                        <input id="logDataRange" type="text" class="form-control" style="width: 300px;"
                                            readonly="true">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="btn-group" style="margin-left: 70px;margin-top: 35px;">
                            <button type="button" class="btn btn-default btn-sm" onclick="searchLog()">
                                <i class="fa  fa-search"><span style="padding-left: 3px"
                                        data-i18n="search">搜索</span></i>
                            </button>
                        </div>
                    </div>
                    <table id="tableLog" class="table table-bordered table-hover full-height">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    initPageI18n();
    var winHeight = $(window).height() * 0.82;
    $('.select2').select2({
        minimumResultsForSearch: Infinity,
    });
    $(window).resize(refreshTableOptions);

    function refreshTableOptions() {
        var height = $(".nav-tabs-custom").height() - 120;
        $(".tab-content").height(height);
        var option = $('#tableLog').bootstrapTable('getOptions');
        option.length = winHeight * 0.89;
    }
    initLogManagerTable();
</script>