{"loginTime": "로그인 시간", "changerUser": "사용자 전환", "change": "전환", "login": "로그인", "systemShutDownConfirmTip": "시스템이 1분 이내에 종료됩니다. 작업을 취소하시겠습니까?", "systemShutDownTip": "1분 땡, 자동 종료!", "systemShutDownCancel": "종료 취소", "loadingData": "데이터를 로드하려고 노력하고 있습니다. 잠시 기다려 주십시오...", "pageSize": "페이지당 개", "pageSelecter": "표시: {1}-{2}, 총 {3} 개", "search": "검색", "noMatch": "일치하는 레코드가 없습니다.", "index": "일련번호", "type": "유형", "aduId": "인코딩", "connectedState": "네트워크 상태", "commType": "통신 방식", "loraSignalLevel": "LoRa 신호 강도", "loraSNR": "신호 잡음비", "powerSupplyMode": "전력 공급 방식", "byBattery": "내장형 배터리", "byCable": "외부 전원 공급 장치", "battaryPercent": "전력", "battaryLife": "기간 (일)", "deviceNameTable": "장치 이름", "pointType": "센서", "isconnected": "상태", "onlineState": "온라인", "offlineState": "연락이 끊기다", "aduNeverConnectedState": "접속되지 않음", "updateTime": "업데이트 시간", "data": "데이터", "chart": "도감", "operate": "작업", "lookUp": "보기", "tbSampleDate": "채집 시간", "collect": "채집", "errorTip": "오류: {1}", "AEamp": "AE 폭값", "aeChartTitle": "AE 폭 스펙트럼", "humidityChartTitle": "습도 곡선", "temperatureChartTitle": "온도 곡선", "noiseChartTitle": "노이즈 곡선", "coreGroundingCurrentChartTitle": "철심 접지 전류 곡선", "TEVamp": "TEV 폭", "TEVYunit": "단위 [dB]", "UnitFomat": "단위 [{1}]", "unitParse": "단위 [{1}]", "maxValue": "최대값", "minValue": "최소", "average": "평균값", "AT&T": "미국 AT&T", "China Unicom": "차이나유니콤", "China Mobile": "차이나 모바일", "Transformer": "변압기", "Breaker": "차단기", "Disconnector": "분리 스위치", "Isolator": "나이프 브레이크", "Arrester": "피뢰기", "PT": "전압 센서", "CT": "전류 센서", "Busbar": "모선", "Circuit": "모련", "Switchgear": "스위치 캐비닛", "Power Cable": "전력 케이블", "Lightning Rod": "피뢰침", "Wall Bushing": "벽을 뚫는 튜브", "Reactor": "저항기", "Electric Conductor": "전력 컨덕터 (도류 배열)", "Power Capacitor": "전기 용기", "Discharge Coil": "방전 코일", "Load Switch": "하중 스위치", "Grounding Transformer": "접지변화", "Grounding Resistance": "접지 저항", "Grounding Grid": "접지망", "Combined Filter": "결합 필터", "Insulator": "절연자", "Coupling Capacitor": "결합 콘덴서", "Cabinet": "스크린 캐비닛", "Other": "기타", "Fuse": "퓨즈", "Using Transformer": "사용 변경", "Arc Suppression Device": "소호 장치", "Main Transformer": "주 변압기", "Wave Trap": "저파기", "Combined Electric Appliance": "조합전기", "Combined Transformer": "콤보 센서", "monitor": "실시간 모니터링", "dataSelect": "데이터 쿼리", "themeSkin": "피부", "themeBlue": "파란색", "settings": "시스템 구성", "datetime_set": "시간 설정", "language_set": "언어 설정", "soft_setting": "시스템 설정", "file_manage": "파일 관리", "instrument_set": "센서 구성", "file_point_set": "포인트 구성", "alarm_param_set": "경고 구성", "alarm_manager": "경보 관리", "audit_view": "감사 보기", "modbus_setting": "odbus 설정", "hardware_update": "펌웨어 업데이트", "collect_mode": "채집 모드", "net_set": "네트워크 설정", "main_station_params": "마스터 구성", "sync_data": "데이터 동기화", "syncingData": "데이터 동기화 중...", "syncingDataCancel": "동기화 취소", "syncingDataFailed": "동기화 실패", "syncingDataSuccess": "데이터 동기화 완료", "syncingDataProgress": "동기화 데이터 진행률: {1}", "syncingDataProgress2": "동기화 데이터 진행률 [{1}]", "export_data": "데이터 내보내기", "system_info": "시스템 정보", "monitoringTable": "데이터 테이블", "PD": "내장 국방 센서", "PD_THREE": "내장형 국방 센서 (3-in-1)", "PD_FIVE": "내장형 국방 센서 (5-in-1)", "OLD_IS": "특수 고주파 지능 센서", "MEU": "기계적 특성 센서", "UHF_IS": "특수 고주파 지능 센서", "HFCT_IS": "고주파 지능 센서", "PD_IS": "외장형 3-in-1 지능형 센서", "TRANSFORMER_AE_IS": "변압기 초음파 센서", "GIS_AE_IS": "GIS 초음파 센서", "ENV_IS": "환경 지능 센서", "Arrester_U_IS": "피뢰기 전압 지능 센서", "Arrester_I_IS": "피뢰기 전류 지능 센서", "LeakageCurrent_IS": "누설 전류 지능 센서", "Vibration_IS": "진동 지능 센서", "MECH_IS": "기계적 특성 지능형 모니터", "TEMP_HUM_IS": "온습도 센서", "GrounddingCurrent_IS": "접지 전류 지능 센서", "MECH_PD_TEMP": "내장 국방 기계 특성 온도 삼합일 센서", "GIS_LOWTEN": "저압 센서", "CHANNEL_OPTICAL_TEMP": "광섬유 온도 측정 센서", "VaisalaDTP145": "비살라 DTP145", "Wika_GDT20": "위카 GDT20", "Wika_GDHT20": "웨이카 GDHT20", "SHQiuqi_SC75D_SF6": "상해 규기 SC75D", "SPTR_IS": "철심 접지 전류 센서", "TEMPFC_OUT": "온도 센서(CLMD)", "TEMPXP_OUT": "온도 센서(SPS061)", "SF6YN_OUT": "SF6 가스 압력 센서(FTP-18)", "FLOOD_IS": "수침 지능 센서", "TEMPKY_OUT": "온도 센서(RFC-01)", "SMOKERK_OUT": "연기 감지 경보기(RS-YG-N01)", "HIKVIDEO_OUT": "비디오 센서(HIK)", "TEMPSB_OUT": "온도 센서(DS18B20)", "TEMP_HUM_JDRK_OUT": "온도 및 습도 센서(RS-WS-N01-2)", "SF6_OUT": "SF6&O2 센서(WFS-S1P-SO)", "FAN_OUT": "송풍기 컨트롤러", "LIGHT_OUT": "조명 컨트롤러", "NOISE_JDRK_OUT": "노이즈 센서(RS-WS-N01)", "IR_DETECTION_OUT": "적외선 이중 탐지기", "TEMPWS_OUT": "온도 및 습도 센서(WS-DMC100)", "FLOODWS_OUT": "침수 센서(WS-DMC100)", "NOISEWS_OUT": "노이즈 센서(WS-DMC100)", "VibrationSY_OUT": "승양 진동 센서", "TEVPRPS_IS": "임시 전압 지능 센서", "SF6_IS": "SF6 지능형 센서", "phase": "위상", "period": "주기", "AMP": "폭", "RMS": "유효한 값", "max": "주기 최대값", "fre1Value": "주파수 성분 1", "fre2Value": "주파수 성분2", "All-pass": "전통", "Low-pass": "저통", "High-pass": "퀄컴", "No-Record": "기록되지 않음", "TEV": "잠정적 전압", "AE": "초음파", "TEMP": "온도", "HFCT": "고주파 전류", "UHF": "특고주파", "Humidity": "습도", "Decibels": "데시벨", "Noisy": "음압", "Noise": "노이즈", "ENVGas": "대기", "envgas": "대기 추세", "MP": "기계적 특성", "FLOOD": "침수", "SMOKE": "연기감", "SF6": "육불화황", "FAN": "풍기", "LIGHT": "조명", "NOISE": "노이즈", "IRDETECTION": "적외선", "SF6YN": "SF6 가스 압력", "ArresterU": "피뢰기 전압", "ArresterI": "피뢰기 전류", "LeakageCurrent": "누설 전류", "Vibration": "진동", "FrostPointRaw": "서리점", "FrostPointATM": "서리점(표준대기압)", "DewPointRaw": "이슬점", "DewPointATM": "이슬점 (표준 대기압)", "Moisture": "미수", "AbsolutePressure": "절대 압력", "NormalPressure": "표준 압력", "Density": "밀도", "Oxygen": "산소 함량", "SPTR": "철심 접지 전류", "GrounddingCurrent": "접지 전류", "VIDEO": "비디오 사진", "TEVPRPS": "임시 전압 PRPS", "confirm": "확인", "close": "닫기", "yes": "예", "no": "아니오", "continue": "계속", "none": "없음", "DISK": "스토리지", "MEMORY": "메모리", "noTestData": "테스트 데이터 없음", "connected": "연결", "disconnected": "분리", "virtualKeyBord": "가상 키보드", "pleaseInput": "입력하십시오.", "pleaseInputFormat": "{1}을 입력하십시오.", "tips": "힌트", "confirmTips": "확인 프롬프트", "getDirFailed": "디렉토리를 가져오지 못했습니다.", "backupSuccessTip": "데이터 데이터가 백업되었습니다. {1} 디렉토리에서 확인하십시오.", "backupFailedTip": "데이터 백업 실패", "exportFailedTip": "데이터 내보내기 실패: {1}", "historyDataTypeNotSuport": "센서 유형 히스토리 데이터 쿼리는 일시적으로 지원되지 않음", "stopExportTips": "데이터 내보내기를 중지해야 합니까?", "stationNameTips": "사이트 이름을 입력하십시오.", "powerUnitNameTips": "전력 단위를 기입해 주십시오", "selectDeviceTips": "장치를 한 번 선택하십시오.", "selectPointTips": "체크포인트를 선택하십시오.", "selectDeviceOfPointTips": "체크포인트를 추가할 장치를 선택하십시오.", "saveSuccess": "저장 성공", "saveFailed": "저장 실패:", "operatFailed": "작업 실패: {1}", "chosseSensorUpdate": "업데이트할 센서를 선택하십시오.", "chooseFileUpdate": "업데이트 파일을 선택하십시오.", "cancelUpdateSensorConfirm": "센서 프로그램의 업데이트를 취소하시겠습니까?", "enterServerUrlTips": "서버 주소를 입력하십시오.", "enterServerUrlError": "서버 주소 입력이 잘못되었습니다. 다시 입력하십시오.", "enterServerPortTips": "서버 포트를 입력하십시오.", "enterServerPortError": "서버 포트 입력 오류, 포트 입력 범위: [165535].", "NTPserverIpError": "에 대한 서버 주소 입력이 잘못되었습니다. 다시 입력하십시오.", "NTPserverPortError": "피어 서버 포트 입력 오류, 포트 입력 범위: [165535].", "enterNetName": "네트워크 이름을 입력하십시오.", "enterIP": "IP를 입력하십시오.", "enterIPError": "IP 주소 입력 오류(예: ***********)", "enterNetMask": "서브넷 마스크를 입력하십시오.", "enterNetError": "서브넷 마스크 입력 오류 (예: *************)", "enterGateWay": "게이트웨이를 입력하십시오.", "enterGateWayError": "게이트웨이 구성 오류: ***********", "enterAPN": "APN 선택", "pdSampleIntervalTip": "PD-샘플링 간격 20s~594099s(99h99m99s)", "meuSampleIntervalTip": "MEU- 샘플링 간격은 10s에서 86400 사이입니다.", "monitorAwakeTimeTip": "1s에서 86400 사이의 절전 모드 간격", "monitorSleepSpaceTip": "소생 간격 범위: [{1}, {2}]min", "uploadIntervalTip": "업로드 간격은 60s에서 86400 사이입니다.", "sampleIntervalTip": "샘플링 간격이 1시간에서 72시간 사이의 정수", "monitorSampleTimeTip": "호스트 샘플링 시작 시각 0에서 23시 사이의 정각 시각", "monitorSampleIntervalTip": "호스트 샘플링 간격이 1시간에서 168시간 사이의 정수", "updatingSensor": "센서 업데이트 중...", "updatingSensorProgress": "센서 업데이트 진행률: {1}", "updatingSensorProgress2": "센서 업데이트 진행률 [{1}]", "selectInstTip": "수정할 센서를 선택하십시오.", "selectChannelTip": "수정할 채널을 선택하십시오.", "instCodeTip": "센서 인코딩은 비워둘 수 없습니다.", "commLinkTypeTip": "통신 링크 유형을 선택하십시오.", "commLinkPortTip": "통신 포트를 선택하십시오.", "continuSampTimeTip": "연속 채집 시간은 {1}min에서 {2}min 사이입니다.", "continuSampTimeCompareTip": "연속 채집 시간은 샘플링 간격보다 작아야 한다.", "instSampleSpaceTip": "샘플링 간격은 {1}min에서 {2}min 사이입니다.", "instSampleStartTimeTip": "샘플링 시작 시간은 0시에서 23시 사이의 정각입니다.", "instNumberPattenTip": "0-9 숫자, a-zA-Z 문자 및:.", "instIPPattenTip": "IP 및 포트 형식: 0.0.0.0:1", "deleteInstTip": "삭제할 센서를 선택하십시오.", "selectSensorTip": "센서를 선택하십시오.", "deleteInstConfirmTip": "</br>{1}</br> 센서 코드: {2}를 삭제하시겠습니까?", "activeInstConfigTip": "이 구성을 </br>{1}의 동급 센서에 적용하시겠습니까?", "activeBatchConfigsSuccess": "센서 대량 구성 성공", "activeBatchConfigsBegin": "센서 대량 구성 시작", "activeBatchConfigsFailed": "센서 대량 구성 실패", "collectStartOnceTip": "</br>{1} 동급 센서의 데이터 수집을 시작하시겠습니까?", "collectStartTip": "</br>{1}</br> 센서 인코딩: {2}의 데이터 수집을 시작하시겠습니까?", "wakeUpInstrumentOnceTip": "</br>{1} 동급 센서를 깨우기 시작하시겠습니까?", "wakeUpInstrumentTip": "확인 깨우기 시작 </br>{1}</br> 센서: {2}?", "emptySensorDataOnceTip": "</br>{1} 동급 센서의 모든 데이터를 비우시겠습니까?", "emptySensorDataTip": "</br>{1}</br> 센서 인코딩: {2}의 모든 데이터를 비우시겠습니까?", "emptySensorDataSuccess": "센서 데이터 삭제 성공", "emptySensorDataFailed": "센서 데이터 삭제 실패:", "ae_chart": "실시간 모니터링-AE 스펙트럼", "uhf_chart": "실시간 모니터링 - UHF 스펙트럼", "hfct_chart": "실시간 모니터링-HFCT 스펙트럼", "tev_chart": "실시간 모니터링 - TEV 폭값", "tev_prps_chart": "실시간 모니터링 - 임시 전압 PRPS 스펙트럼", "temperature_chart": "실시간 모니터링 - 온도", "humidity_chart": "실시간 모니터링 - 습도", "mechanical_chart": "실시간 모니터링 - 기계적 특성", "arrester_chart": "실시간 모니터링 - 피뢰기", "leakage_current_chart": "실시간 모니터링 - 누설 전류", "grounddingcurrent_chart": "실시간 모니터링 - 접지 전류", "vibration_pickup_chart": "실시간 모니터링 - 진동", "density_micro_water_history": "실시간 모니터링 - 밀도 마이크로워터", "core_grounding_current": "실시간 모니터링 - 코어 접지 전류", "noise_chart": "실시간 모니터링 - 노이즈", "water_immersion": "실시간 모니터링 - 침수", "smoke_sensor": "실시간 모니터링. - 연기감.", "video_sensor": "실시간 모니터링 - 비디오", "sf6_sensor": "실시간 모니터링 - SF6", "error": "오류", "warning": "경고", "success": "성공", "userErrorTip": "사용자 이름 또는 암호 오류", "errorNoDeviceId": "장치 ID가 없습니다.", "errorNoSensorId": "센서 ID가 존재하지 않습니다.", "errorExistSensorId": "센서 ID가 이미 있습니다.", "errorNoChannelId": "채널 ID가 없습니다.", "errorNoPointId": "측정 지점 없음", "errorDataExportErrDir": "데이터 내보내기, 파일 경로 오류.", "errorDataExportErrTime": "데이터 내보내기 시간 오류", "errorDataExportNoData": "데이터 내보내기, 데이터 없음.", "errorDataExportNoSpace": "데이터 내보내기, USB 가득 참", "errorDataExportNoDisk": "USB 없이 데이터를 내보냅니다.", "errorDataExportNoInfo": "사이트 정보가 없는 데이터 내보내기", "errorDataExportOther": "데이터 내보내기, 기타 오류.", "errorSetModeBus": "modebus 설정 오류", "errorSameNamePoint": "같은 이름의 측정 지점이 이미 존재합니다.", "errorIP": "IP 오류", "errorGPRSSet": "GPRS 설정 오류", "errorSenorUpdateErrFile": "센서 업데이트 파일 오류", "errorSenorUpdateNotMatch": "센서 업데이트, 센서 유형이 업그레이드 파일과 일치하지 않음", "errorSenorUpdating": "센서 업데이트, 업데이트 중", "errorSenorUpdateSizeCheckFailed": "펌웨어 크기 확인 실패", "errorSenorUpdateVersionCheckFailed": "펌웨어 버전 번호 확인 실패", "errorSenorUpdateDeviceTypeCheckFailed": "펌웨어 장치 유형 확인 실패", "errorSenorUpdateCRCCheckFailed": "펌웨어 CRC 검증 실패", "errorSenorUpdateFailed": "센서 업그레이드 실패", "errorSenorUpdateConnectErr": "펌웨어 업데이트 통신 예외", "errorDataCleanFailed": "데이터 삭제 실패!", "errorCodeWorkGroupNotExist": "워크그룹이 없습니다!", "errorCodeInvalidChannel": "불법 통로!", "errorCodeInvalidWirignMode": "불법 접속 방식!", "errorCodeInvalidAlarmMode": "불법 신고 방식!", "errorNoPermission": "이 사용자에게는 이 권한이 없습니다.", "illegalUser": "불법 사용자!", "legalPattenMsg": "문자만 포함 (공백 제외): 한자 영숫자 로마자 Ⅰ - Ⅻ`. -() [] # 、 _", "groundCurrent": "접지 전류 추세도", "groundCurrentA": "A 접지 전류", "groundCurrentB": "B 접지 전류", "groundCurrentC": "C 접지 전류", "leakageCurrent": "전류 흐름도", "leakageCurrentA": "A상 전류", "leakageCurrentB": "B상 전류", "leakageCurrentC": "C상 전류", "ResistiveCurrent": "저항 전류 추세도", "ResistiveCurrentA": "A 저항성 전류", "ResistiveCurrentB": "B 저항성 전류", "ResistiveCurrentC": "C 저항성 전류", "resistiveCurrentA": "A 저항성 전류", "resistiveCurrentB": "B 저항성 전류", "resistiveCurrentC": "C 저항성 전류", "referenceVoltageA": "A상 참조 전압", "referenceVoltageB": "B상 참조 전압", "referenceVoltageC": "C상 참조 전압", "grounddingCurrent": "접지 전류 추세도", "grounddingCurrentA": "A 접지 전류", "grounddingCurrentB": "B 접지 전류", "grounddingCurrentC": "C 접지 전류", "timeDomain": "타임 맵", "frequencyDomain": "주파수 그래프", "characterParam": "피쳐 매개변수", "TimeDomainDataX": "X축 진동 신호", "TimeDomainDataY": "Y축 진동 신호", "TimeDomainDataZ": "Z축 진동 신호", "FrequencyDomainDataX": "진동신호 스펙트럼도-X축", "FrequencyDomainDataY": "진동신호 스펙트럼도-Y축", "FrequencyDomainDataZ": "진동신호 스펙트럼도-Z축", "ACCAVGX": "X축 가속도 평균", "ACCAVGY": "Y축 가속도 평균값", "ACCAVGZ": "Z축 가속도 평균", "ACCMAXX": "X축 가속도 최대값", "ACCMAXY": "Y축 가속도 최대값", "ACCMAXZ": "Z축 가속도 최대값", "AMPAVGX": "X축 진폭 평균값", "AMPAVGY": "Y축 진폭 평균값", "AMPAVGZ": "Z축 진폭 평균", "AMPMAXX": "X축 진폭 최대값", "AMPMAXY": "Y축 진폭 최대값", "AMPMAXZ": "Z축 진폭 최대값", "MAXFreqX0": "X축 극치점 주파수1", "MAXFreqY0": "Y축 극치점 주파수 1", "MAXFreqZ0": "Z축 극치점 주파수 1", "MAXFreqX1": "X축 극치점 주파수2", "MAXFreqY1": "Y축 극치점 주파수2", "MAXFreqZ1": "Z축 극치점 주파수2", "MAXFreqX2": "X축 극치점 주파수3", "MAXFreqY2": "Y축 극치점 주파수3", "MAXFreqZ2": "Z축 극치점 주파수3", "sensorType": "센서 유형", "sensorList": "센서 목록", "gain": "이득", "trigger": "트리거 폭값", "wave_filter": "주파수 대역", "sample_date": "샘플링 날짜", "sample_time": "샘플링 시간", "rms": "유효한 값", "cycle_max": "주기 최대값", "frequency1": "주파수 성분 1", "frequency2": "주파수 성분2", "time_interval": "시간 간격", "realData": "실시간 데이터", "historyData": "히스토리 데이터", "trendSync": "추세 분석", "preData": "이전 항목", "nextData": "다음", "systemDate": "시스템 날짜", "systemTime": "시스템 시간", "backupType": "백업 유형", "plusBackup": "증분 백업", "allBackup": "전체 백업", "timeRange": "시간 범위", "exportPath": "경로 내보내기", "getExportDir": "디렉터리 가져오기", "checkDir": "디렉토리 확인", "exportingData": "내보내는 중...", "cancel": "취소", "export": "내보내기", "stationConfig": "사이트 구성", "stationDelSuccess": "사이트 삭제 성공", "stationDelFailed": "사이트 삭제 실패:", "deviceConfig": "단일 장치 구성", "pointConfig": "포인트 구성", "stationName": "사이트 이름", "stationPMS": "사이트 인코딩", "stationLevel": "사이트 전압 등급", "powerUnit": "전력 단위", "save": "저장", "operationSuccess": "작업 성공", "deviceAddSuccessTips": "1회 장치 증가 성공", "deviceEditSuccessTips": "장치 수정 성공", "deviceDelSuccessTips": "장치 삭제 성공", "pointAddSuccessTips": "측정점 증가 성공", "pointEditSuccessTips": "체크포인트 수정 성공", "pointDelSuccessTips": "체크포인트 삭제 성공", "deleteFailedTips": "삭제 실패", "save_add": "저장 / 추가", "delete": "삭제", "deviceType": "장치 유형", "deviceLevel": "장치 전압 등급", "add": "증가", "channelTypeAlarm": "데이터 유형", "alarmThreshold": "경고 임계값", "alarmRecoveryThreshold": "경고 복구 임계값", "alarmChannel": "경고 채널 선택", "built_in_channel_1": "내부 채널 1", "External_IO_module": "외부 IO 모듈", "not_associated": "비연관", "alarmExternalIOSN": "외부 IO 모듈 인코딩", "alarmExternalIOChannel": "외부 IO 모듈 채널", "wiringMode": "연결 방식", "alarmMode": "경보 방식", "alarmTime": "경보 시간", "alarmInterval": "경보 간격", "alarmDuration": "경보 시간", "normal_open": "상시", "normal_close": "상폐", "continuous": "연속", "timing": "정시", "interval": "간격", "alarmSaveSuccess": "경고 구성이 성공적으로 저장되었습니다.", "alarmDelSuccess": "경고 구성 삭제 성공", "deviceName": "단일 장치 이름", "pointName": "측정점 이름", "testStation": "테스트 사이트", "device": "1차 장치", "pointList": "측정 목록", "sensorID": "센서 ID", "sensorChannel": "센서 채널", "channelList": "채널 목록", "showPoint": "측정점 표시", "fileSelect": "파일 선택", "selectPlease": "선택하십시오.", "deviceList": "센서 목록", "updating": "업데이트 중...", "updatingFailed": "업데이트 실패:", "updatingCanceled": "업데이트 취소", "updatingComplete": "업데이트 완료", "update": "업데이트", "sampleDate": "샘플링 날짜", "sampleTime": "샘플링 시간", "pdMax": "최대 방전 폭값", "pdAvg": "평균 방전폭", "pdNum": "펄스 개수", "acquisitionTime": "채집 시간", "humidity": "습도", "startDate": "시작 날짜", "endDate": "종료 날짜", "refresh": "새로 고침", "scanData": "질의", "sensorCode": "센서 인코딩", "sensorTypeSet": "센서 유형", "senorName": "센서 이름", "aduAddress": "센서 주소", "senorWorkMode": "능동적으로 전송하다.", "On": "켜기", "Off": "끄기", "ADUMode": "작업 모드", "normalMode": "유지 보수 모드", "lowPowerMode": "저전력 모드", "monitorMode": "모니터링 모드", "artificialStateStartTime": "수동 개입 상태 시작 시각 (정각)", "artificialStateStartTimeTip": "수동 개입 상태 시작 시간 범위 0-23", "artificialStateEndTime": "수동 개입 상태 종료 시각 (정각)", "artificialStateEndTimeTip": "수동 개입 상태 종료 시간 범위 0-23", "artificialStateWakeUpSpace": "인공 간섭 상태 소생 간격(분)", "unartificialStateWakeUpSpace": "비인공 간섭 상태 소생 간격(분)", "isAutoChangeMode": "자동 전환 모드 여부", "monitorModeSampleSapce": "모니터링 모드 채집 간격", "workGroup": "워크그룹 번호", "warnTemp": "경고 온도", "taskGroup": "작업 그룹 번호", "commLinkType": "통신 링크 유형", "commLinkPort": "통신 포트", "frequencyUnit": "전력망 주파수(Hz)", "numInGroup": "그룹 내 번호", "commSpeed": "통신 속도", "commLoad": "통신 채널", "sampleSpace": "샘플링 간격(min)", "sleepTime": "전력 소비 정책", "samleStartTime": "샘플링 시작 시간 (정체)", "applyData": "작업", "applyAllSensor": "동종에 적용", "collectOnce": "대량 채집", "collectOnceEnd": "데이터 수집 종료", "collectOnceProgress": "데이터 수집: {1} ({2})", "collectOnceProgress2": "데이터 수집 [{1}] ({2})", "activeOnce": "대량 적용", "wakeUp": "깨우기", "wakeUpInstrument": "깨우기 센서", "wakeUpInstrumentOnce": "대량 깨우기 센서", "orderSend": "명령이 이미 하달되었다", "cleanData": "데이터 비우기", "sensorAddSuccess": "센서 증가 성공", "sensorEditSuccess": "센서 수정 성공", "sensorEditBegin": "센서 수정 시작", "sensorDelSuccess": "센서 제거 성공", "cleanSensorData": "센서 데이터 비우기", "getSensor": "센서 가져오기", "channelType": "채널 유형", "channelName": "채널 이름", "channelInfoSaveSuccess": "채널 정보가 성공적으로 저장되었습니다.", "channelInfoSaveBegin": "센서 채널 정보 저장 시작", "bias": "오프셋 [dB]", "waveFilter": "주파수 대역 설정", "gainMode": "이득 모드", "gain_unit": "이득 [dB]", "samCycle": "샘플링 주기", "samPointNum": "주기별 샘플링 포인트", "samRate": "주기별 샘플링 포인트", "ratio": "변비", "channelPhase": "이별", "mecLoopCurrentThred": "코일 전류 임계값 [mA]", "mecMotorCurrentThred": "모터 전류 임계값 [mA]", "mecSwitchState": "스위치 양 초기 상태", "awaysOpen": "상시", "awaysClose": "상폐", "mecBreakerType": "회로 차단기 메커니즘 구성", "oneMech": "삼상기계연동(한 기구)", "threeMech": "3상 전기 연동 (3대 기관)", "mecMotorFunctionType": "모터 작업 유형", "threePhaseAsynMotor": "삼상이보전기", "onePhaseMotor": "단일 교차 / DC 모터", "setCurrent": "현재 설정", "setAll": "동일 센서에 적용", "showCoil": "코일 전류 스펙트럼", "showSwitch": "스위치 스펙트럼", "showMotor": "모터 전류 스펙트럼", "showOrig": "원시 전류 스펙트럼", "actionDate": "작업 날짜", "actionTime": "동작 시간", "phaseA": "A상", "phaseB": "B상", "phaseC": "C상", "coil_charge_time": "코일 전원 공급 시간", "coil_cutout_time": "코일 단전 시간", "max_current": "코일 최대 전류", "hit_time": "브레이크 해제 시간", "subswitch_close_time": "보조 스위치 전환 시간", "a_close_time": "A 연결 브레이크 시간", "a_close_coil_charge_time": "A 코일 전원 켜기 시간", "a_close_coil_cutout_time": "A 코일 단전 시간", "a_close_max_current": "A 상합 브레이크 코일 최대 전류", "a_close_hit_time": "A 상합 브레이크 브레이크 해제 시간", "a_close_subswitch_close_time": "A 위상 보조 스위치 전환 시간", "b_close_time": "B 위상 브레이크 시간", "b_close_coil_charge_time": "B 코일 전원 켜기 시간", "b_close_coil_cutout_time": "B 코일 단전 시간", "b_close_max_current": "B 상합 브레이크 코일 최대 전류", "b_close_hit_time": "B 상합 브레이크 브레이크 해제 시간", "b_close_subswitch_close_time": "B 위상 보조 스위치 전환 시간", "c_close_time": "C 위상 브레이크 시간", "c_close_coil_charge_time": "C 코일 전원 공급 시간", "c_close_coil_cutout_time": "C 코일 단전 시간", "c_close_max_current": "C 상합 브레이크 코일 최대 전류", "c_close_hit_time": "C 상합 브레이크 브레이크 해제 시간", "c_close_subswitch_close_time": "C 위상 보조 스위치 전환 시간", "close_sync": "스위치 동기성", "close_time": "브레이크 타임", "a_open_time": "A 분리 브레이크 시간", "a_open_coil_charge_time": "A 코일 전원 켜기 시간", "a_open_coil_cutout_time": "A 코일 단전 시간", "a_open_max_current": "A 위상 브레이크 코일 최대 전류", "a_open_hit_time": "A 분할 브레이크 브레이크 해제 시간", "a_open_subswitch_close_time": "A 위상 보조 스위치 전환 시간", "b_open_time": "B 분할 브레이크 시간", "b_open_coil_charge_time": "B 코일 전원 켜기 시간", "b_open_coil_cutout_time": "B 코일 단전 시간", "b_open_max_current": "B 위상 브레이크 코일 최대 전류", "b_open_hit_time": "B 분할 브레이크 브레이크 해제 시간", "b_open_subswitch_close_time": "B 위상 보조 스위치 전환 시간", "c_open_time": "C 분할 브레이크 시간", "c_open_coil_charge_time": "C 코일 전원 공급 시간", "c_open_coil_cutout_time": "C 코일 단전 시간", "c_open_max_current": "C 위상 브레이크 코일 최대 전류", "c_open_hit_time": "C 상분 브레이크 브레이크 해제 시간", "c_open_subswitch_close_time": "C 위상 보조 스위치 전환 시간", "open_sync": "분리 브레이크 동기성", "open_time": "브레이크 타임", "a_twice_open_time": "A상 2차 브레이크 타임", "a_twice_open_coil_charge_time": "A상 2차 브레이크 코일 대전체 시간", "a_twice_open_coil_cutout_time": "A상 2차 브레이크 코일 단전 시간", "a_twice_open_max_current": "A상 2차 브레이크 코일 최대 전류", "a_twice_open_hit_time": "A상 2점 방아쇠 탈착 시간", "a_twice_open_subswitch_close_time": "A상 2차 분리 브레이크 보조 스위치 해제 시간", "b_twice_open_time": "B상 2차 브레이크 시간", "b_twice_open_coil_cutout_time": "B상 2차 브레이크 코일 단전 시간", "b_twice_open_max_current": "B상 2차 브레이크 코일 최대 전류", "b_twice_open_hit_time": "B상 2점 방아쇠 탈착 시간", "b_twice_open_subswitch_close_time": "B상 2차 분리 브레이크 보조 스위치 해제 시간", "c_twice_open_time": "C상 2차 브레이크 시간", "c_twice_open_coil_cutout_time": "C상 2차 브레이크 코일 단전 시간", "c_twice_open_max_current": "C상 2차 브레이크 코일 최대 전류", "c_twice_open_hit_time": "C상 2분 방아쇠 탈착 시간", "c_twice_open_subswitch_close_time": "C상 2차 분리 브레이크 보조 스위치 해제 시간", "twice_open_sync": "이차 분리 브레이크 동기성", "twice_open_time_text": "이차 브레이크 타임", "a_switch_shot_time": "A상금 단시간", "b_switch_shot_time": "B상금 단시간", "c_switch_shot_time": "C상금 단시간", "a_switch_no_current_time": "A상 무전류 시간", "b_switch_no_current_time": "B상 무전류 시간", "c_switch_no_current_time": "C상 무전류 시간", "motor_start_current": "모터 가동 전류", "motor_max_current": "모터 최대 전류", "storage_time": "모터 에너지 저장 시간", "Chan_A_motor_start_current": "A상 모터 가동 전류", "Chan_A_motor_max_current": "A상 모터 최대 전류", "Chan_A_storage_time": "A상 모터 에너지 저장 시간", "Chan_B_motor_start_current": "B상 모터 가동 전류", "Chan_B_motor_max_current": "B상 모터 최대 전류", "Chan_B_storage_time": "B상 모터 에너지 저장 시간", "Chan_C_motor_start_current": "C상 모터 가동 전류", "Chan_C_motor_max_current": "C상 모터 최대 전류", "Chan_C_storage_time": "C상 모터 에너지 저장 시간", "serialPort": "직렬 포트", "baudRate": "전송률", "dataBit": "데이터 비트", "stopBit": "정지 비트", "checkBit": "검사 비트", "singleCollect": "대량 채집", "sampling": "채집 중...", "serverIP": "서버 IP", "serverPort": "서버 포트", "NTPserverIp": "NTP 서버 IP", "NTPserverPort": "NTP 서버 포트", "netType": "네트워크 유형", "deviceIp": "호스트 IP", "subnetMask": "서브넷 마스크", "gateway": "기본 게이트웨이", "networkAPN": "네트워크 액세스 APN", "userName": "사용자 이름", "passWord": "암호", "deviceWorkGroup": "호스트 워크그룹 번호", "frequency": "전력망 주파수", "pdSampleInterval": "PD - 샘플링 간격", "spaceSecond": " 초", "meuSampleInterval": "MEU-샘플링 간격", "monitorAwakeTime": "휴면 간격", "monitorSleepSpace": "소생 간격", "wakeStartTime": "시작 소생 시간 (정체)", "intervalServer": "업로드 간격 (서버)", "intervalMinus": "샘플링 간격", "continuousAcquisitionTime": "연속 채집 시간", "startSampleTime": "호스트 샘플링 시작 시간", "spacePoint": " 점", "startSampleInterval": "호스트 시작 샘플링 간격", "hour": "시간", "poerOffSpace": "종료 간격", "powerOnSpace": "전원 켜기 간격", "dateRange": "날짜 범위", "synchronizing": "동기화 중...", "synchronize": "동기화", "amplitude": "폭", "switch": "전환", "copyRight": "©2020 PMDT Ltd.", "S1010Name": "데이터 수집 호스트", "modbusCompanySelf": "화승", "logOut": "종료", "logOutTitle": "로그아웃 확인", "logOutConfirm": "현재 사용자 로그인을 종료하시겠습니까?", "logOutTips": "로그인 종료", "enterHost": "호스트 이름을 입력하십시오.", "selectDevTip": "장치를 선택하십시오.", "stopSyncDataTip": "데이터 동기화를 중지해야 합니까?", "modbusAddress": "odBus 주소", "modbusAddressCheckTip": "odBus 주소 범위는 1~254 정수", "deviceWorkGroupCheckTip": "호스트 워크그룹 번호는 정수이며 범위: [{1}~{2}]", "emptyMonitorDatas": "호스트 데이터 비우기", "emptyMonitorDatasTip": "수집 호스트의 히스토리 데이터 모두 비우기", "emptyMonitorDatasSuccess": "데이터 비우기 성공", "emptyMonitorDatasApply": "데이터 삭제 요청 제출됨, 검토 대기 중", "resetSoftSet": "기본 설정 복원", "resetSoftSetTip": "채집 호스트의 모든 구성을 출하 시 상태로 복원", "resetSoftSetSuccess": "출하 시 복구 요청이 제출되었으며 검토 대기 중", "continueConfirmTip": "이 작업을 재개할 수 없습니다. 계속하시겠습니까?", "bias_data": "오프셋 범위는 -100-100입니다.", "back_ground_data": "배경값 범위 0-100", "sam_point": "주기당 샘플링 주파수 범위 20-2000", "sam_rate": "주기당 샘플링 주파수 범위 20-2000", "smartSensorStatus": "상태 테이블", "upThreshold": "최대 임계값", "lowerThreshold": "최소 임계값", "changeThreshold": "변경 임계값", "changeThresholdLimit": "임계값 설정은 소수점 한 자리만 지원", "upThresholdTip": "임계값 상한 범위", "lowerThresholdTip": "하한 임계값 범위", "changeThresholdTip": "변경 임계값 범위", "upLowerThresholderrTip": "하한값은 하한값보다 클 수 없습니다.", "monitorName": "호스트 이름", "monitorType": "호스트 유형", "MonitorTypeHanging": "벽걸이", "MonitorType2U": "2U", "MonitorTypeCollectionNode": "어셈블리 노드", "MonitorTypeLowPower": "저전력 태양열 호스트", "devicePMSCode": "장치 인코딩", "forceChange": "모드 전환", "forceChangeSuccess": "모드 전환 성공", "currentVersion": "현재 버전", "abnormalRecover": "예외 자가 복구", "fromLabel": "시작 시간", "toLabel": "종료 시간", "select": "선택", "sunday": "일", "monday": "하나", "tuesday": "둘", "wednesday": "셋", "thursday": "사", "friday": "다섯", "saturday": "여섯", "January": "일월", "February": "이월", "March": "삼월", "April": "사월", "May": "오월", "June": "유월", "July": "7월", "August": "팔월", "September": "구월", "October": "시월", "November": "십일월", "December": "십이월", "all": "모두", "strong": "강하다", "weak": "약하다", "poor": "차이", "min": "분할", "second": "초", "uploadInterval": "업로드 간격(min)", "loginWelcome": "로그인 시작", "dateStartIsAfterDateEnd": "시작 날짜가 종료 날짜보다 크므로 다시 선택하십시오.", "maxCurrent": "최대 전류", "standMobus": "센서 업체", "config1": "구성 1", "config2": "구성 2", "fWrnThreshold": "주의 임계값", "fAlmThreshold": "경고 임계값", "regularDormancy": "정시 휴면", "noDormancy": "휴면 안 함", "soakingWater": "침수", "dry": "건조", "normal": "정상", "alarm": "경고", "lightGunCamera": "가시광선 사격기", "lightBallCamera": "가시광구기", "infraredGunCamera": "적외선 쌍안 총기", "infraredBallCamera": "적외선 쌍안구기", "maxTemp": "최고 온도", "maxTempPosition": "최고 온도 위치", "devIPError": "이 IP는 이미 사용 중입니다.", "unknown": "알 수 없음", "fault": "장애", "lowPower": "저전력", "mediumPower": "중간 전력", "highPower": "높은 전력", "slaveid": "슬레이브 ID", "LoraFrequency": "호스트 영역", "AREA_CHINA": "중국<1>", "AREA_VIETNAM": "베트남 (AS1)", "AREA_MALAYSIA": "말레이시아 (AS1)", "AREA_EUROPE": "유럽", "AREA_US": "아메리카", "AREA_INDONESIA": "인도네시아 (AS2)", "AREA_INDIA": "인도", "AREA_KOREA": "한국", "AREA_CHINA_RSV": "중국 (대기) <2>", "getLogFileError": "센서 로그 파일 가져오기 실패", "exportLogError": "로그 파일이 없습니다. 내보내기 실패", "alertSyncProgress": "경고 데이터 동기화 업데이트 진행률: {1}", "alertSyncProgress2": "경고 데이터 동기화 업데이트 진행률 [{1}]", "FIRMWARE_EXCEPTION": "펌웨어 예외", "AD_INITIALIZATION_EXCEPTION": "AD 초기화 예외", "REFERENCE_VOLTAGE_EXCEPTION": "참조 전압 예외", "ONCHIP_FLASH_EXCEPTION": "슬라이스 내 Flash 예외", "OFFCHIP_FLASH_EXCEPTION": "슬라이스 외 Flash 예외", "SYSTEM_PARAMETERS_EXCEPTION": "시스템 매개변수 예외", "SAMPLE_PARAMETERS_EXCEPTION": "채집 매개 변수 이상", "CALIBRATION_PARAMETERS_EXCEPTION": "교정 매개변수 예외", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "시스템 매개 변수 예외 복구", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "채집 매개 변수 이상 회복", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "교정 매개변수 예외 복구", "LORA_MODULE_EXCEPTION": "LoRa 모듈 예외", "PHASE_NUM": "위상수", "DISCONNECT_TIME": "연결 해제 시간", "DATA_TOTAL_COUNT": "총 데이터", "ONLINE_RATE": "온라인 속도", "aduInfoSetProgress": "센서 구성: {1} ({2})", "aduInfoSetProgress2": "센서 구성 [{1}]({2})", "aduInfoSetting": "센서 구성 중...", "aduInfoSetEnd": "센서 구성 완료", "aduDataSample": "데이터 수집", "aduInfoSet": "센서 구성", "errorDataSampleRunning": "현재 센서 수집 작업이 진행 중입니다. 완료 후 다시 시도하십시오.", "errorAduInfoSetRunning": "현재 센서 매개 변수 설정 작업이 진행 중입니다. 완료되면 다시 시도하십시오.", "SAMPLE_PERIOD": "샘플링 주파수", "SF6_Density": "SF6 가스 밀도(P20)", "SF6_Temp": "SF6 가스 온도", "Device_env_Temp": "장치 환경 온도", "Alarm_Status": "경고 상태 표시", "SF6_Alarm_Low_Pressure": "저압 경보", "SF6_Alarm_Low_Voltage_Block": "저압 폐쇄", "SF6_Alarm_Over_Voltage": "과압 경보", "AlarmType": "경고 유형", "AlarmData": "신고 내용", "AlarmTime": "경고 시간", "AlarmLevel": "경보 등급", "AlarmStateWarning": "경고", "WarningValue": "경고 값", "AlarmValue": "경보값", "SetSuccess": "설정 성공", "AlarmDate": "경보 시간", "AlarmConfirm": "신고 확인", "Confirm": "확인", "Confirmed": "확인", "AllData": "모든 데이터", "CheckManagerSponsor": "발기자", "CheckManagerCheckType": "감사 유형", "CheckManagerDate": "시작 날짜", "CheckManagerTarget": "피발기자", "CheckManagerExtraInfo": "추가 정보", "CheckManagerResult": "동의 여부", "Refuse": "거부", "Agree": "동의", "DataExport": "데이터 내보내기", "DataExportStep1": "내보낼 데이터 선택", "DataExportStep1Title": "파일 선택 내보내기", "DataExportStep2": "데이터 크기 계산 중...", "DataExportStep2Title": "원본 데이터 크기 계산", "DataExportStep3": "압축 데이터 패키징 중...", "DataExportStep3Title": "압축 데이터 패키지", "DataExportStep4": "데이터 압축 완료", "DataExportStep4Title": "데이터 압축 완료", "DataExportCheckData": "데이터베이스 [/media/data/database]", "DataExportCheckDataFile": "데이터 파일 [/media/data/datafile]", "DataExportCheckLog": "로그 파일 [/media/data/log]", "DataExportCheckConfig": "구성 파일 [/home/<USER>/config.xml]", "DataExportBegin": "내보내기 시작", "SelectAtLeastOneTips": "하나 이상 선택", "DataExportFileSizeError": "파일 크기가 2000MB 이상이므로 Winscp와 같은 도구를 사용하여 데이터를 내보내십시오.", "DataExportFileSizeZeroError": "선택한 파일 크기는 0입니다.", "DataExportCancelTips": "데이터 내보내기가 취소됨", "DataExportDataCompressTips": "원래 데이터 크기는 {1}MB이며 예상 압축 시간 [{2}min {3}s]", "LogExportStep1": "센서 선택", "LogExportStep1Title": "센서 선택", "LogExportStep2": "로그 파일 가져오기...", "LogExportStep2Title": "로그 파일 가져오기", "LogExportStep3": "로그 파일 가져오기 완료", "LogExportStep3Title": "로그 파일 가져오기 완료", "LogExportStep4": "압축 로그 파일 압축...", "LogExportStep4Title": "압축 로그 파일 패키지", "LogExportStep5": "압축 완료", "LogExportStep5Title": "압축 완료", "LogExportCancelTips": "로그 내보내기가 취소됨", "batteryVoltage": "배터리 전압", "superCapvoltage": "슈퍼 커패시터 전압", "OverPressureThreshold": "과압 임계값", "LowPressureThreshold": "저전압 임계값", "ShutThreshold": "폐쇄 임계값", "PhysicalChannelType": "물리적 채널 유형", "GasAbsPressure": "기체 절압", "GasGaugePressure": "기체 표압", "CameraType": "카메라 유형", "DEFAULT_CHECK_Tips": "번호 형식은 m:n(예: 1:100)입니다. 여기서 m 범위[1-255], n 범위[0-65535]", "VibrationSY_OUT_CHECK_Tips": "번호 형식은 address:modbus:id, 예를 들어 1:0:1917입니다. 여기서 address 범위는 [1-255]이고 modbus/id는 [0-65535]입니다.", "NOISEWS_OUT_CHECK_Tips": "번호 형식은 ip:x:y(예: *********:1321:4512)입니다. 여기서 ip 형식은 [xxx.xxx.xxx.xxx]이고 x/y는 모두 길이가 4인 문자열입니다.", "IR_DETECTION_OUT_IO_CHECK_Tips": "번호 형식은 ip:port:ss:se:cs:ce(예: 1:100)입니다. 여기서 ip 형식 [xxx.xxx.xxx.xxx] port 포트 범위 [0-65535] ss 범위 [0-255], se 범위 0-255], cs 범위 0-255], ce 범위 0-255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "번호 형식은 ip:port:x:y(예: 1:100)입니다. 여기서 ip 형식 [xxx.xxx.xxx.xxx] port 포트 범위 [0-65535] x 범위 [1-255], y 범위 [0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "번호 형식은 m:n(예: 1:100)입니다. 여기서 m 범위[1-255], n 범위[0-65535]", "fWrnThreshold_CHECK_Tips": "주의 임계값 범위는 [10-1000]mA입니다.", "fAlmThreshold_CHECK_1_Tips": "경고 임계값 범위는 [50-5000]mA", "fAlmThreshold_CHECK_2_Tips": "경고 임계값은 주의 임계값보다 커야 합니다.", "AuditContent": "감사 내용", "OPTime": "작업 시간", "Executed": "실행", "UserManager": "사용자 관리", "SystemManagement": "시스템 관리", "BusinessManagement": "비즈니스 관리", "LanguageChange": "언어 전환", "AlarmManagement": "경고 관리", "DataOperation": "데이터 작업", "BackupRecovery": "백업 복구", "AddUser": "사용자 추가", "AddUserCheck": "사용자 감사 추가", "UserRightSet": "사용자 권한 설정", "UserRightSetCheck": "사용자 권한 설정 감사", "DeleteUser": "사용자 삭제", "DeleteUserConfirm": "사용자 확인 삭제", "FreezeUser": "사용자 고정", "FreezeUserConfirm": "사용자 확인 동결", "UnlockUser": "사용자 잠금 해제", "UnlockUserConfirm": "사용자 확인 잠금 해제", "FileStationSet": "파일 (사이트) 설정", "FileDeviceSet": "파일 (디바이스) 설정", "SenserSet": "센서 설정", "AlarmSet": "경고 설정", "SavePointSet": "측정점 설정 저장", "DelPointSet": "측정점 설정 삭제", "SingleSample": "1회 채집", "DataBackup": "데이터 백업", "DataRecovery": "데이터 복구", "ClearDataCheck": "데이터 감사 비우기", "RestoreFactorySettingsReview": "기본 감사 복원", "SaveMainStation": "마스터 저장", "DataView": "데이터 보기", "DataSync": "데이터 동기화", "RightSet": "권한 구성", "GetUserList": "사용자 목록 가져오기", "AuditData": "감사 데이터", "AuditPermissions": "승인 권한", "MainStationSet": "마스터 설정", "ChangePasswordSelfOnly": "암호 변경 (이 사용자만)", "ChangePasswordForNormal": "일반 사용자 암호 수정", "ChangeUserRight": "사용자 권한 수정 / 설정", "ChangePassword": "암호 수정", "ChangeLoginInfo": "로그인 정보 수정", "UserStatus": "사용자 상태", "UserLanguage": "사용자 언어", "HasLogin": "로그인 여부", "RoleType": "역할 유형", "IsPassTimeout": "암호 시간 초과 여부", "AuditsManagement": "감사 관리", "SensorOperation": "센서 조작", "SensorLogExport": "센서 로그 내보내기", "SensorAlarmDataSync": "센서 경고 데이터 동기화", "ViewSensorAlarmData": "센서 경고 데이터 보기", "Block": "동결", "BlockPendingReview": "동결 - 보류 중", "UnlockPendingReview": "동결해제 - 보류 중", "DeletePendingReview": "삭제 - 보류 중", "AddUserPendingReview": "신규 - 검토 대기 중", "ChangePassPendingReview": "암호 수정 - 보류 중", "ChangeRightPendingReview": "권한 수정 - 보류 중", "abnormal": "이상", "DataQueryNotSupported": "프런트엔드 유형 히스토리 데이터 쿼리는 지원되지 않음", "DeviceCodeExists": "장치 이름 또는 장치 인코딩이 이미 있습니다.", "OutputSwitchConfig": "액세스 스위치 구성", "OutputSwitch": "스위치 연결", "MainStationAuxRtuID": "보조 RtuID", "MainStationIedRtuID": "컬렉션 RtuID", "MainstationParams1": "마스터 매개 변수 1", "MainstationParams2": "마스터 매개 변수 2", "MainstationInterface1": "마스터 1 커넥터", "MainstationInterface2": "마스터 2 커넥터", "Port": "포트", "IPAddress": "IP 주소", "EnterUriTips": "경로 URI를 입력하십시오.", "ConnectUserName": "통신 사용자 이름", "EnterConnectUserNameTips": "통신 사용자 이름을 입력하십시오.", "ConnectPass": "통신 암호", "EnterConnectPassTips": "통신 암호를 입력하십시오.", "DataSubmissionInterval": "데이터 업로드 간격", "EnderDataSBIntervalTips": "데이터 전송 간격을 입력하십시오.", "HeartbeatInterval": "심장 박동 간격", "EnterHertbeatIntervalTips": "하트비트 간격을 입력하십시오.", "ConnectStatus": "접속 상태", "Reset": "재설정", "Submit": "제출", "RtuidRepeatTips": "보조 제어 RtuID 와 어셈블리 RtuID 는 같을 수 없습니다.", "DataSubmissionIntervalTips": "데이터 전송 간격은 60보다 작을 수 없습니다.", "HeartbeatIntervalTips": "심장 박동 간격은 60보다 작으면 안 된다.", "MainstationParamsSaveSuccess": "마스터 매개 변수가 성공적으로 저장되었습니다.", "MainstationParamsSaveFailed": "마스터 매개 변수 저장 실패", "OutSwitchSaveSuccess": "종료 스위치 구성 저장 성공", "OutSwitchSaveFailed": "종료 스위치 구성 저장 실패", "ChartConfig": "스펙트럼 구성", "Measurement": "측정값", "SENSOR_TIMEOUT_COUNT": "센서 오프라인 판단 횟수", "OUTWARD_CONFIG_ENABLE_STATE": "설정 상태", "OUTWARD_CONFIG_ENABLED": "활성화됨", "OUTWARD_CONFIG_DISABLED": "비활성화됨", "OUTWARD_CONFIG_ENABLING": "활성화 중", "SENSOR_TIMEOUT_DISABLING": "비활성화 중", "ERROR_NO_CONFIG": "설정이 존재하지 않습니다", "ERROR_INPUT_PARAMS": "입력 매개변수 오류", "ERROR_INTEGER": "정수를 입력하세요", "ERROR_TIP_RANGE": "값 범위 [{1}~{2}]", "ERROR_IP_FORMAT": "IP 형식이 올바르지 않습니다", "ERROR_FILE_NAME": "파일 이름은 {1}이어야 합니다", "ERROR_FILE_SUFFIX": "파일 확장자는 {1}이어야 합니다", "ERROR_FILE_SIZE_LESS_THAN_MB": "파일은 {1} MB보다 작아야 합니다", "ERROR_T2_T1": "T2는 T1보다 작아야 합니다", "LENGTH_COMM_LIMIT": "이 필드는 {1}자를 초과할 수 없습니다.", "MANU_FAST_SYNC_DATA": "빠른 동기화", "SYNC_DATA_STATE_LIST": "센서 동기화 상태 목록", "LAST_SYNC_DATE_RANGE": "마지막 동기화 날짜 범위", "NoError": "오류 없음", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "동기화 시간 초과", "SYNC_DATA_ERROR_DISCONNECT": "연결 끊김", "SYNC_DATA_STATUS_WAITING": "연결 대기 중", "SYNC_DATA_STATUS_CONNECTED": "연결됨", "SYNC_DATA_STATUS_SYNCING": "동기화 중", "SYNC_DATA_STATUS_SUCCESS": "동기화 성공", "SYNC_DATA_STATUS_FAILED": "동기화 실패", "SYNC_DATA_STATUS_CANCELED": "동기화 취소됨", "Today": "오늘", "Yesterday": "어제", "LAST_7_DAYS": "지난 7일", "LAST_30_DAYS": "지난 30일", "THIS_MONTH": "이번 달", "LAST_MONTH": "지난 달", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "통신 서비스 시작 실패", "FastSyncDataCancelTips": "데이터 동기화를 취소하시겠습니까?", "FastSyncDataSelectTips": "데이터 동기화를 위한 센서를 선택하세요", "Band-pass": "대역 통과", "aeWaveChartTitle": "AE 파형 스펙트럼", "aePhaseChartTitle": "AE 위상 스펙트럼", "aeFlyChartTitle": "AE 비행 스펙트럼", "LOCAL_PARAMS_TIME": "시간", "LOCAL_CHART_COLORDENSITY": "색상 밀도", "openTime": "열림 시간", "closeTime": "닫힘 시간", "triggerUnit": "트리거 진폭[μV]", "openTimeUnit": "열림 시간[μs]", "closeTimeUnit": "닫힘 시간[μs]", "triggerUnitTips": "트리거 범위: [{1}, {2}]]μV", "TOP_DISCHARGE_AMPLITUDE": "TOP3 방전 진폭", "WS_901_ERROR_TIPS": "명령 푸시 서비스가 등록 중입니다. 2초 후에 다시 시도해 주세요."}