{"loginTime": "登录时间", "changerUser": "用户切换", "change": "切换", "login": "登录", "systemShutDownConfirmTip": "系统将在1分钟内关机，是否取消操作？", "systemShutDownTip": "1分钟时间到，自动关机！", "systemShutDownCancel": "取消关机", "loadingData": "正在努力地加载数据中，请稍候……", "pageSize": "每页 {1} 条", "pageSelecter": "显示：{1}-{2}，共 {3} 条", "search": "搜索", "noMatch": "没有找到匹配的记录", "index": "序号", "type": "类型", "aduId": "编码", "connectedState": "网络状态", "commType": "通讯方式", "loraSignalLevel": "LoRa信号强度", "loraSNR": "信噪比", "powerSupplyMode": "供电方式", "byBattery": "内置电池", "byCable": "外接电源", "battaryPercent": "电量", "battaryLife": "续航时间(天)", "deviceNameTable": "设备名称", "pointType": "传感器", "isconnected": "状态", "onlineState": "在线", "offlineState": "失联", "aduNeverConnectedState": "未连接", "updateTime": "更新时间", "data": "数据", "chart": "图谱", "operate": "操作", "lookUp": "查看", "tbSampleDate": "采集时间", "collect": "采集", "errorTip": "错误：{1}", "AEamp": "AE幅值", "aeChartTitle": "AE幅值图谱", "humidityChartTitle": "湿度曲线", "temperatureChartTitle": "温度曲线", "noiseChartTitle": "噪声曲线", "coreGroundingCurrentChartTitle": "铁芯接地电流曲线", "TEVamp": "TEV幅值", "TEVYunit": "单位[dB]", "UnitFomat": "单位[{1}]", "unitParse": "单位[{1}]", "maxValue": "最大值", "minValue": "最小值", "average": "平均值", "AT&T": "美国AT&T", "China Unicom": "中国联通", "China Mobile": "中国移动", "Transformer": "变压器", "Breaker": "断路器", "Disconnector": "隔离开关", "Isolator": "刀闸", "Arrester": "避雷器", "PT": "电压互感器", "CT": "电流互感器", "Busbar": "母线", "Circuit": "母联", "Switchgear": "开关柜", "Power Cable": "电力电缆", "Lightning Rod": "避雷针", "Wall Bushing": "穿墙套管", "Reactor": "电抗器", "Electric Conductor": "电力导线（导流排）", "Power Capacitor": "电力电容器", "Discharge Coil": "放电线圈", "Load Switch": "负荷开关", "Grounding Transformer": "接地变", "Grounding Resistance": "接地电阻", "Grounding Grid": "接地网", "Combined Filter": "结合滤波器", "Insulator": "绝缘子", "Coupling Capacitor": "耦合电容器", "Cabinet": "屏柜", "Other": "其他", "Fuse": "熔断器", "Using Transformer": "所用变", "Arc Suppression Device": "消弧装置", "Main Transformer": "主变压器", "Wave Trap": "阻波器", "Combined Electric Appliance": "组合电器", "Combined Transformer": "组合互感器", "monitor": "实时监测", "dataSelect": "数据查询", "themeSkin": "皮肤", "themeBlue": "蓝色", "settings": "系统配置", "datetime_set": "时间设置", "language_set": "语言设置", "soft_setting": "系统设置", "file_manage": "档案管理", "instrument_set": "传感器配置", "file_point_set": "测点配置", "alarm_param_set": "报警配置", "alarm_manager": "报警管理", "audit_view": "审计查看", "modbus_setting": "Modbus设置", "hardware_update": "固件更新", "collect_mode": "采集模式", "net_set": "网络设置", "main_station_params": "主站配置", "sync_data": "同步数据", "syncingData": "正在同步数据...", "syncingDataCancel": "取消同步", "syncingDataFailed": "同步失败", "syncingDataSuccess": "同步数据完成", "syncingDataProgress": "同步数据进度：{1}", "syncingDataProgress2": "同步数据进度[{1}]", "export_data": "导出数据", "system_info": "系统信息", "monitoringTable": "数据表", "PD": "内置局放传感器", "PD_THREE": "内置局放传感器（三合一）", "PD_FIVE": "内置局放传感器（五合一）", "OLD_IS": "特高频智能传感器", "MEU": "机械特性传感器", "UHF_IS": "特高频智能传感器", "HFCT_IS": "高频智能传感器", "PD_IS": "外置三合一智能传感器", "TRANSFORMER_AE_IS": "变压器超声传感器", "GIS_AE_IS": "GIS超声传感器", "ENV_IS": "环境智能传感器", "Arrester_U_IS": "避雷器电压智能传感器", "Arrester_I_IS": "避雷器电流智能传感器", "LeakageCurrent_IS": "泄漏电流智能传感器", "Vibration_IS": "振动智能传感器", "MECH_IS": "机械特性智能监视仪", "TEMP_HUM_IS": "温湿度传感器", "GrounddingCurrent_IS": "接地电流智能传感器", "MECH_PD_TEMP": "内置局放机械特性温度三合一传感器", "GIS_LOWTEN": "低压传感器", "CHANNEL_OPTICAL_TEMP": "光纤测温传感器", "VaisalaDTP145": "维萨拉DTP145", "Wika_GDT20": "威卡GDT20", "Wika_GDHT20": "威卡GDHT20", "SHQiuqi_SC75D_SF6": "上海虬祺SC75D", "SPTR_IS": "铁芯接地电流传感器", "TEMPFC_OUT": "温度传感器（CLMD）", "TEMPXP_OUT": "温度传感器（SPS061）", "SF6YN_OUT": "SF6气体压力传感器（FTP-18）", "FLOOD_IS": "水浸智能传感器", "TEMPKY_OUT": "温度传感器（RFC-01）", "SMOKERK_OUT": "烟感报警器(RS-YG-N01)", "HIKVIDEO_OUT": "视频传感器（HIK）", "TEMPSB_OUT": "温度传感器（DS18B20）", "TEMP_HUM_JDRK_OUT": "温湿度传感器（RS-WS-N01-2）", "SF6_OUT": "SF6&O2传感器（WFS-S1P-SO）", "FAN_OUT": "风机控制器", "LIGHT_OUT": "照明控制器", "NOISE_JDRK_OUT": "噪声传感器（RS-WS-N01）", "IR_DETECTION_OUT": "红外双鉴探测器", "TEMPWS_OUT": "温湿度传感器(WS-DMC100)", "FLOODWS_OUT": "水浸传感器(WS-DMC100)", "NOISEWS_OUT": "噪声传感器(WS-DMC100)", "VibrationSY_OUT": "昇阳振动传感器", "TEVPRPS_IS": "暂态地电压智能传感器", "SF6_IS": "SF6智能传感器", "phase": "  相位", "period": "  周期", "AMP": "  幅值", "RMS": "  有效值", "max": "  周期最大值", "fre1Value": "频率成分1", "fre2Value": "频率成分2", "All-pass": "全通", "Low-pass": "低通", "High-pass": "高通", "No-Record": "未记录", "TEV": "暂态地电压", "AE": "超声波", "TEMP": "温度", "HFCT": "高频电流", "UHF": "特高频", "Humidity": "湿度", "Decibels": "分贝", "Noisy": "声压", "Noise": "噪声", "ENVGas": "大气", "envgas": "大气趋势", "MP": "机械特性", "FLOOD": "水浸", "SMOKE": "烟感", "SF6": "六氟化硫", "FAN": "风机", "LIGHT": "照明", "NOISE": "噪声", "IRDETECTION": "红外", "SF6YN": "SF6 气体压力", "ArresterU": "避雷器电压", "ArresterI": "避雷器电流", "LeakageCurrent": "泄露电流", "Vibration": "振动", "FrostPointRaw": "霜点", "FrostPointATM": "霜点(标准大气压)", "DewPointRaw": "露点", "DewPointATM": "露点(标准大气压)", "Moisture": "微水", "AbsolutePressure": "绝对压力", "NormalPressure": "标准压力", "Density": "密度", "Oxygen": "氧含量", "SPTR": "铁芯接地电流", "GrounddingCurrent": "接地电流", "VIDEO": "视频图片", "TEVPRPS": "暂态地电压PRPS", "confirm": "确定", "close": "关闭", "yes": "是", "no": "否", "continue": "继续", "none": "无", "DISK": "存储", "MEMORY": "内存", "noTestData": "无测试数据", "connected": "连接", "disconnected": "断开", "virtualKeyBord": "虚拟键盘", "pleaseInput": "请输入", "pleaseInputFormat": "请输入 {1}.", "tips": "提示", "confirmTips": "确认提示", "getDirFailed": "获取目录失败：", "backupSuccessTip": "数据数据已成功备份，请至{1}目录下查看", "backupFailedTip": "数据备份失败", "exportFailedTip": "数据导出失败：{1}", "historyDataTypeNotSuport": "暂不支持该传感器类型历史数据查询", "stopExportTips": "是否需要停止导出数据?", "stationNameTips": "请填写站点名称", "powerUnitNameTips": "请填写电力单位", "selectDeviceTips": "请选择一次设备", "selectPointTips": "请选择测点", "selectDeviceOfPointTips": "请选择需要添加测点的一次设备", "saveSuccess": "保存成功", "saveFailed": "保存失败", "operatFailed": "操作失败：{1}", "chosseSensorUpdate": "请选择需要更新的传感器", "chooseFileUpdate": "请选择更新文件", "cancelUpdateSensorConfirm": "是否确认取消更新传感器程序?", "enterServerUrlTips": "请输入服务器地址 ", "enterServerUrlError": "服务器地址输入错误，请重新输入.", "enterServerPortTips": "请输入服务器端口.", "enterServerPortError": "服务器端口输入错误，端口输入范围： [1,65535].", "NTPserverIpError": "对时服务器地址输入错误，请重新输入.", "NTPserverPortError": "对时服务器端口输入错误，端口输入范围： [1,65535].", "enterNetName": "请输入网络名称.", "enterIP": "请输入IP.", "enterIPError": "IP地址输入错误，如: ***********", "enterNetMask": "请输入子网掩码", "enterNetError": "子网掩码输入错误，如: *************", "enterGateWay": "请输入网关", "enterGateWayError": "配置网关错误, 如: ***********", "enterAPN": "请选择APN", "pdSampleIntervalTip": "PD-采样间隔在20s至594099s(99h99m99s)之间", "meuSampleIntervalTip": "MEU-采样间隔在10s至86400之间", "monitorAwakeTimeTip": "休眠间隔在1s至86400之间", "monitorSleepSpaceTip": "苏醒间隔范围： [{1},{2}]min", "uploadIntervalTip": "上传间隔在60s至86400之间", "sampleIntervalTip": "采样间隔在1小时至72小时之间的整数", "monitorSampleTimeTip": "主机起始采样时刻在0点至23点之间的整点时刻", "monitorSampleIntervalTip": "主机采样间隔在1小时至168小时之间的整数", "updatingSensor": "正在更新传感器...", "updatingSensorProgress": "传感器更新进度:{1}", "updatingSensorProgress2": "传感器更新进度[{1}]", "selectInstTip": "请选择需要修改的传感器", "selectChannelTip": "请选择需要修改的通道", "instCodeTip": "传感器编码不能为空", "commLinkTypeTip": "请选择通讯链路类型", "commLinkPortTip": "请选择通讯端口", "continuSampTimeTip": "连续采集时间在{1}min至{2}min之间。", "continuSampTimeCompareTip": "连续采集时间应小于采样间隔。", "instSampleSpaceTip": "采样间隔在{1}min至{2}min之间。", "instSampleStartTimeTip": "起始采样时刻在0点至23点之间的整点时刻。", "instNumberPattenTip": " 只能包含0-9数字、a-zA-Z字母及 ：.", "instIPPattenTip": "IP及端口格式如：0.0.0.0:1", "deleteInstTip": "请选择需要删除的传感器", "selectSensorTip": "请选择传感器", "deleteInstConfirmTip": "确定删除</br>{1}</br>传感器编码:{2}？", "activeInstConfigTip": "确定将此配置应用到</br>{1} 的同类传感器？", "activeBatchConfigsSuccess": "批量配置传感器成功", "activeBatchConfigsBegin": "批量配置传感器开始", "activeBatchConfigsFailed": "批量配置传感器失败", "collectStartOnceTip": "确定开始采集</br>{1} 同类传感器的数据？", "collectStartTip": "确定开始采集</br>{1}</br>传感器编码:{2} 的数据？", "wakeUpInstrumentOnceTip": "确定开始唤醒</br>{1} 同类传感器？", "wakeUpInstrumentTip": "确定开始唤醒</br>{1}</br>传感器:{2} ？", "emptySensorDataOnceTip": "确定清空</br>{1} 同类传感器的所有数据？", "emptySensorDataTip": "确定清空</br>{1}</br>传感器编码:{2} 的所有数据？", "emptySensorDataSuccess": "传感器数据清除成功", "emptySensorDataFailed": "传感器数据清除失败：", "ae_chart": "实时监测-AE图谱", "uhf_chart": "实时监测-UHF图谱", "hfct_chart": "实时监测-HFCT图谱", "tev_chart": "实时监测-TEV幅值", "tev_prps_chart": "实时监测-暂态地电压PRPS图谱", "temperature_chart": "实时监测-温度", "humidity_chart": "实时监测-湿度", "mechanical_chart": "实时监测-机械特性", "arrester_chart": "实时监测-避雷器", "leakage_current_chart": "实时监测-泄漏电流", "grounddingcurrent_chart": "实时监测-接地电流", "vibration_pickup_chart": "实时监测-振动", "density_micro_water_history": "实时监测-密度微水", "core_grounding_current": "实时监测-铁芯接地电流", "noise_chart": "实时监测-噪声", "water_immersion": "实时监测-水浸", "smoke_sensor": "实时监测-烟感", "video_sensor": "实时监测-视频", "sf6_sensor": "实时监测-SF6", "error": "错误", "warning": "警告", "success": "成功", "userErrorTip": "用户名或密码错误", "errorNoDeviceId": "设备ID不存在", "errorNoSensorId": "传感器ID不存在", "errorExistSensorId": "传感器ID已存在", "errorNoChannelId": "通道ID不存在", "errorNoPointId": "测点不存在", "errorDataExportErrDir": "数据导出，文件路径错误。", "errorDataExportErrTime": "数据导出 时间错误", "errorDataExportNoData": "数据导出，无数据。", "errorDataExportNoSpace": "数据导出，U盘已满", "errorDataExportNoDisk": "数据导出，无U盘。", "errorDataExportNoInfo": "数据导出，无站点信息。", "errorDataExportOther": "数据导出，其他错误。", "errorSetModeBus": "modebus设置错误", "errorSameNamePoint": "同名测点已存在", "errorIP": "IP错误", "errorGPRSSet": "GPRS设置错误", "errorSenorUpdateErrFile": "传感器更新文件错误", "errorSenorUpdateNotMatch": "传感器更新，传感器类型与升级文件不匹配", "errorSenorUpdating": "传感器更新，正在更新", "errorSenorUpdateSizeCheckFailed": "固件大小校验失败", "errorSenorUpdateVersionCheckFailed": "固件版本号校验失败", "errorSenorUpdateDeviceTypeCheckFailed": "固件设备类型校验失败", "errorSenorUpdateCRCCheckFailed": "固件CRC校验失败", "errorSenorUpdateFailed": "传感器升级失败", "errorSenorUpdateConnectErr": "固件更新通讯异常", "errorDataCleanFailed": "数据清空失败！", "errorCodeWorkGroupNotExist": "工作组不存在！", "errorCodeInvalidChannel": "非法通道！", "errorCodeInvalidWirignMode": "非法接线方式！", "errorCodeInvalidAlarmMode": "非法报警方式！", "errorNoPermission": "该用户无此权限", "illegalUser": "非法用户!", "legalPattenMsg": " 只能包含字符（不含空格）：汉字 字母 数字 罗马数字Ⅰ-Ⅻ ` . - () [] # 、 _", "groundCurrent": "接地电流趋势图", "groundCurrentA": "A相接地电流", "groundCurrentB": "B相接地电流", "groundCurrentC": "C相接地电流", "leakageCurrent": "全电流趋势图", "leakageCurrentA": "A相全电流", "leakageCurrentB": "B相全电流", "leakageCurrentC": "C相全电流", "ResistiveCurrent": "阻性电流趋势图", "ResistiveCurrentA": "A相阻性电流", "ResistiveCurrentB": "B相阻性电流", "ResistiveCurrentC": "C相阻性电流", "resistiveCurrentA": "A相阻性电流", "resistiveCurrentB": "B相阻性电流", "resistiveCurrentC": "C相阻性电流", "referenceVoltageA": "A相参考电压", "referenceVoltageB": "B相参考电压", "referenceVoltageC": "C相参考电压", "grounddingCurrent": "接地电流趋势图", "grounddingCurrentA": "A相接地电流", "grounddingCurrentB": "B相接地电流", "grounddingCurrentC": "C相接地电流", "timeDomain": "时域图", "frequencyDomain": "频域图", "characterParam": "特征参数", "TimeDomainDataX": "X轴振动信号", "TimeDomainDataY": "Y轴振动信号", "TimeDomainDataZ": "Z轴振动信号", "FrequencyDomainDataX": "振动信号频谱图-X轴", "FrequencyDomainDataY": "振动信号频谱图-Y轴", "FrequencyDomainDataZ": "振动信号频谱图-Z轴", "ACCAVGX": "X轴加速度平均值", "ACCAVGY": "Y轴加速度平均值", "ACCAVGZ": "Z轴加速度平均值", "ACCMAXX": "X轴加速度最大值", "ACCMAXY": "Y轴加速度最大值", "ACCMAXZ": "Z轴加速度最大值", "AMPAVGX": "X轴振幅平均值", "AMPAVGY": "Y轴振幅平均值", "AMPAVGZ": "Z轴振幅平均值", "AMPMAXX": "X轴振幅最大值", "AMPMAXY": "Y轴振幅最大值", "AMPMAXZ": "Z轴振幅最大值", "MAXFreqX0": "X轴极值点频率1", "MAXFreqY0": "Y轴极值点频率1", "MAXFreqZ0": "Z轴极值点频率1", "MAXFreqX1": "X轴极值点频率2", "MAXFreqY1": "Y轴极值点频率2", "MAXFreqZ1": "Z轴极值点频率2", "MAXFreqX2": "X轴极值点频率3", "MAXFreqY2": "Y轴极值点频率3", "MAXFreqZ2": "Z轴极值点频率3", "sensorType": "传感器类型", "sensorList": "传感器列表", "gain": "增益", "trigger": "触发幅值", "wave_filter": "频带", "sample_date": "采样日期", "sample_time": "采样时间", "rms": "有效值", "cycle_max": "周期最大值", "frequency1": "频率成分1", "frequency2": "频率成分2", "time_interval": "时间间隔", "realData": "实时数据", "historyData": "历史数据", "trendSync": "趋势分析", "preData": "上一条", "nextData": "下一条", "systemDate": "系统日期", "systemTime": "系统时间", "backupType": "备份类型", "plusBackup": "增量备份", "allBackup": "全量备份", "timeRange": "时间范围", "exportPath": "导出路径", "getExportDir": "获取目录", "checkDir": "检查目录", "exportingData": "正在导出...", "cancel": "取消", "export": "导出", "stationConfig": "站点配置", "stationDelSuccess": "站点删除成功", "stationDelFailed": "站点删除失败：", "deviceConfig": "一次设备配置", "pointConfig": "测点配置", "stationName": "站点名称", "stationPMS": "站点编码", "stationLevel": "站点电压等级", "powerUnit": "电力单位", "save": "保存", "operationSuccess": "操作成功", "deviceAddSuccessTips": "一次设备增加成功", "deviceEditSuccessTips": "一次设备修改成功", "deviceDelSuccessTips": "一次设备删除成功", "pointAddSuccessTips": "测点增加成功", "pointEditSuccessTips": "测点修改成功", "pointDelSuccessTips": "测点删除成功", "deleteFailedTips": "删除失败", "save_add": "保存/增加", "delete": "删除", "deviceType": "设备类型", "deviceLevel": "设备电压等级", "add": "增加", "channelTypeAlarm": "数据类型", "alarmThreshold": "报警阈值", "alarmRecoveryThreshold": "报警恢复阈值", "alarmChannel": "报警通道选择", "built_in_channel_1": "内置通道1", "External_IO_module": "外置IO模块", "not_associated": "不关联", "alarmExternalIOSN": "外置IO模块编码", "alarmExternalIOChannel": "外置IO模块通道", "wiringMode": "接线方式", "alarmMode": "报警方式", "alarmTime": "报警定时", "alarmInterval": "报警间隔", "alarmDuration": "报警时长", "normal_open": "常开", "normal_close": "常闭", "continuous": "连续", "timing": "定时", "interval": "间隔", "alarmSaveSuccess": "报警配置保存成功", "alarmDelSuccess": "报警配置删除成功", "deviceName": "一次设备名称", "pointName": "测点名称", "testStation": "测试站点", "device": "一次设备", "pointList": "测点列表", "sensorID": "传感器ID", "sensorChannel": "传感器通道", "channelList": "通道列表", "showPoint": "显示测点", "fileSelect": "文件选择", "selectPlease": "请选择", "deviceList": "传感器列表", "updating": "正在更新...", "updatingFailed": "更新失败：", "updatingCanceled": "取消更新", "updatingComplete": "完成更新", "update": "更新", "sampleDate": "采样日期", "sampleTime": "采样时间", "pdMax": "最大放电幅值", "pdAvg": "平均放电幅值", "pdNum": "脉冲个数", "acquisitionTime": "采集时间", "humidity": "湿度", "startDate": "起始日期", "endDate": "结束日期", "refresh": "刷新", "scanData": "查询", "sensorCode": "传感器编码", "sensorTypeSet": "传感器类型", "senorName": "传感器名称", "aduAddress": "传感器地址", "senorWorkMode": "主动上送", "On": "开", "Off": "关", "ADUMode": "工作模式", "normalMode": "维护模式", "lowPowerMode": "低功耗模式", "monitorMode": "监测模式", "artificialStateStartTime": "人工干预状态开始时刻（整点）", "artificialStateStartTimeTip": "人工干预状态开始时刻范围0-23", "artificialStateEndTime": "人工干预状态结束时刻（整点）", "artificialStateEndTimeTip": "人工干预状态结束时刻范围0-23", "artificialStateWakeUpSpace": "人工干预状态苏醒间隔（分）", "unartificialStateWakeUpSpace": "非人工干预状态苏醒间隔（分）", "isAutoChangeMode": "是否自动切换模式", "monitorModeSampleSapce": "监测模式采集间隔", "workGroup": "工作组号", "warnTemp": "告警温度", "taskGroup": "任务组号", "commLinkType": "通讯链路类型", "commLinkPort": "通讯端口", "frequencyUnit": "电网频率(Hz)", "numInGroup": "组内编号", "commSpeed": "通讯速率", "commLoad": "通讯信道", "sampleSpace": "采样间隔(min)", "sleepTime": "功耗策略", "samleStartTime": "起始采样时间(整点)", "applyData": "操作", "applyAllSensor": "应用到同种", "collectOnce": "批量采集", "collectOnceEnd": "数据采集结束", "collectOnceProgress": "数据采集：{1} ({2})", "collectOnceProgress2": "数据采集[{1}]({2})", "activeOnce": "批量应用", "wakeUp": "唤醒", "wakeUpInstrument": "唤醒传感器", "wakeUpInstrumentOnce": "批量唤醒传感器", "orderSend": "命令已下发", "cleanData": "清空数据", "sensorAddSuccess": "传感器增加成功", "sensorEditSuccess": "传感器修改成功", "sensorEditBegin": "传感器修改开始", "sensorDelSuccess": "传感器删除成功", "cleanSensorData": "清空传感器数据", "getSensor": "获取传感器", "channelType": "通道类型", "channelName": "通道名称", "channelInfoSaveSuccess": "通道信息保存成功", "channelInfoSaveBegin": "传感器通道信息保存开始", "bias": "偏置[dB]", "waveFilter": "频带设置", "gainMode": "增益模式", "gain_unit": "增益[dB]", "samCycle": "采样周期数", "samPointNum": "每周期采样点数", "samRate": "每周期采样点数", "ratio": "变比", "channelPhase": "相别", "mecLoopCurrentThred": "线圈电流阈值[mA]", "mecMotorCurrentThred": "电机电流阈值[mA]", "mecSwitchState": "开关量初始状态", "awaysOpen": "常开", "awaysClose": "常闭", "mecBreakerType": "断路器机构配置", "oneMech": "三相机械联动（一台机构）", "threeMech": "三相电气联动（三台机构）", "mecMotorFunctionType": "电机工作类型", "threePhaseAsynMotor": "三相异步电机", "onePhaseMotor": "单相交/直流电机", "setCurrent": "设置当前", "setAll": "应用到同种传感器", "showCoil": "线圈电流图谱", "showSwitch": "开关量图谱", "showMotor": "电机电流图谱", "showOrig": "原始电流图谱", "actionDate": "动作日期", "actionTime": "动作时间", "phaseA": "A相", "phaseB": "B相", "phaseC": "C相", "coil_charge_time": "线圈通电时间", "coil_cutout_time": "线圈断电时间", "max_current": "线圈最大电流", "hit_time": "掣子脱扣时间", "subswitch_close_time": "辅助开关切换时间", "a_close_time": "A相合闸时间", "a_close_coil_charge_time": "A相线圈通电时间", "a_close_coil_cutout_time": "A相线圈断电时间", "a_close_max_current": "A相合闸线圈最大电流", "a_close_hit_time": "A相合闸掣子脱扣时间", "a_close_subswitch_close_time": "A相辅助开关切换时间", "b_close_time": "B相合闸时间", "b_close_coil_charge_time": "B相线圈通电时间", "b_close_coil_cutout_time": "B相线圈断电时间", "b_close_max_current": "B相合闸线圈最大电流", "b_close_hit_time": "B相合闸掣子脱扣时间", "b_close_subswitch_close_time": "B相辅助开关切换时间", "c_close_time": "C相合闸时间", "c_close_coil_charge_time": "C相线圈通电时间", "c_close_coil_cutout_time": "C相线圈断电时间", "c_close_max_current": "C相合闸线圈最大电流", "c_close_hit_time": "C相合闸掣子脱扣时间", "c_close_subswitch_close_time": "C相辅助开关切换时间", "close_sync": "合闸同期性", "close_time": "合闸时间", "a_open_time": "A相分闸时间", "a_open_coil_charge_time": "A相线圈通电时间", "a_open_coil_cutout_time": "A相线圈断电时间", "a_open_max_current": "A相分闸线圈最大电流", "a_open_hit_time": "A相分闸掣子脱扣时间", "a_open_subswitch_close_time": "A相辅助开关切换时间", "b_open_time": "B相分闸时间", "b_open_coil_charge_time": "B相线圈通电时间", "b_open_coil_cutout_time": "B相线圈断电时间", "b_open_max_current": "B相分闸线圈最大电流", "b_open_hit_time": "B相分闸掣子脱扣时间", "b_open_subswitch_close_time": "B相辅助开关切换时间", "c_open_time": "C相分闸时间", "c_open_coil_charge_time": "C相线圈通电时间", "c_open_coil_cutout_time": "C相线圈断电时间", "c_open_max_current": "C相分闸线圈最大电流", "c_open_hit_time": "C相分闸掣子脱扣时间", "c_open_subswitch_close_time": "C相辅助开关切换时间", "open_sync": "分闸同期性", "open_time": "分闸时间", "a_twice_open_time": "A相二次分闸时间", "a_twice_open_coil_charge_time": "A相二次分闸线圈带电时刻", "a_twice_open_coil_cutout_time": "A相二次分闸线圈断电时刻", "a_twice_open_max_current": "A相二次分闸线圈最大电流", "a_twice_open_hit_time": "A相二分掣子脱扣时间", "a_twice_open_subswitch_close_time": "A相二次分闸辅助开关断开时刻", "b_twice_open_time": "B相二次分闸时间", "b_twice_open_coil_cutout_time": "B相二次分闸线圈断电时刻", "b_twice_open_max_current": "B相二次分闸线圈最大电流", "b_twice_open_hit_time": "B相二分掣子脱扣时间", "b_twice_open_subswitch_close_time": "B相二次分闸辅助开关断开时刻", "c_twice_open_time": "C相二次分闸时间", "c_twice_open_coil_cutout_time": "C相二次分闸线圈断电时刻", "c_twice_open_max_current": "C相二次分闸线圈最大电流", "c_twice_open_hit_time": "C相二分掣子脱扣时间", "c_twice_open_subswitch_close_time": "C相二次分闸辅助开关断开时刻", "twice_open_sync": "二次分闸同期性", "twice_open_time_text": "二次分闸时间", "a_switch_shot_time": "A相金短时间", "b_switch_shot_time": "B相金短时间", "c_switch_shot_time": "C相金短时间", "a_switch_no_current_time": "A相无电流时间", "b_switch_no_current_time": "B相无电流时间", "c_switch_no_current_time": "C相无电流时间", "motor_start_current": "电机启动电流", "motor_max_current": "电机最大电流", "storage_time": "电机储能时间", "Chan_A_motor_start_current": "A相电机启动电流", "Chan_A_motor_max_current": "A相电机最大电流", "Chan_A_storage_time": "A相电机储能时间", "Chan_B_motor_start_current": "B相电机启动电流", "Chan_B_motor_max_current": "B相电机最大电流", "Chan_B_storage_time": "B相电机储能时间", "Chan_C_motor_start_current": "C相电机启动电流", "Chan_C_motor_max_current": "C相电机最大电流", "Chan_C_storage_time": "C相电机储能时间", "serialPort": "串口端口", "baudRate": "波特率", "dataBit": "数据位", "stopBit": "停止位", "checkBit": "校验位", "singleCollect": "批量采集", "sampling": "正在采集...", "serverIP": "服务器IP", "serverPort": "服务器端口", "NTPserverIp": "NTP服务器IP", "NTPserverPort": "NTP服务器端口", "netType": "网络类型", "deviceIp": "主机IP", "subnetMask": "子网掩码", "gateway": "默认网关", "networkAPN": "网络接入APN", "userName": "用户名", "passWord": "密码", "deviceWorkGroup": "主机工作组号", "frequency": "电网频率", "pdSampleInterval": "PD-采样间隔", "spaceSecond": "&nbsp;秒", "meuSampleInterval": "MEU-采样间隔", "monitorAwakeTime": "休眠间隔", "monitorSleepSpace": "苏醒间隔", "wakeStartTime": "起始苏醒时间（整点）", "intervalServer": "上传间隔（服务器）", "intervalMinus": "采样间隔", "continuousAcquisitionTime": "连续采集时间", "startSampleTime": "主机起始采样时刻", "spacePoint": "&nbsp;点", "startSampleInterval": "主机起始采样间隔", "hour": "小时", "poerOffSpace": "关机间隔", "powerOnSpace": "开机间隔", "dateRange": "日期范围", "synchronizing": "正在同步...", "synchronize": "同步", "amplitude": "幅值", "switch": "切换", "copyRight": "©2020 PMDT Ltd.", "S1010Name": "数据采集主机", "modbusCompanySelf": "华乘", "logOut": "退出", "logOutTitle": "登出确认", "logOutConfirm": "退出当前用户登录？", "logOutTips": "已退出登录", "enterHost": "请输入主机名称", "selectDevTip": "请选择设备", "stopSyncDataTip": "是否需要停止同步数据?", "modbusAddress": "ModBus地址", "modbusAddressCheckTip": "ModBus地址范围为1~254整数", "deviceWorkGroupCheckTip": "主机工作组号为整数,范围:[{1}~{2}]", "emptyMonitorDatas": "清空主机数据", "emptyMonitorDatasTip": "清空采集主机内所有历史数据", "emptyMonitorDatasSuccess": "清空数据成功", "emptyMonitorDatasApply": "清空数据申请已提交，待审核", "resetSoftSet": "恢复出厂设置", "resetSoftSetTip": "将采集主机内所有配置恢复至出厂状态", "resetSoftSetSuccess": "恢复出厂设置申请已提交，待审核", "continueConfirmTip": "该操作不可恢复，还要继续吗？", "bias_data": "偏置值范围为-100-100", "back_ground_data": "背景值范围为0-100", "sam_point": "每周期采样频率范围为20-2000", "sam_rate": "每周期采样频率范围为20-2000", "smartSensorStatus": "状态表", "upThreshold": "阈值上限", "lowerThreshold": "阈值下限", "changeThreshold": "变化阈值", "changeThresholdLimit": "阈值设置仅支持一位小数", "upThresholdTip": "阈值上限范围", "lowerThresholdTip": "阈值下限范围", "changeThresholdTip": "变化阈值范围", "upLowerThresholderrTip": "阈值下限不能大于阈值上限", "monitorName": "主机名称", "monitorType": "主机类型", "MonitorTypeHanging": "壁挂", "MonitorType2U": "2U", "MonitorTypeCollectionNode": "汇集节点", "MonitorTypeLowPower": "低功耗太阳能主机", "devicePMSCode": "设备编码", "forceChange": "模式切换", "forceChangeSuccess": "模式切换成功", "currentVersion": "当前版本", "abnormalRecover": "异常自恢复", "fromLabel": "起始时间", "toLabel": "结束时间", "select": "选择", "sunday": "日", "monday": "一", "tuesday": "二", "wednesday": "三", "thursday": "四", "friday": "五", "saturday": "六", "January": "一月", "February": "二月", "March": "三月", "April": "四月", "May": "五月", "June": "六月", "July": "七月", "August": "八月", "September": "九月", "October": "十月", "November": "十一月", "December": "十二月", "all": "全部", "strong": "强", "weak": "弱", "poor": "差", "min": "分", "second": "秒", "uploadInterval": "上传间隔(min)", "loginWelcome": "欢迎登录", "dateStartIsAfterDateEnd": "起始日期大于结束日期，请重新选择", "maxCurrent": "最大电流", "standMobus": "传感器厂家", "config1": "配置 1", "config2": "配置 2", "fWrnThreshold": "注意阈值", "fAlmThreshold": "告警阈值", "regularDormancy": "定时休眠", "noDormancy": "不休眠", "soakingWater": "浸水", "dry": "干燥", "normal": "正常", "alarm": "告警", "lightGunCamera": "可见光枪机", "lightBallCamera": "可见光球机", "infraredGunCamera": "红外双目枪机", "infraredBallCamera": "红外双目球机", "maxTemp": "最高温度", "maxTempPosition": "最高温度位置", "devIPError": "该IP已被占用", "unknown": "未知", "fault": "故障", "lowPower": "低电量", "mediumPower": "中电量", "highPower": "高电量", "slaveid": "从机ID", "LoraFrequency": "主机区域", "AREA_CHINA": "中国<1>", "AREA_VIETNAM": "越南(AS1)", "AREA_MALAYSIA": "马来西亚(AS1)", "AREA_EUROPE": "欧洲", "AREA_US": "美洲", "AREA_INDONESIA": "印尼(AS2)", "AREA_INDIA": "印度", "AREA_KOREA": "韩国", "AREA_CHINA_RSV": "中国(备用)<2>", "getLogFileError": "获取传感器日志文件失败 ", "exportLogError": "日志文件不存在，导出失败", "alertSyncProgress": "告警数据同步更新进度:{1}", "alertSyncProgress2": "告警数据同步更新进度[{1}]", "FIRMWARE_EXCEPTION": "固件异常", "AD_INITIALIZATION_EXCEPTION": "AD初始化异常", "REFERENCE_VOLTAGE_EXCEPTION": "参考电压异常", "ONCHIP_FLASH_EXCEPTION": "片内Flash异常", "OFFCHIP_FLASH_EXCEPTION": "片外Flash异常", "SYSTEM_PARAMETERS_EXCEPTION": "系统参数异常", "SAMPLE_PARAMETERS_EXCEPTION": "采集参数异常", "CALIBRATION_PARAMETERS_EXCEPTION": "校准参数异常", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "系统参数异常恢复", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "采集参数异常恢复", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "校准参数异常恢复", "LORA_MODULE_EXCEPTION": "LoRa模块异常", "PHASE_NUM": "相位数", "DISCONNECT_TIME": "断开时间", "DATA_TOTAL_COUNT": "数据总量", "ONLINE_RATE": "在线率", "aduInfoSetProgress": "传感器配置：{1} ({2})", "aduInfoSetProgress2": "传感器配置[{1}]({2})", "aduInfoSetting": "正在配置传感器...", "aduInfoSetEnd": "传感器配置完成", "aduDataSample": "数据采集", "aduInfoSet": "传感器配置", "errorDataSampleRunning": "当前正在进行传感器采集业务，请完成后重试", "errorAduInfoSetRunning": "当前正在进行传感器参数设置业务，请完成后重试", "SAMPLE_PERIOD": "采样周波数", "SF6_Density": "SF6 气体密度(P20)", "SF6_Temp": "SF6 气体温度", "Device_env_Temp": "设备环境温度", "Alarm_Status": "告警状态标识", "SF6_Alarm_Low_Pressure": "低压报警", "SF6_Alarm_Low_Voltage_Block": "低压闭锁", "SF6_Alarm_Over_Voltage": "过压报警", "AlarmType": "告警类型", "AlarmData": "告警内容", "AlarmTime": "告警时间", "AlarmLevel": "报警等级", "AlarmStateWarning": "预警", "WarningValue": "预警值", "AlarmValue": "报警值", "SetSuccess": "设置成功", "AlarmDate": "报警时间", "AlarmConfirm": "报警确认", "Confirm": "确认", "Confirmed": "已确认", "AllData": "所有数据", "CheckManagerSponsor": "发起者", "CheckManagerCheckType": "审计类型", "CheckManagerDate": "发起日期", "CheckManagerTarget": "被发起者", "CheckManagerExtraInfo": "附加信息", "CheckManagerResult": "是否同意", "Refuse": "拒绝", "Agree": "同意", "DataExport": "数据导出", "DataExportStep1": "选择要导出的数据", "DataExportStep1Title": "导出文件选择", "DataExportStep2": "正在计算数据大小...", "DataExportStep2Title": "计算原始数据大小", "DataExportStep3": "正在打包压缩数据...", "DataExportStep3Title": "打包压缩数据", "DataExportStep4": "数据压缩完成", "DataExportStep4Title": "压缩数据完成", "DataExportCheckData": "数据库[/media/data/database]", "DataExportCheckDataFile": "数据文件[/media/data/datafile]", "DataExportCheckLog": "日志文件[/media/data/log]", "DataExportCheckConfig": "配置文件[/home/<USER>/config.xml]", "DataExportBegin": "开始导出", "SelectAtLeastOneTips": "至少选择一项", "DataExportFileSizeError": "文件大小超过2000MB，请使用Winscp等工具导出数据", "DataExportFileSizeZeroError": "选择的文件大小为0", "DataExportCancelTips": "导出数据已取消", "DataExportDataCompressTips": "原始数据大小为{1}MB,预计压缩时间[{2}min {3}s]", "LogExportStep1": "选择传感器", "LogExportStep1Title": "选择传感器", "LogExportStep2": "获取日志文件...", "LogExportStep2Title": "获取日志文件", "LogExportStep3": "获取日志文件完成", "LogExportStep3Title": "获取日志文件完成", "LogExportStep4": "打包压缩日志文件...", "LogExportStep4Title": "打包压缩日志文件", "LogExportStep5": "压缩完成", "LogExportStep5Title": "压缩完成", "LogExportCancelTips": "导出日志已取消", "batteryVoltage": "电池电压", "superCapvoltage": "超级电容电压", "OverPressureThreshold": "过压阈值", "LowPressureThreshold": "低压阈值", "ShutThreshold": "闭锁阈值", "PhysicalChannelType": "物理通道类型", "GasAbsPressure": "气体绝压", "GasGaugePressure": "气体表压", "CameraType": "摄像头类型", "DEFAULT_CHECK_Tips": "编号格式为 m:n ,如 1:100,其中m范围[1-255],n范围[0-65535]", "VibrationSY_OUT_CHECK_Tips": "编号格式为 address:modbus:id ,如 1:0:1917,其中address范围,为[1-255],modbus/id均为[0-65535]", "NOISEWS_OUT_CHECK_Tips": "编号格式为 ip:x:y ,如 *********:1321:4512,其中ip格式[xxx.xxx.xxx.xxx] ,x/y均为长度为4的字符串", "IR_DETECTION_OUT_IO_CHECK_Tips": "编号格式为 ip:port:ss:se:cs:ce ,如 1:100,其中ip格式[xxx.xxx.xxx.xxx] port端口范围[0-65535] ss范围[0-255],se范围0-255],cs范围0-255],ce范围0-255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "编号格式为 ip:port:x:y ,如 1:100,其中ip格式[xxx.xxx.xxx.xxx] port端口范围[0-65535] x范围[1-255],y范围[0-65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "编号格式为 m:n ,如 1:100,其中m范围[1-255],n范围[0-65535]", "fWrnThreshold_CHECK_Tips": "注意阈值范围为[10-1000]mA", "fAlmThreshold_CHECK_1_Tips": "告警阈值范围为[50-5000]mA", "fAlmThreshold_CHECK_2_Tips": "告警阈值应大于注意阈值", "AuditContent": "审计内容", "OPTime": "操作时间", "Executed": "执行", "UserManager": "用户管理", "SystemManagement": "系统管理", "BusinessManagement": "业务管理", "LanguageChange": "语言切换", "AlarmManagement": "告警管理", "DataOperation": "数据操作", "BackupRecovery": "备份恢复", "AddUser": "添加用户", "AddUserCheck": "添加用户审核", "UserRightSet": "用户权限设置", "UserRightSetCheck": "用户权限设置审核", "DeleteUser": "删除用户", "DeleteUserConfirm": "删除用户确认", "FreezeUser": "冻结用户", "FreezeUserConfirm": "冻结用户确认", "UnlockUser": "解锁用户", "UnlockUserConfirm": "解锁用户确认", "FileStationSet": "档案(站点)设置", "FileDeviceSet": "档案(设备)设置", "SenserSet": "传感器设置", "AlarmSet": "告警设置", "SavePointSet": "保存测点设置", "DelPointSet": "删除测点设置", "SingleSample": "单次采集", "DataBackup": "数据备份", "DataRecovery": "数据恢复", "ClearDataCheck": "清空数据审核", "RestoreFactorySettingsReview": "恢复出厂设置审核", "SaveMainStation": "保存主站", "DataView": "数据查看", "DataSync": "数据同步", "RightSet": "权限配置", "GetUserList": "获取用户列表", "AuditData": "审计数据", "AuditPermissions": "审核权限", "MainStationSet": "主站设置", "ChangePasswordSelfOnly": "更改密码（仅本用户）", "ChangePasswordForNormal": "修改普通用户密码", "ChangeUserRight": "修改/设置用户权限", "ChangePassword": "修改密码", "ChangeLoginInfo": "修改登录信息", "UserStatus": "用户状态", "UserLanguage": "用户语言", "HasLogin": "是否登陆过", "RoleType": "角色类型", "IsPassTimeout": "密码是否超时", "AuditsManagement": "审核管理", "SensorOperation": "传感器操作", "SensorLogExport": "日志导出", "SensorAlarmDataSync": "告警数据同步", "ViewSensorAlarmData": "告警数据查看", "Block": "冻结", "BlockPendingReview": "冻结-待审核", "UnlockPendingReview": "解冻-待审核", "DeletePendingReview": "删除-待审核", "AddUserPendingReview": "新增-待审核", "ChangePassPendingReview": "修改密码-待审核", "ChangeRightPendingReview": "修改权限-待审核", "abnormal": "异常", "DataQueryNotSupported": "暂不支持该前端类型历史数据查询", "DeviceCodeExists": "设备名称或设备编码已存在", "OutputSwitchConfig": "接出开关配置", "OutputSwitch": "接出开关", "MainStationAuxRtuID": "辅控RtuID", "MainStationIedRtuID": "汇集RtuID", "MainstationParams1": "主站参数1", "MainstationParams2": "主站参数2", "MainstationInterface1": "主站1接口", "MainstationInterface2": "主站2接口", "Port": "端口", "IPAddress": "IP地址", "EnterUriTips": "请输入路径URI", "ConnectUserName": "通信用户名", "EnterConnectUserNameTips": "请输入通信用户名", "ConnectPass": "通信密码", "EnterConnectPassTips": "请输入通信密码", "DataSubmissionInterval": "数据上送间隔", "EnderDataSBIntervalTips": "请输入数据上送间隔", "HeartbeatInterval": "心跳间隔", "EnterHertbeatIntervalTips": "请输入心跳间隔", "ConnectStatus": "连接状态", "Reset": "重置", "Submit": "提交", "RtuidRepeatTips": "辅控RtuID与汇集RtuID不能一样", "DataSubmissionIntervalTips": "数据上送间隔不能小于60", "HeartbeatIntervalTips": "心跳间隔不能小于60", "MainstationParamsSaveSuccess": "主站参数保存成功", "MainstationParamsSaveFailed": "主站参数保存失败", "OutSwitchSaveSuccess": "接出开关配置保存成功", "OutSwitchSaveFailed": "接出开关配置保存失败", "StateLinkError": "链路异常", "StateConnectError": "通信异常", "StateConnecting": "连接中", "CannotConnectServerMSG": "未连接到服务，10s后重试", "LogOutOfTimeMSG": "登录超时，请重新登录", "PasswordChangedMSG": "密码修改成功,请重新登录", "ServerDisConnectMSG": "服务断开，请重新登录", "GroupNumberScan": "组号扫描", "OldPassword": "旧密码", "OldPasswordError": "旧密码错误", "NewPassword": "新密码", "RepeatNewPassword": "再次输入新密码", "OldPasswordPlaceholder": "请输入旧密码", "PasswordCompareTips": "两次输入的新密码要一致", "PasswordFormatTipes": "新密码格式:至少8个字符，至少1个大写字母，1个小写字母，1个数字和1个特殊字符", "AlarmData1000": "固件CRC计算结果和固件自带的CRC校验结果不一致", "AlarmData1001": "AD初始化错误,AD无法正常工作", "AlarmData1002": "电池电压值为 {1}V", "AlarmData1003": "片内Flash读写错误", "AlarmData1004": "片外Flash读写错误", "AlarmData1005": "存储区未读取到参数", "AlarmData1006": "读取到的参数CRC校验不一致", "AlarmData1007": "读取到的参数超合法范围", "AlarmData1008": "能够正确读取参数，且参数在合法范围内", "AlarmData1009": "初始化返回值错误", "NoiseReductionMode": "降噪模式", "Manual": "手动", "Automatic": "自动", "SensorThreshold": "阈值(%)", "AccessKey": "访问秘钥", "ControlMode": "控制方式", "ConventionalSwitch": "常规开关", "InchingSwitch": "点动开关", "SensorIOPlaceHolder": "IP地址:端口号:状态位1:状态位2:控制位1:控制位2", "TEMPSB_OUT_ID_TIPS": "编号格式为 m:n:o ,如 1:1:100,其中m范围[1-255],n范围[0-255],o范围[0-65535]", "OUT_NORMAL_ID_TIPS": "编号格式为 m:n ,如 1:100,其中m范围[1-255],n范围[0-65535]", "SlaveIDRange": "从机ID范围", "inputNameAndPsw": "请输入用户名和密码", "PreStartCollect": "准备开始采集，请稍后", "RefreshComplet": "刷新完成", "State": "状态", "DeleteSuccess": "删除成功", "DeleteFailed": "删除失败", "DeleteUserConfirmTips": "确认删除用户[{1}]?", "FileSaveFailed": "文件保存失败", "GetConnectFailed": "获取通信链路失败", "WakeUpFailed": "唤醒失败", "ConnectFailed": "连接失败", "CollectDataFailed": "采集数据失败", "UnpackFailed": "解压失败，请检查文件格式", "ConfigFileError": "压缩包未包含数据/配置文件，文件类型错误", "ProgramError": "程序异常", "NoData": "暂无数据", "SensorOptRepeatTips": "以下传感器存在重复操作，请等待操作完成:", "StateOpen": "分闸", "StateClose": "合闸", "Unknown": "未知", "ExportDataTimeRangeLimitTips": "导出数据时间跨度最大为一周", "UserManagerAllowLoginTimeStart": "许可登录时间-开始（整点）'", "UserManagerAllowLoginTimeEnd": "许可登录时间-结束（整点）", "UserManagerAllowLoginIPStart": "许可登录IP-起始", "UserManagerAllowLoginIPEnd": "许可登录IP-结束", "UserManagerErrorCount": "错误尝试次数", "UserManagerMaxErrorCount": "最大尝试次数", "UserManagerPasswordDuration": "密码有效时长", "UserManagerPasswordEffective": "密码生效日期", "Day": "天", "UserManagerInputUserNameTips": "请输入用户名", "UserManagerAddUser": "新增用户", "AddSuccess": "新增成功", "AddFailed": "新增失败", "DataImport": "数据导入", "Upload": "上传", "Uploading": "正在上传", "UploadingMsg": "正在上传文件，请勿进行其他操作", "UploadProgress": "上传进度", "ImportConfigConfirmTips": "执行数据导入需要进行重启，确认继续？", "UploadSuccess": "文件上传成功", "UploadFailed": "文件上传失败", "DataImportSuccess": "数据导入成功", "DataImportSuccessTips": "数据导入成功，即将登出并重启程序，请稍后刷新重试", "SensorGroupNum": "传感器组号", "SensorScanState": "扫描状态", "Complete": "完成", "Failed": "失败", "ScanCancelMsg": "扫描已取消", "GroupNumScaning": "正在扫描组号", "SensorGroupNumScan": "传感器组号扫描", "SensorScanID": "待扫描传感器ID", "GroupNumRange": "组号范围", "PreSensorScan": "准备扫描", "SensorScan": "扫描", "SensorScanResult": "扫描结果", "SensorGroupNumScanEnd": "传感器组号扫描结束", "SensorGroupNumScanCancelTips": "是否取消组号扫描", "SensorGroupNumScanSelectTips": "请选择需要扫描的传感器", "GroupNumStartTips": "组号起始值不能大于组号结束值", "GroupNumEndTips": "组号结束值不能小于组号起始值", "Apply": "应用", "Date": "日期", "SearchType": "查询类型", "ActionType": "动作类型", "ConfirmChange": "是否切换?", "None": "暂无", "ADUProgressInvalid": "无效进度", "ADUProgressBEGIN": "开始", "ADUProgressWAKEUP": "唤醒", "ADUProgressCONNECT": "连接", "ADUProgressREGULATE_SPEED": "调速", "ADUProgressSAMPLE_DATA": "正在采集数据", "ADUProgressSUCCESS": "成功", "ADUProgressFAILED": "失败", "ADUProgressEND": "结束", "ADUProgressWRITE_SYSTEM_PARAM": "写系统参数", "ADUProgressWRITE_WORKER_MODEL_PARAM": "写工作模式参数", "ADUProgressWRITE_ADU_PARAM": "写传感器参数", "ADUProgressWRITE_CHANNEL_PARAM": "写通道参数", "ADUProgressREAD_MONITOR_INFO": "读设备信息", "ADUProgressREAD_SYSTEM_PARAM": "读系统参数", "ADUProgressREAD_CHANNEL_PARAM": "读通道参数", "ADUPRogressWRITE_SYSTEM_PARAM_FAILED": "写系统参数失败", "ADUPRogressWRITE_WORKER_MODEL_PARAM_FAILED": "写工作模式参数失败", "ADUPRogressWRITE_ADU_PARAM_FAILED": "写传感器参数失败", "ADUPRogressWRITE_CHANNEL_PARAM_FAILED": "写通道参数失败", "ADUPRogressREAD_MONITOR_INFO_FAILED": "读设备信息失败", "ADUPRogressREAD_SYSTEM_PARAM_FAILED": "读系统参数失败", "ADUPRogressREAD_CHANNEL_PARAM_FAILED": "读通道参数失败", "ADUPRogressCopyIDTips": "ID [{1}] 已复制", "ADUPRogressMsgIDTips": "进度信息 [{1}]", "CopyID": "复制ID", "Wait": "等待", "SwitchToLine": "切换为折线图", "SwitchToBar": "切换为柱状图图", "ManstationParams1": "主站参数1", "ManstationParams2": "主站参数2", "ConnectionProtocolConfiguration": "接出协议配置", "CommunicatProtocol": "通讯协议", "BusiProtocol": "业务协议", "HeartbeatTransmissionInterval": "心跳上送间隔", "DataUploadInterval": "数据上送间隔", "Remark": "备注", "Enable": "启用", "Deactivate": "停用", "Edit": "编辑", "Details": "详情", "timeoutT0": "超时时间[t0]", "timeoutT1": "超时时间[t1]", "timeoutT2": "超时时间[t2]", "timeoutT3": "超时时间[t3]", "MaxNumOfClientConnect": "最大客户端连接数", "MMSprofile": "mms 配置文件", "StartupProfile": "startup 配置文件", "UsermapProfile": "usermap 配置文件", "CIDfile": "cid 文件", "WorkerAddress": "从机地址", "UnitConversion": "单位转换", "PDStarsProtocol": "华乘协议", "DistributionAutoProtocol": "配电自动化应用规约", "SGCCProtocol": "国网协议", "SouthPowerGridProtocol": "南网协议", "GuangZhouDistributionAutoProtocol": "广州智能配电房协议", "IDExists": "ID已存在", "VolumeRatio": "体积比", "ChartConfig": "图谱配置", "Measurement": "测量值", "SENSOR_TIMEOUT_COUNT": "传感器离线判定次数", "OUTWARD_CONFIG_ENABLE_STATE": "配置状态", "OUTWARD_CONFIG_ENABLED": "已启用", "OUTWARD_CONFIG_DISABLED": "已停用", "OUTWARD_CONFIG_ENABLING": "正在启用", "SENSOR_TIMEOUT_DISABLING": "正在停用", "ERROR_NO_CONFIG": "配置不存在", "ERROR_INPUT_PARAMS": "输入参数错误", "ERROR_INTEGER": "请输入整数", "ERROR_TIP_RANGE": "取值范围 [{1}~{2}]", "ERROR_IP_FORMAT": "IP格式不正确", "ERROR_FILE_NAME": "文件名必须是 {1}", "ERROR_FILE_SUFFIX": "文件后缀必须是 {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "文件应小于 {1} MB", "ERROR_T2_T1": "T2必须小于T1", "LENGTH_COMM_LIMIT": "长度不可超过{1}字符", "MANU_FAST_SYNC_DATA": "快速同步", "SYNC_DATA_STATE_LIST": "同步状态列表", "LAST_SYNC_DATE_RANGE": "最近一次同步日期范围", "NoError": "无错误", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "同步超时", "SYNC_DATA_ERROR_DISCONNECT": "断开连接", "SYNC_DATA_STATUS_WAITING": "等待连接", "SYNC_DATA_STATUS_CONNECTED": "已连接", "SYNC_DATA_STATUS_SYNCING": "同步中", "SYNC_DATA_STATUS_SUCCESS": "同步成功", "SYNC_DATA_STATUS_FAILED": "同步失败", "SYNC_DATA_STATUS_CANCELED": "同步取消", "Today": "今天", "Yesterday": "昨天", "LAST_7_DAYS": "最近7天", "LAST_30_DAYS": "最近30天", "THIS_MONTH": "这个月", "LAST_MONTH": "上个月", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "启动通信服务失败", "FastSyncDataCancelTips": "是否取消数据同步", "FastSyncDataSelectTips": "请选择需要同步数据的传感器", "Band-pass": "带通", "aeWaveChartTitle": "AE波形图谱", "aePhaseChartTitle": "AE相位图谱", "aeFlyChartTitle": "AE飞行图谱", "LOCAL_PARAMS_TIME": "时间", "LOCAL_CHART_COLORDENSITY": "颜色密度", "openTime": "开门时间", "closeTime": "关门时间", "triggerUnit": "触发幅值[μV]", "openTimeUnit": "开门时间[μs]", "closeTimeUnit": "关门时间[μs]", "triggerUnitTips": "触发幅值范围： [{1},{2}]μV", "TOP_DISCHARGE_AMPLITUDE": "TOP3放电幅值", "WS_901_ERROR_TIPS": "指令推送服务正在注册，请2s后重试"}