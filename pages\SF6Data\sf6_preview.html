<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SF6趋势图预览</title>
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <!-- jQuery -->
    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- Bootstrap JS -->
    <script src="https://cdn.bootcdn.net/ajax/libs/twitter-bootstrap/3.4.1/js/bootstrap.min.js"></script>
    <!-- ECharts -->
    <script src="https://cdn.bootcdn.net/ajax/libs/echarts/5.4.0/echarts.min.js"></script>
    <!-- Moment.js -->
    <script src="https://cdn.bootcdn.net/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <!-- Date Range Picker -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.css">
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.min.js"></script>
    
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #f4f4f4;
            margin: 0;
            padding: 20px;
        }
        .full-height {
            height: 100vh;
        }
        .sf6-content {
            display: block;
            height: 100%;
        }
        .sf6-params-content{
            border: 1px solid #ddd;
            padding: 20px;
            background-color: #fff;
            border-radius: 4px;
        }
        .sf6-history-btn-group{
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            margin-top: 20px;
        }
        .sf6-trend-controls {
            border: 1px solid #ddd;
            padding: 20px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
            border-radius: 4px;
        }
        .sf6-trend-controls .form-group {
            margin-bottom: 15px;
        }
        .sf6-trend-controls .form-group:last-child {
            margin-bottom: 0;
        }
        #sf6TrendChart {
            width: 100%;
            height: 100%;
            min-height: 350px;
        }
        .sf6-chart-container {
            height: calc(100% - 140px);
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #fff;
            padding: 10px;
        }
        .sf6-left-panel {
            height: 100%;
            padding-right: 15px;
        }
        .sf6-right-panel {
            height: 100%;
            padding-left: 15px;
        }
        .nav-tabs-custom {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .border-left {
            border-left: 1px solid #ddd;
        }
        .bootstrap-dialog {
            background: white;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h2>SF6数据趋势图预览</h2>
        
        <div class="col-sm-12 full-height">
            <div class="nav-tabs-custom full-height sf6-content">
                <div id="graph-SF6" class="col-sm-8 border-left full-height">
                    <!-- 趋势图控制区域 -->
                    <div class="sf6-trend-controls">
                        <div class="row">
                            <div class="col-sm-3">
                                <label>数据类型:</label>
                                <select id="dataTypeSelect" class="form-control">
                                    <option value="SF6_Density">SF6气体密度</option>
                                    <option value="Device_env_Temp">设备环境温度</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                                <label>开始时间:</label>
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <input id="dateStart" type="text" class="form-control" readonly="true">
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <label>结束时间:</label>
                                <div class="input-group">
                                    <div class="input-group-addon">
                                        <i class="fa fa-calendar"></i>
                                    </div>
                                    <input id="dateEnd" type="text" class="form-control" readonly="true">
                                </div>
                            </div>
                            <div class="col-sm-3">
                                <label>&nbsp;</label>
                                <button type="button" id="refreshTrendBtn" class="btn btn-primary btn-block">刷新</button>
                            </div>
                        </div>
                    </div>
                    <!-- 趋势图显示区域 -->
                    <div class="row border-left" id="sf6Div" style="min-height: 400px;height: calc(100% - 120px);">
                        <div id="sf6TrendChart"></div>
                    </div>
                </div>
                <div class="col-sm-4 bootstrap-dialog" style="border: 1px solid #f4f4f4;">
                    <form class="form-horizontal sf6-params-content">
                        <div class="form-group">
                            <label class="col-sm-6 control-label">测点名称:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="pointName">220kV-TR_主变_box</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">传感器编码:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="sensorCode">SF6监测传感器</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">传感器类型:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="sensorType">SF6传感器</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">采样日期:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="sample_date">2025-08-20</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">采样时间:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="sample_time">15:27:24</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">SF6气体密度:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="SF6_Density">3.1MPa</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">SF6气体压力:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="SF6YN">2.3MPa</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">SF6气体温度:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="SF6_Temp">25.81℃</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">设备环境温度:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="Device_env_Temp">26.11℃</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">告警状态:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="Alarm_Status">60%</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">电池电压:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="batteryVoltage">3.6V</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-6 control-label">超级电容电压:</label>
                            <div class="col-sm-6">
                                <label class="control-label" id="superCapvoltage">5.0V</label>
                            </div>
                        </div>
                        <div class="form-group sf6-history-btn-group">
                            <div class="col-sm-6">
                                <button type="button" id="preData" class="btn btn-block btn-primary" style="width: 140px; margin: 10px 0px 10px auto">上一条</button>
                            </div>
                            <div class="col-sm-6">
                                <button type="button" id="nextData" class="btn btn-block btn-primary" style="width: 140px; margin: 10px 0px 10px auto">下一条</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(function () {
            // 初始化时间选择器
            initDatePickers();
            
            // 初始化趋势图
            initSF6TrendChart();

            // 初始化时间选择器
            function initDatePickers() {
                var endDate = new Date();
                var startDate = new Date();
                startDate.setDate(endDate.getDate() - 7); // 默认显示最近7天

                $('#dateStart').daterangepicker({
                    "opens": "right",
                    "autoApply": true,
                    "timePicker": false,
                    "singleDatePicker": true,
                    "locale": {
                        "format": 'YYYY/MM/DD'
                    },
                    "startDate": startDate
                });

                $('#dateEnd').daterangepicker({
                    "opens": "right",
                    "autoApply": true,
                    "timePicker": false,
                    "singleDatePicker": true,
                    "locale": {
                        "format": 'YYYY/MM/DD'
                    },
                    "startDate": endDate
                });
            }

            // 初始化SF6趋势图
            var sf6TrendChart;
            function initSF6TrendChart() {
                sf6TrendChart = echarts.init(document.getElementById('sf6TrendChart'));
                
                // 模拟数据
                var timeData = [];
                var densityData = [];
                var pressureData = [];
                var tempData = [];
                
                // 生成最近7天的模拟数据
                for (var i = 6; i >= 0; i--) {
                    var date = new Date();
                    date.setDate(date.getDate() - i);
                    timeData.push(date.getMonth() + 1 + '/' + date.getDate());
                    
                    // 模拟SF6数据
                    densityData.push((3.0 + Math.random() * 0.2).toFixed(2));
                    pressureData.push((2.2 + Math.random() * 0.2).toFixed(2));
                    tempData.push((20 + Math.random() * 10).toFixed(1));
                }
                
                var dataType = $("#dataTypeSelect").val();
                var dataTypeName = $("#dataTypeSelect option:selected").text();
                var unit = dataType === 'SF6_Density' ? 'MPa' : '℃';
                var currentData = dataType === 'SF6_Density' ? densityData : tempData;

                var option = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'line'
                        },
                        confine: true
                    },
                    title: {
                        text: dataTypeName + '趋势图',
                        x: 'center',
                        y: 'top'
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '3%',
                        top: '10%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: timeData,
                        boundaryGap: false,
                        axisTick: {
                            alignWithLabel: false
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: dataTypeName + ' (' + unit + ')'
                    },
                    series: [
                        {
                            name: dataTypeName,
                            type: 'line',
                            data: currentData,
                            itemStyle: {
                                normal: {
                                    color: '#3c8dbc'
                                }
                            },
                            smooth: true,
                            markPoint: {
                                data: [
                                    {type: 'max', name: '最大值'},
                                    {type: 'min', name: '最小值'}
                                ]
                            }
                        }
                    ]
                };
                
                sf6TrendChart.setOption(option);
            }

            // 数据类型选择器变化事件
            $("#dataTypeSelect").change(function () {
                initSF6TrendChart();
            });

            // 刷新按钮点击事件
            $("#refreshTrendBtn").click(function () {
                // 重新生成数据
                initSF6TrendChart();
                alert('趋势图已刷新！');
            });

            // 窗口大小改变时重新调整图表大小
            window.addEventListener("resize", function () {
                if (sf6TrendChart) {
                    sf6TrendChart.resize();
                }
            });
        });
    </script>
</body>
</html>
