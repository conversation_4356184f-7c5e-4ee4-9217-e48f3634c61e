<!--<div id="AE图谱" class="full-height">-->
<div class="col-sm-12 full-height" id="maingraph" style="padding: 0px">
    <div class="form-group"
        style="height: 90px;border-left: 1px solid #b9b7b7;border-right: 1px solid #b9b7b7;background-color: white;margin-bottom:0px;float:left;width: 100%">
        <div class="col-sm-8" style="margin-top: 10px">
            <label for="dateStart" class="col-sm-12 control-label" style="margin-top: 7px;"
                data-i18n="acquisitionTime">采集时间</label>
            <div class="col-sm-12">
                <div class="bootstrap-datepicker">
                    <div class="input-group">
                        <div class="input-group-addon">
                            <i class="fa fa-calendar"></i>
                        </div>
                        <input id="dateStart" type="text" class="form-control" readonly="true">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-4" style="margin-top: 10px">
            <label for="dateStart" class="col-sm-12 control-label" style="margin-top: 7px;"
                data-i18n="State">状态</label>
            <div class="col-sm-12">
                <select id="smokeState" style="margin-top: 7px;width: 100%">
                    <option value="all" selected data-i18n="all">全部</option>
                    <option value="normal" data-i18n="normal">正常</option>
                    <option value="alarm" data-i18n="alarm">告警</option>
                </select>
            </div>
        </div>
    </div>
    <div class="row" style="height: 100%;" id="smokeTableContent">
        <div class="form-group col-lg-12"
            style="height: 90%;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px;padding: 20px 50px 0 50px;">
            <table id="smoke_table" style="width:100%;height:100%"></table>
        </div>
    </div>
</div>
<!--</div>-->

<script type="text/javascript">
    initPageI18n();
    $('#smokeState').select2();

    var smokeTableParams = {
        pointId: showChartPointId,
        stateType: 'all',
        pointType: 'SMOKE',
        startDate: new Date(new Date(showChartSampleTime).setDate(new Date(showChartSampleTime).getDate() - 1))
            .pattern('yyyy/MM/dd HH:mm:ss'),
        endDate: showChartSampleTime,
    };
    /**
     * 初始化表格内容
     */
    var customWaterTableConfig = {
        id: 'smoke_table',
        method: 'GetTendData',
        showColumns: false,
        //        useMocker: true,
        height: $('#smoke_sensor').height() - 125,
        responseHandler: function (res) {
            var data = res.result.dataChart;
            var tableList = [];
            data.value.forEach(function (item, index) {
                var row = {
                    state: item,
                    time: data.date[index]
                };
                tableList.push(row);
            });
            return {
                rows: tableList,
                total: res.result.total
            };
        },
        requestParam: function (params) {
            return {
                page: params.offset / params.limit + 1,
                size: params.limit,
                pointId: smokeTableParams.pointId,
                pointType: smokeTableParams.pointType,
                stateType: smokeTableParams.stateType,
                startDate: smokeTableParams.startDate,
                endDate: smokeTableParams.endDate,
            };
        },
        columns: [{
                field: 'state',
                title: getI18nName('State'),
                formatter: function (value, row, index) {
                    var state = '-';
                    if (value == 'alarm') {
                        state = getI18nName('alarm');
                    } else if (value == 'normal') {
                        state = getI18nName('normal');
                    }
                    var content = '<span>' + state + '</span>';
                    return content;
                }
            },
            {
                field: 'time',
                title: getI18nName('tbSampleDate'),
            }
        ]
    };

    function refreshWaterImmersionTable() {
        $('#smoke_table').bootstrapTable('refresh', {
            silent: true
        });
    }

    function refreshTableOptions() {
        var height = $('#smoke_sensor').height() - 125;
        $('#smoke_table').bootstrapTable('resetView', {
            height: height
        });
    }

    $(function () {
        // 基于准备好的dom，初始化echarts图表
        //@ sourceURL=tev_chart.js
        //Daterangepicker
        $('#dateStart').daterangepicker({
            opens: "right",
            autoApply: true,
            timePicker: true,
            timePicker24Hour: true,
            locale: {
                format: 'YYYY/MM/DD HH'
            },
            startDate: new Date(new Date(showChartSampleTime).setDate(new Date(showChartSampleTime)
                .getDate() - 1)).pattern('yyyy/MM/dd HH'),
            endDate: showChartSampleTime,
        }, function (start, end, label) {
            smokeTableParams.startDate = start.format('YYYY/MM/DD HH:mm:ss');
            smokeTableParams.endDate = end.format('YYYY/MM/DD HH:mm:ss');
            refreshWaterImmersionTable();
        });

        $('#dateStart').click(function () {
            $('.minuteselect').attr('disabled', 'disabled');
            $('.minuteselect').css('background', 'lightgray');
        });

        initCustomTable(customWaterTableConfig);

        $('#smokeState').on('change', function (obj, a, b) {
            smokeTableParams.stateType = obj.currentTarget.value;
            refreshWaterImmersionTable();
        });

        $(window).resize(function () {
            $('#smokeTableContent').height($('#smoke_sensor').height() - 35);
            refreshTableOptions();
        });
        $(window).resize();

    });
</script>