<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <ul class="nav nav-tabs" style="height:9%">
            <!--<li class="active" onclick="getStationTree()"><a href="#stationSet_tab" data-toggle="tab">站点配置</a></li>-->
            <li id="initOnShow" class="active"><a href="#stationSet_tab" data-toggle="tab"
                                                  data-i18n="stationConfig">站点配置</a></li>
            <li><a href="#prim_dev_tab" data-toggle="tab" data-i18n="deviceConfig">一次设备配置</a></li>
            <li><a href="#test_point_tab" data-toggle="tab" data-i18n="pointConfig">测点配置</a></li>
        </ul>
        <div class="tab-content" style="padding:0px;height:91%">
            <div class="col-sm-12 tab-pane full-height active" id="stationSet_tab">
                <br>
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="col-sm-3 control-label" for="station_input"
                               data-i18n="stationName">站点名称</label>
                        <div class="col-sm-8">
                            <input id="station_input" vk="true" type="text"
                                   class="col-sm-4 form-control"
                                   data-i18n-placeholder="pleaseInput" value="">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" for="station_pms"
                               data-i18n="stationPMS">站点PMS编码</label>
                        <div class="col-sm-8">
                            <input id="station_pms" class="col-sm-4 form-control"
                                   data-i18n-placeholder="pleaseInput">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="station_level" class="col-sm-3 control-label"
                               data-i18n="stationLevel">站点电压等级</label>
                        <div class="col-sm-8">
                            <select id="station_level" class="form-control select2"
                                    data-i18n-placeholder="selectPlease"></select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label" for="power_comp"
                               data-i18n="powerUnit">电力单位</label>
                        <div class="col-sm-8">
                            <input id="power_comp" vk="true" type="text"
                                   class="col-sm-4 form-control"
                                   data-i18n-placeholder="pleaseInput" value="">
                        </div>
                    </div>
                </form>
                <div class="content-footer">
                    <div class="col-sm-8">
                        <button type="button" id="save_station" class="btn btn-block btn-primary"
                                style="width: 100px; margin: 10px 0px 10px auto" data-i18n="save">保存
                        </button>
                    </div>
                    <div class="col-sm-4">
                        <button type="button" id="del_station" class="btn btn-block btn-primary"
                                style="width: 100px; margin: 10px 60px 10px auto"
                                data-i18n="delete">删除
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 tab-pane full-height" id="prim_dev_tab">
                <div class="col-sm-4 full-height zTreeDemoBackground left"
                     style="overflow:auto;border: 1px solid #f4f4f4;">
                    <div id="station_tree" class="ztree"></div>
                </div>
                <div class="col-sm-8 full-height">
                    <br>
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="station_name_dev"
                                   data-i18n="stationName">站点名称</label>
                            <div class="col-sm-7">
                                <label class=" control-label" id="station_name_dev"
                                       data-i18n="stationName">站点名称</label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="dev_name" data-i18n="deviceName">一次设备名称</label>
                            <div class="col-sm-7">
                                <input id="dev_name" vk="true" type="text"
                                       class="col-sm-4 form-control"
                                       data-i18n-placeholder="pleaseInput" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="dev_pms" data-i18n="devicePMSCode">设备PMS编码</label>
                            <div class="col-sm-7">
                                <input id="dev_pms" vk="true" type="text"
                                       class="col-sm-4 form-control"
                                       data-i18n-placeholder="pleaseInput" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="dev_type" class="col-sm-4 control-label"
                                   data-i18n="deviceType">设备类型</label>
                            <div class="col-sm-7">
                                <select id="dev_type" class="form-control select2"
                                        data-i18n-placeholder="selectPlease"
                                        style="width:100%"></select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="dev_level" class="col-sm-4 control-label"
                                   data-i18n="deviceLevel">设备电压等级</label>
                            <div class="col-sm-7">
                                <select id="dev_level" class="form-control select2"
                                        data-i18n-placeholder="selectPlease"
                                        style="width:100%"></select>
                            </div>
                        </div>
                    </form>
                    <div class="content-footer">
                        <div class="col-sm-4">
                            <button type="button" id="add_dev" class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="add">增加
                            </button>
                        </div>
                        <div class="col-sm-4">
                            <button type="button" id="save_dev" class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="save">保存
                            </button>
                        </div>
                        <div class="col-sm-4">
                            <button type="button" id="del_dev" class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="delete">删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-sm-12 tab-pane full-height" id="test_point_tab">
                <div class="col-sm-4 full-height zTreeDemoBackground left"
                     style="overflow:auto;border: 1px solid #f4f4f4;">
                    <div id="station_tree_point" class="ztree"></div>
                </div>
                <div class="col-sm-8 full-height">
                    <br>
                    <form class="form-horizontal">
                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="dev_name"
                                   data-i18n="stationName">站点名称</label>
                            <div class="col-sm-7">
                                <label class=" control-label" id="station_name_point"></label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="dev_name_point"
                                   data-i18n="deviceName">一次设备名称</label>
                            <div class="col-sm-7">
                                <label class=" control-label" id="dev_name_point"></label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-4 control-label" for="test_point_name"
                                   data-i18n="pointName">测点名称</label>
                            <div class="col-sm-7">
                                <input id="test_point_name" vk="true" type="text"
                                       class="col-sm-4 form-control"
                                       data-i18n-placeholder="pleaseInput" value="">
                            </div>
                        </div>
                    </form>
                    <div class="content-footer">
                        <div class="col-sm-4">
                            <button type="button" id="add_test_point"
                                    class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="add">增加
                            </button>
                        </div>
                        <div class="col-sm-4">
                            <button type="button" id="save_test_point"
                                    class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="save">保存
                            </button>
                        </div>
                        <div class="col-sm-4">
                            <button type="button" id="del_test_point"
                                    class="btn btn-block btn-primary"
                                    style="width: 100px; margin: 10px 0px 10px auto"
                                    data-i18n="delete">删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    initPageI18n();
    //校验是否需要虚拟键盘
    checkNeedKeyboard();

    $('.select2').select2({
        minimumResultsForSearch: Infinity,
        language: "zh-CN"
    });
    getVolLevel();
    //    getVolLevel();
    //    getStation();

    $(".nav-tabs").find("li").click(function () {
        var currentTab = $(this).find("a")[0].getAttribute("href");
        switch (currentTab) {
            case "#stationSet_tab":
                getStation();
                break;
            case "#prim_dev_tab":
                getDevType();
                getVolLevel("GetDevVolLevel");
                getDeviceNameList();
                break;
            case "#test_point_tab":
                getDevicePointNameList();
                break;
        }
    });
    document.getElementById("initOnShow").click();

    //显示-修改站点
    $("#save_station").click(function () {
//        var node = $('#station_tree').treeview('getSelected');
//        if (node.length == 0 || node[0].level != 1) {
//            $.showMsgText('请选择站点！');
//            return;
//        }
        var formData = {};
        formData.stationName = $('#station_input').val();
        formData.stationPMS = $('#station_pms').val();
        formData.stationVolLevel = $("#station_level").val();
        formData.powerComp = $('#power_comp').val();
        if (validateStrLen(formData.stationName) == 0) {
            $.notifyUtil.notifyWarning(getI18nName('stationNameTips'));
            return;
        }
        if (validateStrLen(formData.powerComp) == 0) {
            $.notifyUtil.notifyWarning(getI18nName('powerUnitNameTips'));
            return;
        }
        checkLegalNameValue(formData.stationName, 'station_input');
        checkLegalNameValue(formData.stationPMS, 'station_pms');
        checkLegalNameValue(formData.powerComp, 'power_comp');
        formData.stationSvg = "";
        saveStationInfo(formData, "modified");
    });

    $("#del_station").click(function () {
        delStation();
    });


    $("#add_dev").click(function () {
        var formData = {};
        formData.deviceName = $('#dev_name').val();
        formData.devicePMS = $('#dev_pms').val();
        formData.deviceTypeName = $("#dev_type").val();
        formData.deviceVolLevel = $("#dev_level").val();
        checkLegalNameValue(formData.deviceName, 'dev_name');
        checkLegalNameValue(formData.devicePMS, 'dev_pms');
        saveDevInfo(formData, "add");
    });

    $("#save_dev").click(function () {
        if (devCode === null) {
            $.notifyUtil.notifyWarning(getI18nName('selectDeviceTips'));
            return;
        }

        var formData = {};
        formData.deviceId = devCode;
        formData.deviceName = $('#dev_name').val();
        formData.devicePMS = $('#dev_pms').val();
        formData.deviceTypeName = $("#dev_type").val();
        formData.deviceVolLevel = $("#dev_level").val();
        checkLegalNameValue(formData.deviceName, 'dev_name');
        checkLegalNameValue(formData.devicePMS, 'dev_pms');
        saveDevInfo(formData, "save");
    });

    $("#del_dev").click(function () {
        if (devCode === null) {
            $.notifyUtil.notifyWarning(getI18nName('selectDeviceTips'));
            return;
        }
        delDev(devCode);
    });

    $("#add_test_point").click(function () {
        if (devCode === null) {
            $.notifyUtil.notifyWarning(getI18nName('selectDeviceOfPointTips'));
            return;
        }
        var formData = {};
        formData.pointName = $('#test_point_name').val();
        formData.deviceId = devCode;
        checkLegalNameValue(formData.pointName, 'test_point_name');
        saveTestPointInfo(formData, devCode, "add");
    });

    $("#save_test_point").click(function () {
        if (test_point_id_set === null) {
            $.notifyUtil.notifyWarning(getI18nName('selectPointTips'));
            return;
        }

        var formData = {};
        formData.pointId = test_point_id_set;
        formData.deviceId = devCode;
        formData.pointName = $('#test_point_name').val();
        saveTestPointInfo(formData, devCode, "save");
    });

    $("#del_test_point").click(function () {
        if (test_point_id_set === null) {
            $.notifyUtil.notifyWarning(getI18nName('selectPointTips'))
            return;
        }
        delTestPointInfo(test_point_id_set);
    });

</script>