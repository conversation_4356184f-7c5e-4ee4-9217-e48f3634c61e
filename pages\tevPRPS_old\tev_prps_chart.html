<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <div id="graph-TEV" class="col-sm-7 border-left full-height">
            <div id="graph-head" class="row border-right" style="height: 48px; border: 1px solid #f4f4f4;">
                <div class="col-sm-6">
                    <button type="button" id="showPRPS3D" class="btn btn-block btn-primary"
                        style="width: 100px; margin: 5px 0px 10px auto">PRPS3D
                    </button>
                </div>
                <div class="col-sm-6">
                    <button type="button" id="showPRPD2D" class="btn btn-block btn-primary"
                        style="width: 100px; margin: 5px 60px 10px auto">PRPD2D
                    </button>
                </div>
            </div>
            <div class="row border-left" id="tevPRPSDiv">
            </div>
            <div id="graph-end" class="row hide" style="border: 1px solid #f4f4f4;">
                <input type="text" value="" class="slider form-control" data-slider-min="0" data-slider-max="360"
                    data-slider-step="1" data-slider-value="0" data-slider-orientation="horizontal"
                    data-slider-selection="before" data-slider-tooltip="show" data-slider-id="blue">
            </div>
        </div>
        <div class="col-sm-5 full-height bootstrap-dialog" style="border: 1px solid #f4f4f4;">
            <form class="form-horizontal">
                <br>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="pointName">测点名称:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="pointName"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sensorCode">传感器编码:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sensorCode"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sensorType">传感器类型:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sensorType">TEV</label>
                    </div>
                </div>
                <!--               <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="gain">增益:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="gain"></label>
                    </div>
                </div> -->
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="wave_filter">频带:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="wave_filter"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_date">采样日期:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_date"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="sample_time">采样时间:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="sample_time"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="pdMax">最大放电幅值:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="pd_max">66%</label>
                    </div>
                </div>
                <!--<div class="form-group">-->
                <!--<label class="col-sm-6 control-label">最小放电量:</label>-->
                <!--<div class="col-sm-6">-->
                <!--<label class=" control-label" id="pd_min">24%</label>-->
                <!--</div>-->
                <!--</div>-->
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="pdAvg">平均放电量:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="pd_avg"></label>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-6 control-label" data-i18n="pdNum">脉冲个数:</label>
                    <div class="col-sm-6">
                        <label class=" control-label" id="pd_number"></label>
                    </div>
                </div>
            </form>
        </div>
        <div class="content-footer">
            <div class="col-sm-3">
                <button type="button" id="now_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="historyData">历史数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="history_data" class="btn btn-block btn-primary hide"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="realData">实时数据
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                    style="width: 110px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
                </button>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    initPageI18n();
    $(function () {
        //@ sourceURL=TEV_chart.js
        $(window).resize(function () {
            if (!document.getElementById("tevPRPSDiv")) { //js判断元素是否存在
                return;
            }
            var height = $("#graph-TEV").height() - $("#graph-head").height() -
                $("#graph-end").height() - $(".content-footer").height() - 20;
            $("#tevPRPSDiv").height(height);


            var svgEls = document.querySelector('svg');
            if (svgEls) {
                svgEls.setAttribute('width', $("#tevPRPSDiv").width());
                svgEls.setAttribute('height', $("#tevPRPSDiv").height());
            }
        });
        $(window).resize();

        $('.slider').slider();
        var chartDiv = document.getElementById('tevPRPSDiv');

        $("#showPRPS3D").click(function () {
            $("#tevPRPSDiv").empty();
            //            $("#showPRPS3D").addClass("disabled");
            //            $("#showPRPD2D").removeClass("disabled");
            $("#showPRPD2D").removeAttr("disabled");
            $("#showPRPS3D").attr("disabled", true);

            //PRPD-3D
            /* var prps3dChart = PDChart.PRPS.draw3D(prps.chartBody, {
                title: '暂态地电压 PRPS3D',
                backgroundColor: "#ffffff",
                toFixedValue: 1,
            });
            PDChart.loadChart(chartDiv, prps3dChart, {
                id: "prpd3d",
                width: $("#tevPRPSDiv").width(),
                height: $("#tevPRPSDiv").height(),
                append: true,
                saveAsImage: false,
            }); */

            loadTEVPRPS(showChartDataId, showChartPointId, 0);
        });

        $("#showPRPD2D").click(function () {
            $("#tevPRPSDiv").empty();
            //            $("#showPRPD2D").addClass("disabled");
            //            $("#showPRPS3D").removeClass("disabled");
            $("#showPRPS3D").removeAttr("disabled");
            $("#showPRPD2D").attr("disabled", true);

            //PRPD-2D
            /* var prps2dChart = PDChart.PRPD.draw2D(prpd.chartBody, {
                title: '暂态地电压 PRPD2D',
                backgroundColor: "#ffffff",
                toFixedValue: 1,
            });
            PDChart.loadChart(chartDiv, prps2dChart, {
                id: "prpd2d",
                width: $("#tevPRPSDiv").width(),
                height: $("#tevPRPSDiv").height(),
                append: true,
                saveAsImage: false,
            }); */

            loadTEVPRPS(showChartDataId, showChartPointId, 1);
        });

        $('#showPRPS3D').trigger("click");


        var preId = 0,
            nextId = 0;
        var nowId;

        function loadTEVPRPS(dataId, pointId, type) {
            var pointType = "TEVPRPS";
            var data = "dataId=" + dataId + "&pointId=" + pointId + "&pointType=" + pointType + "&dataType=" +
                type;
            poll('GET', 'GetChartData', data, function (text) {
                var respData = text.result;
                $("#tevPRPSDiv").empty();
                preId = respData.preDataId;
                nextId = respData.nextDataId;
                showChartDataId = dataId;
                //判断上一条/下一条按钮是否可用并改变状态
                if (preId === 0) {
                    $("#preData").attr("disabled", true);
                } else {
                    $("#preData").removeAttr("disabled");
                }
                if (nextId === 0) {
                    $("#nextData").attr("disabled", true);
                } else {
                    $("#nextData").removeAttr("disabled");
                }
                //全局管理翻译文件，处理返回数据中的多语言
                var chartData = respData.chartBody;
                var xDesc = globalParms[chartData.axisInfo.xDesc];
                if (validateStrLen(xDesc) != 0) {
                    chartData.axisInfo.xDesc = xDesc;
                }
                var yDesc = globalParms[chartData.axisInfo.yDesc];
                if (validateStrLen(yDesc) != 0) {
                    chartData.axisInfo.yDesc = yDesc;
                }
                var zDesc = globalParms[chartData.axisInfo.zDesc];
                if (validateStrLen(zDesc) != 0) {
                    chartData.axisInfo.zDesc = zDesc;
                }
                //PRPS-3D
                if (type === 0) {
                    var prps3dChart = PDChart.PRPS.draw3D(respData.chartBody, {
                        title: 'TEV PRPS3D',
                        backgroundColor: "#ffffff",
                        toFixedValue: 1,
                    });
                    PDChart.loadChart(chartDiv, prps3dChart, {
                        id: "prpd3d",
                        width: $("#tevPRPSDiv").width(),
                        height: $("#tevPRPSDiv").height(),
                        append: true,
                        saveAsImage: false,
                    });
                } else {
                    var prps2dChart = PDChart.PRPD.draw2D(respData.chartBody, {
                        title: 'TEV PRPD2D',
                        backgroundColor: "#ffffff",
                        toFixedValue: 1,
                    });
                    PDChart.loadChart(chartDiv, prps2dChart, {
                        id: "prpd2d",
                        width: $("#tevPRPSDiv").width(),
                        height: $("#tevPRPSDiv").height(),
                        append: true,
                        saveAsImage: false,
                    });
                }
                //暂时由页面进行Type判断转换中文，未知type直接显示
                var sensorType = getI18nName(respData.params.sensorType);
                $("#pointName").html(respData.params.pointName);
                $("#sensorCode").html(respData.params.aduId);
                $("#sensorType").html(sensorType);
                // $("#gain").html(respData.params.gain);
                $("#sample_date").html(respData.params.sample_date);
                $("#sample_time").html(respData.params.sample_time);
                $("#pd_max").html(parseFloat(respData.params.pd_max).toFixed(1));
                $("#pd_min").html(respData.params.pd_min);
                $("#pd_avg").html(parseFloat(respData.params.pd_avg).toFixed(1));
                $("#pd_number").html(respData.params.pd_number);
                var filter = respData.params.filter;
                $("#wave_filter").parent().parent().removeClass("hide").addClass("hide");
                if (filter) {
                    $("#wave_filter").parent().parent().removeClass("hide");
                    $("#wave_filter").html(getFilter(filter));
                }
            });
        }

        $("#preData").click(function () {
            if (preId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }
            if ($("#showPRPD2D").attr("disabled")) {
                loadTEVPRPS(preId, showChartPointId, 1);
            } else
                loadTEVPRPS(preId, showChartPointId, 0);
        });

        $("#nextData").click(function () {
            if (nextId === 0) {
                layer.alert(getI18nName('noTestData'), {
                    title: getI18nName('tips'),
                    btn: [getI18nName('close')]
                });
                return;
            }

            if ($("#showPRPD2D").attr("disabled")) {
                loadTEVPRPS(nextId, showChartPointId, 1);
            } else
                loadTEVPRPS(nextId, showChartPointId, 0);
        });
    });
</script>