function initEnumCache() {
  pollMokerGet(GET_STATION_PROTOCOL_LIST, {}, function (res) {});
}

function stationParamsRequestErr(errorcode) {
  var text = convertErrorCode(errorcode);
  if (errorcode == 101) text = getI18nName('ERROR_INPUT_PARAMS');
  $('.notifications')
    .notify({
      message: {
        text: text,
      },
      type: 'error',
    })
    .show();
}

var MainStationConfigLayerUtil = {
  renderParam: function (paramConfig, content, configType) {
    var outerContent = content;
    var $itemContent = $('<div>', {
      class: 'form-group station-config-param-content',
      style: 'margin:18px 0',
    }).appendTo(outerContent);

    var $lable = $('<label>', {
      class: 'col-sm-2 control-label',
      'data-i18n': paramConfig.name,
      for: paramConfig.id,
      style: 'padding-top:7px',
    }).appendTo($itemContent);

    var $paramContent = $('<div>', {
      class: 'col-sm-6',
      style: 'text-align:left;position:relative',
    }).appendTo($itemContent);

    //添加必填项标记
    if (paramConfig.isRequired) {
      $('<span>', {
        class: 'required',
        text: '*',
      }).appendTo($paramContent);
    }

    var $param;
    if (paramConfig.type == STATION_PARAM_ENUM.INPUT) {
      $param = $('<input>', {
        id: paramConfig.id,
        name: paramConfig.id,
        class: 'col-sm-4 form-control',
        vk: 'true',
        autocomplete: 'off',
        style: paramConfig.unit ? 'padding-right: 4rem;' : '',
        type: paramConfig.inputType ? paramConfig.inputType : 'text',
      }).appendTo($paramContent);
      if (paramConfig.value != undefined) {
        $param.val(paramConfig.value);
      }
      if (paramConfig.unit) {
        $('<div>', {
          class: 'station-config-unit',
          html: paramConfig.unit,
        }).appendTo($paramContent);
      }
    } else if (paramConfig.type == STATION_PARAM_ENUM.SELECT) {
      $param = $('<select>', {
        id: paramConfig.id,
        class: 'col-sm-4 form-control station-select',
        type: paramConfig.inputType == 'number' ? 'number' : undefined,
        change: paramConfig.onChange,
      }).appendTo($paramContent);

      if (paramConfig.data) {
        $param.empty();
        paramConfig.data.map((item) => {
          var key = (name = item);
          if (typeof item == 'object') {
            key = item.key;
            name = item.name;
          }
          $param.append(`<option value=${key}>${name}</option>`);
        });
      }
      if (paramConfig.value != undefined) {
        $param.val(paramConfig.value);
      }
    } else if (paramConfig.type == STATION_PARAM_ENUM.SWITCH) {
      $param = $('<input>', {
        id: paramConfig.id,
        class: 'switch-box switch-box-anim form-control',
        style: 'outline: none;',
        checked: paramConfig.value,
        type: 'checkbox',
      }).appendTo($paramContent);
      $('<label>', {
        style: 'position: absolute;margin-left: 10px;top: 7px;',
        text: paramConfig.unit,
      }).appendTo($paramContent);
    } else if (paramConfig.type == STATION_PARAM_ENUM.FILE_INPUT) {
      var fileGroup = $('<div>', {
        id: paramConfig.id + '-group',
        name: paramConfig.id + '-group',
        class: 'col-sm-12 input-group file-caption-main',
        style: '',
      }).appendTo($paramContent);

      var fileFakeDiv = $('<div>', {
        id: paramConfig.id + '-fake-div',
        name: paramConfig.id + '-fake-div',
        class: 'file-caption form-control kv-fileinput-caption',
        style: '',
      }).appendTo(fileGroup);

      var fileInputDiv = $('<div>', {
        id: paramConfig.id + '-fake-input',
        name: paramConfig.id + '-fake-input',
        class: 'file-fake-input-div',
        style: 'color:#999',
        text: getI18nName('selectPlease'),
      }).appendTo(fileFakeDiv);

      var fileInputBtnGroup = $('<div>', {
        id: paramConfig.id + '-fake-btn-group',
        name: paramConfig.id + '-fake-btn-group',
        class: 'input-group-btn',
      }).appendTo(fileGroup);

      $param = $('<input>', {
        id: paramConfig.id,
        name: paramConfig.id,
        class: 'file',
        style: 'position: absolute;top: 0;width: 96.5%;height: 34px;',
        title: getI18nName('selectPlease'),
        change: function (e) {
          if (!MainStationConfigLayerUtil.state.hasSelect61850File) {
            MainStationConfigLayerUtil.state.hasSelect61850File = true;
            MainStationConfigLayerUtil.state.paramCache[
              'IEC61850-form'
            ].saveBtn.attr('disabled', false);
            for (var i = 0; i < 4; i++) {
              $('.file-fake-input-div')[i].innerHTML =
                getI18nName('selectPlease');
            }
          }
          var file = e.target.files[0];
          fileInputDiv.html(file.name);
        },
        type: paramConfig.inputType ? paramConfig.inputType : 'text',
      }).appendTo($paramContent);

      var fileInputBtnContent = $('<div>', {
        id: paramConfig.id + '-fake-btn-content',
        name: paramConfig.id + '-fake-btn-content',
        class: 'btn btn-primary btn-file',
        click: function () {
          $param.trigger('click');
        },
      }).appendTo(fileInputBtnGroup);

      var fileSelectIcon = $('<i>', {
        id: paramConfig.id + '-fake-btn-icon',
        name: paramConfig.id + '-fake-btn-icon',
        class: 'glyphicon glyphicon-folder-open',
      }).appendTo(fileInputBtnContent);

      if (paramConfig.value != undefined) {
        // $param.val(paramConfig.value);
      }
    } else if (paramConfig.type == STATION_PARAM_ENUM.STATE_LABEL) {
      $paramContent.css('padding', '7px 15px');
      $param = $('<span>', {
        id: paramConfig.id,
        name: paramConfig.id,
        class: 'label i2-connect-state',
      }).appendTo($paramContent);
    } else if (paramConfig.type == STATION_PARAM_ENUM.ENABLE_STATE) {
      $param = $('<div>', {
        id: paramConfig.id,
        name: paramConfig.id,
        class: 'col-sm-4 form-control',
        style: 'border:none;padding: 7px 0;',
      }).appendTo($paramContent);

      var $enableState = $('<span>', {
        id: paramConfig.id + '-label',
        name: paramConfig.id + '-label',
        class: 'label station-config-status',
      }).appendTo($param);

      var $enableBtn = $('<a>', {
        id: paramConfig.id + '-enable',
        name: paramConfig.id + '-enable',
        class: 'station-config-enable-btn hide',
      })
        .html(getI18nName('Enable'))
        .appendTo($param);

      $enableBtn.click(function () {
        MainStationConfigLayerUtil.switchConfigStatus(
          'enable',
          configType,
          content[0].id
        );
      });

      var $disableBtn = $('<a>', {
        id: paramConfig.id + '-disable',
        name: paramConfig.id + '-disable',
        class: 'station-config-enable-btn hide',
      })
        .html(getI18nName('Deactivate'))
        .appendTo($param);

      $disableBtn.click(function () {
        MainStationConfigLayerUtil.switchConfigStatus(
          'disable',
          configType,
          content[0].id
        );
      });
    } else if (paramConfig.type == STATION_PARAM_ENUM.SAVE_BTN) {
      $itemContent.css({
        margin: '18px 0',
        position: 'absolute',
        width: '100%',
        bottom: '0px',
      });
      $param = $('<button>', {
        id: paramConfig.id,
        name: paramConfig.id,
        class: 'btn btn-primary',
        text: getI18nName('save'),
        click: function () {
          MainStationConfigLayerUtil.saveCurrentConfig(
            content[0].id,
            configType
          );
        },
      }).appendTo($paramContent);
    }
    if (!this.state.paramCache[content[0].id])
      this.state.paramCache[content[0].id] = {};
    this.state.paramCache[content[0].id][paramConfig.id] = $param;
  },
  saveCurrentConfig: function (id, configType, callback) {
    if (configType != 'IEC61850') {
      if (!MainStationConfigLayerUtil.validateSubmitForm(id)) {
        $('.notifications')
          .notify({
            message: {
              text: getI18nName('ERROR_INPUT_PARAMS'),
            },
            type: 'warning',
          })
          .show();
        return;
      }
    } else {
      var checkObj =
        MainStationConfigLayerUtil.state.currentResponseCache.configDetails;
      var hasServerFile = true;
      for (var key in checkObj) {
        if (checkObj[key] == undefined || checkObj[key] == '') {
          hasServerFile = false;
        }
      }
      if (
        hasServerFile ||
        MainStationConfigLayerUtil.state.hasSelect61850File
      ) {
        if (
          MainStationConfigLayerUtil.state.hasSelect61850File &&
          !MainStationConfigLayerUtil.validateSubmitForm(id)
        ) {
          $('.notifications')
            .notify({
              message: {
                text: getI18nName('chooseFileUpdate'),
              },
              type: 'warning',
            })
            .show();
          return;
        } else if (
          !MainStationConfigLayerUtil.state.hasSelect61850File &&
          callback
        ) {
          callback();
          return;
        }
      } else {
        MainStationConfigLayerUtil.validateSubmitForm(id);
        $('.notifications')
          .notify({
            message: {
              text: getI18nName('chooseFileUpdate'),
            },
            type: 'warning',
          })
          .show();
        return;
      }
    }

    //保存当前配置
    var tempCache = MainStationConfigLayerUtil.state.currentResponseCache;
    if (configType != tempCache.communicationProtocol) return;

    //暂无请求数据缓存提示
    if (tempCache == undefined) {
      $('.notifications')
        .notify({
          message: {
            text: getI18nName('NoData'),
          },
          type: 'warning',
        })
        .show();
      return;
    }

    var controller = MainStationConfigLayerUtil.state.paramCache[id];
    var formData = {
      communicationProtocol: tempCache.communicationProtocol,
      configID: tempCache.configID,
      businessProtocol: controller.businessProtocol.val(),
      protocolConfig: tempCache.configDetails,
    };
    if (configType != 'IEC61850') {
      // console.log('lyx-save-params',formData);
      var paramKeys = Object.keys(formData.protocolConfig);
      // var paramKeys = Object.keys(tempCache.configDetails);
      paramKeys.map((key) => {
        var tempValue = controller[key].val();
        console.log('control type', controller[key].attr('type'));

        if (controller[key].attr('type') === 'number') {
          tempValue = tempValue == '' ? 0 : Number(tempValue);
        }
        if (controller[key].attr('type') === 'file') {
          tempValue = controller[key][0].files[0];
        }
        formData.protocolConfig[key] = tempValue;

        if (key == 'isEnableDbToDbm') {
          formData.protocolConfig[key] = controller[key][0].checked;
        }
      });
      // console.log('lyx-save-params-org',formData);
      //二级对象需要转为JSON字符串
      formData.protocolConfig = JSON.stringify(formData.protocolConfig);
      // console.log('lyx-save-params', formData);
      // debugger
      // pollMokerPost(SAVE_OUTWARD_PROTOCOL_CONFIG,formData,function(res){
      pollPost(
        SAVE_OUTWARD_PROTOCOL_CONFIG,
        formData,
        function (res) {
          $('.notifications')
            .notify({
              message: {
                text: getI18nName('saveSuccess'),
              },
              type: 'success',
            })
            .show();
          MainStationConfigLayerUtil.queryAndBindParams(id, configType);
          if (callback) callback();
        },
        stationParamsRequestErr
      );
    } else {
      var fileParamList = ['mms', 'startup', 'usermap', 'cid'];
      var formData = new FormData();
      formData.append('communicationProtocol', tempCache.communicationProtocol);
      formData.append('configID', tempCache.configID);
      formData.append('businessProtocol', controller.businessProtocol.val());
      formData.append('protocolConfig', JSON.stringify({}));
      fileParamList.map((key) => {
        formData.append(key, controller[key][0].files[0]);
      });
      $.ajax({
        url: serverUrl + SAVE_OUTWARD_PROTOCOL_CONFIG,
        type: 'POST',
        data: formData,
        // 告诉jQuery不要去处理发送的数据
        processData: false,
        // 告诉jQuery不要去设置Content-Type请求头
        contentType: false,
        success: function (result) {
          if (result.errorCode == 0) {
            $('.notifications')
              .notify({
                message: {
                  text: getI18nName('saveSuccess'),
                },
                type: 'success',
              })
              .show();
            MainStationConfigLayerUtil.queryAndBindParams(id, configType);
            if (callback) callback();
          } else {
            $('.notifications')
              .notify({
                message: {
                  text: convertErrorCode(result.errorCode),
                },
                type: 'error',
              })
              .show();
          }
        },
        error: function (responseStr) {
          console.log(responseStr);
          stationParamsRequestErr(responseStr.errorCode);
        },
        complete: function (XHR, TS) {
          Pace.stop();
          if ($('body').hasClass('pace-running')) {
            $('body').removeClass('pace-running');
          }
          if ($('body').hasClass('pace-done')) {
            $('body').removeClass('pace-done');
          }

          XHR = null;
        },
      });
    }
  },
  renderProtocolParams: function () {
    this.paramConfigs.map((config) => {
      $('#' + config.contentId).empty();
      let _that = this;
      // pollMokerGet(GET_SUPPORTED_BProtocols,{
      pollGet(
        GET_SUPPORTED_BProtocols,
        {
          communicationProtocol: config.type,
        },
        function (res) {
          config.params.map((param) => {
            if (param.id == 'businessProtocol') {
              param.data = res.result.businessProtocols;
            }
            _that.renderParam(param, $('#' + config.contentId), config.type);
          });
          $('.station-select').select2();
          initPageI18n();
          MainStationConfigLayerUtil.initValidater(config.contentId);
          /* if (config.type != 'IEC61850') {
            MainStationConfigLayerUtil.initValidater(config.contentId);
          } */
          if (config.contentId == 'I2-2-form') {
            MainStationConfigLayerUtil.queryAndBindParams('I2', 'I2');
          }
        },
        stationParamsRequestErr
      );
    });
    // $('.station-select').select2();
  },
  initConfig: function () {
    this.paramConfigs = [];
    let configI2_1 = {
      contentId: 'I2-1-form',
      type: 'I2',
      params: Object.assign(
        [],
        MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS.I2
      ),
    };
    let configI2_2 = {
      contentId: 'I2-2-form',
      type: 'I2',
      params: Object.assign(
        [],
        MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS.I2
      ),
    };
    let config104 = {
      contentId: 'IEC104-form',
      type: 'IEC104',
      params: Object.assign(
        [],
        MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS.IEC104
      ),
    };
    let config61850 = {
      contentId: 'IEC61850-form',
      type: 'IEC61850',
      params: Object.assign(
        [],
        MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS.IEC61850
      ),
    };
    let configmodbus = {
      contentId: 'modbus-form',
      type: 'modbus',
      params: Object.assign(
        [],
        MainStationParamsManager.STATION_PROTOCOL_PARAM_CONFIGS.modbus
      ),
    };
    this.paramConfigs.push(
      configI2_1,
      configI2_2,
      config104,
      config61850,
      configmodbus
    );
  },
  init: function () {
    this.initConfig();
    this.renderProtocolParams();

    this.initTabEvent();
    initPageI18n();
  },
  initValidater: function (formId) {
    // debugger
    var $submitForm = $('#' + formId);
    MainStationConfigLayerUtil.submitForm[formId] = $submitForm;
    $submitForm
      .bootstrapValidator(MAIN_STATION_VALIDATE)
      .on('error.field.bv', function(e, data) {
        var $field = $(data.element); // 当前校验失败的字段
        var $messages = $field.closest('.form-group').find('.help-block[data-bv-validator]'); // 获取该字段的所有错误提示
    
        var highestPriorityMessage = null;
        var highestPriority = -1;
    
        // 遍历所有校验规则，找到优先级最高的错误
        $.each(data.bv.getOptions(data.field).validators, function(validatorName, validatorOptions) {
            var $messageElement = $messages.filter('[data-bv-validator="' + validatorName + '"]');
            if ($messageElement.length && $messageElement.is(':visible')) {
                var priority = validatorOptions.priority || 0; // 如果没设置优先级，默认优先级为0
                if (priority > highestPriority) {
                    highestPriority = priority;
                    highestPriorityMessage = $messageElement;
                }
            }
        });
    
        // 隐藏所有错误提示
        $messages.hide();
    
        // 显示优先级最高的错误提示
        if (highestPriorityMessage) {
            highestPriorityMessage.show();
        }
    });
  },
  resetValidater: function (formId) {
    var validater = MainStationConfigLayerUtil.submitForm[formId];
    if (validater) {
      validater.data('bootstrapValidator').resetForm();
    }
  },
  renderConfigStatus: function (element, value, controller) {
    var configStateEnum = getI18nName(getProtocolConfigStateName(value));
    var statusEle = element.find('#' + element[0].id + '-label');
    statusEle.html(configStateEnum ? configStateEnum : getI18nName('Unknown'));

    statusEle.removeClass('red yellow green');
    var stateClass = ['red', 'green', 'yellow', 'yellow'];
    statusEle.addClass(stateClass[value]);

    var enableBtn = element.find('#' + element[0].id + '-enable');
    var disableBtn = element.find('#' + element[0].id + '-disable');

    enableBtn.removeClass('hide').addClass('hide');
    disableBtn.removeClass('hide').addClass('hide');

    //清除配置检查定时器
    clearTimeout(MainStationConfigLayerUtil.currentConfigCheckTimer);

    var isEditAble = false;
    if (value == 0) {
      enableBtn.removeClass('hide');
      isEditAble = true;
    } else if (value == 1) {
      disableBtn.removeClass('hide');
    } else {
      MainStationConfigLayerUtil.startConfigStatusCheckTimer();
    }
    var tempCache = MainStationConfigLayerUtil.state.currentResponseCache;
    var paramKeys = Object.keys(tempCache.configDetails);
    paramKeys.push('businessProtocol');
    paramKeys.push('saveBtn');
    paramKeys.map((key) => {
      controller[key].attr('disabled', !isEditAble);
    });
  },
  startConfigStatusCheckTimer: function (params) {
    let { communicationProtocol } =
      MainStationConfigLayerUtil.state.currentResponseCache;
    clearTimeout(MainStationConfigLayerUtil.currentConfigCheckTimer);
    MainStationConfigLayerUtil.currentConfigCheckTimer = setTimeout(
      function () {
        MainStationConfigLayerUtil.queryAndBindParams(
          communicationProtocol,
          communicationProtocol
        );
      },
      2000
    );
  },
  changeNetworkStatus: function (element, value) {
    var connectStateEnum = getI18nName(getConnectedStateName(value));
    element.html(connectStateEnum ? connectStateEnum : getI18nName('Unknown'));

    element.removeClass('red yellow green');
    var stateClass = ['red', 'yellow', 'green', 'yellow'];
    element.addClass(stateClass[value]);
  },
  switchConfigStatus: function (enableFlag, communicationProtocol, contentId) {
    // MainStationConfigLayerUtil.validateSubmitForm(contentId);
    var params = {
      communicationProtocol: communicationProtocol,
      configID: MainStationConfigLayerUtil.state.currentResponseCache.configID,
    };
    var url =
      enableFlag == 'enable'
        ? ENABLE_OUTWARD_PROTOCOL
        : DISABLE_OUTWARD_PROTOCOL;
    let check = function () {
      // pollMokerGet(
      let tipMessage =
        enableFlag == 'enable'
          ? 'OUTWARD_CONFIG_ENABLING'
          : 'SENSOR_TIMEOUT_DISABLING';

      $('.notifications')
        .notify({
          message: {
            text: getI18nName(tipMessage),
          },
          type: 'warning',
        })
        .show();

      pollGet(
        url,
        params,
        function (res) {
          MainStationConfigLayerUtil.queryAndBindParams(
            contentId,
            communicationProtocol
          );
          MainStationConfigLayerUtil.startConfigStatusCheckTimer();
        },
        stationParamsRequestErr
      );

      // setTimeout(function () {
      //   MainStationConfigLayerUtil.startConfigStatusCheckTimer();
      // }, 300);
    };
    if (enableFlag == 'enable') {
      MainStationConfigLayerUtil.saveCurrentConfig(
        contentId,
        communicationProtocol,
        check
      );
    } else {
      check();
    }
  },
  queryAndBindParams: function (contentId, type) {
    // console.log('lyx-contentId', contentId);
    var _that = this;
    // pollMokerGet(GET_OUTWARD_PROTOCOL_CONFIG,{
    pollGet(
      GET_OUTWARD_PROTOCOL_CONFIG,
      {
        communicationProtocol: type,
      },
      function (res) {
        // console.log(res);
        var details;
        var controller;
        if (type != 'I2') {
          if (contentId.indexOf('form') == -1) contentId += '-form';
          controller = _that.state.paramCache[contentId];
          var resp = res.result.protocolConfig;
          _that.state.currentResponseCache = resp[0];
          details = resp[0];
          _that.resetValidater(contentId);
        } else {
          contentId = document
            .querySelector('.tab-pane.active')
            .querySelector('.tab-pane.active').id;
          controller =
            _that.state.paramCache[contentId.replace('content', 'form')];
          var resp = res.result.protocolConfig;
          var index = contentId.split('-')[1] - 1;
          _that.state.currentResponseCache = resp[index];
          _that.state.currentContentId = contentId;
          details = resp[index];

          //I2网络状态暂时不进行绘制
          /* _that.changeNetworkStatus(
            controller.connectedState,
            details.communicationState
          ); */

          _that.resetValidater(contentId.replace('content', 'form'));
        }
        _that.renderConfigStatus(
          controller.configStatus,
          details.configStatus,
          controller
        );
        controller.businessProtocol
          .val(details.businessProtocol)
          .select2()
          .trigger('change');
        for (var key in details.configDetails) {
          var element = controller[key];
          var value = details.configDetails[key];
          if (key === 'isEnableDbToDbm') {
            element[0].checked = value;
          } else if (element) {
            if (type === 'IEC61850') {
              selectInput = controller[key]
                .parent()
                .find('#' + key + '-fake-input');
              selectInput.html(value);
              controller[key].val('');
              MainStationConfigLayerUtil.state.hasSelect61850File = false;
              MainStationConfigLayerUtil.state.paramCache[
                'IEC61850-form'
              ].saveBtn.attr('disabled', true);
            } else {
              element[0].value = value;
              element[0].tagName == 'SELECT' && element.select2();
            }
          }
        }
      },
      stationParamsRequestErr
    );
  },
  initTabEvent: function () {
    var _that = this;
    document
      .querySelectorAll('a[data-toggle="tab"]')
      .forEach(function (tabLink) {
        tabLink.addEventListener('click', function (e) {
          // console.log('station-params-tab-click:', tabLink);
          var contentId = tabLink.getAttribute('href').replace('#', '');
          var type = contentId.split('-')[0];
          MainStationConfigLayerUtil.state.hasSelect61850File = false;
          _that.queryAndBindParams(contentId, type);
          e.preventDefault();
          /* const activeForm = document.querySelector('.tab-pane.active form');
          if (hasFormChanged(activeForm)) {
              const confirmSwitch = confirm('表单内容已更改，确定要切换吗？');
              if (!confirmSwitch) {
                  e.preventDefault();
              }
          } */
        });
      });

    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      // initializeForms();
      $('.station-select').select2();
      e.preventDefault();
    });
  },
  validateSubmitForm: function (formId) {
    var validater =
      MainStationConfigLayerUtil.submitForm[formId].data('bootstrapValidator');
    validater.validate();
    return validater.isValid();
  },
  paramConfigs: [],
  state: {
    paramCache: {},
    currentResponseCache: {},
    hasSelect61850File: false,
  },
  submitForm: {},
};

function initOutwardConfigLayer() {
  MainStationConfigLayerUtil.init();
}
