/*
 * @Author: liyaoxu <EMAIL>
 * @Date: 2020-03-24 08:36:43
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-05-23 16:49:21
 * @FilePath: \docroot\pages\monitorTb\monitorTb.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
var ADUStateColor = [];
// var ADUStateColor = [
//     [-80,-100],
//     [0,-4],
//     [20,10]
// ];
function getADUStateColor(index, callback) {
    var tempCallback = callback;
    // 1是信号强度，2是信噪比，3是电量
    poll("GET", GET_ADU_STATE_COLOR_THRED, {
        type: index
    }, function (response) {
        var respData = response.result;
        ADUStateColor[index - 1] = respData;
        if (index < 3) {
            getADUStateColor(index + 1, tempCallback);
        } else {
            tempCallback();
        }
    }, null);
}

function checkDetailNeedDisable(pointType) {
    switch (pointType) {
        case 'SF6YN':
        // case 'SF6':
        // case 'NOISE':
        case 'FAN':
        case 'LIGHT':
        case 'IRDETECTION':
            return true;
    }
    return false;
}

function onItemSwitchClick(obj, aduId, value) {
    if ($($(obj.parentElement).find('img')[0]).is(':hidden') && obj.className.indexOf('active') < 0) {
        var tempValue = value == 1 ? 0 : 1;
        $($(obj.parentElement).find('.active')[0]).removeClass('active');
        $(obj).addClass('active');
        $($(obj.parentElement).find('img')[0]).show();
        var message = {
            "methodName": "operateADU",
            "enablePush": true,
            "parameters": {
                aduId: aduId,
                value: tempValue
            }
        }
        webSocketSend(message);
        // pollGet('operateADU',{aduId:aduId,value:value});
        setTimeout(function () {
            $('#tableAll').bootstrapTable('refresh', {
                silent: true
            });
        }, 5000);
    }
}

/**
 * 传感器状态检索
 * 
 */
var aduStateSearchParams = {
    aduIsOnline: 'all',
    aduId: '',
};

function searchAduState() {
    aduStateSearchParams.aduIsOnline = $('#aduStateSelect').val();
    aduStateSearchParams.aduId = $('#searchAduId').val();
    currentSensorPage = 1;
    setSession('sensorStatusIndex', 1);
    $('#tableSensorStatus').bootstrapTable('selectPage', 1);
}