//开启桌面debug模式
var windowDebug = false;

/*
 * ajax通信
 * -----------------------
 * action:通信方式post/get
 * method：方法名称
 * data：对应方法的入参
 * callback：回调函数
 */
function pollRequest(action, method, data, successcallback, failedcallback, syncFlag) {
    var sync = true;
    if (syncFlag != undefined) {
        sync = syncFlag;
    }
    var ignoreMethod = [];
    if (ignoreMethod.indexOf(method) === -1) {
        Pace.restart();
    }

    return $.ajax({
        async: sync, //sync,
        // type: action,
        type: 'POST',
        url: serverUrl + method,
        // xhrFields: { withCredentials: true },
        // crossDomain: true,
        // credentials: 'include',
        data: data,
        // contentType: action == "POST" ? "multipart/form-data" : null,
        dataType: 'json',
        success: function (result, status, xhr) {
            Pace.stop();
            if (result.errorCode == 0) {
                if (successcallback != undefined) {
                    successcallback(result);
                }
            } else {
                if (result.errorCode == 901) {
                    logOut('repeatLogin');
                    //lyx-TO DEBUG
                    // successcallback(result);
                    return;
                }
                if (result.errorCode == 907) {
                    logOut('logOut');
                    return;
                }
                if (failedcallback != undefined) {
                    failedcallback(result.errorCode);
                }
                commHttpFaliedCallback(result);
            }
        },
        error: function (result, status, xhr) {
            Pace.stop();
            if (failedcallback != undefined) {
                failedcallback('error');
            }
            // console.log(("undefined" == typeof result) ? status : result);


        },
        complete: function (XHR, TS) {
            Pace.stop();
            if ($("body").hasClass('pace-running')) {
                $("body").removeClass('pace-running')
            }
            if ($("body").hasClass('pace-done')) {
                $("body").removeClass('pace-done')
            }

            XHR = null;
        }
    });
}

function initDefServerUrl(callback, onPushError) {
    //获取当前url地址
    serverUrl = window.location.protocol + "//" + window.location.host;
    if (windowDebug) {
        serverUrl = debugServer;
    }
    if (serverUrl.length != serverUrl.lastIndexOf('/') + 1) {
        serverUrl = serverUrl + '/';
    }
    /*  if (callback){
     callback();
     }*/
    pollRequest('Get', 'Push', null, function (text) {
        var respData = text.result;
        if (window.location.protocol === 'https:') {
            wsUri = 'wss://';
        } else {
            wsUri = 'ws://';
        }
        wsUri = wsUri + window.location.hostname + ':' + respData.port + '/' + respData.socketName;
        // needLogin = respData.needLoginScene === 1; //是否需要安全测评
        needLogin = true; //是否需要安全测评
        if (windowDebug) {
            wsUri = debugWebSocket;
        }
        if (callback) {
            callback(text);
        }
        if (!needLogin) {
            connectToWebSocket(true, null);
        }
    }, onPushError, true);
}
/*
 * Websocket保持通信
 * -----------------------
 * waitForConnection：是否常连接
 */
function connectToWebSocket(waitForConnection, waitCallback, checkConnect) {
    if (WebSocket != undefined) {
        if (!checkConnect) {
            initWebSocket();
        }
        if (waitForConnection == true) {
            // console.log(new Date().getSeconds());
            setTimeout(function () {
                if (websocket.readyState === 1) {
                    if (waitCallback != null && !checkConnect) {
                        waitCallback();
                    }
                    connectToWebSocket(waitForConnection, waitCallback, true);
                    return;
                } else if (websocket.readyState === 3) {
                    logOut('wsLogOut')
                } else {
                    console.log('Waiting for WebSocket connection...');
                    if (websocket != undefined) {
                        websocket = null;
                    }
                    //重连需要重新登录ws
                    var name = getSession('user');
                    var password = getSession('token');
                    initWSonLogin(name, password);
                    // connectToWebSocket(waitForConnection, waitCallback);
                }

            }, 2000);
        }
    }
}
/*
 * websocket发送消息
 * -----------------------
 * message：向服务器发送数据
 */
function webSocketSend(message) {
    console.log(message);
    if (WebSocket != undefined) {
        if (websocket != undefined) {
            if (websocket.readyState === 1) {
                message.sessionId = clientGUID;
                websocket.send(JSON.stringify(message));
            } else {
                if (websocket.readyState === 3) {
                    logOut('wsLogOut')
                } else {
                    connectToWebSocket(true, null);
                    message.sessionId = clientGUID;
                    websocket.send(JSON.stringify(message));
                }
            }
        }
    }
}

function poll(action, method, data, successcallback, failedcallback, syncFlag){
    function runRequest(){
        pollRequest(action, method, data, successcallback, failedcallback, syncFlag)
    }
    if(!checkSessionUser(runRequest)){
        return;
    } else{
        runRequest();
    }
}

/*
 * ajax通信GET
 * -----------------------
 * method：方法名称
 * data：对应方法的入参
 * callback：回调函数
 */
function pollGet(method, data, successcallback, failedcallback) {
    poll('GET', method, data, successcallback, failedcallback);
}
/*
 * ajax通信POST
 * -----------------------
 * method：方法名称
 * data：对应方法的入参
 * callback：回调函数
 */
function pollPost(method, data, successcallback, failedcallback) {
    poll('POST', method, data, successcallback, failedcallback)
}

function checkSessionUser(runRequest){
    var sessionUser = getSession('user');
    var cookieUser = getCookie('user');
    
    if(sessionUser != undefined && sessionUser!= cookieUser){
        var tempName = RSAUtil.encode(sessionUser);
        tempPassword = getSession('token');

        var postForm = {
            userName: tempName,
            password: tempPassword
        };
        pollRequest('POST',POST_LOGIN, postForm, function (text) {
            var respData = text.result;
            if (respData.loginResult === "loginSuccess") {
                setSession('user', sessionUser);
                setSession('token', tempPassword);
                setSession('isLogOut', false);
                setSession('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));
                setCookie('user', sessionUser);
                setCookie('token', tempPassword);
                setCookie('isLogOut', null);
                setCookie('userInfo', new Base64Util().encode(JSON.stringify(respData.userInfo)));

                var localName = tempName;
                var localPassword = tempPassword;
                initWSonLogin(localName, localPassword);

                runRequest();

            }
            setTimeout(function(){
                Pace.stop();
            },500)
        }, function (errorCode) {
            Pace.stop();
            $('#login').removeAttr('disabled')
            modals.error(this, '', "error");
        });
        return false;
    }
    return true;
}


/**
 * 模拟数据调试接口请求GET
 */
function pollMokerGet(method, data, successcallback, failedcallback){
    pollMokerRequest('GET', method, data, successcallback, failedcallback);
}
/**
 * 模拟数据调试接口请求POST
 */
function pollMokerPost(method, data, successcallback, failedcallback){
    pollMokerRequest('POST', method, data, successcallback, failedcallback);
}

/**
 * 模拟数据调试接口请求
 */
function pollMokerRequest(action, method, data, successcallback, failedcallback, syncFlag) {
    var sync = true;
    if (syncFlag != undefined) {
        sync = syncFlag;
    }
    var ignoreMethod = [];
    if (ignoreMethod.indexOf(method) === -1) {
        Pace.restart();
    }

    return $.ajax({
        async: sync, //sync,
        type: action,
        // type: 'POST',
        url: mokerServer + method,
        data: data,
        // contentType: action == "POST" ? "multipart/form-data" : null,
        dataType: 'json',
        success: function (result, status, xhr) {
            Pace.stop();
            if (result.errorCode == 0) {
                if (successcallback != undefined) {
                    successcallback(result);
                }
            } else {
                if (result.errorCode == 901) {
                    logOut('repeatLogin');
                    //lyx-TO DEBUG
                    // successcallback(result);
                    return;
                }
                if (result.errorCode == 907) {
                    logOut('logOut');
                    return;
                }
                if (failedcallback != undefined) {
                    failedcallback(result.errorCode);
                }
                commHttpFaliedCallback(result);
            }
        },
        error: function (result, status, xhr) {
            Pace.stop();
            if (failedcallback != undefined) {
                failedcallback('error');
            }
            // console.log(("undefined" == typeof result) ? status : result);


        },
        complete: function (XHR, TS) {
            Pace.stop();
            if ($("body").hasClass('pace-running')) {
                $("body").removeClass('pace-running')
            }
            if ($("body").hasClass('pace-done')) {
                $("body").removeClass('pace-done')
            }

            XHR = null;
        }
    });
}

// /**
//  * 系统级监听动作
//  */
// var systemActions = {};
// var systemParam;
// var sysDateTime;
// systemActions[UPDATE_SYSTEM_STATE] = function (response) {
//     var value = response.result.dateTime;
//     sysDateTime = new Date(value * 1000);
//     $(".time-mini>p").html(sysDateTime.pattern("MM/dd<br>HH:mm"));
//     $(".time-lg>p").html(sysDateTime.pattern("yyyy/MM/dd HH:mm:ss"));
//     systemParam = response.result;
//     if ($('#cacdataParameterLifeMark').val() == 'true') {
//         delete systemParam.dateTime;
//         cacdataParameterRefresh(systemParam);
//     }
//     if ($('#timeSettingParamPage').val() == 'true') {
//         refreshSysTimeSetting();
//     }
// };

/*
var socketActionQueue = {};

!function initSystemActions() {
    for (var item in systemActions) {
        socketActionQueue[item] = systemActions[item];
        var lifeFlag = '<input id="' + item + '" class="lifeFlag hide" value="true"/>'
        $('body').append(lifeFlag);
    }
}();

/!**
 * 注册socketAction监听动作
 * @param actionName
 * @param action
 *!/
function registSocketAction(actionName, action) {
    var tempAction = socketActionQueue[actionName];
    if (typeof tempAction === 'function') {
        console.log("该动作已注册监听，请确认名称是否唯一->" + actionName);
    } else {
        var lifeFlag = '<input id="' + actionName + '" class="lifeFlag hide" value="true"/>'
        $('.main-content').append(lifeFlag);
        socketActionQueue[actionName] = action;
    }
}

//Socket监听分发
function checkSocketMsg(response) {
    var actionName = response.action;
    if ($('#' + actionName).val() != 'true') {
        //注销监听动作
        socketActionQueue[actionName] = undefined;
    }
    var action = socketActionQueue[actionName];
    if (typeof action === 'function') {
        action(response);
    } else {
        if (windowDebug) {
            console.log("未注册/已注销的监听动作->" + actionName);
        }
    }
}*/