<!--<div id="AE图谱" class="full-height">-->
<!-- left column -->
<!-- <script src="./pages/uhfChart/testData.js"></script> -->
<script src="./pages/uhfChart/uhfChartData.js"></script>
<div class="col-sm-12 full-height">
  <div class="nav-tabs-custom full-height">
    <div id="graph-UHF" class="col-sm-7 border-left full-height">
      <div
        id="graph-head"
        class="row border-right"
        style="height: 48px; border: 1px solid #f4f4f4"
      >
        <div class="col-sm-6">
          <button
            type="button"
            id="showPRPS3D"
            class="btn btn-block btn-primary"
            style="width: 100px; margin: 5px 0px 10px auto"
          >
            PRPS3D
          </button>
        </div>
        <div class="col-sm-6">
          <button
            type="button"
            id="showPRPD2D"
            class="btn btn-block btn-primary"
            style="width: 100px; margin: 5px 60px 10px auto"
          >
            PRPD2D
          </button>
        </div>
      </div>
      <div class="row border-left" id="uhfDiv">
        <div id="prps"></div>
        <div id="prpd"></div>
      </div>
      <div id="graph-end" class="row hide" style="border: 1px solid #f4f4f4">
        <input
          type="text"
          value=""
          class="slider form-control"
          data-slider-min="0"
          data-slider-max="360"
          data-slider-step="1"
          data-slider-value="0"
          data-slider-orientation="horizontal"
          data-slider-selection="before"
          data-slider-tooltip="show"
          data-slider-id="blue"
        />
      </div>
    </div>
    <div
      class="col-sm-5 full-height bootstrap-dialog"
      style="border: 1px solid #f4f4f4"
    >
      <form class="form-horizontal">
        <br />
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="pointName"
            >测点名称:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="pointName"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sensorCode"
            >传感器编码:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="sensorCode"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sensorType"
            >传感器类型:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="sensorType"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="gain">增益:</label>
          <div class="col-sm-6">
            <label class="control-label" id="gain"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="wave_filter"
            >频带:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="wave_filter"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sample_date"
            >采样日期:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="sample_date"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="sample_time"
            >采样时间:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="sample_time"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="TOP_DISCHARGE_AMPLITUDE"
            >TOP3放电幅值:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="pd_max"></label>
          </div>
        </div>
        <!--<div class="form-group">-->
        <!--<label class="col-sm-6 control-label">最小放电量:</label>-->
        <!--<div class="col-sm-6">-->
        <!--<label class=" control-label" id="pd_min">24%</label>-->
        <!--</div>-->
        <!--</div>-->
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="pdAvg"
            >平均放电量:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="pd_avg"></label>
          </div>
        </div>
        <div class="form-group">
          <label class="col-sm-6 control-label" data-i18n="pdNum"
            >脉冲个数:</label
          >
          <div class="col-sm-6">
            <label class="control-label" id="pd_number"></label>
          </div>
        </div>
      </form>
    </div>
    <div class="content-footer">
      <div class="col-sm-3">
        <button
          type="button"
          id="now_data"
          class="btn btn-block btn-primary hide"
          style="width: 100px; margin: 10px 0px 10px auto"
          data-i18n="historyData"
        >
          历史数据
        </button>
      </div>
      <div class="col-sm-3">
        <button
          type="button"
          id="history_data"
          class="btn btn-block btn-primary hide"
          style="width: 100px; margin: 10px 0px 10px auto"
          data-i18n="realData"
        >
          实时数据
        </button>
      </div>
      <div class="col-sm-3">
        <button
          type="button"
          id="preData"
          class="btn btn-block btn-primary"
          style="width: 140px; margin: 10px 0px 10px auto"
          data-i18n="preData"
        >
          上一条
        </button>
      </div>
      <div class="col-sm-3">
        <button
          type="button"
          id="nextData"
          class="btn btn-block btn-primary"
          style="width: 140px; margin: 10px 0px 10px auto"
          data-i18n="nextData"
        >
          下一条
        </button>
      </div>
    </div>
  </div>
</div>
<script type="text/javascript">
  initPageI18n();
  $(function () {
    //@ sourceURL=uhf_chart.js

    // 1. 首先声明所有变量
    var preId = 0,
      nextId = 0;
    // 声明缓存对象
    let chartCache = {
      prps: null,
      prpd: null,
      currentId: null,
    };

    // 2. 然后是所有函数定义
    function requestUHFData(dataId, pointId, type, callback) {
      const pointType = 'UHF';
      const data = `dataId=${dataId}&pointId=${pointId}&pointType=${pointType}&dataType=${type}`;

      poll('GET', 'GetChartData', data, function (text) {
        const respData = text.result;

        // 更新按钮状态
        preId = respData.preDataId;
        nextId = respData.nextDataId;
        showChartDataId = dataId;
        updateNavigationButtons();

        // 处理坐标轴描述
        const chartData = respData.chartBody;
        processAxisDescriptions(chartData);

        if (type === 0) {
          chartCache.TOPNValue = undefined;
          //处理特征值缓存记录，供图谱绘图使用
          let unit = respData.chartBody ? respData.chartBody.axisInfo.zUnit : 'dB';
          let value = respData.params['pd_max'].replace(unit, '');
          chartCache.TOPNValue = parseFloat(value);
        }

        preCheckUHFParams(respData, chartCache.isUnitChanged, chartCache);

        // 根据类型缓存数据
        if (type === 0) {
          chartCache.prps = respData;
        } else {
          chartCache.prpd = respData;
        }

        if (callback) callback(respData);
      });
    }

    function drawChart(type, initChart = false) {
      // 定义回调函数对象
      const callbacks = {
        updateParamsDisplay: function (params) {
          const sensorType = getI18nName(params.sensorType);
          $('#pointName').html(params.pointName);
          $('#sensorCode').html(params.aduId);
          $('#sensorType').html(sensorType);
          $('#gain').html(params.gain);
          $('#sample_date').html(params.sample_date);
          $('#sample_time').html(params.sample_time);
          $('#pd_max').html(params.pd_max);
          $('#pd_min').html(params.pd_min);
          $('#pd_avg').html(params.pd_avg);
          $('#pd_number').html(params.pd_number);

          const filter = params.filter;
          $('#wave_filter')
            .parent()
            .parent()
            .removeClass('hide')
            .addClass('hide');
          if (filter) {
            $('#wave_filter').parent().parent().removeClass('hide');
            $('#wave_filter').html(getFilter(filter));
          }
        },
      };

      if (initChart) {
        // 清空并重建子div
        $('#prps').html(
          '<div id="prps-container" style="width: 100%; height: 100%;"></div>'
        );
        $('#prpd').html(
          '<div id="prpd-container" style="width: 100%; height: 100%;"></div>'
        );
        let prpsChartData = getUHFPRPSChartBody(
          chartCache.prps,
          chartCache,
          callbacks
        );
        chartCache.prpsDataList = prpsChartData.series[0].dataList;
        // 初始化时绘制两种图表
        if (chartCache.prps) {
          pdcharts.draw($('#prps-container')[0], {
            title: 'UHF PRPS',
            width: $('#uhfDiv').width(),
            height: $('#uhfDiv').height(),
            type: pdcharts.chartType.uhfprpsd,
            data: prpsChartData,
          });
        }
        if (chartCache.prpd) {
          pdcharts.draw($('#prpd-container')[0], {
            width: $('#uhfDiv').width(),
            height: $('#uhfDiv').height(),
            type: pdcharts.chartType.uhfprpd2d,
            data: getUHFPRPDChartBody(chartCache.prpd, chartCache, callbacks),
          });
        }
      }

      // 根据类型切换显示
      if (type === 0) {
        $('#prps').show();
        $('#prpd').hide();
      } else {
        $('#prps').hide();
        $('#prpd').show();
      }
    }

    function updateNavigationButtons() {
      $('#preData').attr('disabled', preId === 0);
      $('#nextData').attr('disabled', nextId === 0);
    }

    function loadUHFPRPS(dataId, pointId, type) {
      $('#prps').hide();
      $('#prpd').hide();

      // 如果是同一个数据ID，直接切换显示
      if (chartCache.currentId === dataId) {
        drawChart(type, false);
        return;
      }

      // 新数据请求
      chartCache.currentId = dataId;

      // 请求PRPS数据
      requestUHFData(dataId, pointId, 0, function (prpsData) {
        // 请求PRPD数据
        requestUHFData(dataId, pointId, 1, function (prpdData) {
          // 两次请求都完成后初始化绘制图表
          drawChart(type, true);
          // 更新参数显示
          // const currentData = type === 0 ? chartCache.prps : chartCache.prpd;
          const currentData = chartCache.prps;
          if (currentData && currentData.params) {
            updateParamsDisplay(currentData.params);
          }
        });
      });
    }

    function updateParamsDisplay(params) {
      const sensorType = getI18nName(params.sensorType);
      $('#pointName').html(params.pointName);
      $('#sensorCode').html(params.aduId);
      $('#sensorType').html(sensorType);
      $('#gain').html(params.gain);
      $('#sample_date').html(params.sample_date);
      $('#sample_time').html(params.sample_time);
      $('#pd_max').html(params.pd_max);
      $('#pd_min').html(params.pd_min);
      $('#pd_avg').html(params.pd_avg);
      $('#pd_number').html(params.pd_number);

      const filter = params.filter;
      $('#wave_filter').parent().parent().removeClass('hide').addClass('hide');
      if (filter) {
        $('#wave_filter').parent().parent().removeClass('hide');
        $('#wave_filter').html(getFilter(filter));
      }
    }

    function processAxisDescriptions(chartData) {
      ['x', 'y', 'z'].forEach((axis) => {
        const desc = globalParms[chartData.axisInfo[`${axis}Desc`]];
        if (validateStrLen(desc) !== 0) {
          chartData.axisInfo[`${axis}Desc`] = desc;
        }
      });
    }

    // 3. 最后是初始化和事件绑定
    $('#prps').hide();
    $('#prpd').hide();

    $(window).resize(function () {
      if (!document.getElementById('uhfDiv')) {
        //js判断元素是否存在
        return;
      }
      var height =
        $('#graph-UHF').height() -
        $('#graph-head').height() -
        $('#graph-end').height() -
        $('.content-footer').height() -
        20;
      $('#uhfDiv').height(height);

      var svgEls = document.querySelector('svg');
      if (svgEls) {
        svgEls.setAttribute('width', $('#uhfDiv').width());
        svgEls.setAttribute('height', $('#uhfDiv').height());
      }
    });
    $(window).resize();

    $('.slider').slider();

    $('#showPRPS3D').click(function () {
      $('#showPRPD2D').removeAttr('disabled');
      $('#showPRPS3D').attr('disabled', true);
      loadUHFPRPS(showChartDataId, showChartPointId, 0);
    });

    $('#showPRPD2D').click(function () {
      $('#showPRPS3D').removeAttr('disabled');
      $('#showPRPD2D').attr('disabled', true);
      loadUHFPRPS(showChartDataId, showChartPointId, 1);
    });

    $('#showPRPS3D').trigger('click');

    $('#preData').click(function () {
      if (preId === 0) {
        layer.alert(getI18nName('noTestData'), {
          title: getI18nName('tips'),
          btn: [getI18nName('close')],
        });
        return;
      }

      // 根据当前显示的图表类型决定加载哪种类型
      const type = $('#showPRPD2D').attr('disabled') ? 1 : 0;
      loadUHFPRPS(preId, showChartPointId, type);
    });

    $('#nextData').click(function () {
      if (nextId === 0) {
        layer.alert(getI18nName('noTestData'), {
          title: getI18nName('tips'),
          btn: [getI18nName('close')],
        });
        return;
      }

      // 根据当前显示的图表类型决定加载哪种类型
      const type = $('#showPRPD2D').attr('disabled') ? 1 : 0;
      loadUHFPRPS(nextId, showChartPointId, type);
    });
  });
</script>
