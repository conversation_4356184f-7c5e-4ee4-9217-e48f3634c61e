{"loginTime": "وقت الدخول  ", "changerUser": " تبديل المستخدم  ", "change": " تبديل  ", "login": " سجل  ", "systemShutDownConfirmTip": " النظام سوف تغلق في 1 دقيقة ، هل تريد إلغاء العملية ؟  ", "systemShutDownTip": " 1 دقيقة ، الاغلاق التلقائي !  ", "systemShutDownCancel": " إلغاء اغلاق  ", "loadingData": " الرجاء الانتظار بينما كنت تحاول تحميل البيانات .  ", "pageSize": " المادة { 1 }  ", "pageSelecter": " عرض : { 1 } - { 2 } ، ما مجموعه { 3 }  ", "search": " بحث .  ", "noMatch": " لم يتم العثور على سجلات مطابقة  ", "index": " الرقم المتسلسل  ", "type": " نوع .  ", "aduId": " ترميز  ", "connectedState": " حالة الشبكة  ", "commType": " طريقة الاتصال  ", "loraSignalLevel": " لورا قوة الإشارة  ", "loraSNR": " نسبة الإشارة إلى الضوضاء  ", "powerSupplyMode": " وضع امدادات الطاقة  ", "byBattery": " المدمج في البطارية  ", "byCable": " إمدادات الطاقة الخارجية  ", "battaryPercent": " كمية الكهرباء  ", "battaryLife": " ت<PERSON><PERSON>ي<PERSON> الوقت ( أيا<PERSON> )  ", "deviceNameTable": " اسم الجهاز  ", "pointType": " سنسر  ", "isconnected": " مر<PERSON><PERSON>  ", "onlineState": " آنلاین  ", "offlineState": " فق<PERSON> الاتحاد  ", "aduNeverConnectedState": " <PERSON>ير متصل  ", "updateTime": " وقت التحديث  ", "data": " داتا  ", "chart": " أطلس  ", "operate": " العمليات  ", "lookUp": " عرض .  ", "tbSampleDate": " جمع الوقت  ", "collect": " جمع  ", "errorTip": " خطأ : { 1 }  ", "AEamp": " ا السعة  ", "aeChartTitle": " <PERSON><PERSON><PERSON> اللطيف الطيف السعة  ", "humidityChartTitle": " منحنى الرطوبة  ", "temperatureChartTitle": " منحنى درجة الحرارة  ", "noiseChartTitle": " الضوضاء منحنى  ", "coreGroundingCurrentChartTitle": " منحنى التأريض الحالية الأساسية  ", "TEVamp": " تيف السعة  ", "TEVYunit": " وحدة [ ديسيبل ]  ", "UnitFomat": " الوحدة  ", "unitParse": " الوحدة  ", "maxValue": " القيمة القصوى  ", "minValue": " قيمة الحد الأدنى  ", "average": " متوسط القيمة  ", "AT&T": " ايه تي اند تي  ", "China Unicom": " تشاينا يونيكوم  ", "China Mobile": " تشاينا موبايل  ", "Transformer": " المحولات  ", "Breaker": " دائرة الكسارة  ", "Disconnector": " عزل التبديل  ", "Isolator": " سكين الفرامل  ", "Arrester": " صواعق البرق  ", "PT": " محو<PERSON> الجهد  ", "CT": " محو<PERSON> الحالي  ", "Busbar": " بسبار  ", "Circuit": " أ<PERSON> الاتحاد  ", "Switchgear": " مجلس الوزراء التبديل  ", "Power Cable": " كابل الطاقة  ", "Lightning Rod": " مانعة الصواعق  ", "Wall Bushing": " <PERSON>لا<PERSON> الج<PERSON><PERSON>ر  ", "Reactor": " مفاعل  ", "Electric Conductor": " الطاقة الكهربائية موصل ( منحرف )  ", "Power Capacitor": " الطاقة الكهربائية حاوية  ", "Discharge Coil": " لفائف التفريغ  ", "Load Switch": " تحميل التبديل  ", "Grounding Transformer": " أسس التغيير  ", "Grounding Resistance": " أسس المقاومة  ", "Grounding Grid": " شبكة التأريض  ", "Combined Filter": " الجمع بين فلتر  ", "Insulator": " <PERSON>ازل  ", "Coupling Capacitor": " اقتران مكثف  ", "Cabinet": " مجلس الوزراء الشاشة  ", "Other": " أخر<PERSON> .  ", "Fuse": " الصمامات  ", "Using Transformer": " التغييرات المستخدمة  ", "Arc Suppression Device": " قوس إطفاء الجهاز  ", "Main Transformer": " المحولات الرئيسية  ", "Wave Trap": " موجة سدادة  ", "Combined Electric Appliance": " الجمع بين الأجهزة الكهربائية  ", "Combined Transformer": " الجمع بين محول  ", "monitor": " في الوقت الحقيقي رصد  ", "dataSelect": " بيانات الاستعلام  ", "themeSkin": " الجلد .  ", "themeBlue": " بلو  ", "settings": " نظام التكوين  ", "datetime_set": " وقت الإعداد  ", "language_set": " إعدادات اللغة  ", "soft_setting": " إعدادات النظام  ", "file_manage": " إدارة المحفوظات  ", "instrument_set": " تكوين أجهزة الاستشعار  ", "file_point_set": " نقطة قياس التكوين  ", "alarm_param_set": " إنذار التكوين  ", "alarm_manager": " إنذار إدارة  ", "audit_view": " مراجعة عرض  ", "modbus_setting": " إعدادات الناقل Modbus  ", "hardware_update": " تحديث البرامج الثابتة  ", "collect_mode": " طريقة جمع  ", "net_set": " إعدادات الشبكة  ", "main_station_params": " تكوين المحطة الرئيسية  ", "sync_data": " مزامنة البيانات  ", "syncingData": " مزامنة البيانات .  ", "syncingDataCancel": " إلغاء التزامن  ", "syncingDataFailed": " فشل التزامن  ", "syncingDataSuccess": " تزامن البيانات كاملة  ", "syncingDataProgress": " تزامن البيانات التقدم : { 1 }  ", "syncingDataProgress2": " مزامنة البيانات التقدم ( [ 1 ] )  ", "export_data": " تصدير البيانات  ", "system_info": " نظام المعلومات  ", "monitoringTable": " بيانات الجدول  ", "PD": " المدمج في أجهزة الاستشعار المحلية  ", "PD_THREE": " المدمج في أجهزة الاستشعار المحلية ( ثلاثة في واحد )  ", "PD_FIVE": " المدمج في أجهزة الاستشعار المحلية ( خمسة في واحد )  ", "OLD_IS": " الترا عالية التردد الاستشعار الذكي  ", "MEU": " الخصائص الميكانيكية الاستشعار  ", "UHF_IS": " الترا عالية التردد الاستشعار الذكي  ", "HFCT_IS": " عالية التردد الاستشعار الذكي  ", "PD_IS": " ثلاثة في واحد استشعار ذكي خارجي  ", "TRANSFORMER_AE_IS": " محول بالموجات فوق الصوتية الاستشعار  ", "GIS_AE_IS": " نظم المعلومات الجغرافية الاستشعار بالموجات فوق الصوتية  ", "ENV_IS": " البيئة الذكية الاستشعار  ", "Arrester_U_IS": " صواعق الجهد الاستشعار الذكي  ", "Arrester_I_IS": " صواعق البرق الاستشعار الذكية الحالية  ", "LeakageCurrent_IS": " تسرب الاستشعار الذكية الحالية  ", "Vibration_IS": " الاهتزاز الاستشعار الذكي  ", "MECH_IS": " ذكي أداة رصد الخصائص الميكانيكية  ", "TEMP_HUM_IS": " درجة الحرارة والرطوبة الاستشعار  ", "GrounddingCurrent_IS": " الأرض الحالية الاستشعار الذكي  ", "MECH_PD_TEMP": " بنيت في التفريغ الجزئي الخصائص الميكانيكية استشعار درجة الحرارة ثلاثة في واحد  ", "GIS_LOWTEN": " استشعار الضغط المنخفض  ", "CHANNEL_OPTICAL_TEMP": " الألياف البصرية وأجهزة الاستشعار درجة الحرارة  ", "VaisalaDTP145": " فيزا DTP145  ", "Wika_GDT20": " ويكا gdt20  ", "Wika_GDHT20": " ويكا gdht20  ", "SHQiuqi_SC75D_SF6": " شنغهاي Qiuqi sc75d  ", "SPTR_IS": " الحديد الأساسية التأريض الاستشعار الحالية  ", "TEMPFC_OUT": " استشعار درجة الحرارة ( clmd ) .  ", "TEMPXP_OUT": " استشعار درجة الحرارة ( SPS061 )  ", "SF6YN_OUT": " سادس فلوريد الكبريت الغاز استشعار الضغط ( ftp-18 ) .  ", "FLOOD_IS": " غمر المياه الاستشعار الذكي  ", "TEMPKY_OUT": " استشعار درجة الحرارة ( rfc-01 ) .  ", "SMOKERK_OUT": " الدخان الاستشعار إنذار ( rs-yg-n01 ) .  ", "HIKVIDEO_OUT": " فيديو الاستشعار ( HIK )  ", "TEMPSB_OUT": " استشعار درجة الحرارة ( ds18b20 ) .  ", "TEMP_HUM_JDRK_OUT": " درجة الحرارة والرطوبة الاستشعار ( rs-ws-n01-2 ) .  ", "SF6_OUT": " SF6 و O2 الاستشعار ( wfs-s1p-so ) .  ", "FAN_OUT": " مروحة وحدة تحكم  ", "LIGHT_OUT": " وحدة تحكم الإضاءة  ", "NOISE_JDRK_OUT": " الضوضاء الاستشعار ( rs-ws-n01 ) .  ", "IR_DETECTION_OUT": " الأشعة تحت الحمراء للكشف عن التمييز المزدوج  ", "TEMPWS_OUT": " درجة الحرارة والرطوبة الاستشعار ( ws-dmc100 ) .  ", "FLOODWS_OUT": " غمر المياه الاستشعار ( ws-dmc100 ) .  ", "NOISEWS_OUT": " الضوضاء الاستشعار ( ws-dmc100 ) .  ", "VibrationSY_OUT": " الشمس استشعار الاهتزاز  ", "TEVPRPS_IS": " ذكي استشعار الج<PERSON>د عابر  ", "SF6_IS": " SF6 الاستشعار الذكي  ", "phase": " المرحلة  ", "period": " دورة  ", "AMP": " السعة  ", "RMS": " قيمة فعالة  ", "max": " الفترة القصوى  ", "fre1Value": " عنصر التردد  ", "fre2Value": " عنصر التردد  ", "All-pass": " كوالكوم  ", "Low-pass": " انخفاض تمرير  ", "High-pass": " كوالكوم  ", "No-Record": " <PERSON>ير مسجل  ", "TEV": " ا<PERSON><PERSON><PERSON><PERSON> عابر  ", "AE": " بالموجات فوق الصوتية  ", "TEMP": " درجة الحرارة  ", "HFCT": " عالية التردد الحالي  ", "UHF": " الفائق  ", "Humidity": " الرطوبة  ", "Decibels": " ديسيبل  ", "Noisy": " ضغط الصوت  ", "Noise": " ضوضاء  ", "ENVGas": " الغلا<PERSON> الجوي  ", "envgas": " الاتجاه في الغلاف الجوي  ", "MP": " الخصائص الميكانيكية  ", "FLOOD": " غمر  ", "SMOKE": " شعور الدخان  ", "SF6": " سادس فلوريد الكبريت  ", "FAN": " مروحة  ", "LIGHT": " إنارة  ", "NOISE": " ضوضاء  ", "IRDETECTION": " الأشعة تحت الحمراء  ", "SF6YN": " غاز سداسي فلوريد الكبريت الضغط  ", "ArresterU": " صواعق الجهد  ", "ArresterI": " صواعق البرق الحالية  ", "LeakageCurrent": " تسرب الحالية  ", "Vibration": " اهتزازي  ", "FrostPointRaw": " فروست بوينت  ", "FrostPointATM": " نقطة الصقيع ( معيار الضغط الجوي )  ", "DewPointRaw": " نقطة الندى  ", "DewPointATM": " نقطة الندى ( معيار الضغط الجوي )  ", "Moisture": " الماء الصغير  ", "AbsolutePressure": " الضغط المطلق  ", "NormalPressure": " معيار الضغط  ", "Density": " كثافة  ", "Oxygen": " محتوى الأكسجين  ", "SPTR": " الحديد الأساسية التأريض الحالية  ", "GrounddingCurrent": " التأريض الحالية  ", "VIDEO": " فيديو صور  ", "TEVPRPS": " عا<PERSON>ر الج<PERSON><PERSON> الأرضي PRPS  ", "confirm": " أكّد  ", "close": " غلق  ", "yes": " نعم .  ", "no": " لا .  ", "continue": " استمر  ", "none": " لا  ", "DISK": " خزن  ", "MEMORY": " الذاكرة .  ", "noTestData": " لا بيانات الاختبار  ", "connected": " رب<PERSON>  ", "disconnected": " قطع الاتصال  ", "virtualKeyBord": " لوحة المفاتيح الافتراضية  ", "pleaseInput": " من فضلك أدخل  ", "pleaseInputFormat": " من فضلك أدخل { 1 } .  ", "tips": " جديلة  ", "confirmTips": " تأكيد  ", "getDirFailed": " فشل في الحصول على الدليل :  ", "backupSuccessTip": " بيانات النسخ الاحتياطي بنجاح ، يرجى الرجوع إلى { 1 } دليل  ", "backupFailedTip": " فشل النسخ الاحتياطي للبيانات  ", "exportFailedTip": " فشل تصدير البيانات : { 1 }  ", "historyDataTypeNotSuport": " الاستعلام عن البيانات التاريخية من هذا النوع من أجهزة الاستشعار غير معتمد مؤقتا  ", "stopExportTips": " هل تريد إيقاف تصدير البيانات ؟  ", "stationNameTips": " يرجى ملء اسم الموقع  ", "powerUnitNameTips": " يرجى ملء وحدة الطاقة  ", "selectDeviceTips": " الرجاء اختيار الجهاز مرة واحدة  ", "selectPointTips": " يرجى اختيار نقطة قياس  ", "selectDeviceOfPointTips": " الرجاء اختيار المعدات التي تحتاج إلى إضافة نقطة قياس  ", "saveSuccess": " ح<PERSON><PERSON> بنجاح  ", "saveFailed": " فشل الحفظ :  ", "operatFailed": " فشل العملية : { 1 }  ", "chosseSensorUpdate": " الرجاء اختيار أجهزة الاستشعار التي تحتاج إلى تحديث  ", "chooseFileUpdate": " الرجاء اختيار ملف التحديث  ", "cancelUpdateSensorConfirm": " هل أنت متأكد من أنك تريد إلغاء تحديث برنامج الاستشعار ؟  ", "enterServerUrlTips": " الرجاء إدخال عنوان الخادم  ", "enterServerUrlError": " خطأ في إدخال عنوان الخادم ، يرجى إعادة إدخال .  ", "enterServerPortTips": " من فضلك أدخل خادم الميناء .  ", "enterServerPortError": " خادم ميناء الإدخال خطأ ، ميناء الإدخال مجموعة : [ 1 , 65535 ] .  ", "NTPserverIpError": " خطأ في إدخال عنوان الخادم في الوقت المناسب .  ", "NTPserverPortError": " خطأ في وقت الخادم ميناء الإدخال ، ميناء الإدخال مجموعة : [ 1 , 65535 ] .  ", "enterNetName": " من فضلك أدخل اسم الشبكة .  ", "enterIP": " الرجاء إدخال الملكية الفكرية .  ", "enterIPError": " عنوان بروتوكول الإنترنت خطأ الإدخال ، مثل : ***********  ", "enterNetMask": " من فضلك أدخل قناع الشبكة الفرعية  ", "enterNetError": " قناع الشبكة الفرعية إدخال خطأ ، مثل : *************  ", "enterGateWay": " الرجاء إدخال بوابة  ", "enterGateWayError": " تكوين بوابة الخطأ ، مثل : ***********  ", "enterAPN": " الرجاء اختيار APN  ", "pdSampleIntervalTip": " إن pd-sampling الفاصلة بين 20s و 594099s ( 99h99m99s ) .  ", "meuSampleIntervalTip": " مو - <PERSON><PERSON><PERSON> العينات الفاصلة بين 10 و 86400  ", "monitorAwakeTimeTip": " فترة السكون بين 1 و 86400  ", "monitorSleepSpaceTip": " الفاصل الزمني بين الصحوة : [ { 1 } { 2 } ]  ", "uploadIntervalTip": " تحميل الفاصلة بين 60s و 86400  ", "sampleIntervalTip": " عد<PERSON> صحيح بين 1 ساعة و 72 ساعة من فترة أخذ العينات  ", "monitorSampleTimeTip": " كامل الوقت بين 0 و 23 نقطة في وقت أخذ العينات الأولية من المضيف  ", "monitorSampleIntervalTip": " عد<PERSON> صحيح بين 1 ساعة و 168 ساعة من المضيف فترة المعاينة  ", "updatingSensor": " تحديث أجهزة الاستشعار .  ", "updatingSensorProgress": " التقدم المحرز في تحديث أجهزة الاستشعار : { 1 }  ", "updatingSensorProgress2": " التقدم المحرز في تحديث أجهزة الاستشعار ( [ 1 ] )  ", "selectInstTip": " الرجاء اختيار أجهزة الاستشعار التي تحتاج إلى تعديل  ", "selectChannelTip": " الرجاء اختيار القناة التي تحتاج إلى تعديل  ", "instCodeTip": " استشعار الترميز لا يمكن أن تكون فارغة  ", "commLinkTypeTip": " حدد نوع الاتصال  ", "commLinkPortTip": " الرجاء اختيار منفذ الاتصال  ", "continuSampTimeTip": " استمرار جمع الوقت بين { 1 } و { 2 } دقيقة .  ", "continuSampTimeCompareTip": " وقت أخذ العينات المستمر يجب أن يكون أقل من فترة أخذ العينات .  ", "instSampleSpaceTip": " فترة المعاينة بين { 1 } و { 2 } دقيقة .  ", "instSampleStartTimeTip": " نقطة كاملة من الوقت بين 0 و 23 نقطة في بداية أخذ العينات .  ", "instNumberPattenTip": " يمكن أن تحتوي فقط على 0-9 أرقا<PERSON> ، a-za-z الحروف و : .  ", "instIPPattenTip": " الملكية الفكرية ميناء شكل مثل : 0.0.0.0 : 1  ", "deleteInstTip": " حدد أجهزة الاستشعار التي تريد حذفها  ", "selectSensorTip": " الرجاء اختيار أجهزة الاستشعار  ", "deleteInstConfirmTip": " هل أنت متأكد من أنك تريد حذف الترميز الاستشعار : { 2 } ؟  ", "activeInstConfigTip": " هل أنت متأكد من أنك تريد تطبيق هذا التكوين على أجهزة استشعار مماثلة < / ر > { 1 } ؟  ", "activeBatchConfigsSuccess": " استشعار دفعة التكوين بنجاح  ", "activeBatchConfigsBegin": " تكوين دفعة الاستشعار تبدأ  ", "activeBatchConfigsFailed": " استشعار دفعة التكوين فشلت  ", "collectStartOnceTip": " هل أنت متأكد من أنك سوف تبدأ في جمع البيانات من أجهزة الاستشعار من نفس النوع < / ر > { 1 } ؟  ", "collectStartTip": " هل أنت متأكد من أنك سوف تبدأ في جمع البيانات من < / ر > { 1 } < / ر > الاستشعار الترميز : { 2 } ؟  ", "wakeUpInstrumentOnceTip": " هل أنت متأكد من أنك سوف تبدأ في الاستيقاظ < / ر > { 1 } نفس النوع من أجهزة الاستشعار ؟  ", "wakeUpInstrumentTip": " هل أنت متأكد من أنك سوف تبدأ الصحوة < / ر > { 1 } < / ر > الاستشعار : { 2 } ؟  ", "emptySensorDataOnceTip": " هل أنت متأكد من أن جميع البيانات من نفس النوع من أجهزة الاستشعار < / ر > { 1 } ؟  ", "emptySensorDataTip": " تأكد من مسح جميع البيانات من أجهزة الاستشعار الترميز : { 2 } { 1 } < / ر >  ", "emptySensorDataSuccess": " مسح بيانات الاستشعار بنجاح  ", "emptySensorDataFailed": " فشل مسح بيانات الاستشعار :  ", "ae_chart": " في الوقت الحقيقي رصد الطيف  ", "uhf_chart": " في الوقت الحقيقي رصد الطيف الفائق  ", "hfct_chart": " في الوقت الحقيقي رصد hfct أطلس  ", "tev_chart": " في الوقت الحقيقي رصد السعة  ", "tev_prps_chart": " في الوقت الحقيقي رصد الجهد عابر PRPS أطلس  ", "temperature_chart": " في الوقت الحقيقي رصد درجة الحرارة  ", "humidity_chart": " في الوقت الحقيقي رصد الرطوبة  ", "mechanical_chart": " في الوقت الحقيقي رصد الخصائص الميكانيكية  ", "arrester_chart": " في الوقت الحقيقي رصد صواعق البرق  ", "leakage_current_chart": " في الوقت الحقيقي رصد التسرب الحالي  ", "grounddingcurrent_chart": " في الوقت الحقيقي رصد التأريض الحالية  ", "vibration_pickup_chart": " في الوقت الحقيقي رصد الاهتزاز  ", "density_micro_water_history": " في الوقت الحقيقي رصد كثافة المياه الصغيرة  ", "core_grounding_current": " في الوقت الحقيقي رصد الأرض الأساسية الحالية  ", "noise_chart": " في الوقت الحقيقي رصد الضوضاء  ", "water_immersion": " في الوقت الحقيقي رصد الفيضانات  ", "smoke_sensor": " في الوقت الحقيقي رصد الدخان  ", "video_sensor": " في الوقت الحقيقي رصد الفيديو  ", "sf6_sensor": " رصد في الوقت الحقيقي - سادس فلوريد الكبريت  ", "error": " خطأ .  ", "warning": " ح<PERSON>ر  ", "success": " النجاح .  ", "userErrorTip": " اسم المستخدم أو كلمة المرور غير صحيحة  ", "errorNoDeviceId": " معرف الجهاز غير موجود  ", "errorNoSensorId": " استشعار معرف غير موجود  ", "errorExistSensorId": " استشعار معرف موجود بالفعل  ", "errorNoChannelId": " معرف القناة غير موجودة  ", "errorNoPointId": " نقطة قياس لا وجود لها  ", "errorDataExportErrDir": " تصدير البيانات ، مسار الملف خطأ .  ", "errorDataExportErrTime": " خطأ وقت تصدير البيانات  ", "errorDataExportNoData": " تصدير البيانات ، أي بيانات .  ", "errorDataExportNoSpace": " تصدير البيانات ، يو القرص الكامل  ", "errorDataExportNoDisk": " تصدير البيانات ، لا يو القرص .  ", "errorDataExportNoInfo": " تصدير البيانات دون معلومات الموقع .  ", "errorDataExportOther": " تصدير البيانات ، وغيرها من الأخطاء .  ", "errorSetModeBus": " modebus الإعداد خطأ  ", "errorSameNamePoint": " نقطة قياس بنفس الاسم موجود بالفعل  ", "errorIP": " الملكية الفكرية خطأ  ", "errorGPRSSet": " الفقر بغانا الإعداد خطأ  ", "errorSenorUpdateErrFile": " استشعار ملف التحديث خطأ  ", "errorSenorUpdateNotMatch": " تحديث أجهزة الاستشعار ، وأجهزة الاستشعار نوع ملف الترقية لا تتطابق  ", "errorSenorUpdating": " تحديث أجهزة الاستشعار  ", "errorSenorUpdateSizeCheckFailed": " فشل التحقق من حجم البرامج الثابتة  ", "errorSenorUpdateVersionCheckFailed": " فشل التحقق من رقم النسخة الثابتة  ", "errorSenorUpdateDeviceTypeCheckFailed": " فشل التحقق من نوع الجهاز الثابتة  ", "errorSenorUpdateCRCCheckFailed": " فشل التحقق من البرامج الثابتة CRC  ", "errorSenorUpdateFailed": " استشعار ترقية فشلت  ", "errorSenorUpdateConnectErr": " تحديث البرامج الثابتة والاتصالات استثناء  ", "errorDataCleanFailed": " تفريغ البيانات فشلت !  ", "errorCodeWorkGroupNotExist": " الفريق العامل غير موجود !  ", "errorCodeInvalidChannel": " ممر غير شرعي !  ", "errorCodeInvalidWirignMode": " غير طريقة الاتصال !  ", "errorCodeInvalidAlarmMode": " غير طريقة التنبيه !  ", "errorNoPermission": " هذا المستخدم لا يملك هذا الإذن  ", "illegalUser": " المستخدمين غير الشرعيين !  ", "legalPattenMsg": " يمكن أن تحتوي فقط على الأحرف ( بدون مسافات ) : الأحرف الأبجدية الأرقام الرومانية   [ ش ] [ ]  ", "groundCurrent": " الاتجاه الحالي مخطط التأريض  ", "groundCurrentA": " الأرض الحالية  ", "groundCurrentB": " ب المرحلة الحالية  ", "groundCurrentC": " ج الأرض الحالية  ", "leakageCurrent": " الاتجاه الحالي مخطط كامل  ", "leakageCurrentA": " المرحلة الحالية كاملة  ", "leakageCurrentB": " ب المرحلة الحالية  ", "leakageCurrentC": " ج المرحلة الحالية  ", "ResistiveCurrent": " المقاومة الحالية اتجاه الرسم البياني  ", "ResistiveCurrentA": " مرحلة المقاومة الحالية  ", "ResistiveCurrentB": " ب المرحلة المقاومة الحالية  ", "ResistiveCurrentC": " ج المرحلة المقاومة الحالية  ", "resistiveCurrentA": " مرحلة المقاومة الحالية  ", "resistiveCurrentB": " ب المرحلة المقاومة الحالية  ", "resistiveCurrentC": " ج المرحلة المقاومة الحالية  ", "referenceVoltageA": " مرحلة الجهد المرجعي  ", "referenceVoltageB": " ب المرحلة إشارة الجهد  ", "referenceVoltageC": " ج المرحلة إشارة الجهد  ", "grounddingCurrent": " الاتجاه الحالي مخطط التأريض  ", "grounddingCurrentA": " الأرض الحالية  ", "grounddingCurrentB": " ب المرحلة الحالية  ", "grounddingCurrentC": " ج الأرض الحالية  ", "timeDomain": " مخطط المجال الزمني  ", "frequencyDomain": " نطاق التر<PERSON>د على الرسم البياني  ", "characterParam": " معلمة مميزة  ", "TimeDomainDataX": " المحور العاشر إشارة الاهتزاز  ", "TimeDomainDataY": " ذ محور إشارة الاهتزاز  ", "TimeDomainDataZ": " ض محور إشارة الاهتزاز  ", "FrequencyDomainDataX": " إشارة الاهتزاز الطيف - المحور السيني  ", "FrequencyDomainDataY": " الطيف الترددي إشارة الاهتزاز  ", "FrequencyDomainDataZ": " الطيف الترددي إشارة الاهتزاز  ", "ACCAVGX": " المحور السيني التسارع يعني  ", "ACCAVGY": " ذ محور التسارع يعني  ", "ACCAVGZ": " ض محور التسارع يعني  ", "ACCMAXX": " أقصى تسارع المحور السيني  ", "ACCMAXY": " أقصى تسارع محور ص  ", "ACCMAXZ": " أقصى تسارع محور Z  ", "AMPAVGX": " المحور السيني السعة يعني  ", "AMPAVGY": " ذ محور متوسط السعة  ", "AMPAVGZ": " ض محور متوسط السعة  ", "AMPMAXX": " المحور السيني السعة القصوى  ", "AMPMAXY": " ذ محور السعة القصوى  ", "AMPMAXZ": " ض محور السعة القصوى  ", "MAXFreqX0": " المحور العاشر كستريموم نقطة التردد 1  ", "MAXFreqY0": " ذ محور كستريموم التردد 1  ", "MAXFreqZ0": " م<PERSON>و<PERSON> Z كستريموم نقطة التردد 1  ", "MAXFreqX1": " المحور السيني كستريموم نقطة التردد 2  ", "MAXFreqY1": " ذ محور كستريموم نقطة التردد 2  ", "MAXFreqZ1": " م<PERSON>و<PERSON> Z كستريموم نقطة التردد 2  ", "MAXFreqX2": " اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس اكس  ", "MAXFreqY2": " ذ محور كستريموم نقطة التردد  ", "MAXFreqZ2": " م<PERSON>و<PERSON> Z كستريموم نقطة التردد 3  ", "sensorType": " نوع من أجهزة الاستشعار  ", "sensorList": " قائمة أجهزة الاستشعار  ", "gain": " م<PERSON><PERSON><PERSON>  ", "trigger": " الزناد السعة  ", "wave_filter": " نطاق التردد  ", "sample_date": " تاريخ العينة  ", "sample_time": " وقت أخذ العينات  ", "rms": " قيمة فعالة  ", "cycle_max": " الفترة القصوى  ", "frequency1": " عنصر التردد  ", "frequency2": " عنصر التردد  ", "time_interval": " الفاصل الزمني  ", "realData": " البيانات في الوقت الحقيقي  ", "historyData": " البيانات التاريخية  ", "trendSync": " تحليل الاتجاهات  ", "preData": " المادة السابقة  ", "nextData": " المادة التالية  ", "systemDate": " تاريخ النظام  ", "systemTime": " نظام الوقت  ", "backupType": " نوع النسخ الاحتياطي  ", "plusBackup": " النسخ الاحتياطي الإضافية  ", "allBackup": " النسخ الاحتياطي الكامل  ", "timeRange": " نطاق الزمن  ", "exportPath": " مسار التصدير  ", "getExportDir": " الحصول على دليل  ", "checkDir": " التحقق من الدليل  ", "exportingData": " تصدير .  ", "cancel": " أ<PERSON><PERSON><PERSON>  ", "export": " تصدير  ", "stationConfig": " تكوين الموقع  ", "stationDelSuccess": " حذ<PERSON> الموقع بنجاح  ", "stationDelFailed": " فشل حذف الموقع :  ", "deviceConfig": " تكوين الجهاز الرئيسي  ", "pointConfig": " نقطة قياس التكوين  ", "stationName": " اسم الموقع  ", "stationPMS": " ر<PERSON>ز الموقع  ", "stationLevel": " محطة مستوى الجهد  ", "powerUnit": " وحدة الطاقة  ", "save": " حفظ .  ", "operationSuccess": " عملية ناجحة  ", "deviceAddSuccessTips": " زيادة نجاح الجهاز مرة واحدة  ", "deviceEditSuccessTips": " تعديل الجهاز بنجاح  ", "deviceDelSuccessTips": " جهاز واحد حذف بنجاح  ", "pointAddSuccessTips": " نقطة قياس زيادة النجاح  ", "pointEditSuccessTips": " تعديل نقاط القياس بنجاح  ", "pointDelSuccessTips": " حذ<PERSON> بنجاح  ", "deleteFailedTips": " حذ<PERSON> فشل  ", "save_add": " حفظ / إضافة  ", "delete": " حذف .  ", "deviceType": " نوع الجهاز  ", "deviceLevel": " مستوى الجهد المعدات  ", "add": " زيادة  ", "channelTypeAlarm": " أنواع البيانات  ", "alarmThreshold": " عتبة إنذار  ", "alarmRecoveryThreshold": " إنذار عتبة الانتعاش  ", "alarmChannel": " اختيار قناة إنذار  ", "built_in_channel_1": " بنيت في قناة  ", "External_IO_module": " او وحدة خارجية  ", "not_associated": " <PERSON>ير مرتبط  ", "alarmExternalIOSN": " او وحدة ترميز خارجي  ", "alarmExternalIOChannel": " او قناة وحدة خارجية  ", "wiringMode": " طريقة الاتصال  ", "alarmMode": " طريقة التنبيه  ", "alarmTime": " توقيت التنبيه  ", "alarmInterval": " إنذار الفاصلة  ", "alarmDuration": " إنذار طول الوقت  ", "normal_open": " في كثير من الأحيان فتح  ", "normal_close": " عادة مغلقة  ", "continuous": " الاستمرارية  ", "timing": " توقيت  ", "interval": " فاصل  ", "alarmSaveSuccess": " إنذار تكوين حفظ بنجاح  ", "alarmDelSuccess": " إنذار التكوين حذف بنجاح  ", "deviceName": " اسم الجهاز الرئيسي  ", "pointName": " اسم نقطة قياس  ", "testStation": " اختبار الموقع  ", "device": " المعدات الرئيسية  ", "pointList": " قائمة نقاط القياس  ", "sensorID": " استشعار معرف  ", "sensorChannel": " قناة الاستشعار  ", "channelList": " قائمة القنوات  ", "showPoint": " عرض نقطة قياس  ", "fileSelect": " اختيار الملف  ", "selectPlease": " الرجاء اختيار  ", "deviceList": " قائمة أجهزة الاستشعار  ", "updating": " تحديث .  ", "updatingFailed": " فشل التحديث :  ", "updatingCanceled": " إلغاء التحديث  ", "updatingComplete": " استكمال التحديث  ", "update": " تجديد .  ", "sampleDate": " تاريخ العينة  ", "sampleTime": " وقت أخذ العينات  ", "pdMax": " أقصى سعة التفريغ  ", "pdAvg": " متوسط سعة التفريغ  ", "pdNum": " ع<PERSON><PERSON> النبضات  ", "acquisitionTime": " جمع الوقت  ", "humidity": " الرطوبة  ", "startDate": " تاريخ البدء  ", "endDate": " نهاية التاريخ  ", "refresh": " من<PERSON><PERSON>  ", "scanData": " استعلم عن  ", "sensorCode": " استشعار الترميز  ", "sensorTypeSet": " نوع من أجهزة الاستشعار  ", "senorName": " اسم الاستشعار  ", "aduAddress": " استشعار العنوان  ", "senorWorkMode": " نشط الإرسال  ", "On": " فتح .  ", "Off": " قريب  ", "ADUMode": " طريقة العمل  ", "normalMode": " وضع الصيانة  ", "lowPowerMode": " وضع الطاقة المنخفضة  ", "monitorMode": " طريقة الرصد  ", "artificialStateStartTime": " تدخل الدولة وقت البدء ( نقطة كاملة )  ", "artificialStateStartTimeTip": " بداية الوقت مجموعة من التدخل اليدوي  ", "artificialStateEndTime": " تدخل الدولة نهاية الوقت ( نقطة كاملة )  ", "artificialStateEndTimeTip": " نهاية الوقت مجموعة من التدخل اليدوي  ", "artificialStateWakeUpSpace": " فترة الاستيقاظ الاصطناعي تدخل الدولة ( نقاط )  ", "unartificialStateWakeUpSpace": " فترة الاستيقاظ غير مصطنعة تدخل الدولة ( نقاط )  ", "isAutoChangeMode": " طريقة التبديل التلقائي  ", "monitorModeSampleSapce": " رصد وضع الفاصل الزمني  ", "workGroup": " رقم مجموعة العمل  ", "warnTemp": " إنذار درجة الحرارة  ", "taskGroup": " مجموعة مهمة  ", "commLinkType": " نوع الاتصال  ", "commLinkPort": " من<PERSON><PERSON> الاتصالات  ", "frequencyUnit": " تردد الشبكة ( ه<PERSON><PERSON><PERSON> )  ", "numInGroup": " رقم المجموعة  ", "commSpeed": " معدل الاتصالات  ", "commLoad": " قناة الاتصال  ", "sampleSpace": " أخذ العينات الفاصلة ( مين )  ", "sleepTime": " استراتيجية استهلاك الطاقة  ", "samleStartTime": " وقت أخذ العينات الأولية ( كامل )  ", "applyData": " العمليات  ", "applyAllSensor": " تنطبق على نفس  ", "collectOnce": " جمع دفعة  ", "collectOnceEnd": " نهاية جمع البيانات  ", "collectOnceProgress": " جمع البيانات : { 1 } { 2 }  ", "collectOnceProgress2": " الحصول على البيانات ( 1 ) { 2 }  ", "activeOnce": " تطبيق دفعة  ", "wakeUp": " أ<PERSON><PERSON><PERSON>  ", "wakeUpInstrument": " استيقظ سنسر  ", "wakeUpInstrumentOnce": " دفعة استشعار الاستيقاظ  ", "orderSend": " تم إصدار الأوامر  ", "cleanData": " م<PERSON><PERSON> البيانات  ", "sensorAddSuccess": " استشعار زيادة النجاح  ", "sensorEditSuccess": " استشعار تعديل بنجاح  ", "sensorEditBegin": " استشعار تعديل تبدأ  ", "sensorDelSuccess": " استشعار حذف بنجاح  ", "cleanSensorData": " مسح بيانات الاستشعار  ", "getSensor": " الحصول على أجهزة الاستشعار  ", "channelType": " نوع القناة  ", "channelName": " اسم القناة  ", "channelInfoSaveSuccess": " قناة حفظ المعلومات بنجاح  ", "channelInfoSaveBegin": " استشعار قناة حفظ المعلومات تبدأ  ", "bias": " التحيز [ ديسيبل ]  ", "waveFilter": " وضع الفرقة  ", "gainMode": " الحصول على واسطة  ", "gain_unit": " كسب [ ديسيبل ]  ", "samCycle": " فترة أخذ العينات  ", "samPointNum": " نقاط أخذ العينات في الفترة  ", "samRate": " نقاط أخذ العينات في الفترة  ", "ratio": " نسبة متغيرة  ", "channelPhase": " مرحلة التمييز  ", "mecLoopCurrentThred": " لفائف العتبة الحالية [ ما ]  ", "mecMotorCurrentThred": " موتور العتبة الحالية [ ما ]  ", "mecSwitchState": " تبديل الوضع الأولي  ", "awaysOpen": " في كثير من الأحيان فتح  ", "awaysClose": " عادة مغلقة  ", "mecBreakerType": " قواطع دوائر آلية التكوين  ", "oneMech": " ثلاث مراحل الربط الميكانيكية ( آلية واحدة )  ", "threeMech": " ثلاث مراحل الربط الكهربائي ( ثلاث آليات )  ", "mecMotorFunctionType": " نوع المحرك  ", "threePhaseAsynMotor": " ثلاث مراحل للسيارات غير متزامن  ", "onePhaseMotor": " تقاطع واحد / العاصمة المحرك  ", "setCurrent": " الإعداد الحالي  ", "setAll": " تطبيق نفس النوع من أجهزة الاستشعار  ", "showCoil": " ملف الرسم البياني الحالي  ", "showSwitch": " تبديل الرسم البياني  ", "showMotor": " أطلس السيارات الحالية  ", "showOrig": " الرسم البياني الحالي الأصلي  ", "actionDate": " تاريخ العمل  ", "actionTime": " وقت العمل  ", "phaseA": " المرحلة  ", "phaseB": " ب المرحلة  ", "phaseC": " ج المرحلة  ", "coil_charge_time": " ملف تنشيط الوقت  ", "coil_cutout_time": " ملف وقت انقطاع التيار الكهربائي  ", "max_current": " مل<PERSON> ال<PERSON><PERSON> الأقصى الحالي  ", "hit_time": " وقت التعثر  ", "subswitch_close_time": " مساعدة التبديل تبديل الوقت  ", "a_close_time": " مرحلة إغلاق الوقت  ", "a_close_coil_charge_time": " مرحلة تنشيط ملف الوقت  ", "a_close_coil_cutout_time": " مرحلة انقطاع التيار الكهربائي لفائف الوقت  ", "a_close_max_current": " الح<PERSON> الأق<PERSON>ى الحالي مرحلة إغلاق ملف  ", "a_close_hit_time": " رحلة وقت الإغلاق  ", "a_close_subswitch_close_time": " مرحلة مساعدة التبديل تبديل الوقت  ", "b_close_time": " ب مرحلة إغلاق الوقت  ", "b_close_coil_charge_time": " المرحلة ب ملف تنشيط الوقت  ", "b_close_coil_cutout_time": " ب المرحلة لفائف انقطاع التيار الكهربائي  ", "b_close_max_current": " الح<PERSON> الأق<PERSON>ى الحالي ب المرحلة إغلاق ملف  ", "b_close_hit_time": " رحلة الوقت ب المرحلة قفل  ", "b_close_subswitch_close_time": " ب - مرحلة مساعدة التبديل تبديل الوقت  ", "c_close_time": " ج مرحلة إغلاق الوقت  ", "c_close_coil_charge_time": " المرحلة جيم ملف تنشيط الوقت  ", "c_close_coil_cutout_time": " المرحلة جيم ملف انقطاع التيار الكهربائي  ", "c_close_max_current": " الح<PERSON> الأق<PERSON>ى الحالي في مرحلة إغلاق ملف  ", "c_close_hit_time": " الافراج عن الوقت في المرحلة جيم  ", "c_close_subswitch_close_time": " ج المرحلة مساعدة التبديل تبديل الوقت  ", "close_sync": " إغلاق التزامن  ", "close_time": " إغلاق الوقت  ", "a_open_time": " مرحلة فتح الوقت  ", "a_open_coil_charge_time": " مرحلة تنشيط ملف الوقت  ", "a_open_coil_cutout_time": " مرحلة انقطاع التيار الكهربائي لفائف الوقت  ", "a_open_max_current": " الح<PERSON> الأق<PERSON>ى الحالي من مرحلة فتح ملف  ", "a_open_hit_time": " الافراج عن الوقت من مرحلة فتح الفرامل  ", "a_open_subswitch_close_time": " مرحلة مساعدة التبديل تبديل الوقت  ", "b_open_time": " ب مرحلة فتح الوقت  ", "b_open_coil_charge_time": " المرحلة ب ملف تنشيط الوقت  ", "b_open_coil_cutout_time": " ب المرحلة لفائف انقطاع التيار الكهربائي  ", "b_open_max_current": " الح<PERSON> الأق<PERSON>ى الحالي ب المرحلة فتح ملف  ", "b_open_hit_time": " الافراج عن الوقت ب المرحلة الفرامل  ", "b_open_subswitch_close_time": " ب - مرحلة مساعدة التبديل تبديل الوقت  ", "c_open_time": " ج مرحلة فتح الوقت  ", "c_open_coil_charge_time": " المرحلة جيم ملف تنشيط الوقت  ", "c_open_coil_cutout_time": " المرحلة جيم ملف انقطاع التيار الكهربائي", "c_open_max_current": "الح<PERSON> الأق<PERSON>ى الحالي من مرحلة فتح ملف  ", "c_open_hit_time": " الافراج عن الوقت من المرحلة جيم الفرامل  ", "c_open_subswitch_close_time": " ج المرحلة مساعدة التبديل تبديل الوقت  ", "open_sync": " فتح التزامن  ", "open_time": " وقت التبديل  ", "a_twice_open_time": " المرحلة الثانية فتح الوقت  ", "a_twice_open_coil_charge_time": " يعيش وقت المرحلة الثانية فتح ملف  ", "a_twice_open_coil_cutout_time": " لحظة انقطاع التيار الكهربائي من المرحلة الثانية فتح ملف  ", "a_twice_open_max_current": " الح<PERSON> الأق<PERSON>ى الحالي من المرحلة الثانية فتح ملف  ", "a_twice_open_hit_time": " الافراج عن الوقت من مرحلة الانقسام  ", "a_twice_open_subswitch_close_time": " كسر الوقت مساعدة التبديل في المرحلة الثانية فتح  ", "b_twice_open_time": " ب المرحلة الثانية فتح الوقت  ", "b_twice_open_coil_cutout_time": " انقطاع التيار الكهربائي في الوقت ب المرحلة الثانوية فتح ملف  ", "b_twice_open_max_current": " الح<PERSON> الأق<PERSON>ى الحالي ب المرحلة الثانوية فتح ملف  ", "b_twice_open_hit_time": " ب - المرحلة الثانية تنطلق من الوقت  ", "b_twice_open_subswitch_close_time": " كسر الوقت ب المرحلة الثانوية فتح مفتاح مساعد  ", "c_twice_open_time": " المرحلة الثانية فتح الوقت  ", "c_twice_open_coil_cutout_time": " انقطاع التيار الكهربائي في الوقت جيم المرحلة الثانوية فتح ملف  ", "c_twice_open_max_current": " الح<PERSON> الأق<PERSON>ى الحالي ج المرحلة الثانوية فتح ملف  ", "c_twice_open_hit_time": " ج - المرحلة الثانية رحلة الوقت  ", "c_twice_open_subswitch_close_time": " قطع الوقت من الثانوية فتح مفتاح مساعد في المرحلة ج  ", "twice_open_sync": " تزامن فتح الثانوية  ", "twice_open_time_text": " الثانوية فتح الوقت  ", "a_switch_shot_time": " فترة قصيرة من الذهب  ", "b_switch_shot_time": " ب المرحلة الذهب وقت قصير  ", "c_switch_shot_time": " ج المرحلة الذهب وقت قصير  ", "a_switch_no_current_time": " المرحلة الحالية أي وقت  ", "b_switch_no_current_time": " المرحلة ب لا الوقت الحالي  ", "c_switch_no_current_time": " المرحلة جيم لا الوقت الحالي  ", "motor_start_current": " محرك بدء التشغيل الحالي  ", "motor_max_current": " الح<PERSON> الأق<PERSON>ى الحالي للسيارات  ", "storage_time": " موتور تخزين الطاقة في الوقت  ", "Chan_A_motor_start_current": " بداية المرحلة الحالية للسيارات  ", "Chan_A_motor_max_current": " الح<PERSON> الأق<PERSON>ى الحالي من المرحلة موتور  ", "Chan_A_storage_time": " تخزين الطاقة وقت المرحلة موتور  ", "Chan_B_motor_start_current": " بدء التشغيل الحالية ب المرحلة المحرك  ", "Chan_B_motor_max_current": " الح<PERSON> الأق<PERSON>ى الحالي ب المرحلة آلة كهربائية  ", "Chan_B_storage_time": " تخزين الطاقة وقت ب المرحلة آلة كهربائية  ", "Chan_C_motor_start_current": " المرحلة جيم موتور بدء التشغيل الحالي  ", "Chan_C_motor_max_current": " أقصى قدر من التيار الكهربائي للسيارات المرحلة ج  ", "Chan_C_storage_time": " تخزين الطاقة وقت ج المرحلة آلة كهربائية  ", "serialPort": " منفذ تسلسلي  ", "baudRate": " معد<PERSON> الباود  ", "dataBit": " بت البيانات  ", "stopBit": " وقف بت  ", "checkBit": " بت سريع  ", "singleCollect": " جمع دفعة  ", "sampling": " جمع . . .  ", "serverIP": " خادم الملكية الفكرية  ", "serverPort": " م<PERSON><PERSON><PERSON> الخادم  ", "NTPserverIp": " NTP خادم الملكية الفكرية  ", "NTPserverPort": " من<PERSON><PERSON>ادم NTP  ", "netType": " نوع الشبكة  ", "deviceIp": " المضيف الملكية الفكرية  ", "subnetMask": " قناع الشبكة الفرعية  ", "gateway": " البوابة الافتراضية  ", "networkAPN": " الوصول إلى الشبكة  ", "userName": " اسم المستخدم  ", "passWord": " كلمة السر  ", "deviceWorkGroup": " استضافة مجموعة العمل  ", "frequency": " تردد الشبكة  ", "pdSampleInterval": " pd-sampling الفاصلة  ", "spaceSecond": " نبسب ;   ثانية .  ", "meuSampleInterval": " مو - فترة المعاينة  ", "monitorAwakeTime": " فترة السكون  ", "monitorSleepSpace": " فترة الصحوة  ", "wakeStartTime": " بداية وقت الاستيقاظ ( نقطة كاملة )  ", "intervalServer": " تحميل الفاصل الزمني ( الخادم )  ", "intervalMinus": " أخذ العينات الفاصلة  ", "continuousAcquisitionTime": " جمع الوقت المستمر  ", "startSampleTime": " المضيف وقت أخذ العينات الأولية  ", "spacePoint": " نبسب ;   نقطة .  ", "startSampleInterval": " المضيف بدء فترة المعاينة  ", "hour": " ساعة .  ", "poerOffSpace": " اغلاق الفاصلة  ", "powerOnSpace": " فترة التمهيد  ", "dateRange": " نطاق التاريخ  ", "synchronizing": " مزامنة .  ", "synchronize": " مزامنة  ", "amplitude": " السعة  ", "switch": " تبديل  ", "copyRight": " ©   عام 2020 pmdt المحدودة  ", "S1010Name": " الحصول على البيانات المضيف  ", "modbusCompanySelf": " هوا تشنغ  ", "logOut": " س<PERSON><PERSON>  ", "logOutTitle": " تأكيد تسجيل الخروج  ", "logOutConfirm": " خروج المستخدم الحالي الدخول ؟  ", "logOutTips": " تسجيل الخروج  ", "enterHost": " الرجاء إدخال اسم المضيف  ", "selectDevTip": " الرجاء اختيار الجهاز  ", "stopSyncDataTip": " هل تريد إيقاف مزامنة البيانات ؟  ", "modbusAddress": " عنوان الناقل Modbus  ", "modbusAddressCheckTip": " الناقل Modbus معالجة مجموعة من 1 إلى 254 عدد صحيح  ", "deviceWorkGroupCheckTip": " مجموعة العمل عدد صحيح ، مجموعة : { 1 } ~ 2 }  ", "emptyMonitorDatas": " تفريغ البيانات المضيف  ", "emptyMonitorDatasTip": " مسح جميع البيانات التاريخية في الكمبيوتر المضيف  ", "emptyMonitorDatasSuccess": " تفريغ البيانات بنجاح  ", "emptyMonitorDatasApply": " طلب تفريغ البيانات المقدمة للنظر فيها  ", "resetSoftSet": " استعادة إعدادات المصنع  ", "resetSoftSetTip": " استعادة جميع التشكيلات في جمع المضيف إلى حالة المصنع  ", "resetSoftSetSuccess": " طلب استعادة إعدادات المصنع قد قدم إلى مراجعة  ", "continueConfirmTip": " هذه العملية لا يمكن استردادها ، هل تريد الاستمرار ؟  ", "bias_data": " مجموعة من القيم - 100-100 التحيز  ", "back_ground_data": " مجموعة من القيم الأساسية 0-100  ", "sam_point": " تردد أخذ العينات مجموعة من 20-2000 لكل دورة  ", "sam_rate": " تردد أخذ العينات مجموعة من 20-2000 لكل دورة  ", "smartSensorStatus": " حالة الجدول  ", "upThreshold": " عت<PERSON><PERSON> الحد الأعلى  ", "lowerThreshold": " الح<PERSON> الأدنى من عتبة  ", "changeThreshold": " تغيير عتبة  ", "changeThresholdLimit": " عتبة الإعداد يدعم واحد فقط عشري  ", "upThresholdTip": " عت<PERSON>ة الحد الأعلى مجموعة  ", "lowerThresholdTip": " الح<PERSON> الأدنى من مجموعة  ", "changeThresholdTip": " تغيير نطاق العتبة  ", "upLowerThresholderrTip": " عتبة عتبة الحد الأدنى لا يمكن أن يكون أكبر من الحد الأعلى  ", "monitorName": " اسم المضيف  ", "monitorType": " نوع المضيف  ", "MonitorTypeHanging": " الجدار الشنق  ", "MonitorType2U": " ش  ", "MonitorTypeCollectionNode": " تجميع العقدة  ", "MonitorTypeLowPower": " انخفاض استهلاك الطاقة الشمسية المضيف  ", "devicePMSCode": " معدات الترميز  ", "forceChange": " طريقة التبديل  ", "forceChangeSuccess": " طريقة التبديل بنجاح  ", "currentVersion": " النسخة الحالية  ", "abnormalRecover": " الانتعاش الذاتي غير طبيعي  ", "fromLabel": " وقت البدء  ", "toLabel": " نهاية الوقت  ", "select": " مخ<PERSON><PERSON>ر  ", "sunday": " اليوم .  ", "monday": " واحد  ", "tuesday": " ر  ", "wednesday": " ثلاثة  ", "thursday": " أربعة  ", "friday": " خمسة .  ", "saturday": " ستة .  ", "January": " يناير .  ", "February": " شباط / فبراير  ", "March": " مارس .  ", "April": " ابريل  ", "May": " مايو  ", "June": " يونيو  ", "July": " تموز / يوليه  ", "August": " آب / أغسطس  ", "September": " سبتمب<PERSON> .  ", "October": " اكتوبر  ", "November": " تشرين الثاني / نوفمبر  ", "December": " كانون الأول / ديسمبر  ", "all": " كامل  ", "strong": " قوي  ", "weak": " ضعيف  ", "poor": " فرق  ", "min": " من<PERSON><PERSON>م  ", "second": " ثانية .  ", "uploadInterval": " تحميل الفاصل الزمني ( مين )  ", "loginWelcome": " مرحبا بكم في تسجيل الدخول  ", "dateStartIsAfterDateEnd": " تاريخ البدء أكبر من تاريخ الانتهاء ، يرجى إعادة اختيار  ", "maxCurrent": " الح<PERSON> الأق<PERSON>ى الحالي  ", "standMobus": " أجهزة الاستشعار مصنعين  ", "config1": " التكوين  ", "config2": " التكوين  ", "fWrnThreshold": " عتبة الاهتمام  ", "fAlmThreshold": " عتبة إنذار  ", "regularDormancy": " توقيت السكون  ", "noDormancy": " السكون  ", "soakingWater": " غمر  ", "dry": " تجفيف  ", "normal": " عادي .  ", "alarm": " إنذار .  ", "lightGunCamera": " مرئية بندقية  ", "lightBallCamera": " مرئية آلة الكرة  ", "infraredGunCamera": " مجهر الأشعة تحت الحمراء بندقية  ", "infraredBallCamera": " مجهر الأشعة تحت الحمراء آلة الكرة  ", "maxTemp": " درجة الحرارة القصوى  ", "maxTempPosition": " درجة الحرارة القصوى الكلمات  ", "devIPError": " الملكية الفكرية هي بالفعل المحتلة  ", "unknown": " غير معروف  ", "fault": " عطل  ", "lowPower": " انخفاض الطاقة  ", "mediumPower": " متوسط الكمية الكهربائية  ", "highPower": " عالية الطاقة  ", "slaveid": " معر<PERSON> الرقيق  ", "LoraFrequency": " منطقة المضيف  ", "AREA_CHINA": " الصين  ", "AREA_VIETNAM": " فيتنام ( AS1 )  ", "AREA_MALAYSIA": " ماليزيا ( AS1 )  ", "AREA_EUROPE": " أوروبا .  ", "AREA_US": " الأمريكتين  ", "AREA_INDONESIA": " إندونيسيا ( AS2 )  ", "AREA_INDIA": " الهند .  ", "AREA_KOREA": " جمهورية كوريا  ", "AREA_CHINA_RSV": " الصين ( احتياطي )  ", "getLogFileError": " فشل في الحصول على جهاز استشعار ملف السجل  ", "exportLogError": " ملف السجل غير موجود ، تصدير فشلت  ", "alertSyncProgress": " إنذار تزامن تحديث البيانات التقدم : { 1 }  ", "alertSyncProgress2": " إنذار تزامن تحديث البيانات التقدم [ 1 ]  ", "FIRMWARE_EXCEPTION": " الثابتة استثناء  ", "AD_INITIALIZATION_EXCEPTION": " إعلان التهيئة استثناء  ", "REFERENCE_VOLTAGE_EXCEPTION": " إشارة الجهد الشذوذ  ", "ONCHIP_FLASH_EXCEPTION": " فلاش على رقاقة استثناء  ", "OFFCHIP_FLASH_EXCEPTION": " فلاش غير طبيعي  ", "SYSTEM_PARAMETERS_EXCEPTION": " نظام المعلمة الشذوذ  ", "SAMPLE_PARAMETERS_EXCEPTION": " الحصول على المعلمات غير طبيعي  ", "CALIBRATION_PARAMETERS_EXCEPTION": " معايرة المعلمة الشذوذ  ", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": " معلمة نظام استعادة غير طبيعي  ", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": " الحصول على البيانات الانتعاش غير طبيعي  ", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": " معلمة المعايرة الانتعاش غير طبيعي  ", "LORA_MODULE_EXCEPTION": " لورا وحدة استثناء  ", "PHASE_NUM": " المرحلة رقم  ", "DISCONNECT_TIME": " قطع الوقت  ", "DATA_TOTAL_COUNT": " مجموع البيانات  ", "ONLINE_RATE": " معدل على الانترنت  ", "aduInfoSetProgress": " الاستشعار التكوين : { 1 } { 2 }  ", "aduInfoSetProgress2": " تكوين أجهزة الاستشعار ( [ 1 ] ) ( { 2 }  ", "aduInfoSetting": " تكوين أجهزة الاستشعار .  ", "aduInfoSetEnd": " الانتهاء من تكوين أجهزة الاستشعار  ", "aduDataSample": " جمع البيانات  ", "aduInfoSet": " تكوين أجهزة الاستشعار  ", "errorDataSampleRunning": " أجهزة الاستشعار اقتناء الأعمال الجارية ، يرجى المحاولة مرة أخرى بعد الانتهاء  ", "errorAduInfoSetRunning": " أجهزة الاستشعار إعداد المعلمة الخدمة حاليا ، يرجى المحاولة مرة أخرى بعد الانتهاء  ", "SAMPLE_PERIOD": " أخذ العينات دورة عدد  ", "SF6_Density": " كثافة غاز سداسي فلوريد الكبريت  ", "SF6_Temp": " درجة حرارة غاز سداسي فلوريد الكبريت  ", "Device_env_Temp": " معدات درجة الحرارة المحيطة  ", "Alarm_Status": " حالة الإنذار معرف  ", "SF6_Alarm_Low_Pressure": " انخفاض الج<PERSON>د التنبيه  ", "SF6_Alarm_Low_Voltage_Block": " انخفاض الج<PERSON>د قفل  ", "SF6_Alarm_Over_Voltage": " زيادة الجهد التنبيه  ", "AlarmType": " نوع التنبيه  ", "AlarmData": " محتوى التنبيه  ", "AlarmTime": " وقت التنبيه  ", "AlarmLevel": " إنذار الصف  ", "AlarmStateWarning": " الإنذار المبكر  ", "WarningValue": " قيمة الإنذار المبكر  ", "AlarmValue": " إنذار قيمة  ", "SetSuccess": " مجموعة ناجحة  ", "AlarmDate": " وقت التنبيه  ", "AlarmConfirm": " إنذار تأكيد  ", "Confirm": " أكّد  ", "Confirmed": " <PERSON><PERSON><PERSON>  ", "AllData": " جميع البيانات  ", "CheckManagerSponsor": " البادئ  ", "CheckManagerCheckType": " نوع المراجعة  ", "CheckManagerDate": " تاريخ البدء  ", "CheckManagerTarget": " البادئ  ", "CheckManagerExtraInfo": " معلومات إضافية  ", "CheckManagerResult": " توافق أو لا توافق  ", "Refuse": " <PERSON><PERSON><PERSON>  ", "Agree": " موافقة  ", "DataExport": " تصدير البيانات  ", "DataExportStep1": " حدد البيانات التي تريد تصديرها  ", "DataExportStep1Title": " اختيار ملف التصدير  ", "DataExportStep2": " حساب حجم البيانات .  ", "DataExportStep2Title": " حسا<PERSON> حجم البيانات الخام  ", "DataExportStep3": " حزم البيانات المضغوطة .  ", "DataExportStep3Title": " حزم البيانات المضغوطة  ", "DataExportStep4": " ضغط البيانات كاملة  ", "DataExportStep4Title": " ضغط البيانات كاملة  ", "DataExportCheckData": " قاعدة البيانات / وسائل الإعلام / البيانات / قاعدة البيانات ]  ", "DataExportCheckDataFile": " ملف البيانات [ وسائل الإعلام / البيانات / البيانات ]  ", "DataExportCheckLog": " ملف السجل [ وسائل الإعلام / البيانات / سجل ]  ", "DataExportCheckConfig": " ملف التكوين [ المنزل / الجذر / config.xml ]  ", "DataExportBegin": " تبدأ تصدير  ", "SelectAtLeastOneTips": " اختيار وا<PERSON><PERSON> على الأقل  ", "DataExportFileSizeError": " حجم الملف أكثر من 2000 ميغابايت ، يرجى استخدام Winscp وغيرها من أدوات تصدير البيانات  ", "DataExportFileSizeZeroError": " اختيار حجم الملف 0  ", "DataExportCancelTips": " تصدير البيانات إلغاء  ", "DataExportDataCompressTips": " حجم البيانات الخام { 1 } ميغابايت ، يقدر ضغط الوقت [ 2 } دقيقة { 3 } ]  ", "LogExportStep1": " اختيار أجهزة الاستشعار  ", "LogExportStep1Title": " اختيار أجهزة الاستشعار  ", "LogExportStep2": " الحصول على ملف السجل .  ", "LogExportStep2Title": " الحصول على ملف السجل  ", "LogExportStep3": " الحصول على ملف السجل الكامل  ", "LogExportStep3Title": " الحصول على ملف السجل الكامل  ", "LogExportStep4": " حزمة ضغط ملف السجل .  ", "LogExportStep4Title": " <PERSON><PERSON><PERSON> ملف السجل  ", "LogExportStep5": " الانتهاء من ضغط  ", "LogExportStep5Title": " الانتهاء من ضغط  ", "LogExportCancelTips": " تصدير سجل إلغاء  ", "batteryVoltage": " <PERSON><PERSON><PERSON> البطارية  ", "superCapvoltage": " سوبر مكثف الجهد  ", "OverPressureThreshold": " زيادة الجهد عتبة  ", "LowPressureThreshold": " عتبة الجهد المنخفض  ", "ShutThreshold": " عتبة الإغلاق  ", "PhysicalChannelType": " نوع القناة المادية  ", "GasAbsPressure": " الغاز الضغط المطلق  ", "GasGaugePressure": " قياس ضغط الغاز  ", "CameraType": " نوع الكاميرا  ", "DEFAULT_CHECK_Tips": " شكل الترقيم م م : ن ، مثل 1 : 100 ، حيث م مجموعة [ 1-255 ] ن مجموعة [ 0-65535 ]  ", "VibrationSY_OUT_CHECK_Tips": " شكل الترقيم هو عنوان : الناقل Modbus : معرف ، على سبيل المثال : 1 : 0 : 1917 ، حيث العنوان مجموعة [ 1-255 ] ، الناقل Modbus / معرف [ 0-65535 ]  ", "NOISEWS_OUT_CHECK_Tips": " عدد الملكية الفكرية : س : ص ، على سبيل المثال ، ********* : 1321 : 4512 ، حيث شكل الملكية الفكرية [ xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  ", "IR_DETECTION_OUT_IO_CHECK_Tips": " الشكل رقم الملكية الفكرية : ميناء : س : س : س : م ، على سبيل المثال ، 1 : 100 ، حيث شكل الملكية الفكرية [ xxx.xxx.xx.xxx ] ميناء مجموعة [ 0-65535 ] س مجموعة [ 0-255 ] ، ذاته مجموعة 0-255 ] ، CS مجموعة 0-255 ] ، م مجموعة 0-255 [ ]  ", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": " شكل الترقيم هو الملكية الفكرية : ميناء : س : ص ، على سبيل المثال ، 1 : 100 ، حيث شكل الملكية الفكرية [ xxx.xx.xx xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx  ", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": " شكل الترقيم م م : ن ، مثل 1 : 100 ، حيث م مجموعة [ 1-255 ] ن مجموعة [ 0-65535 ]  ", "fWrnThreshold_CHECK_Tips": " ملاحظة عتبة مجموعة [ 10-1000 ما ]  ", "fAlmThreshold_CHECK_1_Tips": " إنذار عتبة مجموعة [ 50-5000 ما ]  ", "fAlmThreshold_CHECK_2_Tips": " إنذار عتبة ينبغي أن تكون أكبر من عتبة الاهتمام  ", "AuditContent": " محتوى المراجعة  ", "OPTime": " وقت التشغيل  ", "Executed": " تنفيذي  ", "UserManager": " إدارة المستخدم  ", "SystemManagement": " إدارة النظام  ", "BusinessManagement": " إدارة الأعمال  ", "LanguageChange": " لغة التبديل  ", "AlarmManagement": " إنذار إدارة  ", "DataOperation": " تشغيل البيانات  ", "BackupRecovery": " استعادة النسخ الاحتياطي  ", "AddUser": " إضافة مستخدم  ", "AddUserCheck": " إضافة مراجعة حسابات المستخدم  ", "UserRightSet": " إعدادات أذونات المستخدم  ", "UserRightSetCheck": " مراجعة إعدادات المستخدم  ", "DeleteUser": " <PERSON>ذ<PERSON> المستخدم  ", "DeleteUserConfirm": " حذ<PERSON> ت<PERSON><PERSON>يد المستخدم  ", "FreezeUser": " تجميد المستخدم  ", "FreezeUserConfirm": " تجميد تأكيد المستخدم  ", "UnlockUser": " فتح المستخدم  ", "UnlockUserConfirm": " فتح تأكيد المستخدم  ", "FileStationSet": " إعدادات الملف ( الموقع )  ", "FileDeviceSet": " إعدادات الملف  ", "SenserSet": " مجموعة أجهزة الاستشعار  ", "AlarmSet": " إعدادات التنبيه  ", "SavePointSet": " ح<PERSON><PERSON> إعدادات نقطة  ", "DelPointSet": " حذ<PERSON> نقطة الإعداد  ", "SingleSample": " جمع واحد  ", "DataBackup": " بيانات النسخ الاحتياطي  ", "DataRecovery": " استعادة البيانات  ", "ClearDataCheck": " مسح بيانات مراجعة الحسابات  ", "RestoreFactorySettingsReview": " استئناف مراجعة إعدادات المصنع  ", "SaveMainStation": " حفظ محطة رئيسية  ", "DataView": " عرض البيانات  ", "DataSync": " مزامنة البيانات  ", "RightSet": " إذن التكوين  ", "GetUserList": " الحصول على قائمة المستخدمين  ", "AuditData": " مراجعة البيانات  ", "AuditPermissions": " سلطة مراجعة الحسابات  ", "MainStationSet": " إعدادات المحطة الرئيسية  ", "ChangePasswordSelfOnly": " تغيير كلمة السر ( هذا المستخدم فقط )  ", "ChangePasswordForNormal": " تغيير كلمة السر للمستخدم العادي  ", "ChangeUserRight": " تعديل / تعيين أذونات المستخدم  ", "ChangePassword": " تعديل كلمة المرور  ", "ChangeLoginInfo": " تعديل معلومات تسجيل الدخول  ", "UserStatus": " حالة المستخدم  ", "UserLanguage": " لغة المستخدم  ", "HasLogin": " إذا كان تسجيل الدخول  ", "RoleType": " نوع الدور  ", "IsPassTimeout": " كلمة السر مهلة  ", "AuditsManagement": " إدارة مراجعة الحسابات  ", "SensorOperation": " تشغيل أجهزة الاستشعار  ", "SensorLogExport": " سجل تصدير أجهزة الاستشعار  ", "SensorAlarmDataSync": " أجهزة الاستشعار إنذار تزامن البيانات  ", "ViewSensorAlarmData": " استشعار إنذار عرض البيانات  ", "Block": " تجمد  ", "BlockPendingReview": " تجميد - مراجعة  ", "UnlockPendingReview": " ذوبان الجليد - مراجعة  ", "DeletePendingReview": " حذف - مراجعة  ", "AddUserPendingReview": " إضافة - مراجعة  ", "ChangePassPendingReview": " تعديل كلمة السر  ", "ChangeRightPendingReview": " تعديل الأذونات - مراجعة  ", "abnormal": " شذوذ  ", "DataQueryNotSupported": " هذه الواجهة الأمامية نوع البيانات التاريخية الاستعلام غير معتمد مؤقتا  ", "DeviceCodeExists": " اسم الجهاز أو رمز الجهاز موجود بالفعل  ", "OutputSwitchConfig": " تكوين التبديل  ", "OutputSwitch": " اتصال التبديل  ", "MainStationAuxRtuID": " مسا<PERSON>د RTUID  ", "MainStationIedRtuID": " تجميع RTUID  ", "MainstationParams1": " محطة رئيسية المعلمة  ", "MainstationParams2": " محطة رئيسية المعلمة  ", "MainstationInterface1": " واجهة المحطة الرئيسية  ", "MainstationInterface2": " واجهة المحطة الرئيسية 2  ", "Port": " ميناء  ", "IPAddress": " عنوان IP  ", "EnterUriTips": " أد<PERSON>ل المسار أوري  ", "ConnectUserName": " الاتصال اسم المستخدم  ", "EnterConnectUserNameTips": " الرجاء إدخال اسم المستخدم للاتصال  ", "ConnectPass": " رمز الاتصال  ", "EnterConnectPassTips": " الرجاء إدخال كلمة المرور  ", "DataSubmissionInterval": " الفاصل الزمني لنقل البيانات  ", "EnderDataSBIntervalTips": " الرجاء إدخال البيانات على فترات  ", "HeartbeatInterval": " نبض القلب الفاصلة  ", "EnterHertbeatIntervalTips": " من فضلك أدخل فترة ضربات القلب  ", "ConnectStatus": " حالة الاتصال  ", "Reset": " إعادة  ", "Submit": " قدم  ", "RtuidRepeatTips": " مساعدة التحكم RTUID لا يمكن نفس مجموعة RTUID  ", "DataSubmissionIntervalTips": " الفاصل الزمني لنقل البيانات لا يمكن أن يكون أقل من 60  ", "HeartbeatIntervalTips": " الفاصل الزمني بين نبضات القلب لا يمكن أن يكون أقل من 60  ", "MainstationParamsSaveSuccess": " محطة رئيسية المعلمات تم حفظها بنجاح  ", "MainstationParamsSaveFailed": " محطة رئيسية المعلمة حفظ فشلت  ", "OutSwitchSaveSuccess": " تكوين مفتاح حفظ بنجاح  ", "OutSwitchSaveFailed": " تكوين مفتاح حفظ فشل الاتصال", "ChartConfig": "أطلس التكوين", "Measurement": "قيمة القياس", "SENSOR_TIMEOUT_COUNT": "عدد مرات تحديد تعطل المستشعر", "OUTWARD_CONFIG_ENABLE_STATE": "حالة التكوين", "OUTWARD_CONFIG_ENABLED": "م<PERSON>عل", "OUTWARD_CONFIG_DISABLED": "معطل", "OUTWARD_CONFIG_ENABLING": "جاري التفعيل", "SENSOR_TIMEOUT_DISABLING": "جاري التعطيل", "ERROR_NO_CONFIG": "التكوين غير موجود", "ERROR_INPUT_PARAMS": "خطأ في معاملات الإدخال", "ERROR_INTEGER": "الرجاء إدخال عدد صحيح", "ERROR_TIP_RANGE": "نطاق القيم [{1}~{2}]", "ERROR_IP_FORMAT": "تنسيق IP غير صحيح", "ERROR_FILE_NAME": "يجب أن يكون اسم الملف {1}", "ERROR_FILE_SUFFIX": "يجب أن يكون لاحقة الملف {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "يجب أن يكون الملف أصغر من {1} ميجابايت", "ERROR_T2_T1": "يج<PERSON> أن يكون T2 أقل من T1", "LENGTH_COMM_LIMIT": "الرجاء ملاحظة، لا يمكن أن يتجاوز هذا الحقل {1} حرفًا.", "MANU_FAST_SYNC_DATA": "مزامنة سريعة", "SYNC_DATA_STATE_LIST": "قائمة حالة مزامنة المستشعر", "LAST_SYNC_DATE_RANGE": "نطاق تاريخ آخر مزامنة", "NoError": "لا يو<PERSON>د خط<PERSON>", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "انتهت مهلة المزامنة", "SYNC_DATA_ERROR_DISCONNECT": "انقطع الاتصال", "SYNC_DATA_STATUS_WAITING": "في انتظار الاتصال", "SYNC_DATA_STATUS_CONNECTED": "متصل", "SYNC_DATA_STATUS_SYNCING": "جاري المزامنة", "SYNC_DATA_STATUS_SUCCESS": "تمت المزامنة بنجاح", "SYNC_DATA_STATUS_FAILED": "فشل المزامنة", "SYNC_DATA_STATUS_CANCELED": "تم إلغاء المزامنة", "Today": "اليوم", "Yesterday": "الأمس", "LAST_7_DAYS": "آخر 7 أيام", "LAST_30_DAYS": "<PERSON><PERSON><PERSON> 30 يومًا", "THIS_MONTH": "هذا الشهر", "LAST_MONTH": "الشهر الماضي", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "فشل بدء خدمة الاتصال", "FastSyncDataCancelTips": "هل أنت متأكد من إلغاء مزامنة البيانات؟", "FastSyncDataSelectTips": "الرجاء اختيار المستشعرات لمزامنة البيانات", "Band-pass": "نطاق التمرير", "aeWaveChartTitle": "طيف شكل موجة AE", "aePhaseChartTitle": "طيف طور AE", "aeFlyChartTitle": "طيف طيران AE", "LOCAL_PARAMS_TIME": "الوقت", "LOCAL_CHART_COLORDENSITY": "كثافة اللون", "openTime": "وقت الفتح", "closeTime": "وقت الإغلاق", "triggerUnit": "سعة التشغيل[μV]", "openTimeUnit": "وقت الفتح[μs]", "closeTimeUnit": "وقت الإغلاق[μs]", "triggerUnitTips": "مجموعة الزناد السعة : [ 1-35000 ] μ V", "TOP_DISCHARGE_AMPLITUDE": "Top3 سعة التفريغ ", "WS_901_ERROR_TIPS": "خدمة دفع الأوامر قيد التسجيل، يرجى إعادة المحاولة بعد ثانيتين"}