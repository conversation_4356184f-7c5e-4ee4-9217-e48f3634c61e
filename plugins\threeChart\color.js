/*
*colorMode=Blaze:火焰色
*colorMode=Rainbow：彩虹色
*colorMode=Gray：灰色
*/

function GetColorStr(colorMode, percent) {
	if(colorMode == "Blaze")
	{
		return GetColor((parseInt)(40 * percent), (parseInt)(150 + 30 * percent), (parseInt)(90 + 80 * percent));
	}
	else if (colorMode == "Rainbow")
	{
		return GetColor((parseInt)(170 * (1 - percent)), 200, 180);
	}
	else if (colorMode == "Gray")
	{
		return GetColor(0, 0, (parseInt)(120 + 120 * percent));
	}
	else
	{
		return GetColor((parseInt)(40 * percent), (parseInt)(150 + 30 * percent), (parseInt)(90 + 80 * percent));
	}
}

/**
 * HSL颜色值转换为RGB.
 * 换算公式改编自 http://en.wikipedia.org/wiki/HSL_color_space.
 * h, s, 和 l 设定在 [0, 1] 之间
 * 返回的 r, g, 和 b 在 [0, 255]之间
 *
 * @param   Number  h       色相
 * @param   Number  s       饱和度
 * @param   Number  l       亮度
 * @return  Array           RGB色值数值
 */
function GetColor(hue, saturation, brightness) {
	var r,
	g,
	b;

	// check input hsb
	hue = hue > 239 ? 239 : hue;
	hue = hue < 0 ? 0 : hue;
	saturation = saturation > 240 ? 240 : saturation;
	saturation = saturation < 0 ? 0 : saturation;
	brightness = brightness > 240 ? 240 : brightness;
	brightness = brightness < 0 ? 0 : brightness;

	var H = parseFloat(hue / 239.0);
	var S = parseFloat(saturation / 240.0);
	var L = parseFloat(brightness / 240.0);

	var red = 0,
	green = 0,
	blue = 0;
	var d1,
	d2;

	if (L == 0) {
		red = green = blue = 0;
	} else {
		if (S == 0) {
			red = green = blue = L;
		} else {
			d2 = (L <= 0.5) ? L * (1.0 + S) : L + S - (L * S);
			d1 = 2.0 * L - d2;

			var d3 = [H + 1.0 / 3.0, H, H - 1.0 / 3.0];
			var rgb = [0, 0, 0];

			for (var i = 0; i < 3; i++) {
				if (d3[i] < 0) {
					d3[i] += 1.0;
				}

				if (d3[i] > 1.0) {
					d3[i] -= 1.0;
				}

				if (6.0 * d3[i] < 1.0) {
					rgb[i] = d1 + (d2 - d1) * d3[i] * 6.0;
				} else {
					if (2.0 * d3[i] < 1.0) {
						rgb[i] = d2;
					} else {
						if (3.0 * d3[i] < 2.0) {
							rgb[i] = (d1 + (d2 - d1) * ((2.0 / 3.0) - d3[i]) * 6.0);
						} else {
							rgb[i] = d1;
						}
					}
				}
			}

			red = rgb[0];
			green = rgb[1];
			blue = rgb[2];
		}
	}

	red = 255.0 * red;
	green = 255.0 * green;
	blue = 255.0 * blue;

	// check rgb
	red = red > 255.0 ? 255.0 : red;
	red = red < 1 ? 0.0 : red;
	green = green > 255.0 ? 255.0 : green;
	green = green < 1 ? 0.0 : green;
	blue = blue > 255.0 ? 255.0 : blue;
	blue = blue < 1.0 ? 0.0 : blue;

	r = parseInt(red + 0.5);
	g = parseInt(green + 0.5);
	b = parseInt(blue + 0.5);

	return showRGB(r, g, b);
}

function showRGB(r, g, b) {
	//将RGB转换为16进制Hex值
	return hexcode = "#" + toHex(parseInt(r)) + toHex(parseInt(g)) + toHex(parseInt(b));
}
function toHex(d) {
	if (isNaN(d)) {
		d = 0;
	}
	//16进制转换方法
	var n = new Number(d).toString(16);
	return (n.length == 1 ? "0" + n : n);
}
