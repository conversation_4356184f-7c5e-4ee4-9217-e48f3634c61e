var STATION_PARAM_ENUM = {
  INPUT: 'input',
  STATE_LABEL: 'state_label',
  TEXT_AREA: 'text_area',
  SELECT: 'select',
  SWITCH: 'switch',
  CHECK: 'check',
  FILE_INPUT: 'file_input',
  ENABLE_STATE: 'enable_state',
  SAVE_BTN: 'save_btn',
};

var _protocolConfigStateEnum = {
  disabled: {
    name: 'OUTWARD_CONFIG_DISABLED', //已停用
    code: 0,
  },
  enabled: {
    name: 'OUTWARD_CONFIG_ENABLED', //已启用
    code: 1,
  },
  enabling: {
    name: 'OUTWARD_CONFIG_ENABLING', //正在启用
    code: 2,
  },
  disabling: {
    name: 'SENSOR_TIMEOUT_DISABLING', //正在停用
    code: 3,
  },
};

function getProtocolConfigStateName(code) {
  return getCodeName(code, _protocolConfigStateEnum);
}

var MainStationParamsManager = {
  STATION_PROTOCOL_PARAM_CONFIGS: {
    I2: [
      {
        id: 'configStatus',
        type: STATION_PARAM_ENUM.ENABLE_STATE,
        name: 'OUTWARD_CONFIG_ENABLE_STATE',
        isRequired: true,
        value: '',
      },
      {
        id: 'businessProtocol',
        type: STATION_PARAM_ENUM.SELECT,
        name: 'BusiProtocol',
        isRequired: true,
        data: [],
      },
      {
        id: 'serverIP',
        type: STATION_PARAM_ENUM.INPUT,
        name: 'serverIP',
        isRequired: true,
        value: '',
      },
      {
        id: 'port',
        type: STATION_PARAM_ENUM.INPUT,
        inputType: 'number',
        name: 'serverPort',
        isRequired: true,
        value: '',
      },
      {
        id: 'url',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'URL',
        value: '',
      },
      {
        id: 'user',
        type: STATION_PARAM_ENUM.INPUT,
        name: 'userName',
        value: '',
      },
      {
        id: 'passwd',
        type: STATION_PARAM_ENUM.INPUT,
        name: 'passWord',
        value: '',
      },
      {
        id: 'heartInterval',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'HeartbeatTransmissionInterval',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      {
        id: 'dataInterval',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'DataUploadInterval',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      // {
      //   id: 'connectedState',
      //   type: STATION_PARAM_ENUM.STATE_LABEL,
      //   name: 'connectedState',
      //   value: '',
      // },
      {
        id: 'saveBtn',
        type: STATION_PARAM_ENUM.SAVE_BTN,
        name: '',
        value: '',
      },
    ],
    IEC104: [
      {
        id: 'configStatus',
        type: STATION_PARAM_ENUM.ENABLE_STATE,
        isRequired: true,
        name: 'OUTWARD_CONFIG_ENABLE_STATE',
        value: '',
      },
      {
        id: 'businessProtocol',
        type: STATION_PARAM_ENUM.SELECT,
        isRequired: true,
        name: 'BusiProtocol',
        data: [],
      },
      {
        id: 'serverIP',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'serverIP',
        value: '',
      },
      {
        id: 'port',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        inputType: 'number',
        name: 'serverPort',
        value: '',
      },
      {
        id: 'K',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'K',
        inputType: 'number',
        value: '',
      },
      {
        id: 'W',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'W',
        inputType: 'number',
        value: '',
      },
      {
        id: 't0',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'timeoutT0',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      {
        id: 't1',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'timeoutT1',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      {
        id: 't2',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'timeoutT2',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      {
        id: 't3',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        name: 'timeoutT3',
        inputType: 'number',
        unit: 'S',
        value: '',
      },
      {
        id: 'saveBtn',
        type: STATION_PARAM_ENUM.SAVE_BTN,
        name: '',
        value: '',
      },
    ],
    IEC61850: [
      {
        id: 'configStatus',
        type: STATION_PARAM_ENUM.ENABLE_STATE,
        isRequired: true,
        name: 'OUTWARD_CONFIG_ENABLE_STATE',
        value: '',
      },
      {
        id: 'businessProtocol',
        type: STATION_PARAM_ENUM.SELECT,
        isRequired: true,
        name: 'BusiProtocol',
        onChange: function () {
          MainStationConfigLayerUtil.state.hasSelect61850File = true;
          MainStationConfigLayerUtil.state.paramCache[
            'IEC61850-form'
          ].saveBtn.attr('disabled', false);
          for (var i = 0; i < 4; i++) {
            $('.file-fake-input-div')[i].innerHTML =
              getI18nName('selectPlease');
          }
        },
        data: [],
      },
      {
        id: 'mms',
        type: STATION_PARAM_ENUM.FILE_INPUT,
        isRequired: true,
        inputType: 'file',
        name: 'MMSprofile',
        value: '',
      },
      {
        id: 'startup',
        type: STATION_PARAM_ENUM.FILE_INPUT,
        isRequired: true,
        inputType: 'file',
        name: 'StartupProfile',
        value: '',
      },
      {
        id: 'usermap',
        type: STATION_PARAM_ENUM.FILE_INPUT,
        isRequired: true,
        inputType: 'file',
        name: 'UsermapProfile',
        value: '',
      },
      {
        id: 'cid',
        type: STATION_PARAM_ENUM.FILE_INPUT,
        isRequired: true,
        inputType: 'file',
        name: 'CIDfile',
        value: '',
      },
      {
        id: 'saveBtn',
        type: STATION_PARAM_ENUM.SAVE_BTN,
        name: '',
        value: '',
      },
    ],
    modbus: [
      {
        id: 'configStatus',
        type: STATION_PARAM_ENUM.ENABLE_STATE,
        isRequired: true,
        name: 'OUTWARD_CONFIG_ENABLE_STATE',
        value: '',
      },
      {
        id: 'businessProtocol',
        type: STATION_PARAM_ENUM.SELECT,
        isRequired: true,
        name: 'BusiProtocol',
        data: [],
      },
      {
        id: 'serialPort',
        type: STATION_PARAM_ENUM.SELECT,
        isRequired: true,
        name: 'serialPort',
        data:
          MONITOR_TYPE_CODE === 'MonitorTypeCollectionNode'
            ? ['/dev/ttyO4']
            : ['/dev/ttyO2', '/dev/ttyO4'],
        value: '',
      },
      {
        id: 'baudRate',
        type: STATION_PARAM_ENUM.SELECT,
        isRequired: true,
        inputType: 'number',
        name: 'baudRate',
        data: [9600, 19200, 38400, 57600, 115200],
        value: '',
      },
      {
        id: 'slaveAddress',
        type: STATION_PARAM_ENUM.INPUT,
        isRequired: true,
        inputType: 'number',
        name: 'WorkerAddress',
        value: '',
      },
      {
        id: 'isEnableDbToDbm',
        type: STATION_PARAM_ENUM.SWITCH,
        isRequired: true,
        name: 'UnitConversion',
        unit: '[Db -> Dbm]',
        value: '',
      },
      {
        id: 'saveBtn',
        type: STATION_PARAM_ENUM.SAVE_BTN,
        name: '',
        value: '',
      },
    ],
  },
};

// 防止t1 t2循环校验的计数器
var checkT1_T2_count = 0;

var MAIN_STATION_VALIDATE = {
  message: '',
  feedbackIcons: {
    validating: 'glyphicon glyphicon-refresh',
  },
  fields: {
    serverIP: {
      validators: {
        regexp: {
          regexp:
            /^((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))(\.((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))){3}$/,
          message: getI18nName('ERROR_IP_FORMAT'),
        },
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('serverIP')
          ),
        },
      },
    },
    port: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('serverPort')
          ),
        },
        between: {
          min: 0,
          max: 65535,
          message: stringFormat.format(
            getI18nName('ERROR_TIP_RANGE'),
            0,
            65535
          ),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    url: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('URL')
          ),
        },
        stringLength: {
          max: 100,
          message: stringFormat.format(getI18nName('LENGTH_COMM_LIMIT'), 100),
        },
      },
    },
    heartInterval: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('HeartbeatTransmissionInterval')
          ),
        },
        between: {
          min: 1,
          max: 86400,
          message: stringFormat.format(
            getI18nName('ERROR_TIP_RANGE'),
            1,
            86400
          ),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    dataInterval: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('DataUploadInterval')
          ),
        },
        between: {
          min: 60,
          max: 86400,
          message: stringFormat.format(
            getI18nName('ERROR_TIP_RANGE'),
            60,
            86400
          ),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    K: {
      validators: {
        notEmpty: {
          message: stringFormat.format(getI18nName('pleaseInputFormat'), 'K'),
        },
        between: {
          min: 1,
          max: 10000,
          message: stringFormat.format(
            getI18nName('ERROR_TIP_RANGE'),
            1,
            10000
          ),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    W: {
      validators: {
        notEmpty: {
          message: stringFormat.format(getI18nName('pleaseInputFormat'), 'W'),
        },
        between: {
          min: 1,
          max: 10000,
          message: stringFormat.format(
            getI18nName('ERROR_TIP_RANGE'),
            1,
            10000
          ),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    t0: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('timeoutT0')
          ),
        },
        between: {
          min: 1,
          max: 1000,
          message: stringFormat.format(getI18nName('ERROR_TIP_RANGE'), 1, 1000),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    t1: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('timeoutT1')
          ),
        },
        between: {
          min: 1,
          max: 1000,
          message: stringFormat.format(getI18nName('ERROR_TIP_RANGE'), 1, 1000),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
        callback: {
          message: getI18nName('ERROR_T2_T1'),
          callback: function (value, validator) {
            if (checkT1_T2_count < 1) {
              checkT1_T2_count++;
              var validater =
                MainStationConfigLayerUtil.submitForm['IEC104-form'];
              validator.resetField('t2');
              validator.validateField('t2');
            } else {
              checkT1_T2_count = 0;
            }
            if (value > 1000 || value < 1) return true;
            var valT2 = $('#t2').val();
            console.log('lyx-t1', { value, valT2 });
            if (parseInt(value) <= parseInt(valT2)) {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    t2: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('timeoutT2')
          ),
        },
        between: {
          min: 1,
          max: 1000,
          message: stringFormat.format(getI18nName('ERROR_TIP_RANGE'), 1, 1000),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
        callback: {
          message: getI18nName('ERROR_T2_T1'),
          callback: function (value, validator) {
            // console.log('lyx', value);
            if (checkT1_T2_count < 1) {
              checkT1_T2_count++;
              var validater =
                MainStationConfigLayerUtil.submitForm['IEC104-form'];
              validator.resetField('t1');
              validator.validateField('t1');
            } else {
              checkT1_T2_count = 0;
            }

            if (value > 1000 || value < 1) return true;
            var valT1 = $('#t1').val();
            console.log('lyx-t2', { value, valT1 });
            if (parseInt(value) >= parseInt(valT1)) {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    t3: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('timeoutT3')
          ),
        },
        between: {
          min: 1,
          max: 1000,
          message: stringFormat.format(getI18nName('ERROR_TIP_RANGE'), 1, 1000),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
      },
    },
    mms: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('MMSprofile')
          ),
        },

        file: {
          maxSize: 5 * 1024 * 1024, // 5 * 1024 * 1024 = 5MB
          priority: 1,
          message: stringFormat.format(
            getI18nName('ERROR_FILE_SIZE_LESS_THAN_MB'),
            5
          ),
        },
        callback: {
          priority: 0,
          message: stringFormat.format(
            getI18nName('ERROR_FILE_NAME'),
            'mms.ini'
          ),
          callback: function (value, validator, ele) {
            if (!ele[0].files[0]) return true;
            var fileName = ele[0].files[0].name;
            if (fileName != 'mms.ini') {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    startup: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('StartupProfile')
          ),
        },
        file: {
          maxSize: 5242880, // 5 * 1024 * 1024 = 5MB
          message: stringFormat.format(
            getI18nName('ERROR_FILE_SIZE_LESS_THAN_MB'),
            5
          ),
        },
        callback: {
          message: stringFormat.format(
            getI18nName('ERROR_FILE_NAME'),
            'startup.cfg'
          ),
          callback: function (value, validator, ele) {
            if (!ele[0].files[0]) return true;
            var fileName = ele[0].files[0].name;
            if (fileName != 'startup.cfg') {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    usermap: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('UsermapProfile')
          ),
        },
        file: {
          extension: 'cfg', // 只允许上传.cfg格式
          maxSize: 5242880, // 5 * 1024 * 1024 = 5MB
          message: stringFormat.format(
            getI18nName('ERROR_FILE_SIZE_LESS_THAN_MB'),
            5
          ),
        },
        callback: {
          message: stringFormat.format(
            getI18nName('ERROR_FILE_NAME'),
            'usermap.cfg'
          ),
          callback: function (value, validator, ele) {
            if (!ele[0].files[0]) return true;
            var fileName = ele[0].files[0].name;
            if (fileName != 'usermap.cfg') {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    cid: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('CIDfile')
          ),
        },
        file: {
          maxSize: 5242880, // 5 * 1024 * 1024 = 5MB
          message: stringFormat.format(
            getI18nName('ERROR_FILE_SIZE_LESS_THAN_MB'),
            5
          ),
        },
        callback: {
          message: stringFormat.format(
            getI18nName('ERROR_FILE_SUFFIX'),
            '.cid'
          ),
          callback: function (value, validator, ele) {
            if (!ele[0].files[0]) return true;
            var fileName = ele[0].files[0].name;
            var strArr = fileName.split('.');
            if (strArr[strArr.length - 1] != 'cid') {
              return false;
            } else {
              return true;
            }
          },
        },
      },
    },
    serialPort: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('serialPort')
          ),
        },
      },
    },
    baudRate: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('baudRate')
          ),
        },
      },
    },
    slaveAddress: {
      validators: {
        notEmpty: {
          message: stringFormat.format(
            getI18nName('pleaseInputFormat'),
            getI18nName('WorkerAddress')
          ),
        },
        between: {
          min: 1,
          max: 254,
          message: stringFormat.format(getI18nName('ERROR_TIP_RANGE'), 1, 254),
        },
        integer: {
          message: getI18nName('ERROR_INTEGER'),
        },
        /* regexp: {
          regexp: /^[\s\S]*$/,
        },
        callback: {
            message: getI18nName('modbusAddressCheckTip'),
            callback: function (value, validator) {
                console.log('lyx',value)
                if (!/^(1?[1-9]?[0-9]|2[0-4][0-9]|25[0-4])$/.test(value) || value == 0) {
                    return false;
                } else {
                    return true;
                }
            }
        } */
      },
    },
  },
};
