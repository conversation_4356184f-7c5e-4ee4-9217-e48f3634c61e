{"loginTime": "Tiempo de inicio de sesión", "changerUser": "Cambio de usuario", "change": "Cambiar", "login": "In<PERSON><PERSON>", "systemShutDownConfirmTip": "¿El sistema se apagará en 1 minuto, ¿ se cancelará la operación?", "systemShutDownTip": "¡Llega 1 minuto y se apaga automáticamente!", "systemShutDownCancel": "<PERSON><PERSON><PERSON>", "loadingData": "Trabajando duro para cargar los datos, por favor espere......", "pageSize": "Por p<PERSON>gina {1} art<PERSON><PERSON><PERSON>", "pageSelecter": "Muestra:  {1}-{2}, un total de {3}.", "search": "Buscar", "noMatch": "No se encontraron registros coincidentes", "index": "Número de serie", "type": "Tipo", "aduId": "Codificación", "connectedState": "Estado de la red", "commType": "Modo de comunicación", "loraSignalLevel": "Intensidad de la señal Lora", "loraSNR": "Relación señal - ruido", "powerSupplyMode": "Modo de suministro de energía", "byBattery": "Batería incorporada", "byCable": "Fuente de alimentación externa", "battaryPercent": "Electricidad", "battaryLife": "Duración de la batería (días)", "deviceNameTable": "Nombre del dispositivo", "pointType": "Sen<PERSON><PERSON>", "isconnected": "Estado", "onlineState": "En línea", "offlineState": "Pérdida de contacto", "aduNeverConnectedState": "No conectado", "updateTime": "Tiempo de actualización", "data": "Datos", "chart": "Mapa", "operate": "Operación", "lookUp": "<PERSON>er", "tbSampleDate": "Tiempo de recolección", "collect": "Recolección", "errorTip": "Error: {1}", "AEamp": "Amplitud AE", "aeChartTitle": "Mapa de amplitud AE", "humidityChartTitle": "<PERSON><PERSON><PERSON> de <PERSON>eda<PERSON>", "temperatureChartTitle": "Curva de temperatura", "noiseChartTitle": "Curva de ruido", "coreGroundingCurrentChartTitle": "Curva de corriente de tierra del núcleo de hierro", "TEVamp": "Amplitud tev", "TEVYunit": "Unidad [db]", "UnitFomat": "Unidad [ {1}]", "unitParse": "Unidad [ {1}]", "maxValue": "<PERSON>or máxi<PERSON>", "minValue": "<PERSON><PERSON> m<PERSON>", "average": "Media", "AT&T": "AT & T en Estados Unidos", "China Unicom": "China Unicom", "China Mobile": "China Mobile", "Transformer": "Transformador", "Breaker": "<PERSON><PERSON><PERSON><PERSON>", "Disconnector": "Interruptor de aislamiento", "Isolator": "<PERSON><PERSON><PERSON>", "Arrester": "Pararrayos", "PT": "Transformador de tensión", "CT": "Transformador de corriente", "Busbar": "Bus", "Circuit": "Unión madre", "Switchgear": "Gabinete de conmutación", "Power Cable": "Cable eléctrico", "Lightning Rod": "Pararrayos", "Wall Bushing": "Manga a través de la pared", "Reactor": "Reactor", "Electric Conductor": "Conductor <PERSON><PERSON><PERSON><PERSON><PERSON> (drenaje de desviación)", "Power Capacitor": "Condensadores eléctricos", "Discharge Coil": "<PERSON><PERSON>", "Load Switch": "Interruptor de carga", "Grounding Transformer": "Cambio de tierra", "Grounding Resistance": "Resistencia a tierra", "Grounding Grid": "Red de puesta a tierra", "Combined Filter": "Filtro combinado", "Insulator": "Aisladores", "Coupling Capacitor": "Condensadores de acoplamiento", "Cabinet": "Gabinete de pantalla", "Other": "<PERSON><PERSON><PERSON>", "Fuse": "Fusibles", "Using Transformer": "Cambios utilizados", "Arc Suppression Device": "Dispositivo de extinción de arco", "Main Transformer": "Transformador principal", "Wave Trap": "Bloqueador de ondas", "Combined Electric Appliance": "Electrodomésticos combinados", "Combined Transformer": "Transformador combinado", "monitor": "Monitoreo en tiempo real", "dataSelect": "Consulta de datos", "themeSkin": "<PERSON><PERSON>", "themeBlue": "Azul", "settings": "Configuración del sistema", "datetime_set": "Configuración del tiempo", "language_set": "Configuración del idioma", "soft_setting": "Configuración del sistema", "file_manage": "Gestión de archivos", "instrument_set": "Configuración del sensor", "file_point_set": "Configuración del punto de medición", "alarm_param_set": "Configuración de alarma", "alarm_manager": "Gestión de alarmas", "audit_view": "Revisión de auditoría", "modbus_setting": "Configuración modbus", "hardware_update": "Actualización del firmware", "collect_mode": "Modo de adquisición", "net_set": "Configuración de la red", "main_station_params": "Configuración de la estación principal", "sync_data": "<PERSON><PERSON> sinc<PERSON>", "syncingData": "Sincronizando datos...", "syncingDataCancel": "Cancelar sincronización", "syncingDataFailed": "Falló la sincronización", "syncingDataSuccess": "Se completan los datos de sincronización", "syncingDataProgress": "Progreso de los datos de sincronización: {1}", "syncingDataProgress2": "Progreso de los datos de sincronización [{1}]", "export_data": "Exportar datos", "system_info": "Información del sistema", "monitoringTable": "Tabla de datos", "PD": "Sensor de descarga local incorporado", "PD_THREE": "Sensor de descarga local incorporado (tres en uno)", "PD_FIVE": "Sensor de descarga local incorporado (cinco en uno)", "OLD_IS": "Sensores inteligentes UHF", "MEU": "Sensores de características mecánicas", "UHF_IS": "Sensores inteligentes UHF", "HFCT_IS": "Sensores inteligentes de alta frecuencia", "PD_IS": "Sensor inteligente externo de tres en uno", "TRANSFORMER_AE_IS": "Sensor ultrasónico del transformador", "GIS_AE_IS": "Sensores ultrasónicos GIS", "ENV_IS": "Sensores inteligentes ambientales", "Arrester_U_IS": "Sensor inteligente de voltaje del pararrayos", "Arrester_I_IS": "Sensor inteligente de corriente del pararrayos", "LeakageCurrent_IS": "Sensor inteligente de corriente de fuga", "Vibration_IS": "Sensor inteligente de vibración", "MECH_IS": "Monitor inteligente de características mecánicas", "TEMP_HUM_IS": "Sensores de temperatura y humedad", "GrounddingCurrent_IS": "Sensor inteligente de corriente de tierra", "MECH_PD_TEMP": "Características mecánicas de descarga local incorporadas sensor de temperatura de tres en uno", "GIS_LOWTEN": "Sensores de baja tensión", "CHANNEL_OPTICAL_TEMP": "Sensor de medición de temperatura de fibra óptica", "VaisalaDTP145": "Vesala dtp145", "Wika_GDT20": "Wika gdt20", "Wika_GDHT20": "Wika gdht20", "SHQiuqi_SC75D_SF6": "Shanghai qiuqi sc75d", "SPTR_IS": "Sensor de corriente de tierra del núcleo de hierro", "TEMPFC_OUT": "Sensor de temperatura (clmd)", "TEMPXP_OUT": "Sensor de temperatura (sps061)", "SF6YN_OUT": "Sensor de presión de gas SF6 (ftp - 18)", "FLOOD_IS": "Sensor inteligente de inmersión en agua", "TEMPKY_OUT": "Sensor de temperatura (rfc - 01)", "SMOKERK_OUT": "<PERSON><PERSON> (rs - YG - n01)", "HIKVIDEO_OUT": "Sensores de vídeo (hik)", "TEMPSB_OUT": "Sensor de temperatura (ds18b20)", "TEMP_HUM_JDRK_OUT": "Sensor de temperatura y humedad (rs - WS - N01 - 2)", "SF6_OUT": "Sensor SF6 & O2 (wfs - s1p - so)", "FAN_OUT": "Controlador de ventilador", "LIGHT_OUT": "Controlador de iluminación", "NOISE_JDRK_OUT": "Sensor de ruido (rs - WS - n01)", "IR_DETECTION_OUT": "Detector infrarrojo de doble discriminación", "TEMPWS_OUT": "Sensor de temperatura y humedad (ws - dmc100)", "FLOODWS_OUT": "Sensor de inmersión en agua (ws - dmc100)", "NOISEWS_OUT": "<PERSON><PERSON> de ruido (ws - dmc100)", "VibrationSY_OUT": "Sensor de vibración de Shengyang", "TEVPRPS_IS": "Sensor inteligente de tensión terrestre transitoria", "SF6_IS": "Sensores inteligentes SF6", "phase": "Fase", "period": "<PERSON><PERSON><PERSON>", "AMP": "<PERSON><PERSON><PERSON><PERSON>", "RMS": "Valor válido", "max": "Valor máximo del ciclo", "fre1Value": "Componente de frecuencia 1", "fre2Value": "Componente de frecuencia 2", "All-pass": "Quantong", "Low-pass": "Paso bajo", "High-pass": "Qualcomm", "No-Record": "No registrado", "TEV": "Tensión de tierra transitoria", "AE": "Ultrasonido", "TEMP": "Temperatura", "HFCT": "Corriente de alta frecuencia", "UHF": "UHF", "Humidity": "Humedad", "Decibels": "<PERSON><PERSON><PERSON>", "Noisy": "Presión acústica", "Noise": "<PERSON><PERSON><PERSON>", "ENVGas": "Atmósfera", "envgas": "Tendencias atmosféricas", "MP": "Características mecánicas", "FLOOD": "Inundaciones", "SMOKE": "Sensación de humo", "SF6": "Hexafluoruro de azufre", "FAN": "Ventilador", "LIGHT": "Iluminación", "NOISE": "<PERSON><PERSON><PERSON>", "IRDETECTION": "Infrarrojo", "SF6YN": "Presión del gas SF6", "ArresterU": "Tensión del pararrayos", "ArresterI": "Corriente del pararrayos", "LeakageCurrent": "Corriente de fuga", "Vibration": "Vibración", "FrostPointRaw": "Punto de congelación", "FrostPointATM": "Punto de congelación (presión atmosférica estándar)", "DewPointRaw": "Punto de rocío", "DewPointATM": "Punto de rocío (presión atmosférica estándar)", "Moisture": "Micro - Agua", "AbsolutePressure": "Presión absoluta", "NormalPressure": "Presión estándar", "Density": "Densidad", "Oxygen": "Contenido de oxígeno", "SPTR": "Corriente de tierra del núcleo de hierro", "GrounddingCurrent": "Corriente de tierra", "VIDEO": "Imágenes de vídeo", "TEVPRPS": "Tensión de tierra transitoria prps", "confirm": "Determinar", "close": "Cierre", "yes": "Sí.", "no": "No", "continue": "Continúa", "none": "<PERSON><PERSON><PERSON>", "DISK": "Almacenamiento", "MEMORY": "Memoria", "noTestData": "No hay datos de prueba", "connected": "Conexión", "disconnected": "Desconexión", "virtualKeyBord": "Teclado virtual", "pleaseInput": "Por favor, introduzca", "pleaseInputFormat": "Por favor, introdu<PERSON><PERSON> {1}.", "tips": "Consejos", "confirmTips": "Consejos de confirmación", "getDirFailed": "Falló al obtener el catálogo:", "backupSuccessTip": "Los datos se han respaldado con éxito, por favor, consulte en el catálogo ('1').", "backupFailedTip": "Falló la copia de Seguridad de datos", "exportFailedTip": "Falló la exportación de datos: ',' 1 '.", "historyDataTypeNotSuport": "Por el momento, no se admite la consulta de datos históricos de este tipo de sensor.", "stopExportTips": "¿¿ es necesario dejar de exportar datos?", "stationNameTips": "Por favor, rellene el nombre del sitio", "powerUnitNameTips": "Por favor, rellene la unidad de energía eléctrica.", "selectDeviceTips": "Por favor, seleccione el dispositivo una vez", "selectPointTips": "Por favor, elija el punto de medición.", "selectDeviceOfPointTips": "Por favor, elija un dispositivo que necesite agregar un punto de medición.", "saveSuccess": "Guardar con éxito", "saveFailed": "Falló el ahorro:", "operatFailed": "La operación falló: ',' 1 '.", "chosseSensorUpdate": "Por favor, seleccione los sensores que deben actualizarse", "chooseFileUpdate": "Por favor, elija actualizar el archivo", "cancelUpdateSensorConfirm": "¿¿ está seguro de cancelar la actualización del programa de sensores?", "enterServerUrlTips": "Introduzca la dirección del servidor", "enterServerUrlError": "La dirección del servidor se introdujo incorrectamente, por favor vuelva a ingresar.", "enterServerPortTips": "Introduzca el puerto del servidor.", "enterServerPortError": "Error de entrada del puerto del servidor, rango de entrada del puerto: [165535].", "NTPserverIpError": "La dirección del servidor cronológico se introdujo incorrectamente, por favor vuelva a ingresar.", "NTPserverPortError": "Error de entrada del puerto del servidor cronológico, rango de entrada del puerto: [165535].", "enterNetName": "Introduzca el nombre de la red.", "enterIP": "Por favor, introduz<PERSON> la Ip.", "enterIPError": "Error al introducir la dirección ip, como: ***********", "enterNetMask": "Introduzca la máscara de subred", "enterNetError": "Error de entrada de la máscara de subred, como: *************", "enterGateWay": "Por favor, introduzca la pasarela", "enterGateWayError": "Error en la configuración de la pasarela, como: ***********", "enterAPN": "Por favor, elija <PERSON>", "pdSampleIntervalTip": "PD - intervalo de muestreo entre 20s y 594099s (99h99m99s)", "meuSampleIntervalTip": "El intervalo de muestreo meu - es de entre 10 y 86.400", "monitorAwakeTimeTip": "Intervalo de hibernación entre 1s y 86400", "monitorSleepSpaceTip": "<PERSON><PERSON> de intervalo de despertar: {1},{2} Min", "uploadIntervalTip": "El intervalo de carga oscila entre 60 y 86.400", "sampleIntervalTip": "Enteros con intervalos de muestreo entre 1 y 72 horas", "monitorSampleTimeTip": "Todo el tiempo entre 0 y 23 puntos en el momento de muestreo inicial del host", "monitorSampleIntervalTip": "Un número entero entre 1 hora y 168 horas de intervalo de muestreo en el host", "updatingSensor": "Actualizando sensores...", "updatingSensorProgress": "Progreso de la actualización del sensor: {1}", "updatingSensorProgress2": "Progreso de la actualización del sensor [{1}]", "selectInstTip": "Por favor, elija el sensor que necesita modificar", "selectChannelTip": "Por favor, elija el canal que desea modificar", "instCodeTip": "El Código del sensor no puede estar vacío", "commLinkTypeTip": "Por favor, elija el tipo de enlace de comunicación", "commLinkPortTip": "Por favor, seleccione el puerto de comunicación", "continuSampTimeTip": "El tiempo de recolección continua oscila entre {1} Min y {2} min.", "continuSampTimeCompareTip": "El tiempo de recolección continua debe ser menor que el intervalo de muestreo.", "instSampleSpaceTip": "El intervalo de muestreo oscila entre {1} Min y {2} min.", "instSampleStartTimeTip": "El momento completo en el que el momento de muestreo inicial es entre 0 y 23 puntos.", "instNumberPattenTip": "Solo puede contener números 0 - 9, letras a - ZA - Z y:.", "instIPPattenTip": "Los formatos IP y de Puerto son: 0.0.0.0: 1", "deleteInstTip": "Por favor, seleccione los sensores que deben eliminarse", "selectSensorTip": "Por favor, seleccione el sensor", "deleteInstConfirmTip": "¿Asegúrese de eliminar: </br> {1} </br> <PERSON><PERSON><PERSON> del sensor: {2}?", "activeInstConfigTip": "¿¿ está seguro de que esta configuración se aplica a sensores similares en </br> {1}?", "activeBatchConfigsSuccess": "Configuración por lotes exitosa del sensor", "activeBatchConfigsBegin": "Comienzan los sensores de configuración por lotes", "activeBatchConfigsFailed": "Falló la configuración del sensor por lotes", "collectStartOnceTip": "¿¿ estás seguro de que empiezas a recopilar datos de sensores similares </br>{1} ?", "collectStartTip": "Asegúrese de comenzar la recolección de los datos de </br> {1} </br> Có<PERSON> del sensor: {2} ", "wakeUpInstrumentOnceTip": "¿¿ estás seguro de que empiezas a despertar los sensores similares </br> {1}", "wakeUpInstrumentTip": "¿Asegúrese de comenzar a despertar </br>{1}</br> sensores: {2}?", "emptySensorDataOnceTip": "¿¿ está seguro de vaciar todos los datos de sensores similares </br>{1} ?", "emptySensorDataTip": "Asegúrese de vaciar todos los datos de (...) / ¿ de (...), (...), (...), (...).", "emptySensorDataSuccess": "Los datos del sensor se eliminaron con éxito.", "emptySensorDataFailed": "Falló la eliminación de los datos del sensor:", "ae_chart": "Monitoreo en tiempo real - mapa AE", "uhf_chart": "Monitoreo en tiempo real - mapa UHF", "hfct_chart": "Monitoreo en tiempo real - mapa de hfct", "tev_chart": "Monitoreo en tiempo real - amplitud tev", "tev_prps_chart": "Monitoreo en tiempo real - mapa prps de voltaje de tierra transitorio", "temperature_chart": "Monitoreo en tiempo real - temperatura", "humidity_chart": "Monitoreo en tiempo real - humedad", "mechanical_chart": "Monitoreo en tiempo real - características mecánicas", "arrester_chart": "Monitoreo en tiempo real - pararrayos", "leakage_current_chart": "Monitoreo en tiempo real - corriente de fuga", "grounddingcurrent_chart": "Monitoreo en tiempo real - corriente de tierra", "vibration_pickup_chart": "Monitoreo en tiempo real - vibración", "density_micro_water_history": "Monitoreo en tiempo real - microagua de densidad", "core_grounding_current": "Monitoreo en tiempo real - corriente de tierra del núcleo de hierro", "noise_chart": "Monitoreo en tiempo real - ruido", "water_immersion": "Monitoreo en tiempo real - inundaciones", "smoke_sensor": "Monitoreo en tiempo real - sensación de humo", "video_sensor": "Monitoreo en tiempo real - Video", "sf6_sensor": "Monitoreo en tiempo real - SF6", "error": "Error", "warning": "Aviso", "success": "éxito", "userErrorTip": "Nombre de usuario o contraseña incorrecta", "errorNoDeviceId": "El ID del dispositivo no existe", "errorNoSensorId": "El ID del sensor no existe", "errorExistSensorId": "El ID del sensor ya existe", "errorNoChannelId": "El ID del canal no existe", "errorNoPointId": "El punto de medición no existe", "errorDataExportErrDir": "Exportación de datos, ruta de archivo incorrecta.", "errorDataExportErrTime": "Error en el tiempo de exportación de datos", "errorDataExportNoData": "Exportación de datos, sin datos.", "errorDataExportNoSpace": "Exportación de datos, la memoria USB está llena", "errorDataExportNoDisk": "Exportación de datos, sin memoria usb.", "errorDataExportNoInfo": "Exportación de datos, sin información del sitio.", "errorDataExportOther": "Exportación de datos, otros errores.", "errorSetModeBus": "Error en la configuración del módem", "errorSameNamePoint": "El punto de medición del mismo nombre ya existe", "errorIP": "Error IP", "errorGPRSSet": "Error en la configuración de GPR", "errorSenorUpdateErrFile": "Error al actualizar el archivo del sensor", "errorSenorUpdateNotMatch": "Actualización del sensor, el tipo del sensor no coincide con el archivo de actualización", "errorSenorUpdating": "Actualización del sensor, en proceso de actualización", "errorSenorUpdateSizeCheckFailed": "Falló la verificación del tamaño del firmware", "errorSenorUpdateVersionCheckFailed": "Falló la verificación del número de versión del firmware", "errorSenorUpdateDeviceTypeCheckFailed": "Falló la verificación del tipo de dispositivo de firmware", "errorSenorUpdateCRCCheckFailed": "Falló la verificación de la Convención del firmware", "errorSenorUpdateFailed": "Falló la actualización del sensor", "errorSenorUpdateConnectErr": "Comunicación anormal de actualización de firmware", "errorDataCleanFailed": "¡¡ falló la evacuación de datos!", "errorCodeWorkGroupNotExist": "¡¡ el Grupo de trabajo no existe!", "errorCodeInvalidChannel": "¡Paso ilegal!", "errorCodeInvalidWirignMode": "¡Modo de cableado ilegal!", "errorCodeInvalidAlarmMode": "¡Métodos ilegales de alarma!", "errorNoPermission": "El usuario no tiene este permiso", "illegalUser": "¡Usuarios ilegales!", "legalPattenMsg": "Solo se pueden contener caracteres (sin espacios en blanco): caracteres chinos alfanuméricos números romanos I - 85550 '- - () [...,.", "groundCurrent": "Mapa de tendencias de la corriente de tierra", "groundCurrentA": "Corriente de tierra de fase a", "groundCurrentB": "Corriente de tierra de conexión B", "groundCurrentC": "Corriente de tierra de conexión C", "leakageCurrent": "Mapa de tendencias de corriente completa", "leakageCurrentA": "Corriente completa de la fase a", "leakageCurrentB": "Corriente completa de la fase B", "leakageCurrentC": "Corriente completa de la fase C", "ResistiveCurrent": "Mapa de tendencias de la corriente resistiva", "ResistiveCurrentA": "Corriente resistiva de fase a", "ResistiveCurrentB": "Corriente resistiva de fase B", "ResistiveCurrentC": "Corriente resistiva de fase C", "resistiveCurrentA": "Corriente resistiva de fase a", "resistiveCurrentB": "Corriente resistiva de fase B", "resistiveCurrentC": "Corriente resistiva de fase C", "referenceVoltageA": "Tensión de referencia de fase a", "referenceVoltageB": "Tensión de referencia de fase B", "referenceVoltageC": "Tensión de referencia de fase C", "grounddingCurrent": "Mapa de tendencias de la corriente de tierra", "grounddingCurrentA": "Corriente de tierra de fase a", "grounddingCurrentB": "Corriente de tierra de conexión B", "grounddingCurrentC": "Corriente de tierra de conexión C", "timeDomain": "Mapa de dominio del tiempo", "frequencyDomain": "Mapa de dominio de frecuencia", "characterParam": "Parámetros <PERSON>", "TimeDomainDataX": "Señal de vibración del eje X", "TimeDomainDataY": "Señal de vibración del eje y", "TimeDomainDataZ": "Señal de vibración del eje Z", "FrequencyDomainDataX": "Mapa espectral de la señal de vibración - eje X", "FrequencyDomainDataY": "Mapa espectral de la señal de vibración - eje y", "FrequencyDomainDataZ": "Mapa espectral de la señal de vibración - eje Z", "ACCAVGX": "Media de aceleración del eje X", "ACCAVGY": "Media de aceleración del eje y", "ACCAVGZ": "Media de aceleración del eje Z", "ACCMAXX": "Valor máximo de aceleración del eje X", "ACCMAXY": "Valor máximo de aceleración del eje y", "ACCMAXZ": "Valor máximo de aceleración del eje Z", "AMPAVGX": "Promedio de amplitud del eje X", "AMPAVGY": "Promedio de amplitud del eje y", "AMPAVGZ": "Promedio de amplitud del eje Z", "AMPMAXX": "Amplitud máxima del eje X", "AMPMAXY": "Amplitud máxima del eje y", "AMPMAXZ": "Amplitud máxima del eje Z", "MAXFreqX0": "Frecuencia del punto extremo del eje X 1", "MAXFreqY0": "Frecuencia del punto extremo del eje y 1", "MAXFreqZ0": "Frecuencia del punto extremo del eje z 1", "MAXFreqX1": "Frecuencia del punto extremo del eje x 2", "MAXFreqY1": "Frecuencia del punto extremo del eje y 2", "MAXFreqZ1": "Frecuencia del punto extremo del eje Z 2", "MAXFreqX2": "Frecuencia del punto extremo del eje x 3", "MAXFreqY2": "Frecuencia del punto extremo del eje y 3", "MAXFreqZ2": "Frecuencia del punto extremo del eje Z 3", "sensorType": "Tipo de sensor", "sensorList": "Lista de sensores", "gain": "Ganancia", "trigger": "Amplitud de activación", "wave_filter": "Banda de frecuencia", "sample_date": "<PERSON>cha de muestreo", "sample_time": "Tiempo de muestreo", "rms": "Valor válido", "cycle_max": "Valor máximo del ciclo", "frequency1": "Componente de frecuencia 1", "frequency2": "Componente de frecuencia 2", "time_interval": "Intervalo de tiempo", "realData": "Datos en tiempo real", "historyData": "<PERSON><PERSON> his<PERSON>", "trendSync": "An<PERSON><PERSON><PERSON> de tendencias", "preData": "Artí<PERSON>lo anterior", "nextData": "Siguiente artículo", "systemDate": "Fecha del sistema", "systemTime": "Tiempo del sistema", "backupType": "<PERSON><PERSON><PERSON>aldo", "plusBackup": "Respaldo incremental", "allBackup": "<PERSON><PERSON><PERSON> completo", "timeRange": "Rango de <PERSON>", "exportPath": "Ruta de exportación", "getExportDir": "Obtener el catálogo", "checkDir": "Catálogo de Inspección", "exportingData": "Exportando...", "cancel": "Cancelación", "export": "Exportar", "stationConfig": "Configuración del sitio", "stationDelSuccess": "El sitio fue eliminado con éxito", "stationDelFailed": "La eliminación del sitio falló:", "deviceConfig": "Configuración del equipo primario", "pointConfig": "Configuración del punto de medición", "stationName": "Nombre del sitio", "stationPMS": "Código del sitio", "stationLevel": "Nivel de tensión de la estación", "powerUnit": "Unidades eléctricas", "save": "Guardar", "operationSuccess": "Operación exitosa", "deviceAddSuccessTips": "Un aumento exitoso del equipo", "deviceEditSuccessTips": "Una modificación exitosa del equipo", "deviceDelSuccessTips": "El dispositivo fue eliminado con éxito a la vez.", "pointAddSuccessTips": "Aumento exitoso de puntos de medición", "pointEditSuccessTips": "El punto de medición fue modificado con éxito.", "pointDelSuccessTips": "El punto de medición fue eliminado con éxito.", "deleteFailedTips": "Falló la eliminación", "save_add": "Guardar / aumentar", "delete": "Eliminar", "deviceType": "Tipo de equipo", "deviceLevel": "Nivel de tensión del equipo", "add": "Aumentar", "channelTypeAlarm": "Tipo de <PERSON>", "alarmThreshold": "Umbral de alarma", "alarmRecoveryThreshold": "Umbral de recuperación de alarma", "alarmChannel": "Selección del canal de alarma", "built_in_channel_1": "Canal incorporado 1", "External_IO_module": "<PERSON><PERSON><PERSON><PERSON> externo", "not_associated": "No relacionado", "alarmExternalIOSN": "Codificación de módulos Io externos", "alarmExternalIOChannel": "Canal de módulo Io externo", "wiringMode": "Modo de cableado", "alarmMode": "<PERSON><PERSON>", "alarmTime": "Tiempo de alarma", "alarmInterval": "Intervalo de alarma", "alarmDuration": "Duración de la alarma", "normal_open": "A menudo abierto", "normal_close": "A menudo cerrado", "continuous": "Continuo", "timing": "Cronometraje", "interval": "Intervalo", "alarmSaveSuccess": "Configuración de alarma guardada con éxito", "alarmDelSuccess": "Configuración de alarma eliminada con éxito", "deviceName": "Nombre del equipo primario", "pointName": "Nombre del punto de medición", "testStation": "Sitio de prueba", "device": "Equipo primario", "pointList": "Lista de puntos de medición", "sensorID": "ID del sensor", "sensorChannel": "Canal del sensor", "channelList": "Lista de canales", "showPoint": "Mostrar puntos de medición", "fileSelect": "Selección de archivos", "selectPlease": "Por favor, elija", "deviceList": "Lista de sensores", "updating": "Se está Actualizando...", "updatingFailed": "La actualización falló:", "updatingCanceled": "Cancelar actualización", "updatingComplete": "Completar la actualización", "update": "Actualización", "sampleDate": "<PERSON>cha de muestreo", "sampleTime": "Tiempo de muestreo", "pdMax": "Amplitud máxima de descarga", "pdAvg": "Amplitud media de descarga", "pdNum": "Número de pulsos", "acquisitionTime": "Tiempo de recolección", "humidity": "Humedad", "startDate": "Fecha de inicio", "endDate": "Fecha de finalización", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "scanData": "Consulta", "sensorCode": "Codificación de sensores", "sensorTypeSet": "Tipo de sensor", "senorName": "Nombre del sensor", "aduAddress": "Dirección del sensor", "senorWorkMode": "Entrega activa", "On": "Abrir", "Off": "Guan", "ADUMode": "Modo de trabajo", "normalMode": "<PERSON>do de mantenim<PERSON>o", "lowPowerMode": "Modo de bajo consumo de energía", "monitorMode": "Modo de monitoreo", "artificialStateStartTime": "Momento de inicio del Estado de intervención manual (punto entero)", "artificialStateStartTimeTip": "Rango de inicio del Estado de intervención manual 0 - 23", "artificialStateEndTime": "Momento final del Estado de intervención manual (punto entero)", "artificialStateEndTimeTip": "Rango de tiempo final del Estado de intervención manual 0 - 23", "artificialStateWakeUpSpace": "Intervalo de despertar del Estado de intervención manual (puntos)", "unartificialStateWakeUpSpace": "Intervalo de despertar en estado de intervención no manual (puntos)", "isAutoChangeMode": "Si cambiar el modo automáticamente", "monitorModeSampleSapce": "Intervalo de adquisición del modo de monitoreo", "workGroup": "Número del Grupo de trabajo", "warnTemp": "Temperatura de alarma", "taskGroup": "Número de grupo de tareas", "commLinkType": "Tipo de enlace de comunicación", "commLinkPort": "Puerto de comunicación", "frequencyUnit": "Frecuencia de la red eléctrica (hz)", "numInGroup": "Número dentro del Grupo", "commSpeed": "Velocidad de comunicación", "commLoad": "Canal de comunicación", "sampleSpace": "Intervalo de muestreo (min)", "sleepTime": "Estrategia de consumo de energía", "samleStartTime": "Tiempo de muestreo inicial (punto entero)", "applyData": "Operación", "applyAllSensor": "Aplicado al mismo tipo", "collectOnce": "Recolección por lotes", "collectOnceEnd": "Fin de la adquisición de datos", "collectOnceProgress": "Adquisición de datos: ',' 1 '(', '2')", "collectOnceProgress2": "Adquisición de da<PERSON> [ (...) (...)", "activeOnce": "Aplicaciones por lotes", "wakeUp": "<PERSON><PERSON><PERSON>", "wakeUpInstrument": "Sensores de despertar", "wakeUpInstrumentOnce": "Sensores de despertar por lotes", "orderSend": "Se ha emitido la orden", "cleanData": "<PERSON><PERSON><PERSON>", "sensorAddSuccess": "Aumento exitoso de sensores", "sensorEditSuccess": "Modificación exitosa del sensor", "sensorEditBegin": "Comienza la modificación del sensor", "sensorDelSuccess": "El sensor fue eliminado con éxito.", "cleanSensorData": "Vaciar los datos del sensor", "getSensor": "Obtener sensores", "channelType": "Tipo de canal", "channelName": "Nombre del canal", "channelInfoSaveSuccess": "La información del canal se salvó con éxito", "channelInfoSaveBegin": "Comienza la conservación de la información del canal del sensor", "bias": "Sesgo [db]", "waveFilter": "Configuración de la banda de frecuencia", "gainMode": "Modo de ganancia", "gain_unit": "Ganancia [db]", "samCycle": "Número de ciclos de muestreo", "samPointNum": "Puntos de muestreo por ciclo", "samRate": "Puntos de muestreo por ciclo", "ratio": "Relación de cambio", "channelPhase": "Separación", "mecLoopCurrentThred": "Umbral de corriente de la bobina [ma]", "mecMotorCurrentThred": "Umbral de corriente del motor [ma]", "mecSwitchState": "Estado inicial de la cantidad de conmutación", "awaysOpen": "A menudo abierto", "awaysClose": "A menudo cerrado", "mecBreakerType": "Configuración del mecanismo del disyuntor", "oneMech": "Enlace mecánico de tres fases (un mecanismo)", "threeMech": "Enlace eléctrico de tres fases (tres instituciones)", "mecMotorFunctionType": "Tipo de trabajo del motor", "threePhaseAsynMotor": "Motor de inducción de tres fases", "onePhaseMotor": "Motores monocruzados / DC", "setCurrent": "<PERSON><PERSON><PERSON> el <PERSON>", "setAll": "Aplicado al mismo sensor", "showCoil": "Mapa de corriente de la bobina", "showSwitch": "Mapa de la cantidad de conmutación", "showMotor": "Mapa de corriente eléctrica del motor", "showOrig": "Mapa de corriente original", "actionDate": "Fecha de acción", "actionTime": "Tiempo de acción", "phaseA": "Fase A", "phaseB": "Fase B", "phaseC": "Fase C", "coil_charge_time": "Tiempo de encendido de la bobina", "coil_cutout_time": "Tiempo de corte de energía de la bobina", "max_current": "Corriente máxi<PERSON> de la bobina", "hit_time": "Tiempo de desconexión del botón", "subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar", "a_close_time": "Tiempo de cierre de la fase a", "a_close_coil_charge_time": "Tiempo de encendido de la bobina de fase a", "a_close_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase a", "a_close_max_current": "Corriente máxima de la bobina de la puerta de fase a", "a_close_hit_time": "Tiempo de desconexión de la puerta de coincidencia a", "a_close_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase a", "b_close_time": "Tiempo de cierre de la fase B", "b_close_coil_charge_time": "Tiempo de encendido de la bobina de fase B", "b_close_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase B", "b_close_max_current": "Corriente máxima de la bobina de cierre de fase B", "b_close_hit_time": "Tiempo de desconexión del interruptor de fase B", "b_close_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase B", "c_close_time": "Tiempo de cierre de la fase C", "c_close_coil_charge_time": "Tiempo de encendido de la bobina de fase C", "c_close_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase C", "c_close_max_current": "Corriente máxima de la bobina de la puerta de fase C", "c_close_hit_time": "Tiempo de desconexión de la puerta de coincidencia C", "c_close_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase C", "close_sync": "Sincronización del cierre", "close_time": "Tiempo de cierre", "a_open_time": "Tiempo de apertura de la fase a", "a_open_coil_charge_time": "Tiempo de encendido de la bobina de fase a", "a_open_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase a", "a_open_max_current": "Corriente máxima de la bobina de apertura de fase a", "a_open_hit_time": "Tiempo de desconexión de la puerta de separación de fase a", "a_open_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase a", "b_open_time": "Tiempo de apertura de la fase B", "b_open_coil_charge_time": "Tiempo de encendido de la bobina de fase B", "b_open_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase B", "b_open_max_current": "Corriente máxima de la bobina de separación de fase B", "b_open_hit_time": "Tiempo de desconexión de la puerta de separación de fase B", "b_open_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase B", "c_open_time": "Tiempo de apertura de la fase C", "c_open_coil_charge_time": "Tiempo de encendido de la bobina de fase C", "c_open_coil_cutout_time": "Tiempo de corte de energía de la bobina de fase C", "c_open_max_current": "Corriente máxima de la bobina de separación de fase C", "c_open_hit_time": "Tiempo de desconexión de la puerta de separación de fase C", "c_open_subswitch_close_time": "Tiempo de conmutación del interruptor auxiliar de fase C", "open_sync": "Sincronización de la apertura de puertas", "open_time": "Tiempo de apertura", "a_twice_open_time": "Tiempo de apertura secundaria de la fase a", "a_twice_open_coil_charge_time": "Momento en que la bobina de apertura secundaria de la fase a está cargada", "a_twice_open_coil_cutout_time": "Momento de corte de energía de la bobina de apertura secundaria de la fase a", "a_twice_open_max_current": "Corriente máxima de la bobina de apertura secundaria de fase a", "a_twice_open_hit_time": "Tiempo de desenganche de la Segunda División de la fase a", "a_twice_open_subswitch_close_time": "Momento de desconexión del interruptor auxiliar de apertura secundaria de la fase a", "b_twice_open_time": "Tiempo de apertura secundaria de la fase B", "b_twice_open_coil_cutout_time": "Momento de corte de energía de la bobina de apertura secundaria de la fase B", "b_twice_open_max_current": "Corriente máxima de la bobina de apertura secundaria de fase B", "b_twice_open_hit_time": "Tiempo de desenganche de la Segunda División de la fase B", "b_twice_open_subswitch_close_time": "Momento de desconexión del interruptor auxiliar de apertura secundaria de la fase B", "c_twice_open_time": "Tiempo de apertura secundaria de la fase C", "c_twice_open_coil_cutout_time": "Momento de corte de energía de la bobina de apertura secundaria de fase C", "c_twice_open_max_current": "Corriente máxima de la bobina de apertura secundaria de fase C", "c_twice_open_hit_time": "Tiempo de desenganche de la Segunda División de la fase C", "c_twice_open_subswitch_close_time": "Momento de desconexión del interruptor auxiliar de apertura secundaria de fase C", "twice_open_sync": "Sincronización de la apertura secundaria", "twice_open_time_text": "Tiempo de apertura secundaria", "a_switch_shot_time": "El oro de la fase a es corto", "b_switch_shot_time": "Fase B oro corto tiempo", "c_switch_shot_time": "Fase C oro corto tiempo", "a_switch_no_current_time": "Tiempo sin corriente en la fase a", "b_switch_no_current_time": "Tiempo sin corriente en la fase B", "c_switch_no_current_time": "Tiempo sin corriente en la fase C", "motor_start_current": "Corriente de arranque del motor", "motor_max_current": "Corriente máxima del motor", "storage_time": "Tiempo de almacenamiento de energía del motor", "Chan_A_motor_start_current": "Corriente de arranque del motor de fase a", "Chan_A_motor_max_current": "Corriente máxima del motor de fase a", "Chan_A_storage_time": "Tiempo de almacenamiento de energía del motor de fase a", "Chan_B_motor_start_current": "Corriente de arranque del motor de fase B", "Chan_B_motor_max_current": "Corriente máxima del motor de fase B", "Chan_B_storage_time": "Tiempo de almacenamiento de energía del motor de fase B", "Chan_C_motor_start_current": "Corriente de arranque del motor de fase C", "Chan_C_motor_max_current": "Corriente máxima del motor de fase C", "Chan_C_storage_time": "Tiempo de almacenamiento de energía del motor de fase C", "serialPort": "Puerto serie", "baudRate": "<PERSON><PERSON>", "dataBit": "Bits de datos", "stopBit": "Posición de parada", "checkBit": "Bit de verificación", "singleCollect": "Recolección por lotes", "sampling": "Se está recogiendo...", "serverIP": "IP del servidor", "serverPort": "Puerto del servidor", "NTPserverIp": "IP del servidor NTP", "NTPserverPort": "Puerto del servidor NTP", "netType": "Tipo de red", "deviceIp": "IP del host", "subnetMask": "Máscara de subred", "gateway": "<PERSON><PERSON><PERSON> predeterminada", "networkAPN": "Acceso a la red APN", "userName": "Nombre de usuario", "passWord": "Contraseña", "deviceWorkGroup": "Número de grupo de trabajo del host", "frequency": "Frecuencia de la red eléctrica", "pdSampleInterval": "PD - intervalo de muestreo", "spaceSecond": "& nbsp; Segundos", "meuSampleInterval": "Meu - intervalo de muestreo", "monitorAwakeTime": "Intervalo de hibernación", "monitorSleepSpace": "Intervalo de despertar", "wakeStartTime": "Hora de despertar inicial (punto entero)", "intervalServer": "Intervalo de carga (servidor)", "intervalMinus": "Intervalo de muestreo", "continuousAcquisitionTime": "Tiempo de recolección continuo", "startSampleTime": "Momento de muestreo inicial del host", "spacePoint": "& nbsp; Punto", "startSampleInterval": "Intervalo de muestreo inicial del host", "hour": "<PERSON><PERSON>", "poerOffSpace": "Intervalo de apagado", "powerOnSpace": "Intervalo de arranque", "dateRange": "<PERSON><PERSON>", "synchronizing": "Sincronizando...", "synchronize": "Sincronizar", "amplitude": "<PERSON><PERSON><PERSON><PERSON>", "switch": "Cambiar", "copyRight": "© 2020 pmdt Ltd.", "S1010Name": "Host de adquisición de datos", "modbusCompanySelf": "Huacheng", "logOut": "Salida", "logOutTitle": "Confirmación de la salida", "logOutConfirm": "¿Salir del inicio de sesión del usuario actual?", "logOutTips": "Se ha retirado del inicio de sesión", "enterHost": "Introduzca el nombre del host", "selectDevTip": "Por favor, seleccione el dispositivo", "stopSyncDataTip": "¿¿ es necesario detener la sincronización de datos?", "modbusAddress": "Dirección modbus", "modbusAddressCheckTip": "El rango de direcciones de modbus es de 1 a 254 enteros", "deviceWorkGroupCheckTip": "El número del Grupo de trabajo del host es un número entero, rango: [... (1) + (... (2))", "emptyMonitorDatas": "Vaciar los datos del host", "emptyMonitorDatasTip": "<PERSON>aciar todos los datos históricos en el host de recolección", "emptyMonitorDatasSuccess": "Datos vaciados con éxito", "emptyMonitorDatasApply": "Se ha presentado la solicitud de vaciado de datos, pendiente de revisión", "resetSoftSet": "Restaurar la configuración de fábrica", "resetSoftSetTip": "Restaurar todas las configuraciones en el host de recolección al Estado de fábrica", "resetSoftSetSuccess": "Se ha presentado la solicitud de reanudación de la configuración de fábrica y está pendiente de revisión.", "continueConfirmTip": "¿La operación no se puede restaurar, ¿ hay que continuar?", "bias_data": "El rango de valores de sesgo es de - 100 - 100", "back_ground_data": "El rango de valores de fondo es de 0 - 100", "sam_point": "El rango de frecuencia de muestreo por ciclo es de 20 - 2000", "sam_rate": "El rango de frecuencia de muestreo por ciclo es de 20 - 2000", "smartSensorStatus": "Tabla de Estado", "upThreshold": "Límite superior del umbral", "lowerThreshold": "Límite inferior del umbral", "changeThreshold": "Umbral de cambio", "changeThresholdLimit": "La configuración del umbral solo admite un dígito", "upThresholdTip": "Rango superior del umbral", "lowerThresholdTip": "Rango inferior del umbral", "changeThresholdTip": "Rango umbral de cambio", "upLowerThresholderrTip": "El límite inferior del umbral no puede ser mayor que el límite superior del umbral.", "monitorName": "Nombre del host", "monitorType": "<PERSON><PERSON><PERSON> de <PERSON>", "MonitorTypeHanging": "Colgant<PERSON> de pared", "MonitorType2U": "2u", "MonitorTypeCollectionNode": "Nodo de reunión", "MonitorTypeLowPower": "Host solar de bajo consumo de energía", "devicePMSCode": "Codificación del equipo", "forceChange": "Cambio de modo", "forceChangeSuccess": "Cambio de modo exitoso", "currentVersion": "Versión actual", "abnormalRecover": "Autorescate anormal", "fromLabel": "Hora de inicio", "toLabel": "Fin del tiempo", "select": "Selección", "sunday": "Día", "monday": "Uno", "tuesday": "<PERSON><PERSON>", "wednesday": "Tres", "thursday": "Cuatro", "friday": "Cinco", "saturday": "<PERSON><PERSON>", "January": "<PERSON><PERSON>", "February": "<PERSON><PERSON><PERSON>", "March": "<PERSON><PERSON>", "April": "Abril", "May": "Mayo", "June": "<PERSON><PERSON>", "July": "<PERSON>", "August": "Agosto", "September": "Septiembre", "October": "Octubre", "November": "Noviembre", "December": "Diciembre", "all": "Todo", "strong": "<PERSON><PERSON>e", "weak": "<PERSON><PERSON><PERSON>", "poor": "Pobre", "min": "Punt<PERSON>", "second": "<PERSON><PERSON><PERSON>", "uploadInterval": "Intervalo de carga (min)", "loginWelcome": "Bienvenido a iniciar sesión", "dateStartIsAfterDateEnd": "La fecha de inicio es mayor que la fecha de fin, por favor vuelva a seleccionar", "maxCurrent": "Co<PERSON>nte <PERSON>", "standMobus": "Fabricante de sensores", "config1": "Configuración 1", "config2": "Configuración 2", "fWrnThreshold": "Umbral de atención", "fAlmThreshold": "Umbral de alarma", "regularDormancy": "Hibernación regular", "noDormancy": "Sin hibernación", "soakingWater": "Inmersión", "dry": "Secado", "normal": "Normal", "alarm": "Alarma", "lightGunCamera": "Pistola de luz visible", "lightBallCamera": "Máquina de bola de luz visible", "infraredGunCamera": "Pistola binocular infrarroja", "infraredBallCamera": "Máquina de bola binocular infrarroja", "maxTemp": "Temperatura máxima", "maxTempPosition": "Posición de temperatura máxima", "devIPError": "La IP ha sido ocupada", "unknown": "Desconocido", "fault": "Aver<PERSON>", "lowPower": "Baja electricidad", "mediumPower": "Electricidad media", "highPower": "Alta electricidad", "slaveid": "ID de la máquina", "LoraFrequency": "<PERSON><PERSON>", "AREA_CHINA": "China < 1]", "AREA_VIETNAM": "Vietnam (as1)", "AREA_MALAYSIA": "Malasia (as1)", "AREA_EUROPE": "Europa", "AREA_US": "América", "AREA_INDONESIA": "Indonesia (as2)", "AREA_INDIA": "India", "AREA_KOREA": "Corea del Sur", "AREA_CHINA_RSV": "China (de repuesto) < 2]", "getLogFileError": "Falló la obtención del archivo de registro del sensor", "exportLogError": "El archivo de registro no existe y la exportación falló", "alertSyncProgress": "Progreso de la actualización simultánea de los datos de alarma: @ 1..", "alertSyncProgress2": "Progreso de la actualización síncrona de los datos de alarma [ (...) 1]", "FIRMWARE_EXCEPTION": "Firmware anormal", "AD_INITIALIZATION_EXCEPTION": "Excepción de iniciación ad", "REFERENCE_VOLTAGE_EXCEPTION": "Tensión de referencia anormal", "ONCHIP_FLASH_EXCEPTION": "Flash anormal en la película", "OFFCHIP_FLASH_EXCEPTION": "Flash fuera de la película anormal", "SYSTEM_PARAMETERS_EXCEPTION": "Parámetros anormales del sistema", "SAMPLE_PARAMETERS_EXCEPTION": "Parámetros de adquisición anormales", "CALIBRATION_PARAMETERS_EXCEPTION": "Parámetros de calibración anormales", "SYSTEM_PARAMETERS_EXCEPTION_RECOVER": "Recuperación anormal de parámetros del sistema", "SAMPLE_PARAMETERS_EXCEPTION_RECOVER": "Recuperación anormal de los parámetros de adquisición", "CALIBRATION_PARAMETERS_EXCEPTION_RECOVER": "Recuperación anormal de parámetros de calibración", "LORA_MODULE_EXCEPTION": "<PERSON><PERSON><PERSON><PERSON> anormal", "PHASE_NUM": "Número de fases", "DISCONNECT_TIME": "Tiempo de desconexión", "DATA_TOTAL_COUNT": "Datos totales", "ONLINE_RATE": "Tasa en línea", "aduInfoSetProgress": "Configuración del sensor: ',' 1 '(', '2')", "aduInfoSetProgress2": "Configuración del sensor [ (',' 1 ') (', '2')", "aduInfoSetting": "Configurar sensores...", "aduInfoSetEnd": "Se completa la configuración del sensor", "aduDataSample": "Adquisición de datos", "aduInfoSet": "Configuración del sensor", "errorDataSampleRunning": "Actualmente se está llevando a cabo el negocio de adquisición de sensores, por favor vuelva a intentarlo después de completarlo.", "errorAduInfoSetRunning": "Actualmente se está llevando a cabo el negocio de configuración de parámetros del sensor, por favor vuelva a intentarlo después de completarlo.", "SAMPLE_PERIOD": "Número de semanas de muestreo", "SF6_Density": "Densidad de gas SF6 (p20)", "SF6_Temp": "Temperatura del gas SF6", "Device_env_Temp": "Temperatura ambiente del equipo", "Alarm_Status": "Identificación del Estado de alarma", "SF6_Alarm_Low_Pressure": "Alarma de baja tensión", "SF6_Alarm_Low_Voltage_Block": "Bloqueo de baja presión", "SF6_Alarm_Over_Voltage": "Alarma de sobrepresión", "AlarmType": "Tipo de alarma", "AlarmData": "Contenido de la alarma", "AlarmTime": "Tiempo de alarma", "AlarmLevel": "<PERSON><PERSON> de <PERSON>", "AlarmStateWarning": "<PERSON><PERSON>a temprana", "WarningValue": "Valor de alerta temprana", "AlarmValue": "Valor de alarma", "SetSuccess": "Configuración exitosa", "AlarmDate": "Tiempo de alarma", "AlarmConfirm": "Confirmación de alarma", "Confirm": "Confirmación", "Confirmed": "<PERSON><PERSON><PERSON><PERSON>", "AllData": "Todos los datos", "CheckManagerSponsor": "Iniciador", "CheckManagerCheckType": "Tipo de auditoría", "CheckManagerDate": "<PERSON><PERSON>", "CheckManagerTarget": "Iniciador", "CheckManagerExtraInfo": "Información adicional", "CheckManagerResult": "Si está de acuerdo o no", "Refuse": "<PERSON><PERSON><PERSON>", "Agree": "De acuerdo", "DataExport": "Exportación de datos", "DataExportStep1": "Seleccione los datos a exportar", "DataExportStep1Title": "Selección de archivos de exportación", "DataExportStep2": "Calcular el tamaño de los datos...", "DataExportStep2Title": "Calcular el tamaño de los datos originales", "DataExportStep3": "Empaquetando datos comprimidos...", "DataExportStep3Title": "Empaquetar datos comprimidos", "DataExportStep4": "Se completa la compresión de datos", "DataExportStep4Title": "Se completan los datos comprimidos", "DataExportCheckData": "Base de datos [ / media / data / base de datos]", "DataExportCheckDataFile": "Archivo de datos [media / data / datafile]", "DataExportCheckLog": "Archivo de registro [ / media / data / log]", "DataExportCheckConfig": "Archivo de configuración [casa / raíz / config.xl]", "DataExportBegin": "Comienza la exportación", "SelectAtLeastOneTips": "Elegir al menos uno", "DataExportFileSizeError": "El tamaño del archivo supera los 2000mb, por favor use herramientas como winscp para exportar datos", "DataExportFileSizeZeroError": "El tamaño del archivo seleccionado es 0", "DataExportCancelTips": "Datos de exportación cancelados", "DataExportDataCompressTips": "El tamaño de los datos brutos es de (...) 1) mb, y el tiempo de compresión estimado (...) Min (...) 3) s]", "LogExportStep1": "Sensor de selección", "LogExportStep1Title": "Sensor de selección", "LogExportStep2": "Obtener el archivo de registro...", "LogExportStep2Title": "Obtener el archivo de registro", "LogExportStep3": "Se completa la obtención del archivo de registro", "LogExportStep3Title": "Se completa la obtención del archivo de registro", "LogExportStep4": "Empaquetar archivos de registro comprimidos...", "LogExportStep4Title": "Empaquetar archivos de registro comprimidos", "LogExportStep5": "Compresión completada", "LogExportStep5Title": "Compresión completada", "LogExportCancelTips": "Se cancela el registro de exportación", "batteryVoltage": "Tensión de la batería", "superCapvoltage": "Voltaje supercapacitor", "OverPressureThreshold": "Umbral de sobrepresión", "LowPressureThreshold": "Umbral de baja presión", "ShutThreshold": "Umbral de bloqueo", "PhysicalChannelType": "Tipo de canal físico", "GasAbsPressure": "Presión absoluta del gas", "GasGaugePressure": "Presión del medidor de gas", "CameraType": "Tipo <PERSON>", "DEFAULT_CHECK_Tips": "El formato de numeración es m: n, como 1: 100, de los cuales el rango M [1 - 255], el rango N [0 - 65535]", "VibrationSY_OUT_CHECK_Tips": "El formato numerado es dirección: modbus: id, como 1: 0: 1917, de los cuales el rango de dirección es [1 - 255] y modbus / id son [0 - 65535]", "NOISEWS_OUT_CHECK_Tips": "El formato numerado es ip: x: y, como *********: 1321: 4512, de los cuales el formato IP [xxx.xxx.xxx.xxx] y X / y son cadenas con una longitud de 4", "IR_DETECTION_OUT_IO_CHECK_Tips": "El formato numerado es ip: port: ss: se: cs: ce, como 1: 100, en el que el formato IP [xxx.xxx.xxx.xxx] rango de Puerto [0 - 65535] rango ss [0 - 255], se rango 0 - 255], CS rango 0 - 255], CE rango 0 - 255]", "IR_DETECTION_OUT_MODBUS_TCP_CHECK_Tips": "El formato numerado es ip: port: x: y, como 1: 100, en el que el formato IP [xxx.xxx.xxx.xxx] rango de puerto puerto [0 - 65535] rango X [1 - 255], rango y [0 - 65535]", "IR_DETECTION_OUT_LORA_485_CHECK_Tips": "El formato de numeración es m: n, como 1: 100, de los cuales el rango M [1 - 255], el rango N [0 - 65535]", "fWrnThreshold_CHECK_Tips": "Tenga en cuenta que el rango umbral es [10 - 1000] ma", "fAlmThreshold_CHECK_1_Tips": "El rango umbral de alarma es [50 - 5000] ma", "fAlmThreshold_CHECK_2_Tips": "El umbral de alerta debe ser mayor que el umbral de atención.", "AuditContent": "Contenido de la auditoría", "OPTime": "Tiempo de operación", "Executed": "Ejecución", "UserManager": "Gestión de usuarios", "SystemManagement": "Gestión del sistema", "BusinessManagement": "Gestión Empresarial", "LanguageChange": "Cambio de lenguaje", "AlarmManagement": "Gestión de alarmas", "DataOperation": "Operación de datos", "BackupRecovery": "Recuperación de respaldo", "AddUser": "<PERSON><PERSON><PERSON>ua<PERSON>", "AddUserCheck": "Añadir auditoría de usuario", "UserRightSet": "Configuración de permisos de usuario", "UserRightSetCheck": "Revisión de la configuración de los derechos del usuario", "DeleteUser": "Eliminar usuarios", "DeleteUserConfirm": "Eliminar la confirmación del usuario", "FreezeUser": "<PERSON><PERSON><PERSON> usuarios", "FreezeUserConfirm": "Congelar la confirmación del usuario", "UnlockUser": "Desbloquear usuarios", "UnlockUserConfirm": "Desbloquear la confirmación del usuario", "FileStationSet": "Configuración del archivo (sitio)", "FileDeviceSet": "Configuración de archivos (equipos)", "SenserSet": "Configuración del sensor", "AlarmSet": "Configuración de la alarma", "SavePointSet": "Guardar la configuración del punto de medición", "DelPointSet": "Eliminar la configuración del punto de medición", "SingleSample": "Recolección única", "DataBackup": "Copia de Seguridad de datos", "DataRecovery": "Recuperación de datos", "ClearDataCheck": "Auditoría de datos vaciados", "RestoreFactorySettingsReview": "Reanudar la auditoría de la configuración de fábrica", "SaveMainStation": "Guardar la estación principal", "DataView": "Vista de datos", "DataSync": "Sincronización de datos", "RightSet": "Configuración de permisos", "GetUserList": "Obtener la lista de usuarios", "AuditData": "Datos de auditoría", "AuditPermissions": "Autoridad de auditoría", "MainStationSet": "Configuración de la estación principal", "ChangePasswordSelfOnly": "Cambiar contraseña (solo para este usuario)", "ChangePasswordForNormal": "Modificar la contraseña del usuario ordinario", "ChangeUserRight": "Modificar / establecer permisos de usuario", "ChangePassword": "Modificar la contraseña", "ChangeLoginInfo": "Modificar la información de inicio de sesión", "UserStatus": "Estado del usuario", "UserLanguage": "Idioma del usuario", "HasLogin": "¿¿ ha aterrizado?", "RoleType": "<PERSON><PERSON><PERSON> de personaje", "IsPassTimeout": "Si la contraseña se excede", "AuditsManagement": "Gestión de auditoría", "SensorOperation": "Operación del sensor", "SensorLogExport": "Exportación de registros de sensores", "SensorAlarmDataSync": "Sincronización de datos de alarma de sensores", "ViewSensorAlarmData": "Visualización de datos de alarma del sensor", "Block": "Congelación", "BlockPendingReview": "Congelación - pendiente de revisión", "UnlockPendingReview": "Descongelación - pendiente de revisión", "DeletePendingReview": "Eliminación - pendiente de revisión", "AddUserPendingReview": "Nuevo - por revisar", "ChangePassPendingReview": "Modificar la contraseña - pendiente de revisión", "ChangeRightPendingReview": "Autoridad de modificación - pendiente de revisión", "abnormal": "Anomalías", "DataQueryNotSupported": "Por el momento, no se admite la consulta de datos históricos de este tipo frontal.", "DeviceCodeExists": "El nombre del dispositivo o el Código del dispositivo ya existe", "OutputSwitchConfig": "Configuración del interruptor de salida", "OutputSwitch": "Conectar el interruptor", "MainStationAuxRtuID": "Control auxiliar rtuid", "MainStationIedRtuID": "<PERSON><PERSON><PERSON>", "MainstationParams1": "Parámetros de la estación principal 1", "MainstationParams2": "Parámetros de la estación principal 2", "MainstationInterface1": "Interfaz de la estación principal 1", "MainstationInterface2": "Interfaz de la estación principal 2", "Port": "Puerto", "IPAddress": "Dirección IP", "EnterUriTips": "Por favor, introduzca la ruta Uri", "ConnectUserName": "Nombre de usuario de la comunicación", "EnterConnectUserNameTips": "Introduzca el nombre de usuario de la comunicación", "ConnectPass": "Contraseña de comunicación", "EnterConnectPassTips": "Introduzca la contraseña de comunicación", "DataSubmissionInterval": "Intervalo de entrega de datos", "EnderDataSBIntervalTips": "Por favor, introduzca el intervalo de envío de datos", "HeartbeatInterval": "Intervalo de latidos cardíacos", "EnterHertbeatIntervalTips": "Introduzca el intervalo del latido del corazón", "ConnectStatus": "Estado de conexión", "Reset": "Restablecer", "Submit": "Presentación", "RtuidRepeatTips": "El control secundario rtuid no puede ser el mismo que el conjunto rtuid", "DataSubmissionIntervalTips": "El intervalo de entrega de datos no puede ser inferior a 60", "HeartbeatIntervalTips": "El intervalo cardíaco no puede ser inferior a 60", "MainstationParamsSaveSuccess": "Los parámetros de la estación principal se guardaron con éxito.", "MainstationParamsSaveFailed": "Falló la conservación de los parámetros de la estación principal", "OutSwitchSaveSuccess": "La configuración del interruptor de conexión se salvó con éxito.", "OutSwitchSaveFailed": "Falló el ahorro de la configuración del interruptor de conexión", "ChartConfig": "Configuración del mapa", "Measurement": "Medición", "SENSOR_TIMEOUT_COUNT": "Conteo de Determinación de Sensor Fuera de Línea", "OUTWARD_CONFIG_ENABLE_STATE": "Estado de configuración", "OUTWARD_CONFIG_ENABLED": "Habilitado", "OUTWARD_CONFIG_DISABLED": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OUTWARD_CONFIG_ENABLING": "Habilitando", "SENSOR_TIMEOUT_DISABLING": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ERROR_NO_CONFIG": "La configuración no existe", "ERROR_INPUT_PARAMS": "Error en los parámetros de entrada", "ERROR_INTEGER": "Por favor, ingrese un número entero", "ERROR_TIP_RANGE": "<PERSON><PERSON> de valo<PERSON> [{1}~{2}]", "ERROR_IP_FORMAT": "El formato de IP es incorrecto", "ERROR_FILE_NAME": "El nombre del archivo debe ser {1}", "ERROR_FILE_SUFFIX": "La extensión del archivo debe ser {1}", "ERROR_FILE_SIZE_LESS_THAN_MB": "El archivo debe ser menor que {1} MB", "ERROR_T2_T1": "T2 debe ser menor que T1", "LENGTH_COMM_LIMIT": "Tenga en cuenta que este campo no puede exceder {1} caracteres.", "MANU_FAST_SYNC_DATA": "Sincronización rápida", "SYNC_DATA_STATE_LIST": "Lista de estado de sincronización de sensores", "LAST_SYNC_DATE_RANGE": "Rango de fecha de la última sincronización", "NoError": "Sin error", "SYNC_DATA_ERROR_SYNC_TIME_OUT": "Tiempo de espera de sincronización", "SYNC_DATA_ERROR_DISCONNECT": "Desconexión", "SYNC_DATA_STATUS_WAITING": "<PERSON><PERSON><PERSON><PERSON>", "SYNC_DATA_STATUS_CONNECTED": "Conectado", "SYNC_DATA_STATUS_SYNCING": "Sincronizando", "SYNC_DATA_STATUS_SUCCESS": "Sincronización exitosa", "SYNC_DATA_STATUS_FAILED": "Sincronización fallida", "SYNC_DATA_STATUS_CANCELED": "Sincronización cancelada", "Today": "Hoy", "Yesterday": "Ayer", "LAST_7_DAYS": "Últimos 7 días", "LAST_30_DAYS": "Últimos 30 días", "THIS_MONTH": "<PERSON>ste mes", "LAST_MONTH": "<PERSON><PERSON> pasado", "SYNC_DATA_TASK_STATUS_START_COMMUNICAT_SERVICE_FAILED": "Error al iniciar el servicio de comunicación", "FastSyncDataCancelTips": "¿Está seguro de cancelar la sincronización de datos?", "FastSyncDataSelectTips": "Por favor, seleccione los sensores para la sincronización de datos", "Band-pass": "Paso de banda", "aeWaveChartTitle": "Espectro de forma de onda AE", "aePhaseChartTitle": "Espectro de fase AE", "aeFlyChartTitle": "Espectro de vuelo AE", "LOCAL_PARAMS_TIME": "Tiempo", "LOCAL_CHART_COLORDENSITY": "Densidad de color", "openTime": "Tiempo de apertura", "closeTime": "Tiempo de cierre", "triggerUnit": "Amplitud de disparo[μV]", "openTimeUnit": "Tiempo de apertura[μs]", "closeTimeUnit": "Tiempo de cierre[μs]", "triggerUnitTips": "Rango de amplitud de activación: [[1], [2]] μv", "TOP_DISCHARGE_AMPLITUDE": "Amplitud de descarga TOP3", "WS_901_ERROR_TIPS": "El servicio de envío de comandos se está registrando, por favor intente de nuevo después de 2 segundos"}