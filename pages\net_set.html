<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <br>
        <form class="form-horizontal">
            <!-- <div class="form-group">
                <label for="serverIp" class="col-sm-3 control-label" data-i18n="serverIP">服务器IP</label>
                <div class="col-sm-8">
                    <input id="serverIp" vk="true" type="text" class="col-sm-4 form-control"
                        data-i18n-placeholder="pleaseInput">
                </div>
            </div>
            <div class="form-group">
                <label for="serverPort" class="col-sm-3 control-label" data-i18n="serverPort">服务器端口</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <button id="port-minus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-minus"></i>
                            </button>
                        </span>
                        <input id="serverPort" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput">
                        <span class="input-group-btn">
                            <button id="port-plus" type="button" class="btn btn-default btn-flat">
                                <i class="fa fa-plus"></i>
                            </button>
                        </span>
                    </div>
                </div>
            </div> -->
            <div class="form-group">
                <label for="networkName" class="col-sm-3 control-label" data-i18n="netType">网络类型</label>
                <div class="col-sm-8">
                    <select id="networkName" class="form-control select2" data-i18n-placeholder="selectPlease"
                        style="width: 100%;"></select>
                </div>
            </div>
            <div class="wired-network">
                <div class="form-group">
                    <label for="deviceIp" class="col-sm-3 control-label" data-i18n="deviceIp">主机IP</label>
                    <div class="col-sm-8">
                        <input id="deviceIp" vk="true" type="text" class="col-sm-4 form-control"
                            data-i18n-placeholder="pleaseInput">
                    </div>
                </div>
                <!--<div class="form-group">-->
                <!--<label for="subnetMask" class="col-sm-3 control-label" data-i18n="subnetMask">子网掩码</label>-->
                <!--<div class="col-sm-8">-->
                <!--<input id="subnetMask" vk="true" type="text" class="col-sm-4 form-control"-->
                <!--data-i18n-placeholder="pleaseInput">-->
                <!--</div>-->
                <!--</div>-->
                <!--<div class="form-group">-->
                <!--<label for="gateway" class="col-sm-3 control-label" data-i18n="gateway">默认网关</label>-->
                <!--<div class="col-sm-8">-->
                <!--<input id="gateway" vk="true" type="text" class="col-sm-4 form-control"-->
                <!--data-i18n-placeholder="pleaseInput">-->
                <!--</div>-->
                <!--</div>-->
            </div>
            <div class="cellular-network hide">
                <div class="form-group">
                    <label for="networkAPN" class="col-sm-3 control-label" data-i18n="networkAPN">网络接入APN</label>
                    <div class="col-sm-8">
                      <div style="position: absolute;
                      width: 100%;
                      padding-right: 30px;"><select id="networkAPNSelect" class="form-control select2" data-placeholder="Please select"
                        style="width: 100%;"></select></div>
                      <input id="networkAPN" maxlength='124' vk="true" type="text" class="col-sm-4 form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label for="userName" class="col-sm-3 control-label" data-i18n="userName">用户名</label>
                    <div class="col-sm-8">
                        <input id="userName" maxlength='124' vk="true" type="text" class="col-sm-4 form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label for="password" class="col-sm-3 control-label" data-i18n="passWord">密码</label>
                    <div class="col-sm-8">
                        <input id="password" maxlength='124' vk="true" type="text" class="col-sm-4 form-control">
                    </div>
                </div>
                <!--<div class="form-group hide">-->
                <!--<label for="password" class="col-sm-3 control-label" data-i18n="abnormalRecover">异常自恢复</label>-->
                <!--<div class="col-sm-8">-->
                <!--<select id="GPRSAutoReset" class="form-control select2" data-placeholder="Please select"-->
                <!--style="width: 100%;">-->
                <!--<option value="1" selected="selected" data-i18n="On">-->
                <!--开-->
                <!--</option>-->
                <!--<option value="0" data-i18n="Off">-->
                <!--关-->
                <!--</option>-->
                <!--</select>-->
                <!--</div>-->
                <!--</div>-->
            </div>
        </form>
        <div class="content-footer">
            <button type="button" id="network-save" class="btn btn-block btn-primary"
                style="width: 200px; margin: 10px 60px 10px auto" data-i18n="save">保存</button>
        </div>
        <br />
    </div>
    <!-- /.box -->
</div>

<!-- /.row -->
<script>
    initPageI18n();
    getNetworkConfig();
    //@ sourceURL=net_set.js
    //获取网络参数信息
    checkNeedKeyboard();
    $('.select2').select2();

    $("#networkAPNSelect").select2({
        tags: true
    });

    $('#networkAPN').click(function(){
      $('#networkAPNSelect').select2('open');
    })
    $('#networkAPN').on('input',function(){
      $('#networkAPNSelect').val('').select2();
      $('#networkAPNSelect').select2('close');
      $('#networkAPN').focus();
    })
    $('#networkAPNSelect').on('change',function(){
      $('#networkAPN').val($('#networkAPNSelect').val())
    })
    /*
     * 获取网络参数信息
     * -----------------------
     */
    function getNetworkConfig() {
        var data = null;
        poll('GET', 'GetNetworkConfig', data, function (text) {
            var respData = text.result;
            for (var item in respData) {
                var key = item;
                if(key === 'networkAPN'){
                   key = 'networkAPNSelect'
                };  
                $("#" + key).empty();
                //PMDT版本暂不使用后台的网络列表
                /* if (item === 'networkAPN') {
                    continue; 
                } */
                var data = respData[item];
                if (data.length > 0) {
                    data.forEach(function (value, index) {
                        var option = $("<option />", {
                            "value": "" + value + ""
                        }).append(key==='networkAPNSelect'?value:getI18nName(value));
                        $("#" + key).append(option);
                    });
                }
            }
            getNetWorkInfo(respData.networkAPN);
        })
    }

    /*
     * 获取网络信息
     * -----------------------
     */
    function getNetWorkInfo(networkAPN) {
        var data = null;
        poll('GET', 'GetNetworkInfo', data, function (text) {
            var respData = text.result;
            /* if (respData.networkName != 'Ethernet' && respData.networkAPN.trim() != "") {
                var checkIsCustomAPN = true;
                //TODO 校验下拉列表逻辑暂时去除，同版本切换功能一同进行重构
                for (var i = 0; i < networkAPN.length; i++) {
                    if (networkAPN[i] == respData.networkAPN) {
                        checkIsCustomAPN = false;
                        break;
                    }
                }
                if (checkIsCustomAPN) {
                    var option = $("<option />", {
                        "value": "" + respData.networkAPN + ""
                    }).append(respData.networkAPN);
                    $("#networkAPNSelect").append(option);
                }
            } */
            for (var item in respData) {
                if (respData[item] === " ") {
                    respData[item] = "";
                }
                $("#" + item).val(respData[item]).trigger("change");
            }
            $('#networkAPNSelect').val('').select2();
            if (respData.ntpIP == undefined) {
                $('#ntpGroup').hide();
            }
        });
    }

    /*
     * 保存网络信息
     * -----------------------
     */
    function saveNetWorkInfo(formData) {
        poll('POST', 'SaveNetWorkInfo', formData, function (text) {
            $('.notifications').notify({
                message: {
                    text: getI18nName('saveSuccess')
                },
                type: "success",
            }).show();
        }, function (errorCode) {
            $('.notifications').notify({
                fadeOut: {
                    enabled: false
                },
                message: {
                    text: getI18nName('saveFailed') + convertErrorCode(errorCode)
                },
                type: "danger",
            }).show();
        });
    }

    //增减端口号
    $("#port-minus").click(function () {
        var value = $("#serverPort").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: 'Server port should be an integer between [1,65535].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#port-plus").removeClass("disabled");
                $("#serverPort").val(number - 1);
            } else {
                $("#serverPort").val(1);
                $("#port-minus").addClass("disabled");
            }
        }
    });

    $("#port-plus").click(function () {
        var value = $("#serverPort").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: 'Server port should be an integer between [1,65535].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 65535) {
                $("#port-minus").removeClass("disabled");
                $("#serverPort").val(number + 1);
            } else {
                $("#serverPort").val(65535);
                $("#port-plus").addClass("disabled");
            }
        }
    });

    //切换网络类型
    $("#networkName").change(function () {
        var selectText = $(this).children('option:selected').text();
        if (selectText == "Ethernet") {
            $(".wired-network").removeClass("hide");
            $(".cellular-network").addClass("hide");
        }
        if (selectText == "3G" || selectText == "4G") {
            $(".wired-network").addClass("hide");
            $(".cellular-network").removeClass("hide");
        }
    });

    //保存网络信息
    $("#network-save").click(function () {
        var check = true;
        var formData = {};
        // formData.serverIp = $("#serverIp").val();
        // formData.serverPort = $("#serverPort").val();
        formData.networkName = $("#networkName").val();
        formData.deviceIp = $("#deviceIp").val();
        formData.subnetMask = $("#subnetMask").val();
        formData.gateway = $("#gateway").val();
        formData.networkAPN = $("#networkAPN").val();
        //        formData.GPRSAutoReset = $('#GPRSAutoReset').val();
        formData.userName = $("#userName").val();
        formData.password = $("#password").val();
        /* if (validateStrLen(formData.serverIp) == 0) {
        var label = "label[for=serverIp]";
        var text = $("#network").find(label).text().toLowerCase();
        $('.notifications').notify({
        message: {
        text: getI18nName('enterServerUrlTips')// + text + '.'
        },
        type: "warning",
        }).show();
        check = false;
        } else {
        if (validateIP(formData.serverIp) == false || validateURL(formData.serverIp) == false) {
        $('.notifications').notify({
        message: {
        text: getI18nName('enterServerUrlError')
        },
        type: "warning",
        }).show();
        check = false;
        }
        }

        if (validateStrLen(formData.serverPort) == 0) {
        var label = "label[for=serverPort]";
        var text = $("#network").find(label).text().toLowerCase();
        $('.notifications').notify({
        message: {
        text: getI18nName('enterServerPortTips')// + text + '.'
        },
        type: "warning",
        }).show();
        check = false;
        } else {
        if (validateNumeric(formData.serverPort) == false || formData.serverPort < 1 || formData.serverPort> 65535) {
            $('.notifications').notify({
            message: {
            text: getI18nName('enterServerPortError')
            },
            type: "warning",
            }).show();
            check = false;
            }
            } */

        if (validateStrLen(formData.networkName) == 0) {
            var label = "label[for=networkName]";
            var text = $("#network").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: getI18nName('enterNetName') // + text + '.'
                },
                type: "warning",
            }).show();
            check = false;
        }


        if (formData.networkName == "Ethernet") {
            if (validateStrLen(formData.deviceIp) == 0) {
                var label = "label[for=deviceIp]";
                var text = $("#network").find(label).text().toLowerCase();
                $('.notifications').notify({
                    message: {
                        text: getI18nName('enterIP') // + text + '.'
                    },
                    type: "warning",
                }).show();
                check = false;
            } else {
                if (validateIP(formData.deviceIp) == false) {
                    $('.notifications').notify({
                        message: {
                            text: getI18nName('enterIPError')
                        },
                        type: "warning",
                    }).show();
                    check = false;
                }
            }
        }else{
          if (validateStrLen(formData.networkAPN) == 0) {
            var label = "label[for=networkAPN]";
            var text = $("#networkAPN").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: getI18nName('enterAPN') // + text + '.'
                },
                type: "warning",
            }).show();
            check = false;
          }
        }

        /*   if (formData.networkName == "Ethernet") {
               if (validateStrLen(formData.subnetMask) == 0) {
                   var label = "label[for=subnetMask]";
                   var text = $("#network").find(label).text().toLowerCase();
                   $('.notifications').notify({
                       message: {
                           text: getI18nName('enterNetMask')// + text + '.'
                       },
                       type: "warning",
                   }).show();
                   check = false;
               } else {
                   if (validateMask(formData.subnetMask) == false) {
                       $('.notifications').notify({
                           message: {
                               text: getI18nName('enterNetError')
                           },
                           type: "warning",
                       }).show();
                       check = false;
                   }
               }
           }

           if (formData.networkName == "Ethernet") {
               if (validateStrLen(formData.gateway) == 0) {
                   var label = "label[for=gateway]";
                   var text = $("#network").find(label).text().toLowerCase();
                   $('.notifications').notify({
                       message: {
                           text: getI18nName('enterGateWay') //+ text + '.'
                       },
                       type: "warning",
                   }).show();
                   check = false;
               } else {
                   if (validateIP(formData.gateway) == false) {
                       $('.notifications').notify({
                           message: {
                               text: getI18nName('enterGateWayError')
                           },
                           type: "warning",
                       }).show();
                       check = false;
                   }
               }
           }*/

        //配置暂定不再校验
        /*if (formData.networkName != "Ethernet") {
            if (validateStrLen(formData.networkAPN) == 0) {
                var label = "label[for=networkAPN]";
                var text = $("#networkAPN").find(label).text().toLowerCase();
                $('.notifications').notify({
                    message: {
                        text: getI18nName('enterAPN') //+ text + '.'
                    },
                    type: "warning",
                }).show();
                check = false;
            }
        }
        
        if (validateStrLen(formData.userName.trim()) == 0) {
            formData.userName = " ";
        }
        
        if (validateStrLen(formData.password.trim()) == 0) {
            formData.password = " ";
        }*/



        if (check) {
            saveNetWorkInfo(formData);
        }
    });
</script>