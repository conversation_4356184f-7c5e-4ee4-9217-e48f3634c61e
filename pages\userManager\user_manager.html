<style>
    .permission-item {
        margin: 10px;
        width: 150px;
        display: inline-flex;
    }

    .info-params-content {
        padding: 8px;
    }

    .info-params-label {
        width: 30%;
        text-align: right;
        padding: 5px;
    }

    .info-params-input {
        width: 50%;
        padding: 0 8px;
    }
</style>
<div class="col-sm-12 full-height" id="userManagerList" style="padding: 0px">
    <div class="row" style="height: 100%;" id="userListTableContent">
        <div id="userToolBar">
            <button id="testAdd" data-i18n="AddUser">增加用户</button>
        </div>
        <div class="form-group col-lg-12"
            style="height: 90%;border: 1px solid #b9b7b7;background-color: white;margin-bottom:0px;padding: 20px 50px 0 50px;">
            <table id="userListTable" style="width:100%;height:100%"></table>
        </div>
    </div>
</div>
<script>
    /*
     * 查询所有普通用户  接口名 getNormalUser
     *
     * 4-20位，英文+字母， 不许特殊字符
     *
     * 账户也要字母+数字组合
     * 4-20个字符
     * 添加用户 接口名 addNormalUser
     参数   userName  用户名
     password 密码
     *
     * 删除用户 deleteNormalUser
     *
     *

     *
     * {
     userName  用户名
     userState 用户状态
     language 用户语言
     firstLogin 是否登录过
     charaType 角色类型
     pasPast 密码是否超时
     }
     *
     * var GET_NORMAL_USER='getNormalUser';//用户管理-获取用户列表
     var ADD_NORMAL_USER='addNormalUser';//用户管理-新增用户
     var DELETE_NORMAL_USER='deleteNormalUser';//用户管理-删除用户
     * */
    initPageI18n();
    var customUserTableConfig = {
        id: 'userListTable',
        method: GET_NORMAL_USER_LIST,
        showColumns: false,
        //        useMocker: true,
        pageList: [8],
        pageSize: 8,
        height: $('#global').height() - 125,
        toolbar: '#userToolBar',
        responseHandler: function (res) {
            if (res.errorCode != 0) {
                if (res.errorCode == 901) {
                    logOut('repeatLogin');
                    return;
                } else {
                    $.notifyUtil.notifyWarning(convertErrorCode(res.errorCode));
                }
            }
            return res.result;
        },
        requestParam: function (params) {
            return {
                page: params.offset / params.limit + 1,
                size: params.limit,
            };
        },
        columns: [{
                field: 'userName',
                title: getI18nName('userName'),
            },
            {
                field: 'userState',
                title: getI18nName('UserStatus'),
                formatter: function (value, row, index) {
                    var state = '-';
                    if (value != undefined && value !== '') {
                        state = getI18nName(getUserStateName(value));
                    }
                    var content = '<span>' + state + '</span>';
                    return content;
                }
            },
            {
                field: 'language',
                title: getI18nName('UserLanguage'),
                formatter: function (value, row, index) {
                    return value == 0 ? 'English' : '中文简体';
                }
            },
            {
                field: 'firstLogin',
                title: getI18nName('HasLogin'),
                formatter: function (value, row, index) {
                    return value ? getI18nName('yes') : getI18nName('no');
                }
            },
            {
                field: 'charaType',
                title: getI18nName('RoleType'),
                formatter: function (value, row, index) {
                    return getCodeName(value, AUDIT_DATA_TYPE_ENUM);
                }
            },
            {
                field: 'pasPast',
                title: getI18nName('IsPassTimeout'),
                formatter: function (value, row, index) {
                    return value ? getI18nName('yes') : getI18nName('no');
                }
            },
            {
                field: 'option',
                title: getI18nName('operate'),
                events: 'userOption',
                formatter: function (value, row, index) {
                    return '<div>' +
                        // '<button class="userRight">权限</button>' +
                        // '<button class="userInfo">编辑</button>' +
                        '<button class="userDelete">'+getI18nName('delete')+'</button>' +
                        '</div>';
                }
            }
        ]
    };

    function refreshUserTable() {
        $('#userListTable').bootstrapTable('refresh', {
            silent: true
        });
    }

    function refreshTableOptions() {
        var height = $('#global').height() - 125;
        $('#userListTable').bootstrapTable('resetView', {
            height: height
        });
    }

    $(function () {
        initCustomTable(customUserTableConfig);
        $(window).resize(function () {
            $('#userListTableContent').height($('#global').height() - 35);
            refreshTableOptions();
        });
        $(window).resize();
        initWindowEvent();
    });

    function initWindowEvent() {
        window.userOption = {
            'click .userDelete': function (e, value, row, index) {
                layer.confirm('', {
                    content:stringFormat.format(getI18nName('DeleteUserConfirmTips'), row.userName),
                    yes: function () {
                        pollGet(DELETE_NORMAL_USER, {
                            userName: row.userName
                        }, function (res) {
                            layer.msg(geti('DeleteSuccess'));
                            refreshTableOptions();
                        }, function () {
                            layer.msg(getI18nName('DeleteFailed'));
                        });
                    }
                })
            },
            'click .userRight': function (e, value, row, index) {
                pollGet(GET_NORMAL_USER_PERLIST, {
                    userName: row.userName
                }, function (res) {
                    var params = res.result;
                    layer.confirm('', {
                        title: getI18nName('RightSet'),
                        area: ['800px', '600px'],
                        content: '<div id="userPermissionContent">' +
                            '<div id="userPermissions">' +
                            '</div>' +
                            '<div id="usedPermissions"></div>' +
                            '</div>',
                        success: function () {
                            var $userPermissions = $('#userPermissions');
                            $userPermissions.css({
                                'display': 'inline-block',
                            });
                            var addCheckBox = function (item, checked, isUsed) {
                                if ($('#permission_' + item)[0] != undefined) {
                                    return;
                                }
                                var itemContent = $('<div>', {
                                    class: 'permission-item'
                                }).appendTo($userPermissions);

                                var itemView = $('<input>', {
                                    type: 'checkbox',
                                    name: isUsed ? 'usedPer' : 'userPer',
                                    id: 'permission_' + item,
                                    checked: checked
                                }).appendTo($userPermissions).appendTo(itemContent);
                                var itemLabel = $('<label>', {
                                    text: getI18nName(getCodeName(item,
                                        RIGHT_OPREATE_TYPE_ENUM)),
                                    for: 'permission_' + item
                                }).appendTo(itemContent);

                            };
                            params.permissonNoUsed.map(function (item, index) {
                                addCheckBox(item, false)
                            })
                        },
                        yes: function () {
                            var checkedPers = $('[name="userPer"]:checked');
                            var permissions = [];
                            checkedPers.map(function (index, item) {
                                permissions.push(item.id.split('_')[1]);
                            });
                            pollGet(SAVE_NORMAL_USER_PERMISSON, {})
                        }
                    })
                }, function () {});
            },
            'click .userInfo': function (e, value, row, index) {
                var params = {
                    userName: 'test123',
                    beginTime: 0,
                    endTime: 24,
                    beginIP: '***********',
                    endIP: '***********',
                    maxFailCount: 3,
                    passwordDays: 365,
                };
                var itemConfig = {
                    userName: {
                        text: getI18nName('userName'),
                    },
                    beginTime: {
                        text: getI18nName('UserManagerAllowLoginTimeStart'),
                        type: 'number'
                    },
                    endTime: {
                        text: getI18nName('UserManagerAllowLoginTimeEnd'),
                        type: 'number'
                    },
                    beginIP: {
                        text: getI18nName('UserManagerAllowLoginIPStart'),
                    },
                    endIP: {
                        text: getI18nName('UserManagerAllowLoginIPEnd'),
                    },
                    loginFailCount: {
                        text: getI18nName('UserManagerErrorCount'),
                        disabled: true,
                    },
                    maxLoginFailCount: {
                        text: getI18nName('UserManagerMaxErrorCount'),
                        type: 'number'
                    },
                    passwordDays: {
                        text: getI18nName('UserManagerPasswordDuration'),
                        unit: getI18nName('Day'),
                        type: 'number'
                    },
                    passwordActiveDate: {
                        text: getI18nName('UserManagerPasswordEffective'),
                        disabled: true,
                    }
                };
                var tempData = row;
                layer.confirm('', {
                    area: ['800px', '550px'],
                    content: '<form id="userInfoContent" style="height: 100%"></form>',
                    success: function () {
                        var addInfoParamItem = function (config, keyName) {
                            var itemContent = $('<div>', {
                                class: 'info-params-content'
                            }).appendTo($('#userInfoContent'));
                            var itemName = $('<label>', {
                                class: 'info-params-label',
                                text: config.text,
                            }).appendTo(itemContent);
                            var itemInput = $('<input>', {
                                class: 'info-params-input',
                                id: keyName,
                                name: keyName,
                                value: tempData[keyName],
                                disabled: config.disabled,
                                type: config.type
                            }).appendTo(itemContent);
                        };
                        for (var key in itemConfig) {
                            addInfoParamItem(itemConfig[key], key);
                        }
                    },
                    yes: function () {}
                })
            }
        }
    }
    $('#testAdd').on('click', function () {
        layer.confirm(getI18nName('UserManagerAddUser'), {
            content: '<div><input id="addUserName" placeholder='+getI18nName('UserManagerInputUserNameTips')+'/></div>',
            yes: function (index) {
                var tempName = $('#addUserName').val();
                if (tempName == "") {
                    layer.msg(getI18nName('UserManagerInputUserNameTips'));
                    return;
                }
                pollGet(ADD_NORMAL_USER, {
                    userName: tempName
                }, function (res) {
                    if (res.result.result == true) {
                        layer.msg(getI18nName('AddSuccess'));
                    } else {
                        layer.msg(getI18nName('AddFailed'))
                    }
                    refreshUserTable()
                }, function () {
                    layer.msg(getI18nName('AddFailed'))
                });
                layer.close(index)
            }
        });

    });
</script>