<style>
    .export-data-content {
        background: white;
        height: calc(100% - 50px);
        width: calc(100% - 40px);
        margin: 20px;
        border-radius: 5px;
    }

    .export-data-content .header {
        width: 100%;
        height: 50px;
        border-bottom: 1px solid lightskyblue;
        padding: 15px;
        color: #5098c5;
    }

    .export-data-config {
        /* display: flex; */
        padding-left: 10px;
        z-index: 10;
        max-width: 280px;
    }

    .export-data-config .ck-content {
        margin: 5px 8px;
        display: flex;
        z-index: 10;
        position: relative;
    }

    .export-data-config .ck-content label {
        z-index: 10;
    }

    .export-data-config .ck-content input {
        margin-left: 5px;
        z-index: 10;
        position: absolute;
        right: 5px;
    }

    .export-data-content .box,
    .export-data-content .container {
        clear: both;
        position: relative;
    }

    .export-data-content pre {
        background: #f2f2f2;
        color: #000;
        width: 800px;
        margin: 0 auto;
        border: 1px solid #dee0e1;
        -webkit-box-shadow: inset 1px 1px 2px #ddd;
        -moz-box-shadow: inset 1px 1px 2px #ddd;
        box-shadow: inset 1px 1px 2px #ddd;
    }

    .export-data-content em {
        color: red;
    }

    .export-data-content .block {
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: 3;
    }

    .eis-stepContent {
        display: flex;
        align-items: center;
    }
</style>
<div style="height: 100%;">
    <div class="export-data-content">
        <div class="header" style="display: flex;">
            <div style="font-size: 18px;margin-right: 2rem;" data-i18n="DataExport">数据导出</div>
            <!-- <div>
                <div class="ck-content" style="margin-top:1px;">
                    <label for="exportAll" style="color:#333;font-size: 13px;">所有数据</label>
                    <input id="exportAll" name="exportGroup" type="checkbox" style="padding-top: 4px;position: absolute;margin-left: 5px;"/>
                </div>
            </div> -->
        </div>
        <div style="margin: 1.2rem;width: 30%;">
            <label data-i18n="timeRange">时间范围</label>
            <div class="input-group date">
                <div class="input-group-addon">
                    <i class="fa fa-calendar"></i>
                </div>
                <input id="da-dateRange" type="text" class="form-control pull-right" readonly="true">
            </div>
        </div>
        <div id="exportStep" class="box eis-horizontal-steps" style="border-top: none;padding: 12px;height: 180px;">
            <div class="block"></div>
            <div class="eis-stepContents">
                <div class="eis-stepContent" style="display: none;">
                    <h2 data-i18n="DataExportStep1">选择要导出的数据</h2>
                </div>
                <div class="eis-stepContent">
                    <h2 data-i18n="DataExportStep2">正在计算数据大小...</h2>
                    <img src="./dist/img/loading1.gif" style="margin-left: 8px;"></img>
                </div>
                <div class="eis-stepContent" style="display: none;">
                    <h2 id="orgFileSize" style="font-size: 30px;"></h2>
                    <h2 data-i18n="DataExportStep3">正在打包压缩数据...</h2>
                    <img src="./dist/img/loading1.gif" style="margin-left: 8px;"></img>
                </div>
                <div class="eis-stepContent" style="display: none;">
                    <h2 data-i18n="DataExportStep4">数据压缩完成</h2>
                </div>
            </div>
        </div>
        <div class="export-data-config">
            <div class="ck-content">
                <label for="ck-data-base" data-i18n="DataExportCheckData">数据库[/media/data/database]</label>
                <input id="ck-data-base" name="exportGroup" type="checkbox" checked='true' />
            </div>
            <div class="ck-content">
                <label for="ck-data-file" data-i18n="DataExportCheckDataFile">数据文件[/media/data/datafile]</label>
                <input id="ck-data-file" name="exportGroup" type="checkbox" checked='true' />
            </div>
            <div class="ck-content">
                <label for="ck-daily-file" data-i18n="DataExportCheckLog">日志文件[/media/data/log]</label>
                <input id="ck-daily-file" name="exportGroup" type="checkbox" checked='true' />
            </div>
            <div class="ck-content">
                <label for="ck-config-file" data-i18n="DataExportCheckConfig">配置文件[/home/<USER>/config.xml]</label>
                <input id="ck-config-file" name="exportGroup" type="checkbox" checked='true' />
            </div>
            </br>
            <button id="beginExportData" class="btn btn-primary table-item-btn" style="margin-left: 5px;"
                onclick="requestExportData()" data-i18n="DataExportBegin">开始导出</button>
            <button id="cancelExportData1" class="btn btn-primary table-item-btn" style="margin-left: 5px;"
                onclick="cancelExportData()" data-i18n="cancel">取消</button>
            <a class="hide">
                <li id="downloadExportData">download</li>
            </a>
        </div>
        <div class="testarea hide">
            <button onclick="requestExportData()">testExport</button>
            <button onclick="cancelExportData()">testCancel</button>
            <button onclick="download()">download</button>
            </br>
            <textarea id="msgcontent" style="width: 280px;height: 200px;overflow: auto;" disabled='disabled'></textarea>
        </div>
    </div>
</div>
<script>

    var exportAllEnable = false;
    $('#exportAll').on('click', function (obj) {
        exportAllEnable = obj.target.checked;
        if (exportAllEnable) {
            $('#da-dateRange').parent().parent().hide();
        } else {
            $('#da-dateRange').parent().parent().show();
        }
    });

    $('input[name=exportGroup]').on('change', function (obj) {
        if ($('input[name=exportGroup]:checked').length == 0) {
            obj.currentTarget.checked = true;
            layer.msg(getI18nName('SelectAtLeastOneTips'));
            return;
        }
        if (checkExportStateConfig.exportId[obj.currentTarget.id]) {
            checkExportStateConfig.exportId[obj.currentTarget.id].need = obj.currentTarget.checked;
        }
    })
    var exportStepBox;
    $(function () {
        exportStepBox = $('#exportStep').step({
            stepDirection: 'x',
            showStepButton: false,
            stepCount: 4,
            stepTitles: [getI18nName('DataExportStep1Title'), getI18nName('DataExportStep2Title'), getI18nName('DataExportStep3Title'), getI18nName('DataExportStep4Title')],
        })[0];
    })

    function onExportData(message) {
        if (message.dataExportSize != undefined) {
            if (message.dataExportSize > 2000) {
                cancelExportData();
                checkExportState(checkExportStateConfig.enum.fileTypeSelect);
                layer.alert(getI18nName('DataExportFileSizeError'));
                return;
            }
            if (message.dataExportSize == 0) {
                checkExportState(checkExportStateConfig.enum.fileTypeSelect);
                layer.alert(getI18nName('DataExportFileSizeZeroError'));
            } else {
                checkExportState(checkExportStateConfig.enum.countFileSize);
                var tempTime = message.dataExportSize / 1.5;
                // $('#orgFileSize').html('原始数据大小为' + message.dataExportSize + 'MB,预计压缩时间[' + parseInt(tempTime / 60) +
                //     'min ' + parseInt(tempTime % 60) + 's],');
                $('#orgFileSize').html(stringFormat.format(getI18nName('DataExportDataCompressTips'),message.dataExportSize,parseInt(tempTime / 60),parseInt(tempTime % 60)))
            }
        }
        if (message.dataCompress && message.dataCompress == 'begin') {
            checkExportState(checkExportStateConfig.enum.beginCompress);
        }
        if (message.dataCompress && message.dataCompress == 'end') {
            checkExportState(checkExportStateConfig.enum.CompressFinish);
            checkExportStateConfig.download(message.path)
        }
    }

    function download() {
        window.open(serverUrl.substring(0, serverUrl.length - 1) + '/media/backup/download/tmp/backUp.tar.gz')
    }

    function cancelExportData() {
        checkExportState(checkExportStateConfig.enum.fileTypeSelect);
        var message = {
            "errorCode": 0,
            "errormsg": getI18nName('success'),
            "result": {
                "ExportData": {
                    "cancelExportData": true
                }
            },
            "success": "true"
        }
        webSocketSend(message);
        layer.msg(getI18nName('DataExportCancelTips'))
    }
    var checkExportStateConfig = {
        exportId: {
            "ck-data-base": {
                need: true,
                path: '/media/data/database'
            },
            "ck-data-file": {
                need: true,
                path: '/media/data/datafile'
            },
            "ck-daily-file": {
                need: true,
                path: '/media/data/log'
            },
            "ck-config-file": {
                need: true,
                path: '/home/<USER>/config.xml'
            },
        },
        step: 'fileTypeSelect',
        enum: {
            fileTypeSelect: 'fileTypeSelect',
            countFileSize: 'countFileSize',
            beginCompress: 'beginCompress',
            CompressFinish: 'CompressFinish'
        },
        download: function (path) {
            $('#downloadExportData').parent().attr('href', serverUrl.substring(0, serverUrl.length - 1) + path);
            $('#downloadExportData').trigger('click');
            // window.open(serverUrl.substring(0, serverUrl.length - 1) + path);
        }
    };

    function checkExportState(step) {
        if (step) {
            checkExportStateConfig.step = step;
        }
        switch (checkExportStateConfig.step) {
            case 'fileTypeSelect':
                $.session.set('dataExporting', '');
                $('#cancelExportData').addClass('hide');
                $('#beginExportData').removeClass('hide');
                $('input[name=exportGroup]').removeAttr('disabled');
                exportStepBox.stepMove(0)
                break;
            case 'countFileSize':
                $('#beginExportData').addClass('hide');
                $('#cancelExportData').removeClass('hide');
                $('input[name=exportGroup]').attr('disabled', 'disabled');
                exportStepBox.stepMove(1)
                break;
            case 'beginCompress':
                exportStepBox.stepMove(2)
                break;
            case 'CompressFinish':
                $.session.set('dataExporting', '');
                $('#cancelExportData').addClass('hide');
                $('#beginExportData').removeClass('hide');
                $('input[name=exportGroup]').removeAttr('disabled');
                exportStepBox.stepMove(3)
                break;
        }
    }

    function requestExportData() {
        $.session.set('dataExporting', 'dataExporting');
        checkExportStateConfig.step = checkExportStateConfig.enum.countFileSize;
        checkExportState();
        var pathArr = [];
        for (var key in checkExportStateConfig.exportId) {
            if (checkExportStateConfig.exportId[key].need) {
                pathArr.push({
                    path: checkExportStateConfig.exportId[key].path
                });
            }
        }
        var dateArray = $('#da-dateRange').val().split('-');
        var beginDate = dateArray[0].trim();
        var endDate = dateArray[1].trim();
        if(exportAllEnable){
            beginDate="";
            endDate="";
        }
        var message = {
            "errorCode": 0,
            "errormsg": getI18nName('success'),
            "result": {
                "ExportData": {
                    "beginExportData": pathArr,
                    "beginDate":beginDate,
                    "endDate":endDate,
                }
            },
            "success": "true"
        }
        webSocketSend(message);
    }
    $('#da-dateRange').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "startDate": moment().subtract(7, 'days'),
        "endDate": moment(),
        autoRangeLimit: 1000 * 60 * 60 * 24 * 7,
        onAutoRangeLimit:function(start,end){
            layer.msg(getI18nName('ExportDataTimeRangeLimitTips'))
        }
    })
    $('#da-dateRange').removeAttr("disabled");
    

</script>