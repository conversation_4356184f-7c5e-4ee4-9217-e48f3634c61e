.notifications {
    position: fixed;
    z-index: 100;
}


/* Positioning */

.notifications.top-center {
    left: 40%;
    top: 0px;
}

.notifications.top-right {
    right: 14px;
    top: 55px;
}

.notifications.top-left {
    left: 10px;
    top: 25px;
}

.notifications.bottom-left {
    left: 10px;
    bottom: 25px;
}

.notifications.bottom-right {
    right: 10px;
    bottom: 25px;
}

.notifications.bottom-center {
    left: 40%;
    bottom: 0px;
}


/* Notification Element */

.notifications>div {
    position: relative;
    margin: 5px 0px;
    padding: 8px 40px 8px 14px;
}

.notifications>div>.close {
    font-size: 20px;
    position: relative;
    right: -20px;
    line-height: 20px;
    opacity: 0.5;
    text-decoration: none;
}