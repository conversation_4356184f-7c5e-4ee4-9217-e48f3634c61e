/*
 * @Author: liyaoxu <EMAIL>
 * @LastEditors: liyaoxu <EMAIL>
 * @FilePath: \docroot\pages\alertData\alertData.js
 */
var alertDataSearchParams = {
    aduID: "",
    getType: 0,
};

function searchAlertData() {
    var sensorId = $('#sensorId').val();
    alertDataSearchParams.aduID = sensorId;
    if (sensorId == "") {
        alertDataSearchParams.getType = 0;
    } else {
        alertDataSearchParams.getType = 1;
    }
    $('#tableAlertData').bootstrapTable('selectPage', 1);
}

var tempAuditDataType = -1;


function initAlertDataManagerTable() {
    alertDataSearchParams.aduID = '';
    /**
     * 初始化表格内容
     */
    initCustomTable({
        id: 'tableAlertData',
        method: GET_ADU_ALARM_INFO,
        toolbar: '#alertToolbar',
        height: $('#global').height() * 0.8,
        pageSize: 20,
        pageList: [20],
        responseHandler: function (res) {
            if (res.errorCode != 0) {
                if (res.errorCode == 901) {
                    logOut('repeatLogin');
                    return;
                } else {
                    $.notifyUtil.notifyWarning(convertErrorCode(res.errorCode));
                }
            }
            return res;
        },
        requestParam: function (params) {
            var req;
            req = {
                page: params.offset / params.limit + 1,
                size: params.limit,
                aduID: alertDataSearchParams.aduID,
                getType: alertDataSearchParams.getType,
            };
            return req;
        },
        columns: [{
            field: 'index',
            title: getI18nName('index')
        },
        {
            field: 'aduId',
            title: getI18nName('pointType'),
        },
        {
            field: 'AlarmType',
            title: getI18nName('AlarmType'),
            formatter: function (value, row, index) {
                //value：当前field的值，即id
                //row：当前行的数据
                // return getAuditDataTypeNameByCode(row.dataType);
                return getI18nName(value);
            }
        }, {
            field: 'AlarmData',
            title: getI18nName('AlarmData'),
            formatter: function (value, row, index) {
              var content=[];
              value.map((data,index) => {
                var msg = getI18nName(getAlarmDataTypeName(data.DataType));
                if(data.DataType==1002){
                  msg = stringFormat.format(msg, data.DataValue);
                }
                content.push(msg);
              });
              return content.join(';<br />');
            }
        },
        {
            field: 'AlarmTime',
            title: getI18nName('AlarmTime'),
        }
        ]
    });
}


function refreshAlertDataTable() {
    $('#tableAlertData').bootstrapTable('refresh', {
        silent: true
    });
}

function getAuditOpTypeNameByCode(code) {
    return getCodeName(code, AUDIT_OPREATE_TYPE_ENUM);
}

function getAuditDataTypeNameByCode(code) {
    return getCodeName(code, AUDIT_DATA_TYPE_ENUM);
}