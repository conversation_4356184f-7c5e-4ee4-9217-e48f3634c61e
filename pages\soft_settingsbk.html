<!-- left column -->
<div class="col-sm-12 full-height">
    <div class="nav-tabs-custom full-height">
        <br>
        <form class="form-horizontal">
            <div class="form-group">
                <label for="systemDate" class="col-sm-3 control-label">系统日期</label>
                <div class="col-sm-8">
                    <div class="bootstrap-datepicker">
                        <div class="input-group">
                            <div class="input-group-addon">
                                <i class="fa fa-calendar"></i>
                            </div>
                            <input id="systemDate" type="text" class="form-control pull-right">
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="systemTime" class="col-sm-3 control-label">系统时间</label>
                <div class="col-sm-8">
                    <div class="bootstrap-timepicker">
                        <div class="input-group">
                            <div class="input-group-addon">
                                <i class="fa fa-clock-o"></i>
                            </div>
                            <input id="systemTime" type="text" class="form-control timepicker">
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="frequency" class="col-sm-3 control-label">电网频率</label>
                <div class="col-sm-8">
                    <select id="frequency" class="form-control select2" data-i18n-placeholder="selectPlease"
                            style="width: 100%;"> </select>
                </div>
            </div>
            <div class="form-group">
                <label for="uploadInterval" class="col-sm-3 control-label">上传间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                                <span class="input-group-btn">
                                    <button id="interval-minus" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </span>
                        <input id="uploadInterval" vk="true" type="text" class="col-sm-4 form-control"
                               placeholder="Please input" value="60">
                        <span class="input-group-btn">
                                    <button id="interval-plus" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="sampleInterval" class="col-sm-3 control-label" data-i18n="intervalMinus">采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                                <span class="input-group-btn">
                                    <button id="interval-minus-s" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </span>
                        <input id="sampleInterval" vk="true" type="text" class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="12">
                        <span class="input-group-btn">
                                    <button id="interval-plus-s" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">小时</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="sampleInterval" class="col-sm-3 control-label" data-i18n="startIntervalMinus">主机起始采样时刻</label>
                <div class="col-sm-8">
                    <div class="input-group">
                                <span class="input-group-btn">
                                    <button id="interval-minus-m" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </span>
                        <input id="monitorSampleTime" vk="true" type="text" class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="2">
                        <span class="input-group-btn">
                                    <button id="interval-plus-m" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">点</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="sampleInterval" class="col-sm-3 control-label">主机起始采样间隔</label>
                <div class="col-sm-8">
                    <div class="input-group">
                                <span class="input-group-btn">
                                    <button id="interval-minus-mi" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-minus"></i>
                                    </button>
                                </span>
                        <input id="monitorSampleInterval" vk="true" type="text" class="col-sm-4 form-control"
                               data-i18n-placeholder="pleaseInput" value="4">
                        <span class="input-group-btn">
                                    <button id="interval-plus-mi" type="button" class="btn btn-default btn-flat">
                                        <i class="fa fa-plus"></i>
                                    </button>
                                </span>
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">小时</label></span>
                    </div>
                </div>
            </div>
            <div class="form-group hide">
                <label for="poweroff" class="col-sm-3 control-label">关机间隔</label>
                <!--<div class="col-sm-8">-->
                <!--<input id="poweroff" vk="true" type="text" class="form-control timepicker col-sm-10">-->
                <!--<label class="col-sm-2">小时</label>-->
                <!--</div>-->
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="poweroff" vk="true" type="text" class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput">
                        <span class="input-group-btn">
                                    <label class="btn btn-default btn-flat">小时
                                    </label>
                                </span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label for="poweron" class="col-sm-3 control-label">开机间隔</label>
                <!--<div class="col-sm-8">-->
                    <!--<input id="poweron" vk="true" type="text" class="form-control timepicker">-->
                    <!--<label>小时</label>-->
                <!--</div>-->
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="poweron" vk="true" type="text" class="col-sm-4 form-control" data-i18n-placeholder="pleaseInput">
                        <span class="input-group-btn"><label class="btn btn-default btn-flat">小时</label></span>
                    </div>
                </div>
            </div>
        </form>
        <div class="content-footer">
            <button type="button" id="save_soft_set" class="btn btn-block btn-primary"
                    style="width: 200px; margin: 10px 60px 10px auto" data-i18n="save">保存
            </button>
        </div>
        <br>
    </div>

</div>


<!-- /.row -->
<script>
    //@ sourceURL=soft_set.js
    //select2
    $('.select2').select2({
        minimumResultsForSearch: Infinity
    });
    
    //Daterangepicker
    $('#systemDate').daterangepicker({
        "opens": "right",
        "autoApply": true,
        "timePicker": false,
        "singleDatePicker": true,
        format: 'YYYY-MM-DD'
    });
    //Timepicker
    $("#systemTime").timepicker({
        showMeridian: false,
        showInputs: false,
        showSeconds: true,
        minuteStep: 1,
        secondStep: 1,
    });
    
    //获取网络参数信息
    getSysConfig();

    checkNeedKeyboard();
    
    //增减上传间隔
    $("#interval-minus").click(function () {
        var value = $("#uploadInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '上传间隔在60s至86400之间。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 60) {
                $("#interval-plus").removeClass("disabled");
                $("#uploadInterval").val(number - 1);
            } else {
                $("#uploadInterval").val(60);
                $("#interval-minus").addClass("disabled");
            }
        }
    });
    $("#interval-plus").click(function () {
        var value = $("#uploadInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '上传间隔在60s至86400之间。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 86400) {
                $("#interval-minus").removeClass("disabled");
                $("#uploadInterval").val(number + 1);
            } else {
                $("#uploadInterval").val(86400);
                $("#interval-plus").addClass("disabled");
            }
        }
    });

    //增减采样间隔
    $("#interval-minus-s").click(function () {
        var value = $("#sampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '采样间隔在1小时至72小时之间的整数。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#interval-plus-s").removeClass("disabled");
                $("#sampleInterval").val(number - 1);
            } else {
                $("#sampleInterval").val(1);
                $("#interval-minus-s").addClass("disabled");
            }
        }
    });
    $("#interval-plus-s").click(function () {
        var value = $("#sampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '采样间隔在1小时至72小时之间的整数。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 72) {
                $("#interval-minus-s").removeClass("disabled");
                $("#sampleInterval").val(number + 1);
            } else {
                $("#sampleInterval").val(72);
                $("#interval-plus-s").addClass("disabled");
            }
        }
    });

    //增减主机起始采样时刻
    $("#interval-minus-m").click(function () {
        var value = $("#monitorSampleTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '主机起始采样时刻在0点至23点之间的整点时刻。'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 0) {
                $("#interval-plus-m").removeClass("disabled");
                $("#monitorSampleTime").val(number - 1);
            } else {
                $("#monitorSampleTime").val(0);
                $("#interval-minus-m").addClass("disabled");
            }
        }
    });
    $("#interval-plus-m").click(function () {
        var value = $("#monitorSampleTime").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '主机起始采样时刻在0点至23点之间的整点时刻。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 23) {
                $("#interval-minus-m").removeClass("disabled");
                $("#monitorSampleTime").val(number + 1);
            } else {
                $("#monitorSampleTime").val(23);
                $("#interval-plus-m").addClass("disabled");
            }
        }
    });

    //增减采样间隔
    $("#interval-minus-mi").click(function () {
        var value = $("#monitorSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '采样间隔在1小时至168小时之间的整数。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number > 1) {
                $("#interval-plus-mi").removeClass("disabled");
                $("#monitorSampleInterval").val(number - 1);
            } else {
                $("#monitorSampleInterval").val(1);
                $("#interval-minus-mi").addClass("disabled");
            }
        }
    });
    $("#interval-plus-mi").click(function () {
        var value = $("#monitorSampleInterval").val();
        if (validateNumeric(value) == false) {
            $('.notifications').notify({
                message: {
                    text: '采样间隔在1小时至168小时之间的整数。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                },
                type: "warning",
            }).show();
        } else {
            var number = parseInt(value);
            if (number < 168) {
                $("#interval-minus-mi").removeClass("disabled");
                $("#monitorSampleInterval").val(number + 1);
            } else {
                $("#monitorSampleInterval").val(168);
                $("#interval-plus-mi").addClass("disabled");
            }
        }
    });
    
    //保存全局信息
    $("#save_soft_set").click(function () {
        var check = true;
        var formData = {};
        var systemDate = $("#systemDate").val();
        formData.sysTime = $("#systemDate").val() + " " + $("#systemTime").val();
//        formData.language = $("#language").val();
        
//        formData.monitorAwakeTime = $("#poweroff").val();
        formData.monitorSleepSpace = $("#poweron").val();
        
        formData.frequency = $("#frequency").val();
        if (validateStrLen(formData.frequency) == 0) {
            var label = "label[for=frequency]";
            var text = $("#global").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: '请输入 ' + text + '.'
                },
                type: "warning",
            }).show();
            check = false;
        }
        formData.uploadInterval = $("#uploadInterval").val();
        formData.sampleInterval = $("#sampleInterval").val();
        formData.monitorSampleTime=$("#monitorSampleTime").val();
        formData.monitorSampleInterval=$("#monitorSampleInterval").val();
        if (validateStrLen(formData.uploadInterval) == 0) {
            var label = "label[for=uploadInterval]";
            var text = $("#global").find(label).text().toLowerCase();
            $('.notifications').notify({
                message: {
                    text: '请输入 ' + text + '.'
                },
                type: "warning",
            }).show();
            check = false;
        } else {
            if (validateNumeric(formData.uploadInterval) == false || formData.uploadInterval < 60 || formData.uploadInterval > 86400) {
                $('.notifications').notify({
                    message: {
                        text: '上传间隔在60s至86400之间。'//'Upload Interval (seconds) should be an integer between [60,86400].'
                    },
                    type: "warning",
                }).show();
                check = false;
            }
        }
        
        formData.isDaq = $("#isDaq").val();
        if (check) {
            saveSysInfo(formData);
        }
    });
</script>