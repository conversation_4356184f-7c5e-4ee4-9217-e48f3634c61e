<div class="col-sm-12 nav-tabs-custom full-height">
    <div>
        <ul class="nav nav-tabs" style="height:9%;">
            <!--<li class="active" onclick="getStationTree()"><a href="#stationSet_tab" data-toggle="tab">站点配置</a></li>-->
            <li id="timeDomainTab" class="active"><a href="#timeDomain" data-toggle="tab" data-i18n="timeDomain">时域图</a>
            </li>
            <li id="frequencyDomainTab" class="hide"><a href="#frequencyDomain" data-toggle="tab"
                    data-i18n="frequencyDomain">频域图</a>
            </li>
            <li id="characterParamTab"><a href="#characterParam" data-toggle="tab" data-i18n="characterParam">特征参数</a>
            </li>
        </ul>
    </div>
    <div class="tab-content" style="padding:0px;height:91%">
        <div class="col-sm-12 tab-pane full-height active" id="timeDomain">
            <div id="timeDomainChart" style="width:100%;height:100%"></div>
        </div>
        <div class="col-sm-12 tab-pane full-height" id="frequencyDomain" style="padding: 0px;">
            <div id="frequencyDomainChart" style="width:100%;height:100%"></div>
        </div>
        <div class="col-sm-12 tab-pane full-height" id="characterParam" style="padding: 0px;">
            <form id="characterParamContent" class="form-horizontal full-height"
                style="width:100%;height:100%;padding: 35px;">
                <!--<div class="col-sm-4">-->
                <!--<div class="form-group">-->
                <!--<label class="col-sm-7 control-label" style="padding-right:5px">传感器类型:</label>-->
                <!--<div class="col-sm-5">-->
                <!--<label class=" control-label" id="1">避雷器</label>-->
                <!--</div>-->
                <!--</div>-->
                <!--</div>-->
            </form>
        </div>
        <div class="content-footer">
            <div class="col-sm-3">
                <button type="button" id="preData" class="btn btn-block btn-primary"
                    style="width: 110px; margin: 10px 0px 10px auto" data-i18n="preData">上一条
                </button>
            </div>
            <div class="col-sm-3">
                <button type="button" id="nextData" class="btn btn-block btn-primary"
                    style="width: 100px; margin: 10px 0px 10px auto" data-i18n="nextData">下一条
                </button>
            </div>
        </div>
    </div>
</div>
<style>
    .no-padding {
        padding: 0px;
    }
</style>

<script>
    var currentTab;
    var currentState = {
        "timeDomainChart": function (dataId) {
            for (var i = 1; i < 4; i++) {
                getVibrationTendDatas(currentTab, dataId, i);
            }
        },
        "frequencyDomainChart": function (dataId) {
            for (var i = 4; i < 7; i++) {
                getVibrationTendDatas(currentTab, dataId, i);
            }
        },
        "characterParamContent": function (dataId) {
            initVibrationParams();
            getVibrationParams(dataId);
        }
    };

    $('#timeDomainTab').click(function () {
        $('#timeDomainChart').empty();
        currentTab = "timeDomainChart";
        currentState[currentTab](showChartDataId);
    });
    $('#frequencyDomainTab').click(function () {
        $('#frequencyDomainChart').empty();
        currentTab = "frequencyDomainChart";
        currentState[currentTab](showChartDataId);
    });
    $('#characterParamTab').click(function () {
        $('#characterParamContent').empty();
        currentTab = "characterParamContent";
        currentState[currentTab](showChartDataId);
    });
    $('#timeDomainTab').trigger('click');

    $("#preData").click(function () {
        if (preVibrationId === 0) {
            layer.alert(getI18nName('noTestData'), {
                title: getI18nName('tips'),
                btn: [getI18nName('close')]
            });
            return;
        }
        currentState[currentTab](preVibrationId);
    });


    $("#nextData").click(function () {
        if (nextVibrationId === 0) {
            layer.alert(getI18nName('noTestData'), {
                title: getI18nName('tips'),
                btn: [getI18nName('close')]
            });
            return;
        }
        currentState[currentTab](nextVibrationId);
    });
</script>