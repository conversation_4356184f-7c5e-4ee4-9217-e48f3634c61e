/**
 * 初始化自定义table工具
 * @type {{id: undefined, method: undefined, requestParam: undefined, columns: undefined}}
 */
var defCustomTableOption = {
  id: undefined,
  method: undefined,
  requestParam: undefined,
  responseHandler: undefined,
  columns: undefined,
};
var testServer = 'https://921066bd-daa9-43c7-8d45-90eaa1c5b6a3.mock.pstmn.io/';

function initCustomTable(option) {
  var tempMethod = '';
  if (option.method) {
    tempMethod = option.method;
  }
  var defId = '#tableAll';
  if (option.id) {
    defId = '#' + option.id;
  }
  var defRequestParam = function (params) {
    return {
      page: params.offset / params.limit + 1,
      size: params.limit,
    };
  };
  if (option.requestParam) {
    defRequestParam = option.requestParam;
  }
  var tableUrl;
  tableUrl = serverUrl + tempMethod;
  if (windowDebug && option.useMocker == true) {
    tableUrl = mokerServer + tempMethod;
  }
  var defResponseHandler = function (res) {
    return res;
  };
  if (option.responseHandler) {
    defResponseHandler = option.responseHandler;
  }
  var tempTable = $(defId).bootstrapTable({
    formatLoadingMessage: function () {
      return getI18nName('loadingData');
    },
    formatRecordsPerPage: function (pageNumber) {
      //            return '每页 ' + pageNumber + ' 条';
      return stringFormat.format(getI18nName('pageSize'), pageNumber);
    },
    formatShowingRows: function (pageFrom, pageTo, totalRows) {
      //            return '显示：' + pageFrom + '-' + pageTo + '，共 ' + totalRows + ' 条';
      return stringFormat.format(
        getI18nName('pageSelecter'),
        pageFrom,
        pageTo,
        totalRows
      );
    },
    formatSearch: function () {
      return getI18nName('search');
    },
    formatNoMatches: function () {
      return getI18nName('noMatch');
    },
    onLoadSuccess: function (data) {
      if (option.onLoadSuccess) {
        option.onLoadSuccess();
      }
      $('div .pull-left .pagination-detail').css('margin-left', '25px');
      $('div .pull-right .pagination').css('margin-right', '25px');
    },
    onPageChange: function (number, size) {
      if (option.onPageChange) {
        option.onPageChange();
      }
    },
    // showExport: true,              //是否显示导出按钮(此方法是自己写的目的是判断终端是电脑还是手机,电脑则返回true,手机返回falsee,手机不显示按钮)
    // exportDataType: "all",              //basic', 'all', 'selected'.
    // exportTypes: ['excel', 'xlsx'],	    //导出类型
    // //exportButton: $('#btn_export'),     //为按钮btn_export  绑定导出事件  自定义导出按钮(可以不用)
    // exportOptions: {
    //     //ignoreColumn: [0,0],            //忽略某一列的索引
    //     fileName: '审计数据导出',              //文件名称设置
    //     worksheetName: '审计数据',          //表格工作区名称
    //     tableName: '审计数据表',
    //     excelstyles: ['background-color', 'color', 'font-size', 'font-weight'],
    // },
    toolbar: option.toolbar,
    toolbarAlign: 'left',
    method: 'get',
    pagination: true, //分页
    singleSelect: false,
    cache: false,
    striped: true,
    sidePagination: option.sidePagination ? option.sidePagination : 'server',
    showRefresh: false,
    paginationLoop: false,
    searchAlign: 'left',
    pageList: option.pageList ? option.pageList : [7, 10, 25],
    url: tableUrl,
    search: false,
    showColumns: option.showColumns != undefined ? option.showColumns : true,
    paginationPreText: '<<',
    paginationNextText: '>>',
    ajaxOptions: {
      settings: {
        async: true,
      },
    },
    height: option.height,
    pageSize: option.pageSize ? option.pageSize : 7,
    responseHandler: defResponseHandler,
    queryParams: defRequestParam,
    columns: option.columns,
    onRefresh: function () {},
  });
  $('.keep-open.btn-group').removeAttr('title');
  return tempTable;
}

function initLocalCustomTable(option) {
  var defId = '#tableAll';
  var data = [];
  if (option.id) {
    defId = '#' + option.id;
  }

  if (option.data) {
    data = option.data;
  }

  var tempTable = $(defId).bootstrapTable({
    formatLoadingMessage: function () {
      return getI18nName('loadingData');
    },
    formatRecordsPerPage: function (pageNumber) {
      //            return '每页 ' + pageNumber + ' 条';
      return stringFormat.format(getI18nName('pageSize'), pageNumber);
    },
    formatShowingRows: function (pageFrom, pageTo, totalRows) {
      //            return '显示：' + pageFrom + '-' + pageTo + '，共 ' + totalRows + ' 条';
      return stringFormat.format(
        getI18nName('pageSelecter'),
        pageFrom,
        pageTo,
        totalRows
      );
    },
    formatSearch: function () {
      return getI18nName('search');
    },
    formatNoMatches: function () {
      return getI18nName('noMatch');
    },
    onLoadSuccess: function (data) {
      if (option.onLoadSuccess) {
        option.onLoadSuccess();
      }
      $('div .pull-left').css('margin-left', '25px');
      $('div .pull-right').css('margin-right', '25px');
    },
    onPageChange: function (number, size) {
      if (option.onPageChange) {
        option.onPageChange(number, size);
      }
    },
    toolbar: option.toolbar,
    toolbarAlign: 'left',
    method: 'get',
    pagination: true, //分页
    singleSelect: false,
    cache: false,
    striped: true,
    sidePagination: option.sidePagination ? option.sidePagination : 'client',
    showRefresh: false,
    paginationLoop: false,
    searchAlign: 'left',
    pageList: option.pageList ? option.pageList : [7, 10, 25],
    search: false,
    showColumns: option.showColumns != undefined ? option.showColumns : true,
    paginationPreText: '<<',
    paginationNextText: '>>',
    height: option.height,
    pageSize: option.pageSize ? option.pageSize : 7,
    columns: option.columns,
    onRefresh: function () {},
  });
  $('.keep-open.btn-group').removeAttr('title');
  return tempTable;
}

/**
 * 时间获取器初始化工具
 * @type {{daterangepicker: daterangepicker, timeHandler: timeHandler}}
 */
var common = {
  daterangepicker: daterangepicker,
  timeHandler: timeHandler,
};

function daterangepicker($elementTime, stratDate, endData) {
  var beginTimeStore = '';
  var endTimeStore = '';
  $elementTime.daterangepicker(
    {
      opens: 'left',
      startDate: stratDate,
      endDate: endData,
      timePicker: true,
      timePicker24Hour: true,
      linkedCalendars: false,
      autoUpdateInput: false,
      locale: {
        format: 'YYYY/MM/DD HH:mm:ss',
        separator: ' ~ ',
        applyLabel: getI18nName('Apply'),
        cancelLabel: getI18nName('cancel'),
        resetLabel: getI18nName('Reset'),
      },
    },
    function (start, end, label) {
      beginTimeStore = start;
      endTimeStore = end;
      console.log(this.startDate.format(this.locale.format));
      console.log(this.endDate.format(this.locale.format));
      if (!this.startDate) {
        this.element.val('');
      } else {
        this.element.val(
          this.startDate.format(this.locale.format) +
            this.locale.separator +
            this.endDate.format(this.locale.format)
        );
      }
    }
  );
}

//处理时间方法：处理初始时间为initDate的时间，往前推num天，或往后推num天，返回处理后的时间handlerDate
function timeHandler(initDate, num, flag) {
  var odate = new Date(initDate);
  if (flag == 'pre') {
    var date = new Date(odate.getTime() - num * 24 * 3600 * 1000);
  } else {
    var date = new Date(odate.getTime() + num * 24 * 3600 * 1000);
  }
  var year = date.getFullYear();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var minute = date.getMinutes();
  if (minute.toString().length == 1) {
    minute = '0' + minute;
  }
  var second = date.getSeconds();
  var handlerDate =
    year + '/' + month + '/' + day + ' ' + hour + ':' + minute + ':' + second;
  return handlerDate;
}

function getObjRealCount(o) {
  var t = typeof o;
  if (t == 'string') {
    return o.length;
  } else if (t == 'object') {
    var n = 0;
    for (var i in o) {
      n++;
    }
    return n;
  }
  return false;
}

var BSValidater = function (config) {
  this.init(config);
};
BSValidater.prototype = {
  init: function (config) {
    this.submitForm = $('#' + config.id);
    this.config = config;
    this.submitForm.bootstrapValidator(this.config);
  },
  validateSubmitForm: function () {
    var validater = this.submitForm.data('bootstrapValidator');
    validater.validate();
    return validater.isValid();
  },
  reset: function () {
    this.submitForm.data('bootstrapValidator').resetForm();
  },
};

/**
 * 判断参数是否为空
 * @param {object} obj 需校验的参数
 */
function checkIsEmpty(obj) {
  if (obj === null || obj === undefined) {
    return true;
  } else if (typeof obj === 'number') {
    return false;
  } else {
    if (obj === '') {
      return true;
    }

    let arr = Array.isArray(obj);
    if (arr && arr.length === 0) {
      return true;
    }

    let keys = Object.keys(obj);
    if (keys && keys.length === 0) {
      return true;
    }
  }
  return false;
}
