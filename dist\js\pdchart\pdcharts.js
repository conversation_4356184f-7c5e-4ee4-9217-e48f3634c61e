﻿/**
 *
 *  <AUTHOR>
 *  @copyright 2017 PDStars <<EMAIL>>
 *  @version $Rev: 1.0.0
 *  @lastchange $Author: 张正强$ $Date: 2017-04-10 11:21 $
 *  @class PDChart
 *
 */

PDChart = new function () {
    var _self = this;

    /** 图谱-PRPS **/
    _self.PRPS = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "PRPS",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** XOZ背景颜色 **/
            xozBackgroundColor: '#efefef',

            /** XOY背景颜色 **/
            xoyBackgroundColor: '#fff',

            /** 柱条边框颜色 **/
            columnStrokeColor: "#fff",

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 正弦曲线颜色 **/
            polylineColor: "#0000ff",

            /** 是否显示标题 **/
            showTitle: true,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 矩形高 **/
            rectHeight: 160,

            /** 矩形宽 (rectHeight*1.8)**/
            rectWidth: 288,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /** 线条宽度 **/
            lineWidth: 1,

            /** 背景矩形上竖向线条数量（单矩形） **/
            verticalLineSum: 3,

            /** 背景矩形上横向线条数量（单矩形） **/
            crossLineSum: 3,

            /** 柱条边框宽 **/
            columnStrokeWidth: 0.01,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 圆点半径 **/
            circleSize: 2,

            /** 幅值精度 **/
            toFixedValue: 0,
        },

        /** 绘制3D图谱 **/
        draw3D: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);
            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "prps",
                type: "3d",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    id: 'title',
                    x: chartTitle_x,
                    y: chartTitle_y - 200,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** 绘制矩行-XOZ面板 **/
            var rectXoz_x = (options.chartWidth - options.rectWidth + options.rectHeight) / 2;
            var rectXoz_y = chartTitle_y + titleFontOffset.height * 2;
            var rectXozEl = creatSvgElement('rect', {
                x: rectXoz_x,
                y: rectXoz_y,
                width: options.rectWidth,
                height: options.rectHeight,
                fill: options.xozBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXozEl);


            /** 绘制XOZ面上竖向线条 **/
            var verticalLineInterval = options.rectWidth / (options.verticalLineSum + 1);
            var verticalLineLength = options.rectHeight;
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rectXoz_x + verticalLineInterval * i;
                var lineYFrom = rectXoz_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + verticalLineLength;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制XOZ面上横向线条 **/
            var crossLineInterval = options.rectHeight / (options.crossLineSum + 1);
            var crossLineLength = options.rectWidth;
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rectXoz_x;
                var lineYFrom = rectXoz_y + crossLineInterval * i;
                var lineXTo = lineXFrom + crossLineLength;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }


            /** 绘制矩行-XOY面板 **/
            var rectXoy_1_x = rectXoz_x;
            var rectXoy_1_y = rectXoz_y + options.rectHeight;
            var rectXoy_2_x = rectXoz_x + options.rectWidth;
            var rectXoy_2_y = rectXoy_1_y;
            var rectXoy_3_x = rectXoy_2_x - options.rectHeight;
            var rectXoy_3_y = rectXoy_2_y + options.rectHeight;
            var rectXoy_4_x = rectXoy_3_x - options.rectWidth;
            var rectXoy_4_y = rectXoy_3_y;
            var rectXoyEl = creatSvgElement('path', {
                d: "M" + rectXoy_1_x + " " + rectXoy_1_y + "L" + rectXoy_2_x + " " + rectXoy_2_y + "L" + rectXoy_3_x + " " + rectXoy_3_y + "L" + rectXoy_4_x + " " + rectXoy_4_y + "Z",
                fill: options.xoyBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEl);


            /** 绘制XOY面上竖向线条 **/
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rectXoy_1_x + verticalLineInterval * i;
                var lineYFrom = rectXoy_1_y;
                var lineXTo = lineXFrom - options.rectHeight;
                var lineYTo = lineYFrom + options.rectHeight;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制XOY面上横向线条 **/
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rectXoy_1_x - crossLineInterval * i;
                var lineYFrom = rectXoy_1_y + crossLineInterval * i;
                var lineXTo = lineXFrom + crossLineLength;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }

            /** Z轴刻度值、单位及轴名标注 **/
            /** Z轴单位 **/
            var zUnit = chartBody.axisInfo.zUnit;
            /** Z轴量程最小值 **/
            var zRangeMin = chartBody.axisInfo.zRangeMin;
            /** Z轴量程最大值 **/
            var zRangeMax = chartBody.axisInfo.zRangeMax;
            /** Z轴量程 **/
            var zRange = zRangeMax - zRangeMin;

            /** 绘制最大值 **/
            var tempMax = options.toFixedValue == 0 ? parseInt(chartBody.axisInfo.zMaxValue) : parseFloat(chartBody.axisInfo.zMaxValue).toFixed(options.toFixedValue);
            var maxValue = 'Max = ' + tempMax + zUnit;
            var maxValue_x = rectXoz_x;
            var maxValue_y = rectXoz_y - 10;
            var maxValueEl = creatSvgElement('text', {
                id: "maxValue",
                x: maxValue_x,
                y: maxValue_y,
                text: maxValue,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(maxValueEl);

            /** 绘制Z轴最小值  **/
            var zDesc = chartBody.axisInfo.zDesc;
            var zDescFontOffset = getFontOffset(chartBody.axisInfo.zDesc, options.titleFontSize);
            var zDesc_x = rectXoz_x + options.rectWidth + 5;
            var zDesc_y = rectXoz_y + options.rectHeight;
            var zRangeMinEle = creatSvgElement('text', {
                x: zDesc_x,
                y: zDesc_y,
                text: zRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(zRangeMinEle);

            /** Z轴名 **/
            var zDesc = chartBody.axisInfo.zDesc;
            var zDescFontOffset = getFontOffset(chartBody.axisInfo.zDesc, options.titleFontSize);
            var zDesc_x = rectXoz_x + options.rectWidth + 40;
            var zDesc_y = rectXoz_y + options.rectHeight - (options.rectHeight - zDescFontOffset.width) / 2;
            var zDescEl = creatSvgElement('text', {
                id: "zDesc",
                x: zDesc_x,
                y: zDesc_y,
                text: zDesc + '[' + zUnit + ']',
                fill: options.textColor,
                transform: "rotate(-90," + zDesc_x + " " + zDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px",
                    "text-anchor": "start"
                }
            });
            svgEl.appendChild(zDescEl);

            /** 绘制Z轴最大值  **/
            var zDesc = chartBody.axisInfo.zDesc;
            var zDescFontOffset = getFontOffset(chartBody.axisInfo.zDesc, options.titleFontSize);
            var zDesc_x = rectXoz_x + options.rectWidth + 5;
            var zDesc_y = rectXoz_y + parseFloat(options.axisDescFontSize) - 5;
            var zRangeMinEle = creatSvgElement('text', {
                x: zDesc_x,
                y: zDesc_y,
                text: zRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(zRangeMinEle);


            /** Y轴刻度值、单位及轴名标注 **/
            /** Y轴单位 **/
            var yUnit = chartBody.axisInfo.yUnit;
            /** Y轴量程最小值 **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            /** Y轴量程最大值 **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            /** Y轴量程 **/
            var yRange = yRangeMax - yRangeMin;

            /** Y轴值 **/
            var yRangeMaxValue = yUnit + "=" + yRangeMax;
            var yRangeFontOffset = getFontOffset(yRangeMaxValue, options.axisNumFontSize);
            var yRangeMax_x = rectXoy_3_x + yRangeFontOffset.width;
            var yRangeMax_y = rectXoy_3_y - yRangeFontOffset.height / 2;
            var yRangeMaxEl = creatSvgElement('text', {
                id: "yRangeMaxValue",
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMaxValue,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisNumFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEl);


            /** Y轴名 **/
            var yDesc = chartBody.axisInfo.yDesc + '[' + yUnit + ']';
            var yDescFontOffset = getFontOffset(yDesc, options.axisDescFontSize);
            var yDesc_x = rectXoy_2_x - options.rectHeight / 2 - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_2_y + options.rectHeight / 2 + yDescFontOffset.height * 1.5;
            var yDescEl = creatSvgElement('text', {
                id: "yDesc",
                x: yDesc_x,
                y: yDesc_y,
                text: yDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yDescEl);

            /** X轴刻度值、单位及轴名标注 **/
            /** X轴单位 **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** X轴量程最小值 **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** X轴量程最大值 **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;
            /** X轴量程 **/
            var xRange = xRangeMax - xRangeMin;
            /** 计算分度值 **/
            var xDivision = xRange / (options.verticalLineSum + 1);

            /** X轴值标注 **/
            var xRangeFontSize = getFontOffset(xRangeMin + xUnit, options.axisNumFontSize);
            for (var i = 0; i <= (options.verticalLineSum + 1); i++) {
                var xAxisNum = xRangeMin + xDivision * i;
                var xAxisNum_x = rectXoy_4_x + verticalLineInterval * i - xRangeFontSize.width;
                var xAxisNum_y = rectXoy_4_y + xRangeFontSize.height;
                var xAxisNumEl = creatSvgElement('text', {
                    id: "xAxisNum" + i,
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xAxisNumEl);
            }

            /** X轴名 **/
            var dataList = chartBody.dataList;
            var xDesc = chartBody.axisInfo.xDesc + '[' + xUnit + ']';
            var xDescFontSize = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_4_x + options.rectWidth / 2 - xDescFontSize.width / 2;
            var xDesc_y = rectXoy_4_y + xRangeFontSize.height + xDescFontSize.height * 1.5;
            var xDescEl = creatSvgElement('text', {
                id: "xDesc",
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEl);

            /** 绘制柱状内容 **/
            /** 柱条宽 **/
            var columnWidth = options.rectHeight / 72;
            /** 柱条单位 **/

            var xInterval = options.rectWidth / xRange;
            var yInterval = options.rectHeight / yRange;
            var zInterval = options.rectHeight / zRange;

            /** 基准柱条位置 **/
            var baseColumn_x = rectXoy_1_x - columnWidth / 2;
            var baseColumn_y = rectXoy_1_y;

            /** 数据组 **/
            var gEl = creatSvgElement('g', {
                rectWidth: options.rectWidth,
                xInterval: xInterval,
                yInterval: yInterval,
                zInterval: zInterval,
                xBase: baseColumn_x,
                yRange: yRange,
                zRange: zRange,
                zMaxValue: chartBody.axisInfo.zMaxValue,
                zUnit: zUnit,
                phase: 0
            });

            var dataList = chartBody.dataList;
            for (var i = 0; i < dataList.length; i++) {
                /* if (i >= 3000)
                    break; */
                /** 获取单个柱条数据 **/
                var data = dataList[i];
                var x = parseFloat(data.x);
                var y = parseFloat(data.y);
                var z = parseFloat(data.z);
                var color = data.color;

                /** 柱条高 **/
                var columnHeight = 0;
                if (z > zRangeMin) {
                    columnHeight = (z - zRangeMin) * zInterval;
                }

                var column_x = baseColumn_x + (x - xRangeMin) * xInterval - (y - yRangeMin) * yInterval + xInterval / 2;
                var column_y = baseColumn_y + (y - yRangeMin) * yInterval - columnHeight + yInterval / 2;

                var columnEl = creatSvgElement('rect', {
                    x: column_x,
                    y: column_y,
                    width: columnWidth,
                    height: columnHeight,
                    fill: color,
                    opacity: 1,
                    stroke: options.columnStrokeColor,
                    "stroke-width": options.columnStrokeWidth
                });
                gEl.appendChild(columnEl);
            }
            svgEl.appendChild(gEl);

            return svgEl;
        },

        /** 绘制2D图谱 **/
        draw2D: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);
            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "prps",
                type: "2d",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.7;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.7;

            /** 绘制矩行面板 **/
            var rect_x = (options.chartWidth - rectXoyWidth) / 2;
            var rect_y = chartTitle_y + titleFontOffset.height * 1;
            var rectEl = creatSvgElement('rect', {
                x: rect_x,
                y: rect_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                fill: options.xoyBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectEl);


            /** 绘制面板上竖向线条 **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1);
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rect_x + verticalLineInterval * i;
                var lineYFrom = rect_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制面板上横向线条 **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1);
            var crossLineLength = options.rectWidth;
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rect_x;
                var lineYFrom = rect_y + crossLineInterval * i;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rect_x;
                var lineYFrom = rect_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rect_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rect_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** Y轴刻度值、单位及轴名标注 **/
            /** Y轴单位 **/
            var yUnit = chartBody.axisInfo.yUnit;
            /** Y轴量程最小值 **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            /** Y轴量程最大值 **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            /** Y轴量程 **/
            var yRange = yRangeMax - yRangeMin;

            /** 绘制Y轴最小值  **/
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rect_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rect_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMinEle);

            /** 绘制Y轴坐标名  **/
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yRangeMinFontOffset.height;
            var yDesc_y = rect_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yDescEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yDescEle);

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisNumFontSize);
            var yRangeMax_x = rect_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rect_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** X轴刻度值、单位及轴名标注 **/
            /** X轴单位 **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** X轴量程最小值 **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** X轴量程最大值 **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;
            /** X轴量程 **/
            var xRange = xRangeMax - xRangeMin;
            /** 计算分度值 **/
            var xDivision = xRange / (options.verticalLineSum + 1);

            /** X轴值标注 **/
            var xRangeFontSize = getFontOffset(xRangeMin + xUnit, options.axisNumFontSize);
            for (var i = 0; i <= (options.verticalLineSum + 1); i++) {
                var xAxisNum = xRangeMin + xDivision * i;
                var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                var xAxisNum_x = rect_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * i;
                var xAxisNum_y = rect_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                var xAxisNumEl = creatSvgElement('text', {
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xAxisNumEl);
            }

            /** X轴名 **/
            var dataList = chartBody.dataList;
            var xDesc = chartBody.axisInfo.xDesc + '[' + xUnit + ']';
            var xDescFontSize = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rect_x + rectXoyWidth / 2 - xDescFontSize.width / 2;
            var xDesc_y = rect_y + rectXoyHeight + xDescFontSize.height * 2;
            var xDescEl = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEl);

            /** 绘制正弦曲线 **/
            var pathEl = creatSvgElement('path', {
                d: "M" + rect_x + ',' + (rect_y + rectXoyHeight / 2) + ' Q' + (rect_x + rectXoyWidth / 4) + ',' + (rect_y - rectXoyHeight / 2) + ' ' + (rect_x + rectXoyWidth / 2) + ',' + (rect_y + rectXoyHeight / 2) + ' T' + (rect_x + rectXoyWidth) + ',' + (rect_y + rectXoyHeight / 2),
                stroke: options.polylineColor,
                fill: "none",
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(pathEl);

            /** 绘制柱状内容 **/

            /** Z轴量程最小值 **/
            var zRangeMin = chartBody.axisInfo.zRangeMin;
            /** Z轴量程最大值 **/
            var zRangeMax = chartBody.axisInfo.zRangeMax;
            /** Z轴量程 **/
            var zRange = zRangeMax - zRangeMin;

            /** 柱条单位 **/
            var xInterval = rectXoyWidth / xRange;
            var yInterval = rectXoyHeight / yRange;
            var zInterval = options.rectHeight / zRange;

            /** 数据组 **/
            var gEl = creatSvgElement('g', {
                rectWidth: rectXoyWidth,
                xInterval: xInterval,
                zInterval: zInterval,
                yInterval: yInterval,
                xBase: rect_x,
                phase: 0
            });

            var dataList = chartBody.dataList;
            for (var i = 0; i < dataList.length; i++) {
                /** 获取单个柱条数据 **/
                var data = dataList[i];
                var x = parseFloat(data.x);
                var y = parseFloat(data.y);
                var z = parseFloat(data.z);
                var color = data.color;

                /** 柱条高，用于2D调整阈值的时候用**/
                var columnHeight = (z - zRangeMin) * zInterval;

                if (z <= zRangeMin) {
                    continue;
                } else if (z >= 1) {
                    z = 1;
                }

                var column_x = rect_x + (x - xRangeMin) * xInterval;
                var column_y = rect_y - (y - yRangeMin) * yInterval + rectXoyHeight;

                var columnEl = creatSvgElement('circle', {
                    cx: column_x,
                    cy: column_y,
                    r: options.circleSize,
                    columnHeight: columnHeight,
                    fill: color,
                    "stroke-width": options.lineWidth
                });
                gEl.appendChild(columnEl);
            }
            svgEl.appendChild(gEl);

            return svgEl;
        },

        /** 调整幅值,value[0,zRange] **/
        changeAmplitude: function (svgEl, minValue, maxValue) {
            /** 图谱类型 **/
            var type = svgEl.getAttribute('type');
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** z轴单位间隔 **/
            var zInterval = parseFloat(gEl.getAttribute('zInterval'));
            /** 所有的数据点 **/
            var length = circleEls;
            var circleEls = svgEl.querySelectorAll('g circle');
            var rectEls = svgEl.querySelectorAll('g rect');
            if (circleEls.length > 0) {
                length = circleEls.length;
            }
            if (rectEls.length > 0) {
                length = rectEls.length;
            }
            for (var i = 0; i < length; i++) {
                var columnEl;
                var height = 0;
                if (type == '2d') {
                    columnEl = circleEls[i];
                    height = parseFloat(columnEl.getAttribute('columnHeight'));
                }
                if (type == '3d') {
                    columnEl = rectEls[i]
                    height = parseFloat(columnEl.getAttribute('height'));
                }
                if (height >= minValue * zInterval && height <= maxValue * zInterval) {
                    columnEl.setAttribute('opacity', 1);
                } else {
                    columnEl.setAttribute('opacity', 0);
                }
            }
        },

        /** 调整相位,value[0,xRange] **/
        changePhase: function (svgEl, value) {
            /** 图谱类型 **/
            var type = svgEl.getAttribute('type');
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** 图谱宽度 **/
            var rectWidth = parseFloat(gEl.getAttribute('rectWidth'));
            /** x轴单位间隔 **/
            var xInterval = parseFloat(gEl.getAttribute('xInterval'));
            /** X轴0点坐标 **/
            var xBase = parseFloat(gEl.getAttribute('xBase'));
            /** 已执行的相位偏移值 **/
            var phase = parseFloat(gEl.getAttribute('phase'));
            /** 相位偏移量 **/
            var offset = (value - phase) * xInterval;

            /** 所有的数据点 **/
            if (type == '2d') {
                var columnEls = svgEl.querySelectorAll('g circle');
                for (var i = 0; i < columnEls.length; i++) {
                    var columnEl = columnEls[i];
                    var x = parseFloat(columnEl.getAttribute('cx')) - offset;
                    if (x < xBase) {
                        x = x + rectWidth;
                    } else if (x > (xBase + rectWidth)) {
                        x = x - rectWidth;
                    }
                    columnEl.setAttribute('cx', x);
                }
            }
            if (type == '3d') {
                var columnEls = svgEl.querySelectorAll('g rect');
                /** y轴单位间隔 **/
                var yInterval = parseFloat(gEl.getAttribute('yInterval'));
                /** Y轴范围 **/
                var yRange = parseFloat(gEl.getAttribute('yRange'));
                var xBaseArr = new Array();
                for (var y = 0; y < yRange; y++) {
                    xBaseArr.push(xBase - y * yInterval);
                }
                for (var i = 0; i < columnEls.length; i++) {
                    var columnEl = columnEls[i];
                    var xBase = xBaseArr[i % yRange];
                    var x = parseFloat(columnEl.getAttribute('x')) - offset;
                    if (x < xBase) {
                        x = x + rectWidth;
                    } else if (x > (xBase + rectWidth)) {
                        x = x - rectWidth;
                    }
                    columnEl.setAttribute('x', x);
                }
            }
            gEl.setAttribute("phase", value);
        },

        /** 切换单位，mv|dB转% **/
        tanslateZUnit: function (svgEl) {
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** 幅值范围 **/
            var zRange = gEl.getAttribute('zRange');
            /** 百分号 **/
            var percent = "%";
            /** 最大值 **/
            var maxValueEl = svgEl.getElementById('maxValue');
            if (maxValueEl != null) {
                var maxValue = maxValueEl.textContent.split('=')[1].replace(/(^\s*)|(\s*$)/g, "");
                var value = maxValue.replace(/[^0-9.]/ig, '');
                var unit = maxValue.replace(value, '');
                var newUnit;
                if (unit != percent) {
                    newUnit = percent;
                    var newValue = (parseFloat(value) / parseFloat(zRange) * 100).toFixed(2);
                    maxValueEl.textContent = maxValueEl.textContent.replace(value, newValue);
                } else {
                    /** 幅值单位 **/
                    var zUnit = gEl.getAttribute('zUnit');
                    /** 最大幅值 **/
                    var zMaxValue = gEl.getAttribute('zMaxValue');
                    newUnit = zUnit;
                    maxValueEl.textContent = maxValueEl.textContent.replace(value, zMaxValue);
                }
                var textEls = svgEl.querySelectorAll('text');
                for (var i = 0; i < textEls.length; i++) {
                    if (textEls[i].textContent.indexOf(unit) > -1) {
                        textEls[i].textContent = textEls[i].textContent.replace(unit, newUnit);
                    }
                }
            }
        }
    };

    /** 图谱-PRPD **/
    _self.PRPD = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "PRPS",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** XOZ背景颜色 **/
            xozBackgroundColor: '#efefef',

            /** XOY背景颜色 **/
            xoyBackgroundColor: '#fff',

            /** 柱条边框颜色 **/
            columnStrokeColor: "#fff",

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 正弦曲线颜色 **/
            polylineColor: "#0000ff",

            /** 是否显示标题 **/
            showTitle: true,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 矩形高 **/
            rectHeight: 160,

            /** 矩形宽 (rectHeight*1.8)**/
            rectWidth: 288,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /** 线条宽度 **/
            lineWidth: 1,

            /** 背景矩形上竖向线条数量（单矩形） **/
            verticalLineSum: 3,

            /** 背景矩形上横向线条数量（单矩形） **/
            crossLineSum: 3,

            /** 柱条边框宽 **/
            columnStrokeWidth: 0.01,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 圆点半径 **/
            circleSize: 2,
        },

        /** 绘制3D图谱 **/
        draw3D: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);
            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "prpd",
                type: "3d",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** 绘制矩行-XOZ面板 **/
            var rectXoz_x = (options.chartWidth - options.rectWidth + options.rectHeight) / 2;
            var rectXoz_y = chartTitle_y + titleFontOffset.height * 2;
            var rectXozEl = creatSvgElement('rect', {
                x: rectXoz_x,
                y: rectXoz_y,
                width: options.rectWidth,
                height: options.rectHeight,
                fill: options.xozBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXozEl);


            /** 绘制XOZ面上竖向线条 **/
            var verticalLineInterval = options.rectWidth / (options.verticalLineSum + 1);
            var verticalLineLength = options.rectHeight;
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rectXoz_x + verticalLineInterval * i;
                var lineYFrom = rectXoz_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + verticalLineLength;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制XOZ面上横向线条 **/
            var crossLineInterval = options.rectHeight / (options.crossLineSum + 1);
            var crossLineLength = options.rectWidth;
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rectXoz_x;
                var lineYFrom = rectXoz_y + crossLineInterval * i;
                var lineXTo = lineXFrom + crossLineLength;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }


            /** 绘制矩行-XOY面板 **/
            var rectXoy_1_x = rectXoz_x;
            var rectXoy_1_y = rectXoz_y + options.rectHeight;
            var rectXoy_2_x = rectXoz_x + options.rectWidth;
            var rectXoy_2_y = rectXoy_1_y;
            var rectXoy_3_x = rectXoy_2_x - options.rectHeight;
            var rectXoy_3_y = rectXoy_2_y + options.rectHeight;
            var rectXoy_4_x = rectXoy_3_x - options.rectWidth;
            var rectXoy_4_y = rectXoy_3_y;
            var rectXoyEl = creatSvgElement('path', {
                d: "M" + rectXoy_1_x + " " + rectXoy_1_y + "L" + rectXoy_2_x + " " + rectXoy_2_y + "L" + rectXoy_3_x + " " + rectXoy_3_y + "L" + rectXoy_4_x + " " + rectXoy_4_y + "Z",
                fill: options.xoyBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEl);


            /** 绘制XOY面上竖向线条 **/
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rectXoy_1_x + verticalLineInterval * i;
                var lineYFrom = rectXoy_1_y;
                var lineXTo = lineXFrom - options.rectHeight;
                var lineYTo = lineYFrom + options.rectHeight;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制XOY面上横向线条 **/
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rectXoy_1_x - crossLineInterval * i;
                var lineYFrom = rectXoy_1_y + crossLineInterval * i;
                var lineXTo = lineXFrom + crossLineLength;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }

            /** Z轴刻度值、单位及轴名标注 **/
            /** Z轴量程最小值 **/
            var zRangeMin = 0;
            /** Z轴量程最大值（轴最大值超过100，则为值的1.25倍，否则为100） **/
            var zRangeMax = parseFloat(chartBody.axisInfo.zMaxValue) <= 100 ? 100 : parseFloat(chartBody.axisInfo.zMaxValue) * 1.25;
            /** Z轴量程 **/
            var zRange = zRangeMax - zRangeMin;
            /** 绘制最大值 **/
            var tempMax = options.toFixedValue == 0 ? parseInt(chartBody.axisInfo.zMaxValue) : parseFloat(chartBody.axisInfo.zMaxValue).toFixed(options.toFixedValue);
            var maxValue = 'Max = ' + tempMax;
            var maxValue = 'Max = ' + parseFloat(chartBody.axisInfo.zMaxValue).toFixed(1);
            var maxValue_x = rectXoz_x;
            var maxValue_y = rectXoz_y - 10;
            var maxValueEl = creatSvgElement('text', {
                x: maxValue_x,
                y: maxValue_y,
                text: maxValue,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(maxValueEl);

            /** Z轴名 **/
            var zDesc = chartBody.axisInfo.zDesc;
            var zDescFontOffset = getFontOffset(chartBody.axisInfo.zDesc, options.titleFontSize);
            var zDesc_x = rectXoz_x + options.rectWidth + zDescFontOffset.width / 2;
            var zDesc_y = rectXoz_y + options.rectHeight;
            var zDescEl = creatSvgElement('text', {
                x: zDesc_x,
                y: zDesc_y,
                text: zDesc,
                fill: options.textColor,
                transform: "rotate(-90," + zDesc_x + " " + zDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px",
                    "text-anchor": "start"
                }
            });
            svgEl.appendChild(zDescEl);


            /** Y轴刻度值、单位及轴名标注 **/
            /** Y轴单位 **/
            var yUnit = chartBody.axisInfo.yUnit;
            /** Y轴量程最小值 **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            /** Y轴量程最大值 **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            /** Y轴量程 **/
            var yRange = (yRangeMax - yRangeMin) / chartBody.axisInfo.yInterval;
            /** 计算分度值 **/
            var yDivision = (yRangeMax - yRangeMin) / (options.crossLineSum + 1);

            /** Y轴值 **/
            var yRangeFontOffset = getFontOffset(yRangeMax, options.axisNumFontSize);
            for (var i = 0; i <= (options.crossLineSum + 1); i++) {
                var yAxisNum = yRangeMin + yDivision * i;
                var yAxisNum_x = rectXoy_2_x - (rectXoy_2_x - rectXoy_3_x) / (options.crossLineSum + 1) * i + yRangeFontOffset.height / 2;
                var yAxisNum_y = rectXoy_2_y + (rectXoy_3_y - rectXoy_2_y) / (options.crossLineSum + 1) * i + 2;
                var yAxisNumEl = creatSvgElement('text', {
                    type: 'yAxisNum',
                    x: yAxisNum_x,
                    y: yAxisNum_y,
                    text: yAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(yAxisNumEl);
            }

            /** Y轴名 **/
            var yDesc = chartBody.axisInfo.yDesc + '[' + yUnit + ']';
            var yDescFontOffset = getFontOffset(yDesc, options.axisDescFontSize);
            var yDesc_x = rectXoy_2_x - options.rectHeight / 2 - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_2_y + options.rectHeight / 2 + yDescFontOffset.height + 5;
            var yDescEl = creatSvgElement('text', {
                id: "yDesc",
                x: yDesc_x,
                y: yDesc_y,
                text: yDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yDescEl);

            /** X轴刻度值、单位及轴名标注 **/
            /** X轴单位 **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** X轴量程最小值 **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** X轴量程最大值 **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;
            /** X轴量程 **/
            var xRange = xRangeMax - xRangeMin;
            /** 计算分度值 **/
            var xDivision = xRange / (options.verticalLineSum + 1);

            /** X轴值标注 **/
            var xRangeFontSize = getFontOffset(xRangeMin + xUnit, options.axisNumFontSize);
            for (var i = 0; i <= (options.verticalLineSum + 1); i++) {
                var xAxisNum = xRangeMin + xDivision * i;
                var xAxisNum_x = rectXoy_4_x + verticalLineInterval * i - xRangeFontSize.width;
                var xAxisNum_y = rectXoy_4_y + xRangeFontSize.height;
                var xAxisNumEl = creatSvgElement('text', {
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xAxisNumEl);
            }

            /** X轴名 **/
            var dataList = chartBody.dataList;
            var xDesc = chartBody.axisInfo.xDesc + '[' + xUnit + ']';
            var xDescFontSize = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_4_x + options.rectWidth / 2 - xDescFontSize.width / 2;
            var xDesc_y = rectXoy_4_y + xRangeFontSize.height + xDescFontSize.height * 1.5;
            var xDescEl = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEl);

            /** 绘制柱状内容 **/
            /** 柱条宽 **/
            var columnWidth = options.rectHeight / 72;
            /** 柱条单位 **/
            var xInterval = options.rectWidth / xRange;
            var yInterval = options.rectHeight / yRange;
            var zInterval = options.rectHeight / zRange;

            /** 基准柱条位置 **/
            var baseColumn_x = rectXoy_1_x - columnWidth / 2;
            var baseColumn_y = rectXoy_1_y;

            /** 数据组 **/
            var gEl = creatSvgElement('g', {
                rectWidth: options.rectWidth,
                xInterval: xInterval,
                yInterval: yInterval,
                zInterval: zInterval,
                xBase: baseColumn_x,
                yRange: yRange,
                zRange: zRange,
                zMaxValue: chartBody.axisInfo.zMaxValue,
                yUnit: yUnit,
                phase: 0
            });

            var dataList = chartBody.dataList;
            for (var i = 0; i < dataList.length; i++) {
                /** 获取单个柱条数据 **/
                var data = dataList[i];
                var x = parseFloat(data.x);
                var y = parseFloat(data.y / chartBody.axisInfo.yInterval);
                var z = parseFloat(data.z);
                var color = data.color;

                /** 柱条高 **/
                var columnHeight = 0;
                if (z > zRangeMin) {
                    columnHeight = (z - zRangeMin) * zInterval;
                }

                var column_x = baseColumn_x + (x - xRangeMin) * xInterval - (y - yRangeMin) * yInterval + xInterval / 2;
                var column_y = baseColumn_y + (y - yRangeMin) * yInterval - columnHeight + yInterval / 2;

                var columnEl = creatSvgElement('rect', {
                    x: column_x,
                    y: column_y,
                    width: columnWidth,
                    height: columnHeight,
                    fill: color,
                    opacity: 1,
                    stroke: options.columnStrokeColor,
                    "stroke-width": options.columnStrokeWidth
                });
                gEl.appendChild(columnEl);
            }
            svgEl.appendChild(gEl);

            return svgEl;
        },

        /** 绘制2D图谱 **/
        draw2D: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);
            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "prpd",
                type: "2d",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.7;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.67;

            /** 绘制矩行面板 **/
            var rect_x = (options.chartWidth - rectXoyWidth) / 2;
            var rect_y = chartTitle_y + titleFontOffset.height * 2;
            var rectEl = creatSvgElement('rect', {
                x: rect_x,
                y: rect_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                fill: options.xoyBackgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectEl);

            /** 绘制面板上竖向线条 **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1);
            for (var i = 1; i <= options.verticalLineSum; i++) {
                var lineXFrom = rect_x + verticalLineInterval * i;
                var lineYFrom = rect_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEl);
            }

            /** 绘制面板上横向线条 **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1);
            var crossLineLength = options.rectWidth;
            for (var i = 1; i <= options.crossLineSum; i++) {
                var lineXFrom = rect_x;
                var lineYFrom = rect_y + crossLineInterval * i;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEl = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEl);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rect_x;
                var lineYFrom = rect_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rect_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rect_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** Y轴刻度值、单位及轴名标注 **/
            /** Y轴单位 **/
            var yUnit = chartBody.axisInfo.yUnit;
            /** Y轴量程最小值 **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            /** Y轴量程最大值 **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            /** Y轴量程 **/
            var yRange = (yRangeMax - yRangeMin) / chartBody.axisInfo.yInterval;

            /** 绘制Y轴最小值  **/
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rect_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rect_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMinEle);

            /** 绘制Y轴坐标名  **/
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yRangeMinFontOffset.height / 2;
            var yDesc_y = rect_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yDescEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yDescEle);

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisNumFontSize);
            var yRangeMax_x = rect_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rect_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** X轴刻度值、单位及轴名标注 **/
            /** X轴单位 **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** X轴量程最小值 **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** X轴量程最大值 **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;
            /** X轴量程 **/
            var xRange = xRangeMax - xRangeMin;
            /** 计算分度值 **/
            var xDivision = xRange / (options.verticalLineSum + 1);

            /** X轴值标注 **/
            var xRangeFontSize = getFontOffset(xRangeMin + xUnit, options.axisNumFontSize);
            for (var i = 0; i <= (options.verticalLineSum + 1); i++) {
                var xAxisNum = xRangeMin + xDivision * i;
                var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                var xAxisNum_x = rect_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * i;
                var xAxisNum_y = rect_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                var xAxisNumEl = creatSvgElement('text', {
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xAxisNumEl);
            }

            /** X轴名 **/
            var dataList = chartBody.dataList;
            var xDesc = chartBody.axisInfo.xDesc + '[' + xUnit + ']';
            var xDescFontSize = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rect_x + rectXoyWidth / 2 - xDescFontSize.width / 2;
            var xDesc_y = rect_y + rectXoyHeight + xRangeFontSize.height * 2;
            var xDescEl = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEl);

            /** 绘制正弦曲线 **/
            var pathEl = creatSvgElement('path', {
                d: "M" + rect_x + ',' + (rect_y + rectXoyHeight / 2) + ' Q' + (rect_x + rectXoyWidth / 4) + ',' + (rect_y - rectXoyHeight / 2) + ' ' + (rect_x + rectXoyWidth / 2) + ',' + (rect_y + rectXoyHeight / 2) + ' T' + (rect_x + rectXoyWidth) + ',' + (rect_y + rectXoyHeight / 2),
                stroke: options.polylineColor,
                fill: "none",
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(pathEl);

            /** 绘制柱状内容 **/

            /** Z轴量程最小值 **/
            var zRangeMin = chartBody.axisInfo.zRangeMin;
            /** Z轴量程最大值 **/
            var zRangeMax = chartBody.axisInfo.zRangeMax;
            /** Z轴量程 **/
            var zRange = zRangeMax - zRangeMin;

            /** 柱条单位 **/
            var xInterval = rectXoyWidth / xRange;
            var yInterval = rectXoyHeight / yRange;
            var zInterval = options.rectHeight / zRange;

            /** 数据组 **/
            var gEl = creatSvgElement('g', {
                rectWidth: rectXoyWidth,
                xInterval: xInterval,
                zInterval: zInterval,
                xBase: rect_x,
                yUnit: yUnit,
                yRange: yRange,
                phase: 0
            });
            var dataList = chartBody.dataList;
            for (var i = 0; i < dataList.length; i++) {
                /** 获取单个柱条数据 **/
                var data = dataList[i];
                var x = parseFloat(data.x);
                var y = parseFloat((data.y - yRangeMin) / chartBody.axisInfo.yInterval);
                var z = parseFloat(data.z);
                var color = data.color;

                if (y <= 0) {
                    continue;
                }
                /** 柱条高，用于2D调整阈值的时候用**/
                var columnHeight = (z - zRangeMin) * zInterval;

                if (z <= zRangeMin) {
                    continue;
                } else if (z >= 1) {
                    z = 1;
                }

                var column_x = rect_x + (x - xRangeMin) * xInterval;
                var column_y = rect_y - (y) * yInterval + rectXoyHeight;

                var columnEl = creatSvgElement('circle', {
                    cx: column_x,
                    cy: column_y,
                    r: options.circleSize,
                    fill: color,
                    "stroke-width": options.lineWidth
                });
                gEl.appendChild(columnEl);
            }
            svgEl.appendChild(gEl);

            return svgEl;
        },

        /** 调整相位,value[0,xRange] **/
        changePhase: function (svgEl, value) {
            /** 图谱类型 **/
            var type = svgEl.getAttribute('type');
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** 图谱宽度 **/
            var rectWidth = parseFloat(gEl.getAttribute('rectWidth'));
            /** x轴单位间隔 **/
            var xInterval = parseFloat(gEl.getAttribute('xInterval'));
            /** X轴0点坐标 **/
            var xBase = parseFloat(gEl.getAttribute('xBase'));
            /** 已执行的相位偏移值 **/
            var phase = parseFloat(gEl.getAttribute('phase'));
            /** 相位偏移量 **/
            var offset = (value - phase) * xInterval;

            /** 所有的数据点 **/

            if (type == '2d') {
                var columnEls = svgEl.querySelectorAll('g circle');
                for (var i = 0; i < columnEls.length; i++) {
                    var columnEl = columnEls[i];
                    var x = parseFloat(columnEl.getAttribute('cx')) - offset;
                    if (x < xBase) {
                        x = x + rectWidth;
                    } else if (x > (xBase + rectWidth)) {
                        x = x - rectWidth;
                    }
                    columnEl.setAttribute('cx', x);
                }
            }
            if (type == '3d') {
                var columnEls = svgEl.querySelectorAll('g rect');
                /** y轴单位间隔 **/
                var yInterval = parseFloat(gEl.getAttribute('yInterval'));
                /** Y轴范围 **/
                var yRange = parseFloat(gEl.getAttribute('yRange'));
                var xBaseArr = new Array();
                for (var y = 0; y < yRange; y++) {
                    xBaseArr.push(xBase - y * yInterval);
                }
                for (var i = 0; i < columnEls.length; i++) {
                    var columnEl = columnEls[i];
                    var xBase = xBaseArr[i % yRange];
                    var x = parseFloat(columnEl.getAttribute('x')) - offset;
                    if (x < xBase) {
                        x = x + rectWidth;
                    } else if (x > (xBase + rectWidth)) {
                        x = x - rectWidth;
                    }
                    columnEl.setAttribute('x', x);
                }
            }
            gEl.setAttribute("phase", value);
        },

        /** 切换单位，mv|dB转% **/
        tanslateZUnit: function (svgEl) {
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** 幅值范围 **/
            var yRange = gEl.getAttribute('yRange');
            /** 百分号 **/
            var percent = "%";

            /** 最大值 **/
            var yDesc = svgEl.getElementById('yDesc');
            var unit = yDesc.textContent.substr(yDesc.textContent.indexOf('[') + 1, yDesc.textContent.length).replace(']', '');

            var textEls = svgEl.querySelectorAll('text');
            for (var i = 0; i < textEls.length; i++) {
                if (textEls[i].getAttribute('type') == "yAxisNum") {
                    if (unit != percent) {
                        newUnit = percent;
                        textEls[i].textContent = (parseFloat(textEls[i].textContent) / parseFloat(yRange)) * 100;
                    } else {
                        /** 幅值单位 **/
                        newUnit = gEl.getAttribute('yUnit');
                        textEls[i].textContent = (parseFloat(textEls[i].textContent) / 100) * parseFloat(yRange);
                    }
                }
            }
            yDesc.textContent = yDesc.textContent.replace(unit, newUnit);
        }
    };

    /** 图谱-幅值 **/
    _self.AMPLITUDE = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "AMPLITUDE",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 标题字号 **/
            titleFontSize: 24,

            /** 是否显示标题 **/
            showTitle: true,

            /**  每张图之间的间距  **/
            chartGapVal: 100,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            verticalLineSum: 9,

            /**  坐标轴标注线长度  **/
            labelLineLength: 4,

            /**  间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 线条颜色 **/
            lineColor: '#999',

            /** 线条宽度 **/
            lineWidth: 1,

            /**  数据柱条的宽度  **/
            dataRectHeight: 10,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,
        },

        /** 绘制 **/
        draw: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);

            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "amplitude",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /**  Y轴长度  **/
            var yAxisLength = options.chartHeight * 0.1;

            /**  X轴长度  **/
            var xAxisLength = options.chartWidth * 0.8;

            /**  绘制幅值图谱  **/
            for (var chartIndex = 0; chartIndex < chartBody.length; chartIndex++) {
                /**  记录每张图与页面顶部之间的间距  **/
                var chartY = chartTitle_y + titleFontOffset.height + options.chartGapVal * chartIndex;

                /**  每张图对应的数据  **/
                var chartData = chartBody[chartIndex];

                /**  绘制XOY面  **/
                /**  绘制Y轴  **/
                var yAxisLine_x_from = (options.chartWidth - xAxisLength) / 2;
                var yAxisLine_y_from = chartY;
                var yAxisLine_x_to = yAxisLine_x_from;
                var yAxisLine_y_to = chartY + yAxisLength;
                var yAxisLineEle = creatSvgElement('line', {
                    x1: yAxisLine_x_from,
                    x2: yAxisLine_x_to,
                    y1: yAxisLine_y_from,
                    y2: yAxisLine_y_to,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(yAxisLineEle);

                /**  绘制X轴  **/
                var xAxisLine_x_from = yAxisLine_x_to;
                var xAxisLine_y_from = yAxisLine_y_to;
                var xAxisLine_x_to = xAxisLine_x_from + xAxisLength;
                var xAxisLine_y_to = xAxisLine_y_from;
                var xAxisLineEle = creatSvgElement('line', {
                    x1: xAxisLine_x_from,
                    x2: xAxisLine_x_to,
                    y1: xAxisLine_y_from,
                    y2: xAxisLine_y_to,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(xAxisLineEle);

                /**  绘制X轴标注线  **/
                /**  背景矩形竖向线条间间隔  **/
                var verticalLineInterval = xAxisLength / (options.verticalLineSum + 1);
                for (var xAxisLabelIndex = 0; xAxisLabelIndex <= options.verticalLineSum + 1; xAxisLabelIndex++) {
                    var lineXFrom = xAxisLine_x_from + verticalLineInterval * xAxisLabelIndex;
                    if (xAxisLabelIndex != 0) {
                        lineXFrom = lineXFrom - options.lineWidth;
                    }
                    var lineYFrom = xAxisLine_y_from;
                    var lineXTo = lineXFrom;
                    var lineYTo = lineYFrom + options.labelLineLength;
                    var lineXEle = creatSvgElement('line', {
                        x1: lineXFrom,
                        x2: lineXTo,
                        y1: lineYFrom,
                        y2: lineYTo,
                        stroke: options.lineColor,
                        "stroke-width": options.lineWidth
                    });
                    svgEl.appendChild(lineXEle);
                }

                /**  绘制X轴最大值和最小值  **/
                var xUnit = chartData.axisInfo.xUnit;
                var xRangeMin = chartData.axisInfo.xRangeMin;
                var xRangeMinFontOffSet = getFontOffset(xRangeMin, options.axisNumFontSize);
                var xRangeMin_x = xAxisLine_x_from;
                var xRangeMin_y = xAxisLine_y_from + options.labelLineLength + xRangeMinFontOffSet.height / 2 + options.gapVal;
                var xRangeMinEle = creatSvgElement('text', {
                    x: xRangeMin_x,
                    y: xRangeMin_y,
                    text: xRangeMin + xUnit,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xRangeMinEle);

                var xRangeMax = chartData.axisInfo.xRangeMax;
                var xRangeMaxFontOffSet = getFontOffset(xRangeMax, options.axisNumFontSize);
                var xRangeMax_x = xAxisLine_x_to - xRangeMaxFontOffSet.width / 2;
                var xRangeMax_y = xAxisLine_y_to + options.labelLineLength + xRangeMaxFontOffSet.height / 2 + options.gapVal;
                var xRangeMaxEle = creatSvgElement('text', {
                    x: xRangeMax_x,
                    y: xRangeMax_y,
                    text: xRangeMax + xUnit,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisNumFontSize) + "px"
                    }
                });
                svgEl.appendChild(xRangeMaxEle);

                /**  绘制X轴坐标名  **/

                var xDesc = chartData.axisInfo.xDesc;
                var axisDescFontOffSet = getFontOffset(xDesc, options.axisDescFontSize);
                var xLabel = xDesc;

                var xDesc_x = (xAxisLine_x_from + xAxisLength) / 2 - axisDescFontOffSet.width / 2;
                var xDesc_y = xRangeMax_y + axisDescFontOffSet.height / 2 + options.gapVal;
                var xDescEle = creatSvgElement('text', {
                    x: xDesc_x,
                    y: xDesc_y,
                    text: xLabel,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(xDescEle);

                /**  绘制数据线条  **/
                var dataList = chartData.dataList;
                if (typeof (dataList) != "undefined" && dataList.length != 0) {

                    /**  测量值标注是否需要中间位置定位  **/
                    var isTestDataTextOffset = false;
                    /**  背景值标注是否需要中间位置定位  **/
                    var isBgDataTextOffset = false;

                    var zero_x = xAxisLine_x_from + options.lineWidth;
                    var zero_y = xAxisLine_y_from - yAxisLength / 2 - options.dataRectHeight / 2;
                    /**  X轴量程  **/
                    var xRange = xRangeMax - xRangeMin;

                    /**  测量值  **/
                    var testData = dataList[0];
                    var testData_x = testData.x;
                    testData_x = Math.round(testData_x * 100) / 100;
                    var testDataRectWidth = (testData_x - xRangeMin) / xRange * xAxisLength;

                    /**  测量值数据标注  **/
                    var testDataVal = testData_x;
                    var testDataFontOffSet = getFontOffset(testDataVal, options.axisNumFontSize);
                    var testDataValWidth = testDataFontOffSet.width;
                    var testDataText_x = zero_x + testDataRectWidth;
                    var testDataText_y = zero_y - 5;

                    if ((testDataText_x - testDataValWidth / 2) >= zero_x) {
                        isTestDataTextOffset = true;
                    }

                    /**  背景值  **/
                    var bgData = dataList[1];
                    var bgData_x = bgData.x;
                    bgData_x = Math.round(bgData_x * 100) / 100;
                    var bgDataRectWidth = (bgData_x - xRangeMin) / xRange * xAxisLength;

                    /**  背景值数据标注  **/
                    var bgDataVal = bgData_x;
                    var bgDataValFontOffSet = getFontOffset(bgDataVal, options.axisNumFontSize);
                    var bgDataValWidth = bgDataValFontOffSet.width;
                    var bgDataText_x = zero_x + bgDataRectWidth;
                    var bgDataText_y = zero_y - 5;

                    if ((bgDataText_x - bgDataValWidth / 2) >= zero_x) {
                        isBgDataTextOffset = true;
                    }

                    /**  判断背景值标注是否需要移到数据条下方（在坐标左边的值以右下角点的坐标来判断，在坐标右边的值以左下角点的坐标来判断）  **/
                    var testDataTextFianl_x = testDataText_x;
                    var bgDataTextFianl_x = bgDataText_x;
                    /**  计算右下角点坐标的偏移值  **/
                    var testDataTextTempOffset = testDataValWidth / 2;
                    var bgDataTextTempOffset = bgDataValWidth / 2;

                    if (isTestDataTextOffset) {
                        testDataTextFianl_x = testDataTextFianl_x - testDataValWidth / 2;
                        testDataTextTempOffset = testDataValWidth;
                    }
                    if (isBgDataTextOffset) {
                        bgDataTextFianl_x = bgDataTextFianl_x - bgDataValWidth / 2;
                        bgDataTextTempOffset = bgDataValWidth;
                    }

                    if (testData_x > bgData_x) {
                        /**  背景值在测量值左边  **/
                        bgDataTextFianl_x = bgDataTextFianl_x + bgDataTextTempOffset;
                        if (bgDataTextFianl_x >= testDataTextFianl_x) {
                            bgDataText_y = zero_y + options.dataRectHeight + 5 + bgDataValFontOffSet.height;
                        }
                    } else {
                        /**  背景值在测量值右边  **/
                        testDataTextFianl_x = testDataTextFianl_x + testDataTextTempOffset;
                        if (testDataTextFianl_x >= bgDataTextFianl_x) {
                            testDataText_y = zero_y + options.dataRectHeight + 5 + testDataFontOffSet.height;
                        }
                    }

                    /**  绘制数据条和标注  **/
                    if (testData_x >= bgData_x) {
                        /**  测量值大于背景值，先绘制测量值  **/
                        /**  绘制测量值  **/
                        var testDataRectEle = creatSvgElement('rect', {
                            x: zero_x,
                            y: zero_y,
                            width: testDataRectWidth,
                            height: options.dataRectHeight,
                            fill: testData.color,
                            stroke: testData.color,
                            opacity: 1
                        });
                        svgEl.appendChild(testDataRectEle);

                        if (testData_x > xRangeMin) {
                            var testDataTextEle = creatSvgElement('text', {
                                x: testDataText_x,
                                y: testDataText_y,
                                text: testDataVal,
                                fill: options.textColor,
                                style: {
                                    "font-size": parseFloat(options.axisNumFontSize) + "px",
                                    "text-anchor": isTestDataTextOffset ? "middle" : ""
                                }
                            });
                            svgEl.appendChild(testDataTextEle);
                        }

                        /**  绘制背景值  **/
                        var bgDataRectEle = creatSvgElement('rect', {
                            x: zero_x,
                            y: zero_y,
                            width: bgDataRectWidth,
                            height: options.dataRectHeight,
                            fill: bgData.color,
                            stroke: bgData.color,
                            opacity: 1
                        });
                        svgEl.appendChild(bgDataRectEle);

                        if (bgData_x > xRangeMin) {
                            var bgDataTextEle = creatSvgElement('text', {
                                x: bgDataText_x,
                                y: bgDataText_y,
                                text: bgDataVal,
                                fill: options.textColor,
                                style: {
                                    "font-size": parseFloat(options.axisNumFontSize) + "px",
                                    "text-anchor": isBgDataTextOffset ? "middle" : ""
                                }
                            });
                            svgEl.appendChild(bgDataTextEle);
                        }
                    } else {
                        /**  测量值小于背景值，先绘制背景值  **/
                        /**  绘制背景值  **/
                        var bgDataRectEle = creatSvgElement('rect', {
                            x: zero_x,
                            y: zero_y,
                            width: bgDataRectWidth,
                            height: options.dataRectHeight,
                            fill: bgData.color,
                            stroke: bgData.color,
                            opacity: 1
                        });
                        svgEl.appendChild(bgDataRectEle);

                        if (bgData_x > xRangeMin) {
                            var bgDataTextEle = creatSvgElement('text', {
                                x: bgDataText_x,
                                y: bgDataText_y,
                                text: bgDataVal,
                                fill: options.textColor,
                                style: {
                                    "font-size": parseFloat(options.axisNumFontSize) + "px",
                                    "text-anchor": isBgDataTextOffset ? "middle" : ""
                                }
                            });
                            svgEl.appendChild(bgDataTextEle);
                        }

                        /**  绘制测量值  **/
                        var testDataRectEle = creatSvgElement('rect', {
                            x: zero_x,
                            y: zero_y,
                            width: testDataRectWidth,
                            height: options.dataRectHeight,
                            fill: testData.color,
                            stroke: testData.color,
                            opacity: 1
                        });
                        svgEl.appendChild(testDataRectEle);

                        if (testData_x > xRangeMin) {
                            var testDataTextEle = creatSvgElement('text', {
                                x: testDataText_x,
                                y: testDataText_y,
                                text: testDataVal,
                                fill: options.textColor,
                                style: {
                                    "font-size": parseFloat(options.axisNumFontSize) + "px",
                                    "text-anchor": isTestDataTextOffset ? "middle" : ""
                                }
                            });
                            svgEl.appendChild(testDataTextEle);
                        }

                    }
                }
            }
            return svgEl;
        }
    };

    /** 图谱-波形 **/
    _self.WAVE = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "WAVE",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 曲线颜色 **/
            polylineColor: "#ff0000",

            /** 是否显示标题 **/
            showTitle: true,

            /** 是否显示触发幅值线 **/
            showTriggerLine: false,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            verticalLineSum: 3,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            crossLineSum: 3,

            /** 线条宽度 **/
            lineWidth: 1,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 图例线段长度  **/
            triggerLineLength: 20,
        },

        /** 绘制 **/
        draw: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);

            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "amplitude",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }
            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.7;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.65;

            /** 绘制XOY矩形  **/
            var rectXoy_x = (options.chartWidth - rectXoyWidth) / 2;
            var rectXoy_y = chartTitle_y + titleFontOffset.height + 10 - 30;
            var rectXoyEle = creatSvgElement('rect', {
                x: rectXoy_x,
                y: rectXoy_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                opacity: 1,
                fill: options.backgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEle);

            /** 绘制XOY面上的线条  **/
            /** 竖向  **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1)
            for (var verticalLineIndex = 1; verticalLineIndex <= options.verticalLineSum; verticalLineIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineIndex;
                var lineYFrom = rectXoy_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEle);
            }
            /** 横向  **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1)
            for (var crossLineIndex = 1; crossLineIndex <= options.crossLineSum; crossLineIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineIndex;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEle);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rectXoy_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** 绘制Y轴最小值  **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rectXoy_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rectXoy_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMinEle);

            /** 绘制Y轴坐标名  **/
            var yUnit = chartBody.axisInfo.yUnit;
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisDescFontSize);
            var yRangeMax_x = rectXoy_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rectXoy_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** X轴最大值  **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;

            /** X轴最小值  **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** 计算分度值  **/
            var xDivision = (xRangeMax - xRangeMin) / (options.verticalLineSum + 1);
            for (var xDivisionIndex = 0; xDivisionIndex <= options.verticalLineSum + 1; xDivisionIndex++) {
                var xAxisNum = xRangeMin + xDivision * xDivisionIndex;
                if (xAxisNum % 1 == 0) {
                    var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                    var xAxisNum_x = rectXoy_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * xDivisionIndex;
                    var xAxisNum_y = rectXoy_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                    var xRangeMinEle = creatSvgElement('text', {
                        x: xAxisNum_x,
                        y: xAxisNum_y,
                        text: xAxisNum,
                        fill: options.textColor,
                        style: {
                            "font-size": parseFloat(options.axisDescFontSize) + "px"
                        }
                    });
                    svgEl.appendChild(xRangeMinEle);
                }
            }


            /** X轴值单位  **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** 绘制X轴坐标名  **/
            var xDesc = chartBody.axisInfo.xDesc + "[" + xUnit + "]";
            var xDescFontOffset = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_x + rectXoyWidth / 2 - xDescFontOffset.width / 2;
            var xDesc_y = rectXoy_y + rectXoyHeight + xDescFontOffset.height * 2;
            var xDescEle = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEle);

            if (options.showTriggerLine) {
                /** 绘制触发值 **/
                var triggerLineText = chartBody.trigger.triggerDesc;
                var triggerLineTextFontOffset = getFontOffset(triggerLineText, options.axisDescFontSize);
                var triggerLineText_x = rectXoy_x + rectXoyWidth / 2 + options.gapVal / 2;
                var triggerLineText_y = xDesc_y + triggerLineTextFontOffset.height;
                var triggerLineTextEle = creatSvgElement('text', {
                    x: triggerLineText_x,
                    y: triggerLineText_y,
                    text: triggerLineText,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(triggerLineTextEle);

                /** 绘制触发值图例  **/
                var triggerLine_x_from = rectXoy_x + rectXoyWidth / 2 - options.gapVal / 2;
                var triggerLine_y_from = triggerLineText_y - triggerLineTextFontOffset.height / 4;
                var triggerLine_x_to = triggerLine_x_from - options.triggerLineLength;
                var triggerLine_y_to = triggerLine_y_from;
                /** 图例线段颜色  **/
                var triggerLineColor = chartBody.trigger.color;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }

            /** 记录下零点坐标  **/
            var zero_x = rectXoy_x;
            var zero_y = rectXoy_y + rectXoyHeight;

            /** X轴量程  **/
            var xRange = (chartBody.axisInfo.xRangeMax - chartBody.axisInfo.xRangeMin);// * chartBody.axisInfo.cycleValue;

            /** Y轴量程  **/
            var yRange = chartBody.axisInfo.yRangeMax - chartBody.axisInfo.yRangeMin;

            /** 获取触发值  **/
            var triggerValue = chartBody.trigger.triggerValue;

            if (options.showTriggerLine) {
                /** 绘制触发值线条  **/
                var triggerLine_x_from = zero_x;
                var triggerLine_y_from = zero_y - (triggerValue - yRangeMin) / yRange * rectXoyHeight;
                var triggerLine_x_to = zero_x + rectXoyWidth;
                var triggerLine_y_to = triggerLine_y_from;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }
            /** 准备数据数组  **/
            var dataList = chartBody.dataList;
            /** 绘制数据线条  **/
            if (typeof (dataList) != "undefined" && dataList.length != 0) {
                var points = "";
                for (var dataIndex = 0; dataIndex < dataList.length; dataIndex++) {
                    var data = dataList[dataIndex];
                    var x = data.x;
                    var y = data.y;

                    var data_x = zero_x + (x - xRangeMin) / xRange * rectXoyWidth;
                    var data_y = zero_y - (y - yRangeMin) / yRange * rectXoyHeight;

                    points = points + data_x + "," + data_y + " ";
                }
                var dataLineEle = creatSvgElement('polyline', {
                    points: points,
                    stroke: options.polylineColor,
                    fill: "none",
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(dataLineEle);
            }
            return svgEl;
        }
    };

    /** 图谱-相位 **/
    _self.PHASE = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "AMPLITUDE",

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 曲线颜色 **/
            polylineColor: "#0000ff",

            /** 是否显示标题 **/
            showTitle: true,

            /** 是否显示触发幅值线 **/
            showTriggerLine: false,

            /** 是否显示渐变密度条 **/
            showGradientRect: true,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /** 渐进色块的宽度  **/
            gradientRectWidth: 6,

            /** 渐进色块的分段数量  **/
            gradientRectSum: 8,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            verticalLineSum: 3,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            crossLineSum: 3,

            /** 线条宽度 **/
            lineWidth: 1,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 图例线段长度  **/
            triggerLineLength: 20,

            /** 圆点半径 **/
            circleSize: 2,
        },

        /** 绘制 **/
        draw: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);

            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "phase",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制渐变密度条 **/
            if (options.showGradientRect) {
                var defsEl = creatSvgElement('defs');
                var linearGradientEl = creatSvgElement('linearGradient', {
                    id: "linearGradient",
                    x1: 0,
                    y1: 1,
                    x2: 0,
                    y2: 0,
                });
                for (var i = 0; i < chartBody.densityColor.length; i++) {
                    var stopData = chartBody.densityColor[i];
                    var stopEl = creatSvgElement('stop', {
                        offset: Math.round(stopData.value * 10000) / 100 + "%",
                        "stop-color": stopData.color
                    });
                    linearGradientEl.appendChild(stopEl);
                }
                defsEl.appendChild(linearGradientEl);
                svgEl.appendChild(defsEl);
            }

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.7;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.7;

            /** 绘制XOY矩形  **/
            var rectXoy_x = (options.chartWidth - rectXoyWidth) / 2;
            var rectXoy_y = chartTitle_y + titleFontOffset.height * 2;
            var rectXoyEle = creatSvgElement('rect', {
                x: rectXoy_x,
                y: rectXoy_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                opacity: 1,
                fill: options.backgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEle);

            /** 绘制XOY面上的线条  **/
            /** 竖向  **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1)
            for (var verticalLineIndex = 1; verticalLineIndex <= options.verticalLineSum; verticalLineIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineIndex;
                var lineYFrom = rectXoy_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEle);
            }
            /** 横向  **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1)
            for (var crossLineIndex = 1; crossLineIndex <= options.crossLineSum; crossLineIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineIndex;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEle);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rectXoy_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** 绘制Y轴最小值  **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rectXoy_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rectXoy_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMinEle);

            /** 绘制Y轴坐标名  **/
            var yUnit = chartBody.axisInfo.yUnit;
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yDescEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yDescEle);

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisDescFontSize);
            var yRangeMax_x = rectXoy_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rectXoy_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** X轴最大值  **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;

            /** X轴最小值  **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;

            /** 计算分度值  **/
            var xDivision = (xRangeMax - xRangeMin) / (options.verticalLineSum + 1);
            for (var xDivisionIndex = 0; xDivisionIndex <= options.verticalLineSum + 1; xDivisionIndex++) {
                var xAxisNum = xRangeMin + xDivision * xDivisionIndex;
                var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                var xAxisNum_x = rectXoy_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * xDivisionIndex;
                var xAxisNum_y = rectXoy_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                var xRangeMinEle = creatSvgElement('text', {
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(xRangeMinEle);
            }


            /** X轴值单位  **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** 绘制X轴坐标名  **/
            var xDesc = chartBody.axisInfo.xDesc + "[" + xUnit + "]";
            var xDescFontOffset = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_x + rectXoyWidth / 2 - xDescFontOffset.width / 2;
            var xDesc_y = rectXoy_y + rectXoyHeight + xDescFontOffset.height * 2;
            var xDescEle = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEle);

            if (options.showTriggerLine) {
                /** 绘制触发值 **/
                var triggerLineText = chartBody.trigger.triggerDesc;
                var triggerLineTextFontOffset = getFontOffset(triggerLineText, options.axisDescFontSize);
                var triggerLineText_x = rectXoy_x + rectXoyWidth / 2 + options.gapVal / 2;
                var triggerLineText_y = xDesc_y + triggerLineTextFontOffset.height;
                var triggerLineTextEle = creatSvgElement('text', {
                    x: triggerLineText_x,
                    y: triggerLineText_y,
                    text: triggerLineText,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(triggerLineTextEle);

                /** 绘制触发值图例  **/
                var triggerLine_x_from = rectXoy_x + rectXoyWidth / 2 - options.gapVal / 2;
                var triggerLine_y_from = triggerLineText_y - triggerLineTextFontOffset.height / 4;
                var triggerLine_x_to = triggerLine_x_from - options.triggerLineLength;
                var triggerLine_y_to = triggerLine_y_from;

                /** 图例线段颜色  **/
                var triggerLineColor = chartBody.trigger.color;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }


            /** 绘制正弦曲线 **/
            var pathEl = creatSvgElement('path', {
                d: "M" + rectXoy_x + ',' + (rectXoy_y + rectXoyHeight / 2) + ' Q' + (rectXoy_y + rectXoyWidth / 4) + ',' + (rectXoy_y - rectXoyHeight / 2) + ' ' + (rectXoy_x + rectXoyWidth / 2) + ',' + (rectXoy_y + rectXoyHeight / 2) + ' T' + (rectXoy_x + rectXoyWidth) + ',' + (rectXoy_y + rectXoyHeight / 2),
                stroke: options.polylineColor,
                fill: "none",
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(pathEl);

            if (options.showGradientRect) {
                /** 绘制右侧渐进色块  **/
                var gradientRect_x = rectXoy_x + rectXoyWidth + options.labelLineLength;
                var gradientRect_y = rectXoy_y - options.lineWidth;
                var gradientRectHeight = rectXoyHeight + options.lineWidth;
                var gradientEle = creatSvgElement('rect', {
                    x: gradientRect_x,
                    y: gradientRect_y,
                    width: options.gradientRectWidth,
                    height: gradientRectHeight,
                    opacity: 1,
                    fill: "url('#linearGradient')",
                });
                svgEl.appendChild(gradientEle);

                /** 绘制渐进色块上的分割线和标注  **/
                var gradientRectLabel_x_from = gradientRect_x;
                var gradientRectLabel_x_to = gradientRect_x + options.gradientRectWidth;
                var gradientRectDivision = gradientRectHeight / options.gradientRectSum;
                for (var gradientRectIndex = 0; gradientRectIndex <= options.gradientRectSum; gradientRectIndex++) {
                    /** 绘制渐进色块上的分割线  **/
                    var gradientRectLabel_y = gradientRect_y + gradientRectHeight - gradientRectDivision * gradientRectIndex
                    var gradientLineEle = creatSvgElement('line', {
                        x1: gradientRectLabel_x_from,
                        x2: gradientRectLabel_x_to,
                        y1: gradientRectLabel_y,
                        y2: gradientRectLabel_y,
                        stroke: options.backgroundColor,
                        "stroke-width": options.lineWidth
                    });
                    svgEl.appendChild(gradientLineEle);
                    /** 绘制渐进色块上的标注  **/
                    var gradientRectText = options.gradientRectSum * gradientRectIndex;
                    var gradientRectTextFontOffset = getFontOffset(gradientRectText, options.axisNumFontSize);
                    var gradientRectText_x = gradientRectLabel_x_to + options.gapVal;
                    var gradientRectText_y = gradientRectLabel_y + gradientRectTextFontOffset.height / 4;
                    var gradientRectTextEle = creatSvgElement('text', {
                        x: gradientRectText_x,
                        y: gradientRectText_y,
                        text: gradientRectText,
                        fill: options.textColor,
                        style: {
                            "font-size": parseFloat(options.axisDescFontSize) + "px"
                        }
                    });
                    svgEl.appendChild(gradientRectTextEle);
                }
            }

            /** 记录下零点坐标  **/
            var zero_x = rectXoy_x;
            var zero_y = rectXoy_y + rectXoyHeight;

            /** X轴量程  **/
            var xRange = chartBody.axisInfo.xRangeMax - chartBody.axisInfo.xRangeMin;
            /** x轴单位间隔 **/
            var xInterval = rectXoyWidth / xRange;

            /** Y轴量程  **/
            var yRange = chartBody.axisInfo.yRangeMax - chartBody.axisInfo.yRangeMin;
            /** x轴单位间隔 **/
            var yInterval = rectXoyHeight / yRange;

            var zRangeMin = chartBody.axisInfo.zRangeMin;

            /** 获取触发值  **/
            var triggerValue = chartBody.trigger.triggerValue;

            if (options.showTriggerLine) {
                /** 绘制触发值线条  **/
                var triggerLine_x_from = zero_x;
                var triggerLine_y_from = zero_y - (triggerValue - yRangeMin) / yRange * rectXoyHeight;
                var triggerLine_x_to = zero_x + rectXoyWidth;
                var triggerLine_y_to = triggerLine_y_from;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }


            /** 准备数据数组  **/
            var dataList = chartBody.dataList;
            /** 绘制数据线条  **/
            if (typeof (dataList) != "undefined" && dataList.length != 0) {
                /** 数据组 **/
                var gEl = creatSvgElement('g', {
                    rectWidth: rectXoyWidth,
                    xInterval: xInterval,
                    xBase: zero_x,
                    phase: 0
                });
                for (var dataIndex = 0; dataIndex < dataList.length; dataIndex++) {
                    var data = dataList[dataIndex];
                    var x = data.x;
                    var y = data.y;
                    var z = data.z;

                    if (z <= zRangeMin) {
                        continue;
                    } else if (z >= 1) {
                        z = 1;
                    }

                    var data_x = zero_x + (x - xRangeMin) * xInterval;
                    var data_y = zero_y - (y - yRangeMin) * yInterval;

                    var dataLineEle = creatSvgElement('circle', {
                        cx: data_x,
                        cy: data_y,
                        r: options.circleSize,
                        fill: dataList[dataIndex].color,
                        //stroke: dataList[dataIndex].color,
                        "stroke-width": options.lineWidth
                    });
                    gEl.appendChild(dataLineEle);
                }
                svgEl.appendChild(gEl);
            }
            return svgEl;
        },

        /** 调整相位,value[0,xRange] **/
        changePhase: function (svgEl, value) {
            /** 数据组 **/
            var gEl = svgEl.querySelector('g');
            /** 图谱宽度 **/
            var rectWidth = parseFloat(gEl.getAttribute('rectWidth'));
            /** x轴单位间隔 **/
            var xInterval = parseFloat(gEl.getAttribute('xInterval'));
            /** X轴0点坐标 **/
            var xBase = parseFloat(gEl.getAttribute('xBase'));
            /** 已执行的相位偏移值 **/
            var phase = parseFloat(gEl.getAttribute('phase'));
            /** 相位偏移量 **/
            var offset = (value - phase) * xInterval;

            /** 所有的数据点 **/
            var circleEls = svgEl.querySelectorAll('circle');
            for (var i = 0; i < circleEls.length; i++) {
                var circleEl = circleEls[i];
                var cx = parseFloat(circleEl.getAttribute('cx')) - offset;
                if (cx < xBase) {
                    cx = cx + rectWidth;
                } else if (cx > (xBase + rectWidth)) {
                    cx = cx - rectWidth;
                }
                circleEl.setAttribute('cx', cx);
            }
            gEl.setAttribute("phase", value);
        },
    };

    /** 图谱-飞行 **/
    _self.FLY = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "AMPLITUDE",

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 曲线颜色 **/
            polylineColor: "#0000ff",

            /** 是否显示标题 **/
            showTitle: true,

            /** 是否显示触发幅值线 **/
            showTriggerLine: false,

            /** 是否显示渐变密度条 **/
            showGradientRect: true,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /** 渐进色块的宽度  **/
            gradientRectWidth: 6,

            /** 渐进色块的分段数量  **/
            gradientRectSum: 8,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            verticalLineSum: 3,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            crossLineSum: 3,

            /** 线条宽度 **/
            lineWidth: 1,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 图例线段长度  **/
            triggerLineLength: 20,

            /** 圆点半径 **/
            circleSize: 2,
        },

        /** 绘制 **/
        draw: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);

            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "amplitude",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制渐变密度条 **/
            if (options.showGradientRect) {
                var defsEl = creatSvgElement('defs');
                var linearGradientEl = creatSvgElement('linearGradient', {
                    id: "linearGradient",
                    x1: 0,
                    y1: 1,
                    x2: 0,
                    y2: 0,
                });
                /** 准备数据数组  **/
                var densityColor = chartBody.densityColor;
                /** 绘制数据线条  **/
                if (typeof (densityColor) != "undefined" && densityColor.length != 0) {
                    for (var i = 0; i < densityColor.length; i++) {
                        var stopData = densityColor[i];
                        var stopEl = creatSvgElement('stop', {
                            offset: Math.round(stopData.value * 10000) / 100 + "%",
                            "stop-color": stopData.color
                        });
                        linearGradientEl.appendChild(stopEl);
                    }
                }
                defsEl.appendChild(linearGradientEl);
                svgEl.appendChild(defsEl);
            }

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }

            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.7;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.7;

            /** 绘制XOY矩形  **/
            var rectXoy_x = (options.chartWidth - rectXoyWidth) / 2;
            var rectXoy_y = chartTitle_y + titleFontOffset.height * 2;
            var rectXoyEle = creatSvgElement('rect', {
                x: rectXoy_x,
                y: rectXoy_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                opacity: 1,
                fill: options.backgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEle);

            /** 绘制XOY面上的线条  **/
            /** 竖向  **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1)
            for (var verticalLineIndex = 1; verticalLineIndex <= options.verticalLineSum; verticalLineIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineIndex;
                var lineYFrom = rectXoy_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEle);
            }
            /** 横向  **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1)
            for (var crossLineIndex = 1; crossLineIndex <= options.crossLineSum; crossLineIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineIndex;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEle);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rectXoy_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** 绘制Y轴最小值  **/
            var yRangeMin = chartBody.axisInfo.yRangeMin;
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rectXoy_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rectXoy_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMinEle);

            /** 绘制Y轴坐标名  **/
            var yUnit = chartBody.axisInfo.yUnit;
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisDescFontSize);
            var yRangeMax_x = rectXoy_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rectXoy_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(yRangeMaxEle);

            /** X轴最大值  **/
            var xRangeMax = chartBody.axisInfo.xRangeMax;

            /** X轴最小值  **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;

            /** 计算分度值  **/
            var xDivision = (xRangeMax - xRangeMin) / (options.verticalLineSum + 1);
            for (var xDivisionIndex = 0; xDivisionIndex <= options.verticalLineSum + 1; xDivisionIndex++) {
                var xAxisNum = xRangeMin + xDivision * xDivisionIndex;
                var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                var xAxisNum_x = rectXoy_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * xDivisionIndex;
                var xAxisNum_y = rectXoy_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                var xRangeMinEle = creatSvgElement('text', {
                    x: xAxisNum_x,
                    y: xAxisNum_y,
                    text: xAxisNum,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(xRangeMinEle);
            }


            /** X轴值单位  **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** 绘制X轴坐标名  **/
            var xDesc = chartBody.axisInfo.xDesc + "[" + xUnit + "]";
            var xDescFontOffset = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_x + rectXoyWidth / 2 - xDescFontOffset.width / 2;
            var xDesc_y = rectXoy_y + rectXoyHeight + xDescFontOffset.height * 2;
            var xDescEle = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEle);

            if (options.showTriggerLine) {
                /** 绘制触发值 **/
                var triggerLineText = chartBody.trigger.triggerDesc;
                var triggerLineTextFontOffset = getFontOffset(triggerLineText, options.axisDescFontSize);
                var triggerLineText_x = rectXoy_x + rectXoyWidth / 2 + options.gapVal / 2;
                var triggerLineText_y = xDesc_y + triggerLineTextFontOffset.height;
                var triggerLineTextEle = creatSvgElement('text', {
                    x: triggerLineText_x,
                    y: triggerLineText_y,
                    text: triggerLineText,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(triggerLineTextEle);

                /** 绘制触发值图例  **/
                var triggerLine_x_from = rectXoy_x + rectXoyWidth / 2 - options.gapVal / 2;
                var triggerLine_y_from = triggerLineText_y - triggerLineTextFontOffset.height / 4;
                var triggerLine_x_to = triggerLine_x_from - options.triggerLineLength;
                var triggerLine_y_to = triggerLine_y_from;

                /** 图例线段颜色  **/
                var triggerLineColor = chartBody.trigger.color;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }

            if (options.showGradientRect) {
                /** 绘制右侧渐进色块  **/
                var gradientRect_x = rectXoy_x + rectXoyWidth + options.labelLineLength;
                var gradientRect_y = rectXoy_y - options.lineWidth;
                var gradientRectHeight = rectXoyHeight + options.lineWidth;
                var gradientEle = creatSvgElement('rect', {
                    x: gradientRect_x,
                    y: gradientRect_y,
                    width: options.gradientRectWidth,
                    height: gradientRectHeight,
                    opacity: 1,
                    fill: "url('#linearGradient')",
                });
                svgEl.appendChild(gradientEle);

                /** 绘制渐进色块上的分割线和标注  **/
                var gradientRectLabel_x_from = gradientRect_x;
                var gradientRectLabel_x_to = gradientRect_x + options.gradientRectWidth;
                var gradientRectDivision = gradientRectHeight / options.gradientRectSum;
                for (var gradientRectIndex = 0; gradientRectIndex <= options.gradientRectSum; gradientRectIndex++) {
                    /** 绘制渐进色块上的分割线  **/
                    var gradientRectLabel_y = gradientRect_y + gradientRectHeight - gradientRectDivision * gradientRectIndex
                    var gradientLineEle = creatSvgElement('line', {
                        x1: gradientRectLabel_x_from,
                        x2: gradientRectLabel_x_to,
                        y1: gradientRectLabel_y,
                        y2: gradientRectLabel_y,
                        stroke: options.backgroundColor,
                        "stroke-width": options.lineWidth
                    });
                    svgEl.appendChild(gradientLineEle);
                    /** 绘制渐进色块上的标注  **/
                    var gradientRectText = options.gradientRectSum * gradientRectIndex;
                    var gradientRectTextFontOffset = getFontOffset(gradientRectText, options.axisNumFontSize);
                    var gradientRectText_x = gradientRectLabel_x_to + options.gapVal;
                    var gradientRectText_y = gradientRectLabel_y + gradientRectTextFontOffset.height / 4;
                    var gradientRectTextEle = creatSvgElement('text', {
                        x: gradientRectText_x,
                        y: gradientRectText_y,
                        text: gradientRectText,
                        fill: options.textColor,
                        style: {
                            "font-size": parseFloat(options.axisDescFontSize) + "px"
                        }
                    });
                    svgEl.appendChild(gradientRectTextEle);
                }
            }

            /** 记录下零点坐标  **/
            var zero_x = rectXoy_x;
            var zero_y = rectXoy_y + rectXoyHeight;

            /** X轴量程  **/
            var xRange = chartBody.axisInfo.xRangeMax - chartBody.axisInfo.xRangeMin;

            /** Y轴量程  **/
            var yRange = chartBody.axisInfo.yRangeMax - chartBody.axisInfo.yRangeMin;

            /** 获取触发值  **/
            var triggerValue = chartBody.trigger.triggerValue;

            if (options.showTriggerLine) {
                /** 绘制触发值线条  **/
                var triggerLine_x_from = zero_x;
                var triggerLine_y_from = zero_y - (triggerValue - yRangeMin) / yRange * rectXoyHeight;
                var triggerLine_x_to = zero_x + rectXoyWidth;
                var triggerLine_y_to = triggerLine_y_from;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }

            /** 准备数据数组  **/
            var dataList = chartBody.dataList;
            /** 绘制数据线条  **/
            if (typeof (dataList) != "undefined" && dataList.length != 0) {
                /** 数据组 **/
                var gEl = creatSvgElement('g');
                for (var dataIndex = 0; dataIndex < dataList.length; dataIndex++) {
                    var data = dataList[dataIndex];
                    var x = data.x;
                    var y = data.y;

                    var data_x = zero_x + (x - xRangeMin) / xRange * rectXoyWidth;
                    var data_y = zero_y - (y - yRangeMin) / yRange * rectXoyHeight;

                    var dataLineEle = creatSvgElement('circle', {
                        cx: data_x,
                        cy: data_y,
                        r: options.circleSize,
                        fill: dataList[dataIndex].color,
                        stroke: dataList[dataIndex].color,
                        "stroke-width": options.lineWidth
                    });
                    gEl.appendChild(dataLineEle);
                }
                svgEl.appendChild(gEl);
            }
            return svgEl;
        }
    };

    /** 图谱-机械特性 **/
    _self.Mech = {
        /** 默认参数 **/
        initOptions: {
            /** 标题 **/
            title: "WAVE",

            /** 字体颜色 #666（黑色） **/
            textColor: '#333',

            /** 图谱背景颜色 **/
            backgroundColor: "#fff",

            /** 外框线条颜色 **/
            lineColor: '#999',

            /** 内部线条颜色 **/
            dashlineColor: '#d3d3d3',

            /** 曲线颜色 **/
            polylineColor: "#0000ff",

            /** 是否显示标题 **/
            showTitle: true,

            /** 是否显示Y轴标签 **/
            showYAxisLabel: true,

            /** 自动分配颜色 **/
            autoColor: true,

            /** 是否显示触发幅值线 **/
            showTriggerLine: false,

            /** 图谱高 **/
            chartHeight: 500,

            /** 图谱宽 **/
            chartWidth: 500,

            /** 标题字号 **/
            titleFontSize: 24,

            /** 坐标轴名字字号 **/
            axisDescFontSize: 18,

            /** 坐标轴数字字号 **/
            axisNumFontSize: 18,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            verticalLineSum: 3,

            /**  背景矩形上竖向线条数量（单个矩形上）  **/
            crossLineSum: 3,

            /** 线条宽度 **/
            lineWidth: 2,

            /** 坐标轴标注线长度  **/
            labelLineLength: 4,

            /** 间隙值，表示坐标值和坐标轴标注线之间的距离  **/
            gapVal: 4,

            /** 图例线段长度  **/
            triggerLineLength: 20,
        },

        /** 绘制 **/
        draw: function (chartBody, userOptions) {
            var options = setOptions(this.initOptions, userOptions);

            /** 初始化svg对象 **/
            var svgEl = creatSvgElement('svg', {
                xmlns: "http://www.w3.org/2000/svg",
                chart: "amplitude",
                initWidth: options.chartWidth,
                initHeight: options.chartHeight,
                viewBox: '0,0,' + options.chartWidth + ',' + options.chartHeight,
                style: {
                    "background-color": options.backgroundColor
                }
            });

            /** 绘制标题 **/
            var titleFontOffset = getFontOffset(options.title, options.titleFontSize);
            var chartTitle_x = options.chartWidth / 2 - titleFontOffset.width / 2;
            var chartTitle_y = options.titleFontSize + titleFontOffset.height / 2;
            if (options.showTitle) {
                var titleEl = creatSvgElement('text', {
                    x: chartTitle_x,
                    y: chartTitle_y,
                    text: options.title,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.titleFontSize) + "px"
                    }
                });
                svgEl.appendChild(titleEl);
            }
            /** XOY矩形宽  **/
            var rectXoyWidth = options.chartWidth * 0.8;

            /** XOY矩形高  **/
            var rectXoyHeight = options.chartHeight * 0.65;

            /** 绘制XOY矩形  **/
            var rectXoy_x = (options.chartWidth - rectXoyWidth) / 2;
            var rectXoy_y = chartTitle_y + titleFontOffset.height + 10 - 30;
            var rectXoyEle = creatSvgElement('rect', {
                x: rectXoy_x,
                y: rectXoy_y,
                width: rectXoyWidth,
                height: rectXoyHeight,
                opacity: 1,
                fill: options.backgroundColor,
                stroke: options.lineColor,
                "stroke-width": options.lineWidth
            });
            svgEl.appendChild(rectXoyEle);

            /** 绘制XOY面上的线条  **/
            /** 竖向  **/
            var verticalLineInterval = rectXoyWidth / (options.verticalLineSum + 1)
            for (var verticalLineIndex = 1; verticalLineIndex <= options.verticalLineSum; verticalLineIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineIndex;
                var lineYFrom = rectXoy_y;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + rectXoyHeight;
                var verticalLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineEle);
            }
            /** 横向  **/
            var crossLineInterval = rectXoyHeight / (options.crossLineSum + 1)
            for (var crossLineIndex = 1; crossLineIndex <= options.crossLineSum; crossLineIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineIndex;
                var lineXTo = lineXFrom + rectXoyWidth;
                var lineYTo = lineYFrom;
                var crossLineEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.dashlineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineEle);
            }

            /** 绘制Y轴标注线  **/
            for (var crossLineLabelIndex = 0; crossLineLabelIndex <= options.crossLineSum + 1; crossLineLabelIndex++) {
                var lineXFrom = rectXoy_x;
                var lineYFrom = rectXoy_y + crossLineInterval * crossLineLabelIndex;
                var lineXTo = lineXFrom - options.labelLineLength;
                var lineYTo = lineYFrom;
                var crossLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(crossLineLabelEle);
            }

            /** 绘制X轴标注线  **/
            for (var verticalLineLabelIndex = 0; verticalLineLabelIndex <= options.verticalLineSum + 1; verticalLineLabelIndex++) {
                var lineXFrom = rectXoy_x + verticalLineInterval * verticalLineLabelIndex;
                var lineYFrom = rectXoy_y + rectXoyHeight;
                var lineXTo = lineXFrom;
                var lineYTo = lineYFrom + options.labelLineLength;
                var verticalLineLabelEle = creatSvgElement('line', {
                    x1: lineXFrom,
                    x2: lineXTo,
                    y1: lineYFrom,
                    y2: lineYTo,
                    stroke: options.lineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(verticalLineLabelEle);
            }

            /** 绘制Y轴最小值  **/

            var yRangeMin = chartBody.axisInfo.yRangeMin;
            var yRangeMinFontOffset = getFontOffset(yRangeMin, options.axisDescFontSize);
            var yRangeMin_x = rectXoy_x - yRangeMinFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMin_y = rectXoy_y + rectXoyHeight;
            var yRangeMinEle = creatSvgElement('text', {
                x: yRangeMin_x,
                y: yRangeMin_y,
                text: yRangeMin === 0 ? "" : yRangeMin,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });

            if (options.showYAxisLabel) {
                svgEl.appendChild(yRangeMinEle);
            }


            /** 绘制Y轴坐标名  **/
            var yUnit = chartBody.axisInfo.yUnit;
            var yDesc = chartBody.axisInfo.yDesc;
            var yLabel = yDesc + "[" + yUnit + "]";
            var yDescFontOffset = getFontOffset(yLabel, options.axisDescFontSize);
            var yDesc_x = yRangeMin_x - yDescFontOffset.height / 2;
            var yDesc_y = rectXoy_y + rectXoyHeight / 2 + yDescFontOffset.width / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yDesc_x,
                y: yDesc_y,
                text: yLabel,
                fill: options.textColor,
                transform: "rotate(-90," + yDesc_x + " " + yDesc_y + ")",
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });

            if (options.showYAxisLabel) {
                svgEl.appendChild(yRangeMaxEle);
            }

            /** 绘制Y轴最大值  **/
            var yRangeMax = chartBody.axisInfo.yRangeMax;
            var yRangeMaxFontOffset = getFontOffset(yRangeMax, options.axisDescFontSize);
            var yRangeMax_x = rectXoy_x - yRangeMaxFontOffset.width - options.labelLineLength - options.gapVal;
            var yRangeMax_y = rectXoy_y + yRangeMaxFontOffset.height / 2;
            var yRangeMaxEle = creatSvgElement('text', {
                x: yRangeMax_x,
                y: yRangeMax_y,
                text: yRangeMax === 0 ? 1 : yRangeMax,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });

            if (options.showYAxisLabel) {
                svgEl.appendChild(yRangeMaxEle);
            }

            /** X轴最大值  **/
            var xRangeMax = chartBody.axisInfo.xRangeMax === 0 ? 1 : chartBody.axisInfo.xRangeMax;

            /** X轴最小值  **/
            var xRangeMin = chartBody.axisInfo.xRangeMin;
            /** 计算分度值  **/
            var xDivision = (xRangeMax - xRangeMin) / (options.verticalLineSum + 1);
            for (var xDivisionIndex = 0; xDivisionIndex <= options.verticalLineSum + 1; xDivisionIndex++) {
                var xAxisNum = xRangeMin + xDivision * xDivisionIndex;
                //if (xAxisNum % 1 == 0) {
                if (true) {
                    var xAxisNumFontOffset = getFontOffset(xAxisNum, options.axisNumFontSize);
                    var xAxisNum_x = rectXoy_x - xAxisNumFontOffset.width / 2 + verticalLineInterval * xDivisionIndex;
                    var xAxisNum_y = rectXoy_y + rectXoyHeight + options.labelLineLength + options.gapVal + xAxisNumFontOffset.height / 2;
                    var xRangeMinEle = creatSvgElement('text', {
                        x: xAxisNum_x,
                        y: xAxisNum_y,
                        text: xAxisNum,
                        fill: options.textColor,
                        style: {
                            "font-size": parseFloat(options.axisDescFontSize) + "px"
                        }
                    });
                    svgEl.appendChild(xRangeMinEle);
                }
            }


            /** X轴值单位  **/
            var xUnit = chartBody.axisInfo.xUnit;
            /** 绘制X轴坐标名  **/
            var xDesc = chartBody.axisInfo.xDesc + "[" + xUnit + "]";
            var xDescFontOffset = getFontOffset(xDesc, options.axisDescFontSize);
            var xDesc_x = rectXoy_x + rectXoyWidth / 2 - xDescFontOffset.width / 2;
            var xDesc_y = rectXoy_y + rectXoyHeight + xDescFontOffset.height * 2;
            var xDescEle = creatSvgElement('text', {
                x: xDesc_x,
                y: xDesc_y,
                text: xDesc,
                fill: options.textColor,
                style: {
                    "font-size": parseFloat(options.axisDescFontSize) + "px"
                }
            });
            svgEl.appendChild(xDescEle);

            if (options.showTriggerLine) {
                /** 绘制触发值 **/
                var triggerLineText = chartBody.trigger.triggerDesc;
                var triggerLineTextFontOffset = getFontOffset(triggerLineText, options.axisDescFontSize);
                var triggerLineText_x = rectXoy_x + rectXoyWidth / 2 + options.gapVal / 2;
                var triggerLineText_y = xDesc_y + triggerLineTextFontOffset.height;
                var triggerLineTextEle = creatSvgElement('text', {
                    x: triggerLineText_x,
                    y: triggerLineText_y,
                    text: triggerLineText,
                    fill: options.textColor,
                    style: {
                        "font-size": parseFloat(options.axisDescFontSize) + "px"
                    }
                });
                svgEl.appendChild(triggerLineTextEle);

                /** 绘制触发值图例  **/
                var triggerLine_x_from = rectXoy_x + rectXoyWidth / 2 - options.gapVal / 2;
                var triggerLine_y_from = triggerLineText_y - triggerLineTextFontOffset.height / 4;
                var triggerLine_x_to = triggerLine_x_from - options.triggerLineLength;
                var triggerLine_y_to = triggerLine_y_from;
                /** 图例线段颜色  **/
                var triggerLineColor = chartBody.trigger.color;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }

            /** 记录下零点坐标  **/
            var zero_x = rectXoy_x;
            var zero_y = rectXoy_y + rectXoyHeight;

            /** X轴量程  **/
            var xRange = (chartBody.axisInfo.xRangeMax - chartBody.axisInfo.xRangeMin);// * chartBody.axisInfo.cycleValue;

            /** Y轴量程  **/
            var yRange = chartBody.axisInfo.yRangeMax - chartBody.axisInfo.yRangeMin;

            /** 获取触发值  **/
            var triggerValue = null;

            if (options.showTriggerLine) {
                triggerValue = chartBody.trigger.triggerValue;
                /** 绘制触发值线条  **/
                var triggerLine_x_from = zero_x;
                var triggerLine_y_from = zero_y - (triggerValue - yRangeMin) / yRange * rectXoyHeight;
                var triggerLine_x_to = zero_x + rectXoyWidth;
                var triggerLine_y_to = triggerLine_y_from;
                var triggerLineEle = creatSvgElement('line', {
                    x1: triggerLine_x_from,
                    x2: triggerLine_x_to,
                    y1: triggerLine_y_from,
                    y2: triggerLine_y_to,
                    stroke: triggerLineColor,
                    "stroke-width": options.lineWidth
                });
                svgEl.appendChild(triggerLineEle);
            }
            /** 准备数据数组  **/

            var lineColor = ["#FFCC00",
                "#00CC00",
                "#FF0000",
                "#0000FF",
                "#FF33FF",
                "#993366"];
            var dataSeries = chartBody.dataSeries;
            /** 绘制数据线条  **/
            if (typeof (dataSeries) != "undefined" && dataSeries.length != 0) {
                for (var seriesIndex = 0; seriesIndex < dataSeries.length; seriesIndex++) {
                    var points = "";
                    var dataList = chartBody.dataSeries[seriesIndex].dataList;
                    for (var dataIndex = 0; dataIndex < dataList.length; dataIndex++) {
                        var data = dataList[dataIndex];
                        var x = data.x;
                        var y = data.y;

                        var data_x = zero_x + (x - xRangeMin) / xRange * rectXoyWidth;
                        var data_y = zero_y - (y - yRangeMin) / yRange * rectXoyHeight;

                        points = points + data_x + "," + data_y + " ";
                    }
                    var dataLineEle = creatSvgElement('polyline', {
                        points: points,
                        // stroke: options.polylineColor[seriesIndex],
                        stroke: options.autoColor ? lineColor[seriesIndex] : chartBody.dataSeries[seriesIndex].dataSeriesColor,
                        fill: "none",
                        "stroke-width": options.lineWidth
                    });
                    svgEl.appendChild(dataLineEle);
                }
            }
            return svgEl;
        }
    };

    /** 向目标元素中添加svg图谱 **/
    _self.loadChart = function (targetEl, svgEl, userOptions) {
        var initOptions = {
            id: "pdchart",
            append: false,
            saveAsImage: false,
            width: parseFloat(targetEl.clientWidth),
            height: Math.max(parseFloat(targetEl.clientWidth), parseFloat(targetEl.clientHeight)),
        }
        var options = setOptions(initOptions, userOptions);

        if (options.saveAsImage) {
            var saveAsImageEl_x = parseFloat(svgEl.getAttribute('initWidth')) - 40;
            var saveAsImageEl_y = 10;
            var saveAsImageEl = creatSvgElement('g', {
                id: "saveAsImage",
                opacity: .6,
                style: { "cursor": "pointer" },
                transform: "translate(" + saveAsImageEl_x + "," + saveAsImageEl_y + ") scale(0.05)"
            });

            var pathEl1 = creatSvgElement('path', {
                d: "m471.46,306.2c15,0,10.205,0.8036,10.205,15.804v124.61c0,15,2.9284,23.334-12.071,23.268l-421.72-1.87c-15-0.0664-16.804,1.0625-16.804-13.938v-132.07c0-15-1.9285-15.804,13.071-15.804s12.07,0.92026,13.938,15.804v120.87l401.32,1.8661v-122.73c0-15-2.9285-15.804,12.071-15.804z"
            });
            saveAsImageEl.appendChild(pathEl1);

            var pathEl2 = creatSvgElement('path', {
                d: "m236,350.4-110-110c-11-11-14.732-13.938-3.7321-24.938s10.205-7.2679,21.205,3.7321l99.455,98.455v-288.12c0-15-1.0625-13.938,13.938-13.938s13.84,0.80392,13.938,15.804l1.8661,286.25,90.991-92.857c10.888-11.111,7.4733-12.866,18.473-1.8661s12.866,6.4733,1.8661,17.473l-110,110c-5,5-12,8-19,8s-14-3-19-8z"
            });
            saveAsImageEl.appendChild(pathEl2);

            saveAsImageEl.addEventListener('click', function () {
                _self.saveAsImage(svgEl, 'image/png', function (dataUrl) {
                    saveAsImageEl.setAttribute('display', '');
                    var fileName = svgEl.getAttribute('id') + '_' + new Date().getTime();
                    var link = document.createElement('a');
                    link.setAttribute('download', fileName);
                    link.setAttribute('href', dataUrl);
                    link.click();
                });
            });
            svgEl.appendChild(saveAsImageEl);
        }

        svgEl.setAttribute('id', options.id);
        svgEl.setAttribute('width', options.width);
        svgEl.setAttribute('height', options.height);

        if (options.append == false) {
            var childNodes = targetEl.childNodes;
            for (var i = 0; i < childNodes.length; i++) {
                targetEl.removeChild(childNodes[i]);
            }
        }
        targetEl.appendChild(svgEl);
    };

    /** 平移 **/
    _self.localtion = function (svgEl, x, y) {
        var viewBox = svgEl.getAttribute('viewBox');
        if (viewBox != null) {
            var arr = viewBox.split(',');
            var intWidth = parseFloat(svgEl.getAttribute('width'));
            var intHeight = parseFloat(svgEl.getAttribute('height'));
            var svgWidth = parseFloat(arr[2]);
            var svgHeight = parseFloat(arr[3]);
            var oX = parseFloat(arr[0]) + parseFloat(x);
            var oY = parseFloat(arr[1]) + parseFloat(y);
            /** 判断是否超出范围 **/
            if (oX < intWidth - 20 && oY < intHeight - 20 && oX > 20 - intWidth && oY > 20 - intHeight) {
                viewBox = oX + "," + oY + "," + svgWidth + "," + svgHeight;
                svgEl.setAttribute("viewBox", viewBox);
            } else {
                oX = arr[0];
                oY = arr[1];
            }
        }

    };

    /** 缩放 **/
    _self.zoom = function (svgEl, size) {
        var viewBox = svgEl.getAttribute('viewBox');
        if (viewBox != null) {
            var arr = viewBox.split(',');
            var intWidth = parseFloat(svgEl.getAttribute('width'));
            var intHeight = parseFloat(svgEl.getAttribute('height'));
            var svgWidth = parseFloat(arr[2]);
            var svgHeight = parseFloat(arr[3]);
            var oX = parseFloat(arr[0]) + size / 2;
            var oY = parseFloat(arr[1]) + size / 2;
            svgWidth = parseFloat(svgWidth) - size;
            svgHeight = parseFloat(svgHeight) - size;

            /** 判断是否超出范围 **/
            if (svgWidth > intWidth * 0.5 && svgWidth < intWidth * 2) {
                viewBox = oX + "," + oY + "," + svgWidth + "," + svgHeight;
                svgEl.setAttribute("viewBox", viewBox);
            } else {
                svgWidth = arr[2];
                svgHeight = arr[3];
            }
        }
    };

    /** 重置 **/
    _self.reset = function (svgEl) {
        var initWidth = svgEl.getAttribute('initWidth');
        var initHeight = svgEl.getAttribute('initHeight');
        var oX = oY = 0;
        viewBoxValue = oX + "," + oY + "," + initWidth + "," + initWidth;
        svgEl.setAttribute("viewBox", viewBoxValue);
    };

    /** 保存成png格式图片 **/
    _self.saveAsImage = function (svgEl, type, callback) {
        svgEl.setAttribute('display', '');
        svgEl.toDataURL(type, { callback: callback });
    };

    /** 对比用户配置和默认配置 **/
    function setOptions(initOptions, userOptions) {
        /** 遍历用户自定义配置**/
        for (var option in userOptions) {
            /** 判断默认配置中是否包含用户定义的配置项**/
            if (initOptions.hasOwnProperty(option)) {
                /** 如果包含，则替换默认配置项**/
                initOptions[option] = userOptions[option];
            }
        }
        return initOptions;
    };

    /** 创建svg元素 **/
    function creatSvgElement(elementType, attributes) {
        var element = document.createElementNS("http://www.w3.org/2000/svg", elementType);
        for (var attr in attributes) {
            if (attr == "style") {
                var style = "";
                var styleObj = attributes[attr];
                for (var item in styleObj) {
                    style += item + ":" + styleObj[item] + "; "
                }
                element.setAttribute(attr, style);
            } else if (attr == "text") {
                var text = document.createTextNode(attributes[attr]);
                element.appendChild(text);
            } else {
                element.setAttribute(attr, attributes[attr]);
            }
        }
        return element;
    }

    /** 获取字符文本宽度和高度 **/
    function getFontOffset(text, fontSize) {
        var div = document.createElement("div");
        try {
            div.setAttribute('style', 'visibility: hidden; position: absolute;font-size:' + parseFloat(fontSize) + 'px');
            div.appendChild(document.createTextNode(text));
            document.body.appendChild(div);
            return { width: div.offsetWidth, height: div.offsetHeight };
        } finally {
            document.body.removeChild(div);
            div = null;
        }

    }
};

/**
 *  Add toDataURL prototype for SVGElement （need canvg.js）
 *  parmeters: type:svg,png,jpeg
 *             options:callback,renderer
 *  return: base64 string
 */
SVGElement.prototype.toDataURL = function (type, options) {
    var svg = this;

    function debug(s) {
        console.log("SVG.toDataURL:", s);
    }

    function exportSVG() {
        var svg_xml = XMLSerialize(svg);
        var svg_dataurl = base64dataURLencode(svg_xml);
        debug(type + " length: " + svg_dataurl.length);

        // NOTE double data carrier
        if (options.callback) {
            options.callback(svg_dataurl);
        }
        return svg_dataurl;
    }

    function exportImage(type) {
        var canvas = document.createElement("canvas");
        var ctx = canvas.getContext('2d');

        var svg_img = new Image();
        var svg_xml = XMLSerialize(svg);
        svg_img.src = base64dataURLencode(svg_xml);

        svg_img.onload = function () {
            debug("exported image size: " + [svg_img.width, svg_img.height]);
            canvas.width = svg_img.width;
            canvas.height = svg_img.height;
            ctx.drawImage(svg_img, 0, 0);

            // SECURITY_ERR WILL HAPPEN NOW
            var png_dataurl = canvas.toDataURL(type);
            debug(type + " length: " + png_dataurl.length);

            if (options.callback) {
                options.callback(png_dataurl);
            } else {
                debug("WARNING: no callback set, so nothing happens.");
            }
        };

        svg_img.onerror = debug(
            "Can't export! Maybe your browser doesn't support " +
            "SVG in img element or SVG input for Canvas drawImage?\n" +
            "http://en.wikipedia.org/wiki/SVG#Native_support"
        );

        // NOTE: will not return anything
    }

    function exportImageCanvg(type) {
        var canvas = document.createElement("canvas");
        var ctx = canvas.getContext('2d');
        var svg_xml = XMLSerialize(svg);

        // NOTE: canvg gets the SVG element dimensions incorrectly if not specified as attributes
        //debug("detected svg dimensions " + [svg.clientWidth, svg.clientHeight])
        //debug("canvas dimensions " + [canvas.width, canvas.height])

        var keepBB = options.keepOutsideViewport;
        if (keepBB) var bb = svg.getBBox();

        // NOTE: this canvg call is synchronous and blocks

        canvg(canvas, svg_xml, {
            ignoreMouse: true,
            ignoreAnimation: true,
            offsetX: keepBB ? -bb.x : undefined,
            offsetY: keepBB ? -bb.y : undefined,
            scaleWidth: keepBB ? bb.width + bb.x : undefined,
            scaleHeight: keepBB ? bb.height + bb.y : undefined,
            renderCallback: function () {
                debug("exported image dimensions " + [canvas.width, canvas.height]);
                var png_dataurl = canvas.toDataURL(type);
                debug(type + " length: " + png_dataurl.length);
                if (options.callback) options.callback(png_dataurl);
            }
        });

        // NOTE: return in addition to callback
        return canvas.toDataURL(type);
    }

    function XMLSerialize(svg) {

        //移除下载按钮
        if (svg.querySelector('g#saveAsImage')) {
            svg.querySelector('g#saveAsImage').setAttribute('display', 'none');
        }

        // quick-n-serialize an SVG dom, needed for IE9 where there's no XMLSerializer nor SVG.xml
        // s: SVG dom, which is the <svg> elemennt
        function XMLSerializerForIE(s) {
            var out = "";

            out += "<" + s.nodeName;
            for (var n = 0; n < s.attributes.length; n++) {
                out += " " + s.attributes[n].name + "=" + "'" + s.attributes[n].value + "'";
            }

            if (s.hasChildNodes()) {
                out += ">\n";

                for (var n = 0; n < s.childNodes.length; n++) {
                    out += XMLSerializerForIE(s.childNodes[n]);
                }

                out += "</" + s.nodeName + ">" + "\n";

            } else out += " />\n";

            return out;
        }

        if (window.XMLSerializer) {
            debug("using standard XMLSerializer.serializeToString");
            return (new XMLSerializer()).serializeToString(svg);
        } else {
            debug("using custom XMLSerializerForIE");
            return XMLSerializerForIE(svg);
        }
    }

    function base64dataURLencode(s) {
        var b64 = "data:image/svg+xml;base64,";

        if (window.btoa) {
            debug("using window.btoa for base64 encoding");
            //encodeURIComponent,解决btoa 仅支持 ASCII 字符序列问题
            b64 += btoa(encodeURIComponent(s));
        } else {
            debug("using custom base64 encoder");
            b64 += Base64.encode(s);
        }

        return b64;
    }


    // BEGIN MAIN

    if (!type) type = "image/svg+xml";
    if (!options) options = {};
    var reslut;
    switch (type) {

        case "image/svg+xml":
            reslut = exportSVG();
            break;

        case "image/png":
        case "image/jpeg":
            if (!options.renderer) {
                if (window.canvg) options.renderer = "canvg";
                else options.renderer = "native";
            }
            switch (options.renderer) {
                case "canvg":
                    debug("using canvg renderer for png export");
                    reslut = exportImageCanvg(type);
                    break;

                case "native":
                    debug("using native renderer for png export. THIS MIGHT FAIL.");
                    reslut = exportImage(type);
                    break;

                default:
                    debug("unknown png renderer given, doing noting (" + options.renderer + ")");
            }
            break;
        default:
            debug("Sorry! Exporting as '" + type + "' is not supported!")
    }
    return reslut;
};