/*
 * @Author: liyaoxu <EMAIL>
 * @Date: 2022-09-22 11:19:17
 * @LastEditors: liyaoxu <EMAIL>
 * @LastEditTime: 2023-11-22 10:27:58
 * @FilePath: \docroot\pages\alertSync\alertSync.js
 */
/*
 *同步告警数据进度
 * -----------------------
 * value:服务端返回的进度
 */
var syncAlertProgressFlag;
function checkSyncAlertProgress() {
    if (syncAlertProgressFlag) {
        //显示自定义进度条
        $('#exp-al-progress').removeClass("hide");
        $('#exp-al-cancel').removeClass("hide");
        $('#exp-al-ok').addClass('hide');
    } else {
        dismissIProgress();
        $('#exp-al-ok').removeClass('hide');
        $('#exp-al-progress').addClass("hide");
        $('#exp-al-cancel').addClass('hide').prop('disabled', false);
        $('#exp-al-cancel img').addClass('hide');
        $('#da-adus_leftAll').trigger('click');
    }
}
function syncAlertDataProgress(value) {
    var progress = $('#exp-al-progress');
    var progressBar = $('#exp-al-progress>.progress>.progress-bar');
    var progressText = $('#exp-al-progress>.progress-text');
    var progressNum = $('#exp-al-progress>.progress-number');

    var i_progress = $('#i-progress');
    var i_progressBar = $('#i-prog');
    var i_progressText = $('#i-proglabel');
    var i_progressNum = $('#i-prognum');

    var aduType = $("#exp-al-aduType");
    if (value.progress) {
        $.session.set('i-progress-sync', 'true');
        syncAlertProgressFlag = true;
        aduType.prop("disabled", true);
        progress.removeClass("hide");
        progressBar.css({
            "width": value.percent + "%"
        });
        progressText.text(stringFormat.format(getI18nName('alertSyncProgress'), value.progress));
        progressNum.text(value.percent + "%");

        i_progress.removeClass("hide");
        i_progressBar.css({
            "width": value.percent + "%"
        });
        i_progressText.text(stringFormat.format(getI18nName('alertSyncProgress2'), value.progress));
        i_progressNum.text(value.percent + "%");
    }
    if (value.aduId) {
        //var failadu = $("#aduItems  option[value='" + value.aduId + "']");
        var aduTypeText = aduType.children('option:selected').val();
        // var msg = '更新失败：' + aduTypeText + '_' + value.aduId;
        var msg = getI18nName('syncingDataFailed') + ":" + value.aduId;
        if (validateStrLen(aduTypeText) == 0) {
            msg = getI18nName('syncingDataFailed') + ":" + value.aduId;
        }
        if (value.aduId != "") {
            $('.notifications').notify({
                fadeOut: {
                    enabled: false
                },
                message: {
                    text: msg
                },
                type: "danger"
            }).show();
        }
    }
    if (value.isDone) {
        $.session.set('i-progress-sync', 'false');
        syncAlertProgressFlag = false;
        $('#exp-al-update').removeClass('hide');
        $('#exp-al-cancel').addClass('hide').prop('disabled', false);
        $('#exp-al-cancel img').addClass('hide');
        aduType.prop("disabled", false);
        progress.addClass("hide");
        progressBar.css({
            "width": ""
        });
        progressText.text(getI18nName('syncingData'));
        progressNum.text("");


        if (value.isDone == "update") {
            $('.notifications').notify({
                message: {
                    text: getI18nName('syncingDataSuccess')
                },
                type: "success",
            }).show();
        }
        if (value.isDone == "cancel") {
            //取消成功
            $('.notifications').notify({
                message: {
                    text: getI18nName('syncingDataCancel')
                },
                type: "warning",
            }).show();
        }
        dismissIProgress();
        checkSyncAlertProgress()
        //刷新前端版本
        var aduTypeText = aduType.children('option:selected').val();
        getADUVersionList(aduTypeText);
        $('#da-adus_leftAll').trigger('click');
    }
}