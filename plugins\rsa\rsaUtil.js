var RSA = function () {
    this.g_jsEncrypt = new JSEncrypt();
    var publicKey =
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDljZzJ5KU0y7wNL5tbQ1wuat2v" +
        "ZcZohoJd7V9hMZmc8Wiy2hgIdbLxCGLHMYFjXzu2RLqZaG/AjhAXwyRFOAhr7oo/" +
        "+XbA7PA0UfjofqoJh9DICPIRj6Lz8cImxLd3g91rFDp80h9i8VT2VjweK/ezsX44" +
        "ZrND9A+37C3TN3EPEQIDAQAB";
    // var publicKey =
    //     "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCgA8BjYDrczVgy97ovj0rgAZUDpPVt9e1+7TYDMLy" +
    //     "XSwqX0PIRtWORVL09Sj31DQo8goZW2BcABarfFgqRIP027UnLNKNjAiU+uUbF9PEwjrxIDhj88qHCfr4" +
    //     "0tsYCwUDkWjjfukgqI2da6xP5XtrFYJVFMaXaCDXkm0JgoiPoFQIDAQAB";
    this.g_jsEncrypt.setPublicKey(publicKey);
};
RSA.prototype.encode = function (msg) {
    return this.g_jsEncrypt.encrypt(msg);
};
var RSAUtil = new RSA();